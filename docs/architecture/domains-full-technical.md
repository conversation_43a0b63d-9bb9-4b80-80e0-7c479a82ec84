# Titan Domains - Comprehensive Technical Documentation

**Version**: 2.0  
**Last Updated**: July 2025

This document provides in-depth technical documentation for the domains within the Titan CRM system, covering APIs, data models, and business logic.

## 📋 Table of Contents

1. [Accounts Domain](#accounts-domain)
2. [Recipients Domain](#recipients-domain)
3. [Merge Tags Domain](#merge-tags-domain)
4. [Auth Domain](#auth-domain)
5. [Templates Domain](#templates-domain)
6. [Analytics Domain](#analytics-domain)
7. [Conversations Domain](#conversations-domain)
8. [Custom Pages Domain](#custom-pages-domain)
9. [Dashboard Domain](#dashboard-domain)
10. [Enterprise Domain](#enterprise-domain)
11. [Notifications Domain](#notifications-domain)
12. [Settings Domain](#settings-domain)
13. [Integrations Domain](#integrations-domain)
14. [Images Domain](#images-domain)
15. [Feature Flags Domain](#feature-flags-domain)
16. [User Domain](#user-domain)
17. [Products Domain](#products-domain)
18. [Twilio Domain](#twilio-domain)
19. [Data Sources Domain](#data-sources-domain)
20. [Notes Domain](#notes-domain)


## Accounts Domain

### Overview
The Accounts domain manages operations related to ads, logs, and account groups.

### Key Files
- **`account.ts`:** Provides functions to handle accounts, including ads, logs, and groups.

### Core Functions
```typescript
export function useAd(accountId: string, adId: string) {}
export function updateMultipleAds(accountId: string, adIds: string[], data: any) {}
```

### APIs
- **Firebase Firestore**: Manages account data

### Example Usage
```typescript
const { data, error } = useAd(accountId, adId);
```

## Recipients Domain

### Overview
Manages recipients for accounts.

### Key Files
- **`recipient.ts`:** Handles different recipient-related queries and mutations.

### Core Functions
```typescript
export function useRecipientsByAccountId(accountId: string) {}
export function useRecipient(accountId: string, recipientId: string) {}
```

### APIs
- **Firebase Firestore**: Recipients data storage
- **Lodash**: Utility functions

### Example Usage
```typescript
const { data } = useRecipientsByAccountId(accountId);
```

## Merge Tags Domain

### Overview
Facilitates merge tags operations.

### Key Files
- **`mergeTags.js`:** Offers CRUD functions on merge tags.

### Core Functions
```javascript
export async function createMergeTag(input) {}
export function useMergeTag(id) {}
```

### APIs
- **Firebase Firestore**: Provides data management

### Example Usage
```javascript
const { data } = useMergeTagList(filters);
```

## Auth Domain

### Overview
Manages user roles and authentication mechanisms.

### Key Files
- **`USER_ROLES.js`:** Enumerates user roles including Admin and SuperAdmin.

### Constants
```javascript
const USER_ROLES = {
  AccountManager: "ACT_MGR",
  Admin: "ADMIN",
};
```

### Example Usage
```javascript
import USER_ROLES from './USER_ROLES';
console.log(USER_ROLES.Admin);
```

## Templates Domain

### Overview
Focuses on email templates management.

### Key Files
- **`emails.js`:** Functions to handle email templates and campaigns.

### Core Functions
```javascript
export async function createEmailTemplate(email) {}
export function useEmailTemplates() {}
```

### APIs
- **Firebase Firestore**: Email template management

### Example Usage
```javascript
const { data } = useEmailTemplates();
```

## Analytics Domain

### Overview
Handles fetching of analytics data based on various parameters.

### Key Files
- **`analytics.js`:** Provides hooks to retrieve analytics data.

### Core Functions
```javascript
export function useLandingPageAnalyticsByAccountId(accountId) {}
export function useQRScanDataByAccountIdAndProductId(accountId, productId) {}
```

### APIs
- **Firebase Firestore**: Analytics data management

### Example Usage
```javascript
const { data } = useLandingPageAnalyticsByAccountId(accountId);
```

## Conversations Domain

### Overview
Manages conversations and messages for an account.

### Key Files
- **`conversations.tsx`:** Manages conversation states and data fetching.

### Core Functions
```typescript
export function useConversationsByAccountId(accountId: string, flag = "All") {}
export function useMessagesByConversationId(accountId: string, conversationId: string) {}
```

### APIs
- **Firebase Firestore**: Conversations data

### Example Usage
```typescript
const { conversations } = useConversationsByAccountId(accountId);
```

## Custom Pages Domain

### Overview
Maintains custom page designs and validations.

### Key Files
- **`validation.js`:** Validations for customizing pages.

### Core Functions
```javascript
const basicRecipientSchema = z.object({...});
```

### APIs
- **Zod**: For schema validations

### Example Usage
```javascript
const isValid = basicRecipientSchema.safeParse(inputData);
```

## Dashboard Domain

### Overview
Deals with dashboard-related data handling.

### Key Files
- **`minerva.ts`:** Hooks for managing Minerva dashboard data.

### Core Functions
```typescript
export function useMinervaById(id: string) {}
```

### APIs
- **Firebase Firestore**: Dashboard management

### Example Usage
```typescript
const { data } = useMinervaById(id);
```

## Enterprise Domain

### Overview
Handles enterprise-specific operations.

### Key Files
- **`enterprise.ts`:** Enterprise management functions and queries.

### Core Functions
```typescript
export function useEnterprise(id: string) {}
export async function createNewEnterprise(data: object) {}
```

### APIs
- **Firebase Firestore**: Enterprise data

### Example Usage
```typescript
const { data } = useEnterprise(id);
```

## Notifications Domain

### Overview
Manages notifications for accounts.

### Key Files
- **`notifications.js`:** Handles notification retrieval and update.

### Core Functions
```javascript
export function getNotificationData(notificationId) {}
export async function updateAllIsRead(accountId) {}
```

### APIs
- **Firebase Firestore**: Notifications management

### Example Usage
```javascript
const { data } = useAllUnreadNotifications(accountId, false);
```

## Settings Domain

### Overview
Deals with application settings.

### Key Files
- **`settings.ts`:** Provides setting retrieval and modifications.

### Core Functions
```typescript
export const fetchSettingsForGroup(group: string) {}
```

### APIs
- **Firebase Firestore**: Settings management

### Example Usage
```typescript
const options = await fetchSettingsForGroup(groupName);
```

## Integrations Domain

### Overview
Maintains integration data for accounts.

### Key Files
- **`account.ts`:** Integration functions such as fetching and updating.

### Core Functions
```typescript
export function useAccount(accountId: string) {}
export const validateRmApiKeyAtCyclr = async (accountId: string) => {}
```

### APIs
- **Firebase Firestore**: Integrations data

### Example Usage
```typescript
const { data } = useAccount(accountId);
```

## Images Domain

### Overview
Supports image operations and management.

### Key Files
- **`images.js`:** Image management and DAM (Digital Asset Management) API interaction.

### Core Functions
```javascript
export async function createImage(id, collectionName, note) {}
export async function getDamBucket(id) {}
```

### APIs
- **Firebase Firestore**: Image data storage

### Example Usage
```javascript
const bucket = await getDamBucket(bucketId);
```

## Feature Flags Domain

### Overview
Manages feature flag settings for applications.

### Key Files
- **`featureFlag.js`:** Feature flag CRUD operations.

### Core Functions
```javascript
export function useFeatureFlags() {}
```

### APIs
- **Firebase Firestore**: Feature flag data

### Example Usage
```javascript
const { flags } = useFeatureFlags();
```

## User Domain

### Overview
Deals with user management.

### Key Files
- **`user.ts`:** User CRUD operations and queries.

### Core Functions
```typescript
export function useUser(uid: any) {}
export function getUser(uid: string) {}
```

### APIs
- **Firebase Firestore**: User data management

### Example Usage
```typescript
const { data } = useUser(uid);
```

## Products Domain

### Overview
Relates to products data and operations.

### Key Files
- **`product.ts`:** Product data handling and manipulation.

### Core Functions
```typescript
export function useAllProducts() {}
```

### APIs
- **Firebase Firestore**: Product data management

### Example Usage
```typescript
const { data } = useAllProducts();
```

## Twilio Domain

### Overview
Manages Twilio-related account actions.

### Key Files
- **`account.js`:** Integrates with Twilio API to manage account connections.

### Core Functions
```javascript
export function useAllAccounts(active) {}
```

### APIs
- **Firebase Firestore**: Twilio data

### Example Usage
```javascript
const { data } = useAllAccounts(true);
```

## Data Sources Domain

### Overview
Handles operations related to data sources connections.

### Key Files
- **`dataSources.js`:** Data source management functions, including create, update, and delete.

### Core Functions
```javascript
export const useDataSources = (enabledOnly = false) => {}
```

### APIs
- **React Query**: Enables state management

### Example Usage
```javascript
const { data } = useDataSources();
```

## Notes Domain

### Overview
Offers functionality for managing notes.

### Key Files
- **`notes.ts`:** CRUD operations for notes in collections.

### Core Functions
```typescript
export async function createNote(
  id: string,
  collectionName: string,
  note: object
) {}
```

### APIs
- **Firebase Firestore**: Notes data storage

### Example Usage
```typescript
await createNote("123", "Accounts", { text: "This is a note " });
```

*This documentation provides meticulous insight into the core domains of the Titan CRM System and serves as a comprehensive guide for developers engaging in system maintenance or enhancement.*

---

For complete details on each function, refer to the corresponding file in the `src/domain` directory.
