# Global Components Documentation

## Overview
This document provides technical documentation for global components used throughout the Titan system, facilitating UI development with shared React components.

## Components

### RMTable
- **File**: `src/components/rm-ui/rm-table/RmTable.jsx`
- **Purpose**: Provides a data table with pagination, sorting, filtering, and column visibility toggle features.
- **Key Features**:
  - Utilizes `@tanstack/react-table` for core row models.
  - Supports column resizing and sorting.
  - Navigation on row click if `isClickable` is true.
  - Extensively customizable through props like `data`, `columns`, and `pageSize`.

### DialogWrapper
- **File**: `src/components/rm-ui/dialog/Dialog.tsx`
- **Purpose**: Renders an accessible modal dialog with customizable styles.
- **Integration**: Uses `@headlessui/react` for transitions and accessibility.
- **Key Features**:
  - Comes with props like `size`, `title`, `bgColor`, and `onClose`.
  - Includes enter/leave animations for smooth transitions.
  - Suitable for any content as children are passed as-is.

### BasicButton
- **File**: `src/components/rm-ui/button/BaseButton.tsx`
- **Purpose**: Provides a responsive button with text resizing functionality.
- **Integration**: Utilizes `react-fitty` to auto-adjust text size.
- **Key Features**:
  - Configurable color, size, and text.
  - Automatically adjusts font size between `minSize` and `maxSize` based on text length.
  - Inherits standard button behaviors with additional styling options.

## Conclusion
These global components help standardize the user interface across the application by providing reusable, configurable building blocks. They promote code consistency and reduce redundancy in UI code.

---
