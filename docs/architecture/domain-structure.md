# Domain Structure Documentation

## Overview
This document provides detailed insights into the domain structures under the `src/domain` directory. Titan leverages domain-driven design, organizing functionality by business module.

## Domains

### Accounts
- **Core Functionality**: Manages account details, advertisements, billing, and workflows.
- **Data Handling**:
  - Uses `useQuery` from React Query to fetch and cache data.
  - Handles operations like updating ads, managing workflows, and accessing logs.
- **Components**:
  - Provides multiple React components for dashboards, ad management, and billing.

### Recipients
- **Core Functionality**: Manages recipient data, mailing addresses, and deliverability.
- **Data Handling**:
  - Provides querying capabilities for recipient information, email deliverability, and archived data.
  - Batch operations for importing and processing recipient data.
- **Components**:
  - Includes components for displaying recipient details, groups, and importing data.

### Merge Tags
- **Core Functionality**: Manages and utilizes merge tags for personalization in emails and documents.
- **Data Handling**:
  - Provides CRUD operations and hooks to fetch merge tag lists with optional filters.
  - Utilizes Firestore for real-time tag updates and management.
- **Components**:
  - Supports enhanced tag picker and integration interfaces for dynamic content replacement.

## Conclusion
The domain-driven structure of Titan ensures modularity and scalability. Each domain is equipped with specialized functionality to manage specific business processes while maintaining a unified architecture.

---
