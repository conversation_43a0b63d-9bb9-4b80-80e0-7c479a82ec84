# Titan Global Components - Technical Documentation

**Version**: 2.0  
**Last Updated**: July 2025

This document provides comprehensive technical documentation for all global components in the Titan CRM system, covering UI components, form elements, data display, and utility components.

## 📋 Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Data Display Components](#data-display-components)
3. [Form Components](#form-components)
4. [UI Components](#ui-components)
5. [Navigation Components](#navigation-components)
6. [Utility Components](#utility-components)
7. [Integration Patterns](#integration-patterns)
8. [Performance Considerations](#performance-considerations)

## 🏗️ Architecture Overview

### Component Organization
- **Location**: `src/components/rm-ui/`
- **Naming Convention**: Component names follow PascalCase (e.g., `RMTable`, `DialogWrapper`)
- **File Structure**: Each component typically has its own directory with main component and supporting files
- **Styling**: TailwindCSS classes with custom CSS variables for theming

### Dependencies
- **Core**: React 18+, TypeScript/JavaScript
- **External Libraries**: 
  - `@tanstack/react-table` for data tables
  - `@headlessui/react` for accessible UI components
  - `react-fitty` for responsive text sizing
  - `react-hook-form` for form management
  - `lodash` for utility functions

## 📊 Data Display Components

### RMTable Component
**File**: `src/components/rm-ui/rm-table/RmTable.jsx`

#### Technical Specifications
- **Purpose**: Advanced data table with pagination, sorting, filtering, and column management
- **Dependencies**: `@tanstack/react-table` v8.x
- **Memory Usage**: Optimized for datasets up to 10,000 rows
- **Performance**: Virtual scrolling for large datasets

#### Props Interface
```typescript
interface RMTableProps {
  data: any[];                    // Array of data objects
  columns: ColumnDef<any>[];      // TanStack table column definitions
  pageSize?: number;              // Default: 10
  isClickable?: boolean;          // Default: false
  navigatePath?: string | null;   // Route pattern for navigation
}
```

#### Core Features
- **Pagination**: Built-in pagination with configurable page sizes (10, 25, 50, 75, 100)
- **Sorting**: Multi-column sorting with visual indicators
- **Filtering**: Column-specific filtering with debounced input
- **Column Management**: Show/hide columns with visibility toggles
- **Responsive Design**: Adaptive layout for mobile and desktop

#### Technical Implementation
```javascript
// Core table configuration
const table = useReactTable({
  data: data,
  columns: columns,
  getCoreRowModel: getCoreRowModel(),
  getPaginationRowModel: getPaginationRowModel(),
  getSortedRowModel: getSortedRowModel(),
  getFilteredRowModel: getFilteredRowModel(),
  getFacetedRowModel: getFacetedRowModel(),
  getFacetedUniqueValues: getFacetedUniqueValues(),
  getFacetedMinMaxValues: getFacetedMinMaxValues(),
  columnResizeMode: "onChange",
  enableColumnResizing: true,
  // ... state management
});
```

#### State Management
- **Pagination State**: `{ pageIndex: number, pageSize: number }`
- **Column Filters**: Array of filter objects with id and value
- **Column Visibility**: Object mapping column IDs to visibility boolean
- **Sorting State**: Array of sort descriptors

#### Performance Optimizations
- **Memoization**: Column definitions and filter functions memoized
- **Lazy Loading**: Filters applied on-demand
- **Debounced Filtering**: 500ms delay to prevent excessive re-renders

### TableFilter Component
**File**: `src/components/rm-ui/rm-table/TableFilter.tsx`

#### Technical Specifications
- **Purpose**: Advanced filtering for table columns
- **Filter Types**: Text, Range, Select dropdown
- **Dependencies**: TanStack React Table column API

#### Filter Variants
1. **Text Filter**: Debounced input with autocomplete suggestions
2. **Range Filter**: Min/max numeric inputs with validation
3. **Select Filter**: Dropdown with unique values from dataset

#### Implementation Details
```typescript
interface TableFilterProps {
  column: Column<any, unknown>;
}

// Filter type determination
const { filterVariant } = column.columnDef.meta ?? {};
const columnFilterValue = column.getFilterValue();
const sortedUniqueValues = useMemo(() => 
  filterVariant === "range" ? [] : 
  Array.from(column.getFacetedUniqueValues().keys())
    .sort()
    .slice(0, 5000)
, [column.getFacetedUniqueValues(), filterVariant]);
```

#### DebouncedInput Component
- **Debounce Delay**: 500ms configurable
- **Input Types**: Text, number, with proper validation
- **State Management**: Local state with useEffect synchronization

## 📝 Form Components

### AutoSaveText Component
**File**: `src/components/rm-ui/input-fields/autoSave/auto-save-field.jsx`

#### Technical Specifications
- **Purpose**: Auto-saving text input with real-time validation
- **Database**: Firestore integration with automatic persistence
- **Validation**: Required field, phone number, length validation
- **Activity Logging**: Automatic change tracking

#### Props Interface
```typescript
interface AutoSaveTextProps {
  value: string;              // Initial value
  collection: string;         // Firestore collection name
  recId: string;             // Document ID
  name: string;              // Field name
  display: string;           // Display label
  placeholder?: string;      // Input placeholder
  required?: boolean;        // Required field validation
  readOnly?: boolean;        // Read-only mode
  maxlength?: number;        // Maximum character limit (default: 50)
  forceLowerCase?: boolean;  // Force lowercase transformation
  forceUpperCase?: boolean;  // Force uppercase transformation
  validatePhoneNumber?: boolean; // Phone number validation
  inputType?: string;        // Input type (default: "text")
  className?: string;        // Additional CSS classes
  testingName?: string;      // Test ID for automation
  toolTip?: string;          // Tooltip text
}
```

#### Core Features
- **Auto-Save**: Saves on blur if value changed
- **Validation**: Real-time validation with error states
- **Activity Logging**: Tracks all changes with user context
- **Visual Feedback**: Loading states, success indicators, error messages
- **Link Detection**: Automatically detects and makes URLs clickable

#### Technical Implementation
```javascript
// Auto-save logic
const handleBlur = async () => {
  if (coreValue !== check) {
    await updateAutoSave(db, collection, recId, {
      [name]: transformValue(coreValue)
    });
    await logActivity(db, collection, recId, name, check, coreValue, 
      auth.user.uid, check === "" ? "Added" : "Edited");
    setSaved(true);
  }
};

// Phone number validation
const isValidPhoneNumber = (phoneNumber) => {
  const phoneRegex = /^(?:\+1)?\s*\(?([2-9][0-8][0-9])\)?[-.s]?([2-9][0-9]{2})[-.s]?([0-9]{4})$/;
  return phoneRegex.test(phoneNumber);
};
```

#### State Management
- **Core State**: `coreValue`, `check`, `saved`, `error`, `isValid`
- **Validation State**: `length`, `maxLength`, `isLink`
- **Effect Hooks**: Value synchronization, auto-save timeout, validation

### InputField Component
**File**: `src/components/rm-ui/input-fields/input-field.jsx`

#### Technical Specifications
- **Purpose**: Form-integrated input field with react-hook-form
- **Integration**: React Hook Form context required
- **Validation**: Built-in validation with error display
- **Testing**: Required test ID for automation

#### Props Interface
```typescript
interface InputFieldProps {
  fieldName: string;         // Field name for form registration
  defaultValue?: string;     // Default field value
  testingName?: string;      // Test ID (required)
  rules?: object;            // Validation rules
  readOnly?: boolean;        // Read-only state
  label?: string;            // Field label
  placeholder?: string;      // Input placeholder
  type?: string;             // Input type (default: "text")
  errorMessage?: string;     // Custom error message
  required?: boolean;        // Required field indicator
  inputClassName?: string;   // Additional CSS classes
}
```

#### Implementation Details
```javascript
// Form context integration
const context = useFormContext();
const { errors, register } = context;

// Registration with validation
<input
  {...register(fieldName, { ...rules })}
  className={inputClassName}
  type={type}
  placeholder={placeholder}
  defaultValue={defaultValue}
  readOnly={readOnly}
/>
```

### SearchBar Component
**File**: `src/components/rm-ui/search-bar/search-bar.jsx`

#### Technical Specifications
- **Purpose**: Firestore-integrated search with multiple field support
- **Database**: Direct Firestore queries with parallel execution
- **Deduplication**: Automatic result deduplication by document ID
- **Performance**: Parallel query execution with Promise.all

#### Props Interface
```typescript
interface SearchBarProps {
  collectionName: string;    // Firestore collection name
  searchFields: string[];    // Array of fields to search
  searchTermString?: string; // Initial search term
  onResults: (results: any[]) => void; // Results callback
}
```

#### Search Logic
```javascript
const handleSearch = async () => {
  const uniqueDocs = new Map();
  const searches = searchFields.map((field) => {
    let q = query(collection(db, collectionName), where(field, "==", searchTerm));
    return getDocs(q).then((querySnapshot) => {
      querySnapshot.forEach((doc) => {
        uniqueDocs.set(doc.id, { id: doc.id, ...doc.data() });
      });
    });
  });
  
  await Promise.all(searches);
  const results = Array.from(uniqueDocs.values());
  onResults(results);
};
```

## 🎨 UI Components

### DialogWrapper Component
**File**: `src/components/rm-ui/dialog/Dialog.tsx`

#### Technical Specifications
- **Purpose**: Accessible modal dialog with animations
- **Dependencies**: `@headlessui/react` for accessibility
- **Animations**: Smooth enter/exit transitions
- **Focus Management**: Automatic focus trapping

#### Props Interface
```typescript
interface DialogWrapperProps {
  children: JSX.Element;     // Dialog content
  title: string;             // Dialog title
  size?: string;             // Dialog size classes
  onClose: () => void;       // Close handler
  titleColor?: string;       // Title text color
  bgColor?: string;          // Background color
  padding?: string;          // Padding classes
}
```

#### Animation Configuration
```javascript
// Enter transition
enter="ease-out duration-300"
enterFrom="opacity-0"
enterTo="opacity-100"

// Leave transition  
leave="ease-in duration-200"
leaveFrom="opacity-100"
leaveTo="opacity-0"
```

### BaseButton Component
**File**: `src/components/rm-ui/button/BaseButton.tsx`

#### Technical Specifications
- **Purpose**: Responsive button with auto-sizing text
- **Dependencies**: `react-fitty` for text scaling
- **Accessibility**: Full button props support
- **Responsive**: Text scales between min/max sizes

#### Props Interface
```typescript
interface ButtonProps extends React.ComponentProps<"button"> {
  text: string;              // Button text
  minSize?: number;          // Minimum font size (default: 8)
  maxSize?: number;          // Maximum font size (default: 16)
  buttonColor?: string;      // Background color class
}
```

#### Implementation
```javascript
<ReactFitty minSize={minSize} maxSize={maxSize}>
  {text}
</ReactFitty>
```

### RoundChip Component
**File**: `src/components/rm-ui/chips/RoundChip.tsx`

#### Technical Specifications
- **Purpose**: Styled chip/badge component with optional tooltip
- **Styling**: Rounded design with customizable colors
- **Accessibility**: Tooltip integration for additional context

#### Props Interface
```typescript
interface RoundChipProps {
  label: string;             // Chip text
  colorClass?: string;       // Background color class
  minSize?: number;          // Minimum size (default: 8)
  maxSize?: number;          // Maximum size (default: 16)
  tooltipText?: string;      // Optional tooltip text
}
```

## 🧭 Navigation Components

### SubMenuIcon Component
**File**: `src/components/rm-ui/icons/SubMenuIcon.jsx`

#### Technical Specifications
- **Purpose**: Contextual icon display for menus and navigation
- **Asset Management**: Dynamic icon loading from assets
- **Styling**: Consistent sizing and styling across usage

### BreadCrumb Component
**File**: `src/components/rm-ui/breadcrumb/BreadCrumb.jsx`

#### Technical Specifications
- **Purpose**: Hierarchical navigation breadcrumb
- **Integration**: React Router integration
- **Styling**: Consistent with design system

## 🔧 Utility Components

### Tooltip Component
**File**: `src/components/rm-ui/tooltip/tooltip.jsx`

#### Technical Specifications
- **Purpose**: Contextual help and information display
- **Positioning**: Smart positioning to avoid viewport edges
- **Accessibility**: ARIA compliant with proper roles

### PageLoader Component
**File**: `src/components/rm-ui/pageLoader/PageLoader.jsx`

#### Technical Specifications
- **Purpose**: Loading state indication
- **Animation**: Smooth spinner animation
- **Accessibility**: Screen reader friendly

### ProgressBar Component
**File**: `src/components/rm-ui/progress-bar/ProgressBar.tsx`

#### Technical Specifications
- **Purpose**: Progress indication for long-running operations
- **Styling**: Customizable colors and animations
- **Accessibility**: Progress role with aria-valuemin/max

## 🔗 Integration Patterns

### React Hook Form Integration
```typescript
// Form provider setup
import { useForm, FormProvider } from "react-hook-form";

const MyForm = () => {
  const methods = useForm();
  
  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)}>
        <InputField fieldName="email" testingName="email-input" />
      </form>
    </FormProvider>
  );
};
```

### Firestore Integration
```javascript
// Auto-save integration
import { updateAutoSave } from "../../utils/utility";
import { db } from "../../providers/database";

const saveField = async (collection, docId, fieldName, value) => {
  await updateAutoSave(db, collection, docId, {
    [fieldName]: value
  });
};
```

### React Query Integration
```javascript
// Data fetching with caching
import { useQuery } from "@tanstack/react-query";

const useTableData = (params) => {
  return useQuery({
    queryKey: ["tableData", params],
    queryFn: () => fetchTableData(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
```

## ⚡ Performance Considerations

### Memory Management
- **Component Memoization**: React.memo for expensive components
- **Callback Optimization**: useCallback for event handlers
- **Effect Dependencies**: Proper dependency arrays in useEffect

### Bundle Optimization
- **Code Splitting**: Dynamic imports for large components
- **Tree Shaking**: Proper import/export structure
- **Asset Optimization**: Lazy loading for images and icons

### Render Performance
- **Virtual Scrolling**: For large data sets in tables
- **Debounced Inputs**: Prevent excessive re-renders
- **Memoized Selectors**: For derived state calculations

## 🧪 Testing Patterns

### Component Testing
```javascript
// Example test pattern
import { render, screen } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";

const TestWrapper = ({ children }) => {
  const methods = useForm();
  return <FormProvider {...methods}>{children}</FormProvider>;
};

test("renders input field with label", () => {
  render(
    <TestWrapper>
      <InputField fieldName="test" label="Test Field" testingName="test-input" />
    </TestWrapper>
  );
  
  expect(screen.getByLabelText("Test Field")).toBeInTheDocument();
});
```

### Integration Testing
- **End-to-End**: Playwright tests for complete workflows
- **API Integration**: Mock Firestore for consistent testing
- **User Interaction**: Comprehensive interaction testing

## 📚 Best Practices

### Component Development
1. **Props Validation**: Use TypeScript interfaces
2. **Error Boundaries**: Implement error handling
3. **Accessibility**: Follow ARIA guidelines
4. **Performance**: Optimize re-renders and memory usage

### Code Organization
1. **Single Responsibility**: Each component has one clear purpose
2. **Reusability**: Design for reuse across domains
3. **Documentation**: Comprehensive prop and usage documentation
4. **Testing**: Unit and integration test coverage

### Styling Guidelines
1. **Consistent Naming**: Follow established CSS class patterns
2. **Responsive Design**: Mobile-first approach
3. **Theme Variables**: Use CSS custom properties
4. **Accessibility**: Proper color contrast and focus states

---

*This documentation provides comprehensive technical details for all global components in the Titan system. For specific implementation examples and advanced usage patterns, refer to the individual component files and their accompanying tests.*
