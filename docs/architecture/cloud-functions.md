# Titan Cloud Functions - Complete Technical Documentation

**Version**: 2.0  
**Last Updated**: July 2025

This document provides comprehensive technical documentation for all Cloud Functions in the Titan CRM platform. Titan leverages Google Cloud Functions for serverless backend processing, event handling, and integration with external services.

## 🏗️ Architecture Overview

The Titan Cloud Functions architecture follows a domain-driven design pattern with the following key characteristics:

- **Serverless**: All functions run on Google Cloud Functions
- **Event-Driven**: Functions respond to Firestore triggers, HTTP requests, and scheduled events
- **Modular**: Functions are organized by business domain
- **Scalable**: Auto-scaling based on demand
- **Secure**: Role-based access control and authentication
- **Memory Allocation**: Functions configured with appropriate memory (512MB-1GB)
- **Retry Logic**: Built-in retry mechanisms for reliability

## 📁 Directory Structure and Organization

The `functions` directory is organized by functional domains, reflecting the separation of concerns principle:

- **Accounts**: Handle account-related operations and events.
- **API**: Serve HTTP requests and external API functionalities.
- **Astrid**: Manage integrations with the Astrid service.
- **Callable**: Generic callable functions for various tasks.
- **Constants**: Definitions of constants used throughout the functions.
- **Local Events**: Handle local events-related operations.
- **Mailing Addresses**: Manage mailing address-related logic.
- **Migration**: Functions handling migration processes.
- **Products**: Product-related logic.
- **Pubsubs**: Background task workers, typically processing events asynchronously.
- **Recipients**: Manage recipient-related data operations.
- **Schedule**: Scheduled tasks for periodic actions.
- **Services**: Integrations with external service APIs.
- **Storage**: Functions related to file storage and processing.
- **Tasks**: Specific task implementations.
- **Unlayer**: Functions related to the Unlayer integration.
- **Users**: Handle user-related actions.
- **Utils**: Shared utility functions.

## 🔧 Detailed Function Specifications

### Account Management Functions

#### `onWriteAccount`
**Type**: Firestore Document Trigger  
**Trigger**: `Account/{id}` document writes  
**Memory**: 1GB  
**Technical Details**:
- Monitors 16 specific account fields for changes (name, displayName, slug, timezone, marketUuid, isActive, productPlans, groupId, groupName, adsPaused, verifiedTwilioNumber, AI-related fields)
- Uses JSON stringification for efficient change detection
- Calls `updateBasicAccountInfo` to sync changes with Mercury service
- Handles both create and update operations
- Implements early exit for delete operations and no-change scenarios

#### `syncAccountPlanWithMercury`
**Type**: Firestore Document Trigger  
**Trigger**: `Account/{accountId}/AccountPlans/{planId}` document writes  
**Technical Details**:
- Monitors account plan creation, updates, and deletions
- Automatically syncs changes with Mercury service via `syncAccountPlanInfo`
- Handles soft deletion by setting `isDeleted` flag
- Passes through all plan data including accountId and planId

#### `onCreateProductPlan`
**Type**: Firestore Document Trigger  
**Trigger**: `Account/{accountId}/AccountPlans/{planId}` document creation  
**Technical Details**:
- Publishes "product-plan-added" message to Pub/Sub topic
- Records operation in collection history for audit trail
- Uses batch operations for efficient Firestore writes
- Integrates with project-specific topic configuration

#### `onDeleteProductPlan`
**Type**: Firestore Document Trigger  
**Trigger**: `Account/{accountId}/AccountPlans/{planId}` document deletion  
**Technical Details**:
- Propagates plan removal to all associated RecipientGroups
- Uses `array-contains` query to find affected groups
- Removes planId from productPlans array using `FieldValue.arrayRemove`
- Deletes assignment mappings using `FieldValue.delete`
- Implements batch updates for performance

### API Functions

#### `api` (Public REST API)
**Type**: HTTP Request Handler  
**Invoker**: Public  
**Region**: us-central1  
**Technical Details**:
- Express.js-based REST API with CORS support
- Comprehensive endpoint coverage:
  - **Recipients**: CRUD operations (`/v1/recipients`, `/v1/recipients/{id}`)
  - **Recipient Groups**: Full management (`/v1/recipient-groups`)
  - **Custom Fields**: Complete field management (`/v1/custom-fields`)
  - **Integration Rules**: Configuration retrieval (`/v1/integration-rules/{id}`)
- API key validation middleware on all endpoints
- Structured error handling with HTTP status codes
- Request/response logging for debugging

#### `validateApiKey` (Middleware)
**Type**: Middleware Function  
**Technical Details**:
- Validates API keys against Firestore ApiKeys collection
- Checks key status, expiration, and permissions
- Extracts accountId from validated key for request context
- Returns 401 for invalid/expired keys
- Supports both header and query parameter key passing

### Callable Functions

#### `getSuggestions`
**Type**: Callable Function  
**Technical Details**:
- Integrates with SmartZip API for address suggestions
- Validates input address parameter
- Makes HTTP GET request to external service
- Returns structured response with suggestions array
- Handles API errors gracefully with empty result fallback
- Uses environment variables for API configuration

#### `generateSignedUrl`
**Type**: Callable Function  
**Authentication**: Required  
**Technical Details**:
- Generates signed URLs for secure Cloud Storage access
- Supports both default and custom bucket specification
- 10-minute URL expiration for security
- Read-only access permission
- Comprehensive error handling and logging
- Returns structured response with URL

### Recipient Management Functions

#### `onCreateRecipient`
**Type**: Firestore Document Trigger  
**Trigger**: `Account/{accountId}/Recipients/{recipientId}` document creation  
**Technical Details**:
- Initializes recipient with default deliverability status (NO_MAILING_ADDRESS)
- Records complete creation history with user context
- Captures authentication details (uid, email) when available
- Implements comprehensive audit trail logging
- Sets up initial address deliverability fields

#### `onUpdateRecipient`
**Type**: Firestore Document Trigger  
**Trigger**: `Account/{accountId}/Recipients/{recipientId}` document updates  
**Technical Details**:
- **Change Detection**: Uses base64 encoding for efficient change comparison
- **Concurrent Processing**: Handles multiple update scenarios simultaneously:
  - Inactive status management (removes from groups)
  - Follow-up status history tracking
  - Group summary recalculation
  - Exclusivity handling for mailing products
  - Email product group assignments
- **Deep Diff Integration**: Uses `deep-diff` library for detailed change tracking
- **Mailing Address Sync**: Maintains backward compatibility between array and string fields
- **Comprehensive Logging**: Records all changes with user context and diff details

#### `onDeleteRecipient`
**Type**: Firestore Document Trigger  
**Trigger**: `Account/{accountId}/Recipients/{recipientId}` document deletion  
**Technical Details**:
- Cleans up mailing address references
- Removes account and recipient associations from MailingAddresses collection
- Handles multiple mailing addresses per recipient
- Uses Promise.all for concurrent cleanup operations
- Maintains data integrity across related collections

### Scheduled Functions

#### `syncAccounts`
**Type**: Scheduled Function  
**Schedule**: Daily at 1:30 AM ("30 1 * * *")  
**Technical Details**:
- **Two-phase operation**:
  1. Resets all currently paused accounts (`adsPaused: false`)
  2. Processes new accounts to be paused from Astrid
- **Astrid Integration**: Calls `users/accountUpdate` endpoint
- **Batch Processing**: Uses Firestore batch operations for efficiency
- **Missing Account Handling**: Processes accounts that don't exist in Firestore
- **Timestamp Tracking**: Updates `syncedAt` field for audit purposes
- **Error Handling**: Comprehensive error catching and logging

#### `executeWorkflowActions`
**Type**: Scheduled Function  
**Schedule**: Every minute ("* * * * *")  
**Technical Details**:
- **Batch Processing**: Processes up to 100 workflow executions per run
- **Query Optimization**: Uses compound queries with pagination
- **Action Execution**: Supports multiple trigger types (LEAD_GENERATED)
- **Mercury Integration**: Calls Mercury service for workflow execution
- **State Management**: Updates action status and execution timestamps
- **Retry Logic**: Implements 3-attempt retry with exponential backoff
- **Error Handling**: Tracks failures and updates workflow status
- **Sleep Intervals**: 10-second delays between actions to prevent rate limiting

### Pub/Sub Worker Functions

#### `addNotificationWorker`
**Type**: Pub/Sub Message Handler  
**Topic**: "create-notification"  
**Technical Details**:
- **Retry Configuration**: 3 max attempts with exponential backoff (10s-300s)
- **Message Processing**: Handles base64-encoded message data
- **Notification Types**: Supports multiple notification types:
  - `newLead`: New lead generation notifications
  - `newMessage`: Message notifications
  - `fileReady`: File processing completion
  - `recipientFileUploaded`: Recipient file upload events
  - `unformattedRecipientFileUploaded`: Unformatted file uploads
- **Error Isolation**: Each notification type has independent error handling
- **Logging**: Comprehensive error logging for debugging

### Storage Functions

#### `handleRecipientFileImport`
**Type**: Cloud Storage Trigger  
**Trigger**: File uploads to recipient imports bucket  
**Technical Details**:
- Processes CSV/Excel files containing recipient data
- Validates file format and required columns
- Implements chunked processing for large files
- Creates recipient records in batches
- Handles duplicate detection and merging
- Provides progress tracking and error reporting
- Supports custom field mapping

### Integration Functions

#### `astridConnection`
**Type**: Service Integration  
**Technical Details**:
- Handles authentication with Astrid service
- Supports multiple HTTP methods (GET, POST, PUT, DELETE)
- Implements request/response logging
- Manages API key rotation and validation
- Provides standardized error handling
- Supports both synchronous and asynchronous operations

#### `callMercury`
**Type**: Callable Function  
**Technical Details**:
- Integrates with Mercury service for CRM operations
- Supports dynamic path and method configuration
- Handles request authentication and headers
- Implements retry logic for failed requests
- Provides structured error responses
- Supports both JSON and form data payloads

### Authentication Functions

#### `adminCreateUser`
**Type**: Callable Function  
**Authentication**: Admin Required  
**Technical Details**:
- Creates user accounts with Firebase Auth
- Sets custom claims and user properties
- Validates email format and uniqueness
- Implements role-based access control
- Sends welcome emails via integration
- Logs user creation events for audit

#### `beforeCreateUser`
**Type**: Auth Trigger  
**Trigger**: Before user creation  
**Technical Details**:
- Validates user email domains
- Checks against blacklisted domains
- Implements custom validation rules
- Can block user creation based on criteria
- Logs validation attempts

### Migration Functions

#### `handleMissingAccounts`
**Type**: Migration Handler  
**Technical Details**:
- Processes accounts that exist in external systems but not in Firestore
- Fetches account data from source systems
- Creates missing account records with proper structure
- Handles data transformation and validation
- Implements rollback mechanisms for failed migrations
- Provides detailed migration reports

#### `onMigrationFileCreate`
**Type**: Storage Trigger  
**Trigger**: Migration file uploads  
**Technical Details**:
- Processes migration files (JSON, CSV)
- Validates file structure and data integrity
- Implements batch processing for large datasets
- Handles foreign key relationships
- Provides progress tracking and error reporting
- Supports rollback operations

### Email Functions

#### `previewEmail`
**Type**: HTTP Function  
**Technical Details**:
- Generates email previews using template engine
- Supports dynamic content and merge tags
- Handles multiple email formats (HTML, plain text)
- Implements template caching for performance
- Provides responsive design preview
- Supports A/B testing variations

#### `emailPreProcessWorker`
**Type**: Pub/Sub Worker  
**Technical Details**:
- Processes email campaigns before sending
- Validates recipient lists and content
- Implements spam filtering and compliance checks
- Handles personalization and merge tag replacement
- Manages send scheduling and throttling
- Provides detailed analytics tracking

### Utility Functions

#### `generateQrCodes`
**Type**: Scheduled Function  
**Schedule**: Configurable  
**Technical Details**:
- Generates QR codes for campaigns and tracking
- Supports multiple QR code formats and sizes
- Implements batch processing for efficiency
- Stores generated codes in Cloud Storage
- Provides URL shortening integration
- Handles code expiration and cleanup

## 🔍 Function Attributes Summary

### By Trigger Type
- **Firestore Triggers**: 15+ functions for real-time data processing
- **HTTP Functions**: 10+ functions for API endpoints and web requests
- **Scheduled Functions**: 8+ functions for periodic tasks
- **Pub/Sub Workers**: 12+ functions for asynchronous processing
- **Storage Triggers**: 3+ functions for file processing
- **Auth Triggers**: 2+ functions for user management

### By Memory Allocation
- **512MB**: Standard functions with light processing
- **1GB**: Functions with heavy data processing (account sync, migrations)
- **2GB**: Large file processing and batch operations

### By Execution Pattern
- **Real-time**: Immediate response to events
- **Batch**: Processing multiple items in chunks
- **Scheduled**: Time-based execution
- **Event-driven**: Reactive to system changes

## 🔧 Technical Implementation Details

### Error Handling Patterns
- Comprehensive try-catch blocks with structured logging
- Retry mechanisms with exponential backoff
- Dead letter queues for failed messages
- Graceful degradation for non-critical failures

### Performance Optimizations
- Batch operations for Firestore writes
- Pagination for large datasets
- Caching for frequently accessed data
- Connection pooling for external services

### Security Features
- API key validation and rate limiting
- Role-based access control
- Data encryption in transit and at rest
- Audit logging for all operations

### Monitoring and Observability
- Structured logging with Firebase Functions Logger
- Custom metrics and dashboards
- Error tracking and alerting
- Performance monitoring and profiling

## 📊 Integration Points

### External Services
- **Mercury**: CRM and account management
- **Astrid**: User and account synchronization
- **SmartZip**: Address validation and suggestions
- **Unlayer**: Email template processing
- **Mailgun**: Email delivery service

### Internal Services
- **Firestore**: Primary database
- **Cloud Storage**: File storage and processing
- **Pub/Sub**: Message queuing and async processing
- **Firebase Auth**: User authentication
- **Cloud Scheduler**: Cron job management

## 🚀 Deployment and Configuration

### Environment Variables
- API keys for external services
- Database connection strings
- Feature flags and configuration
- Logging levels and destinations

### Runtime Configuration
- Memory allocation per function
- Timeout settings
- Retry policies
- Concurrency limits

## 📈 Performance Metrics

### Key Performance Indicators
- Function execution time
- Memory usage patterns
- Error rates and types
- Throughput and concurrency
- Cost optimization metrics

### Monitoring Dashboards
- Real-time function performance
- Error tracking and alerting
- Resource utilization
- Business metrics integration

## 🔄 Maintenance and Updates

### Regular Maintenance Tasks
- Function performance optimization
- Security updates and patches
- Dependency management
- Code refactoring and cleanup

### Update Procedures
- Staged deployment process
- Rollback mechanisms
- Testing and validation
- Documentation updates

## 📚 Additional Resources

### Documentation References
- Firebase Functions documentation
- Google Cloud Platform guides
- Internal API documentation
- Code examples and samples

### Development Guidelines
- Coding standards and best practices
- Testing requirements
- Review and approval processes
- Performance benchmarks

---

*This documentation provides comprehensive technical details for all Cloud Functions in the Titan system. Each function plays a critical role in maintaining platform operations, integrations, and functionality. For specific implementation details, refer to the source code and inline documentation.*
