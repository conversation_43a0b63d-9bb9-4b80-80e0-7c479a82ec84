# Titan Domains - Technical Documentation

**Version**: 2.0  
**Last Updated**: July 2025

This document provides comprehensive technical documentation for the domains within the Titan CRM system, covering APIs, data models, and business logic.

## 📋 Table of Contents

1. [Accounts Domain](#accounts-domain)
2. [Recipients Domain](#recipients-domain)
3. [Merge Tags Domain](#merge-tags-domain)
4. [Auth Domain](#auth-domain)
5. [Templates Domain](#templates-domain)

## Accounts Domain

### Overview
The Accounts domain manages operations related to accounts, including managing ads, logs, and accounts groups.

### Key Files
- **`account.ts`:** Handles operations with accounts, including querying and updating ads, logs, and groups.

#### Functions
```typescript
export function useAd(accountId: string, adId: string) {}
export function updateAd(accountId: string, adId: string, data: any) {}
```

### Dependencies
- **Firebase Firestore**: For database operations
- **React Query**: For managing server state

### Example Usage
```typescript
const { data, error } = useAd(accountId, adId);
```

## Recipients Domain

### Overview
Manages recipients for each account, including retrieval, updates, and exclusivity checks.

### Key Files
- **`recipient.ts`:** Manages recipient-related operations with various Firebase interactions.

#### Functions
```typescript
export function useRecipientsByAccountId(accountId: string) {}
export function useRecipient(accountId: string, recipientId: string) {}
```

### Dependencies
- **Firebase Firestore**: For storing recipient information
- **Lodash**: Utility functions

### Example Usage
```typescript
const { data } = useRecipientsByAccountId(accountId);
```

## Merge Tags Domain

### Overview
Handles the operations associated with managing merge tags, supporting insertion and real-time updates.

### Key Files
- **`mergeTags.js`:** Provides CRUD functions for managing merge tags in Firestore.

#### Functions
```javascript
export async function createMergeTag(input) {}
export function useMergeTag(id) {}
```

### Dependencies
- **Firebase Firestore**: Merge tags storage
- **React Query**: For data-fetching and state management

### Example Usage
```javascript
const { data } = useMergeTagList(filters);
```

## Auth Domain

### Overview
Defines user roles and authentication using set roles.

### Key Files
- **`USER_ROLES.js`:** Contains definitions for different user roles like Admin and SuperAdmin.

#### Constants
```javascript
const USER_ROLES = {
  AccountManager: "ACT_MGR",
  Admin: "ADMIN",
};
```

### Example Usage
```javascript
import USER_ROLES from './USER_ROLES';
console.log(USER_ROLES.Admin);
```

## Templates Domain

### Overview
Covers email templates, including their creation, query, and modification.

### Key Files
- **`emails.js`:** Provides management functions for email templates and campaigns.

#### Functions
```javascript
export async function createEmailTemplate(email) {}
export function useEmailTemplates() {}
```

### Dependencies
- **Firebase Firestore**: Email templates and campaign storage
- **React Query**: Provides management of server-side state

### Example Usage
```javascript
const { data } = useEmailTemplates();
```

*This documentation provides a detailed view of the domain functionality in the Titan CRM system and serves as a guide for developers engaged in its maintenance or enhancement.*
