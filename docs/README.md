# Titan Project Documentation

## Overview
Welcome to the comprehensive documentation for the Titan project. This documentation is organized to help developers, architects, and users understand and work with the Titan platform effectively.

## Documentation Structure

### 📚 [Getting Started](getting-started/)
New to Titan? Start here for setup and basic concepts.

### 👩‍💻 [Developer Guide](developer-guide/)
Comprehensive guides for developers working with Titan.
- **[Auto-Commit Guide](developer-guide/auto-commit-guide.md)** - AI-enhanced git workflow
- **[Test Runner Guide](developer-guide/test-runner-guide.md)** - Interactive test execution
- **[Helpers & Templates](developer-guide/developer-guide-helpers-templates.md)** - Development utilities
- **[Feedback & Iteration](developer-guide/feedback-and-iteration.md)** - Development process

### 🏗️ [Architecture](architecture/)
System architecture, design patterns, and technical documentation.
- **[Design Patterns](architecture/DesignPatterns.md)** - Architectural patterns used
- **[Architecture Improvements](architecture/ArchitectureImprovements.md)** - Enhancement proposals
- **[Cloud Components](architecture/cloud-components.md)** - Cloud infrastructure
- **[Cloud Functions](architecture/cloud-functions.md)** - Serverless functions
- **[Domain Structure](architecture/domain-structure.md)** - Domain organization
- **[Domains Technical](architecture/domains-technical.md)** - Technical domain details
- **[Domains Full Technical](architecture/domains-full-technical.md)** - Comprehensive domain guide
- **[Global Components](architecture/global-components-technical.md)** - Shared components

### 🚀 [Deployment](deployment/)
Deployment procedures, environment management, and operations.
- **[Deployment Script Guide](deployment/deployment-script-guide.md)** - Automated deployment
- **[Environment Setup Guide](deployment/environment-setup-guide.md)** - Environment configuration

### 🔌 [API Reference](api/)
API documentation and specifications.
- **[API Reference](api/api-reference.yaml)** - OpenAPI specification

### 👥 [User Guides](user-guides/)
Guides for end users and marketers.
- **[Marketer Dashboard Guide](user-guides/marketer-guide-dashboard.md)** - Marketing dashboard usage

### 📖 [Reference](reference/)
Quick reference materials and internal documentation.
- **[Developer Tools Reference](reference/developer-tools-documentation.md)** - Tools overview
- **[Merge Tags Wiki](reference/merge-tags-internal-wiki.md)** - Internal merge tags
- **[Merge Tags Schema](reference/merge-tags-schema-extension.md)** - Schema extensions

## Quick Start

### Essential Scripts
```bash
# Environment setup
npm run build-dev-env              # Create development .env
npm run build-dev-functions-env    # Create functions .env

# Development
npm run start-dev                  # Start development server
npm run build                      # Build for production

# Testing
node runTests.mjs                  # Interactive test runner

# Deployment
./deploy.sh dev                    # Deploy to development
./deploy.sh prod                   # Deploy to production

# Git workflow
./auto-commit.mjs                  # AI-enhanced commits
```

### First-Time Setup
1. Configure `extEnv.json` with your Google Cloud credentials
2. Set up Jira API token for auto-commit integration
3. Configure OpenAI API key for AI-enhanced descriptions
4. Run environment setup scripts
5. Verify all tools are working with test commands

## Tool Categories

### 🔧 Build & Configuration
- **babel.config.js** - JavaScript/TypeScript compilation
- **jest.config.js** - Test framework setup
- **playwright.config.ts** - E2E testing configuration
- **postcss.config.js** - CSS processing
- **package.json** - Project manifest and scripts

### 🚀 Deployment & Infrastructure
- **deploy.sh** - Main deployment script with tracking
- **createEnv.js** - Environment variable management
- **deployWithBlueGreen.sh** - Blue-green deployment strategy
- **deployBlueGreen.mjs** - Canary deployment automation

### 🧪 Testing & Quality
- **runTests.mjs** - Interactive test runner
- **jest.setup.js** - Test environment setup
- **playwright/** - E2E test configurations
- **tests.json** - Test suite definitions

### 🔄 Development Workflow
- **auto-commit.mjs** - AI-enhanced git commits
- **chatGpt/code-questions.mjs** - AI-powered code assistance
- **scripts/** - Various utility scripts

## Best Practices

### Security
- Never commit `extEnv.json` to version control
- Use environment variables for sensitive data
- Regularly rotate API tokens and credentials
- Follow least-privilege principle for service accounts

### Development Workflow
- Use the auto-commit tool for consistent commit messages
- Run tests locally before pushing changes
- Use interactive deployment script for safety
- Monitor deployment tracking for issues

### Environment Management
- Keep separate environments for dev/staging/prod
- Use labeled secrets in Google Cloud Secret Manager
- Validate environment files before deployment
- Document environment-specific configurations

## Integration Points

### External Services
- **Google Cloud Secret Manager** - Environment variable storage
- **Jira** - Issue tracking and project management
- **OpenAI** - AI-enhanced commit messages and descriptions
- **Firebase** - Hosting and backend services
- **Qase** - Test management and reporting

### CI/CD Integration
- All scripts can be integrated into CI/CD pipelines
- Environment variables support automated deployment
- Test runner supports headless execution
- Deployment tracking provides audit trails

## Troubleshooting

### Common Issues
1. **Permission denied** - Check file permissions and make scripts executable
2. **Missing dependencies** - Run `npm install` and verify all required tools
3. **Authentication errors** - Verify API tokens and service account credentials
4. **Configuration errors** - Validate JSON files and environment settings

### Getting Help
- Check individual tool documentation for specific issues
- Review error messages and logs for debugging information
- Use interactive prompts for guided troubleshooting
- Consult team members for environment-specific issues

## Contributing

### Adding New Tools
1. Create the tool with proper error handling
2. Add comprehensive documentation
3. Include examples and troubleshooting guides
4. Update this index with the new tool
5. Test in different environments

### Documentation Standards
- Include overview, prerequisites, and usage examples
- Provide troubleshooting sections
- Document all configuration options
- Include security considerations
- Add integration examples

## Support

For issues with these tools:
1. Check the specific tool documentation
2. Review common troubleshooting steps
3. Verify configuration and dependencies
4. Test in a clean environment
5. Contact the development team if issues persist
