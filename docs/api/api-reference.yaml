openapi: "3.0.0"
info:
  title: "Local Events API"
  description: |
    API for managing local events, markets, and generating email previews.
    
    ## Authentication
    All API endpoints require authentication using Bearer tokens.
    
    ## Rate Limits
    - Standard endpoints: 1000 requests per hour
    - Email generation: 100 requests per hour
    
    ## Error Handling
    The API uses standard HTTP status codes and returns error details in JSON format.
  version: "1.0.0"
  contact:
    name: "API Support"
    email: "<EMAIL>"
  license:
    name: "MIT"
servers:
  - url: "https://api.titan.example.com/v1"
    description: "Production server"
  - url: "https://dev-api.titan.example.com/v1"
    description: "Development server"
  - url: "http://localhost:3000/api/v1"
    description: "Local development server"

security:
  - bearerAuth: []

paths:
  /markets:
    get:
      summary: "List all markets"
      description: "Retrieve a paginated list of all markets with optional filtering"
      operationId: "listMarkets"
      tags:
        - "Markets"
      parameters:
        - name: "page"
          in: "query"
          description: "Page number for pagination"
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: "limit"
          in: "query"
          description: "Number of items per page"
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: "isActive"
          in: "query"
          description: "Filter by active status"
          schema:
            type: boolean
        - name: "search"
          in: "query"
          description: "Search markets by name or location"
          schema:
            type: string
            minLength: 2
      responses:
        '200':
          description: "A paginated list of markets"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketListResponse'
        '400':
          description: "Bad request"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: "Unauthorized"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      summary: "Create a new market"
      description: "Create a new market with geographic boundaries"
      operationId: "createMarket"
      tags:
        - "Markets"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMarketRequest'
      responses:
        '201':
          description: "Market created successfully"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Market'
        '400':
          description: "Invalid request data"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: "Market already exists"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /markets/{marketId}:
    get:
      summary: "Retrieve market by ID"
      description: "Get detailed information about a specific market"
      operationId: "getMarketById"
      tags:
        - "Markets"
      parameters:
        - name: "marketId"
          in: "path"
          required: true
          description: "Unique identifier for the market"
          schema:
            type: "string"
            format: "uuid"
      responses:
        '200':
          description: "Market details"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Market'
        '404':
          description: "Market not found"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: "Update market"
      description: "Update market information and settings"
      operationId: "updateMarket"
      tags:
        - "Markets"
      parameters:
        - name: "marketId"
          in: "path"
          required: true
          schema:
            type: "string"
            format: "uuid"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMarketRequest'
      responses:
        '200':
          description: "Market updated successfully"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Market'
        '404':
          description: "Market not found"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: "Delete market"
      description: "Soft delete a market (sets isActive to false)"
      operationId: "deleteMarket"
      tags:
        - "Markets"
      parameters:
        - name: "marketId"
          in: "path"
          required: true
          schema:
            type: "string"
            format: "uuid"
      responses:
        '204':
          description: "Market deleted successfully"
        '404':
          description: "Market not found"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /markets/{marketId}/events:
    get:
      summary: "Get events for a market"
      description: "Retrieve all events within a specific market"
      operationId: "getMarketEvents"
      tags:
        - "Markets"
        - "Events"
      parameters:
        - name: "marketId"
          in: "path"
          required: true
          schema:
            type: "string"
            format: "uuid"
        - name: "startDate"
          in: "query"
          description: "Filter events starting from this date"
          schema:
            type: string
            format: date-time
        - name: "endDate"
          in: "query"
          description: "Filter events ending before this date"
          schema:
            type: string
            format: date-time
        - name: "category"
          in: "query"
          description: "Filter by event category"
          schema:
            type: string
            enum: ["Concert", "Festival", "Conference", "Workshop", "Exhibition", "Sports", "Theater", "Community"]
        - name: "minPopularity"
          in: "query"
          description: "Minimum popularity score (0-6)"
          schema:
            type: integer
            minimum: 0
            maximum: 6
      responses:
        '200':
          description: "List of events in the market"
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Event'

  /events:
    get:
      summary: "List all events"
      description: "Retrieve a paginated list of all events with filtering options"
      operationId: "listEvents"
      tags:
        - "Events"
      parameters:
        - name: "page"
          in: "query"
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: "limit"
          in: "query"
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: "marketId"
          in: "query"
          description: "Filter by market ID"
          schema:
            type: string
            format: uuid
        - name: "isActive"
          in: "query"
          description: "Filter by active status"
          schema:
            type: boolean
      responses:
        '200':
          description: "A paginated list of events"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EventListResponse'

  /events/{eventId}:
    get:
      summary: "Get event by ID"
      description: "Retrieve detailed information about a specific event"
      operationId: "getEventById"
      tags:
        - "Events"
      parameters:
        - name: "eventId"
          in: "path"
          required: true
          schema:
            type: "string"
            format: "uuid"
      responses:
        '200':
          description: "Event details"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Event'
        '404':
          description: "Event not found"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /email-previews:
    post:
      summary: "Generate an email preview"
      description: |
        Generate an HTML and plain text email preview for a specific market.
        This endpoint creates a formatted email featuring the most popular events
        in the market with customizable options.
      operationId: "generateEmailPreview"
      tags:
        - "Email Preview"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailPreviewRequest'
            examples:
              basic:
                summary: "Basic email preview"
                value:
                  marketId: "550e8400-e29b-41d4-a716-************"
                  options:
                    limit: 7
                    includeImages: true
              custom:
                summary: "Customized email preview"
                value:
                  marketId: "550e8400-e29b-41d4-a716-************"
                  options:
                    limit: 5
                    includeImages: false
                    userEmail: "<EMAIL>"
                    userName: "Demo User"
      responses:
        '200':
          description: "Email preview generated successfully"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailPreviewResponse'
        '400':
          description: "Invalid request parameters"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: "Market not found or has no events"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '429':
          description: "Rate limit exceeded"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /email-previews/validate:
    post:
      summary: "Validate email content"
      description: "Validate email content without generating the full preview"
      operationId: "validateEmailContent"
      tags:
        - "Email Preview"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailValidationRequest'
      responses:
        '200':
          description: "Validation results"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailValidationResponse'

  /templates:
    get:
      summary: "List available email templates"
      description: "Get all available email templates and their metadata"
      operationId: "listTemplates"
      tags:
        - "Templates"
      responses:
        '200':
          description: "List of available templates"
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Template'

  /templates/{templateId}:
    get:
      summary: "Get template details"
      description: "Retrieve detailed information about a specific template"
      operationId: "getTemplateById"
      tags:
        - "Templates"
      parameters:
        - name: "templateId"
          in: "path"
          required: true
          schema:
            type: "string"
      responses:
        '200':
          description: "Template details"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Template'

  /health:
    get:
      summary: "Health check"
      description: "Check API health status"
      operationId: "healthCheck"
      tags:
        - "System"
      security: []
      responses:
        '200':
          description: "API is healthy"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Market:
      type: object
      required:
        - area_uuid
        - area_name
        - isActive
      properties:
        area_uuid:
          type: string
          format: uuid
          description: "Unique identifier for the market"
          example: "550e8400-e29b-41d4-a716-************"
        area_name:
          type: string
          description: "Human-readable name of the market"
          example: "Boston, MA"
        area_address:
          type: string
          description: "Physical address or description of the market center"
          example: "123 Main St, Boston, MA 02101"
        area_radius:
          type: number
          format: float
          minimum: 1
          maximum: 100
          description: "Coverage radius in miles"
          example: 25.0
        event_count:
          type: integer
          minimum: 0
          description: "Total number of events in this market"
          example: 42
        isActive:
          type: boolean
          description: "Whether the market is currently active"
          example: true
        created_at:
          type: string
          format: date-time
          description: "When the market was created"
        updated_at:
          type: string
          format: date-time
          description: "When the market was last updated"
        coordinates:
          $ref: '#/components/schemas/Coordinates'

    Event:
      type: object
      required:
        - name
        - area_uuid
        - start_date
        - venue_name
      properties:
        event_id:
          type: string
          format: uuid
          description: "Unique identifier for the event"
        name:
          type: string
          description: "Event name"
          example: "Summer Music Festival"
        description:
          type: string
          description: "Event description"
          example: "Join us for an amazing outdoor music festival featuring local and national artists."
        area_uuid:
          type: string
          format: uuid
          description: "Market ID this event belongs to"
        start_date:
          type: string
          format: date-time
          description: "Event start date and time"
          example: "2025-04-19T19:00:00.000Z"
        end_date:
          type: string
          format: date-time
          description: "Event end date and time"
        venue_name:
          type: string
          description: "Venue where the event takes place"
          example: "Boston Common"
        venue_address:
          type: string
          description: "Venue address"
          example: "139 Tremont Street, Boston, MA 02111"
        minimum_price:
          type: number
          format: float
          minimum: 0
          description: "Minimum ticket price"
          example: 25.00
        maximum_price:
          type: number
          format: float
          minimum: 0
          description: "Maximum ticket price"
          example: 75.00
        popularity_score:
          type: integer
          minimum: 0
          maximum: 6
          description: "Popularity score from 0-6 (6 being most popular)"
          example: 3
        image_url:
          type: string
          format: uri
          description: "URL to event image"
          example: "https://example.com/images/event123.jpg"
        ticket_url:
          type: string
          format: uri
          description: "URL to purchase tickets"
          example: "https://tickets.example.com/event/123"
        category:
          type: string
          enum: ["Concert", "Festival", "Conference", "Workshop", "Exhibition", "Sports", "Theater", "Community"]
          description: "Event category"
          example: "Festival"
        _isActive:
          type: boolean
          description: "Whether the event is currently active"
          example: true
        cancelled:
          type: boolean
          description: "Whether the event has been cancelled"
          example: false
        _isPostponed:
          type: boolean
          description: "Whether the event has been postponed"
          example: false
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    EmailPreviewRequest:
      type: object
      required:
        - marketId
      properties:
        marketId:
          type: string
          format: uuid
          description: "ID of the market to generate preview for"
          example: "550e8400-e29b-41d4-a716-************"
        options:
          type: object
          properties:
            limit:
              type: integer
              minimum: 1
              maximum: 10
              default: 7
              description: "Number of events to include in the email"
              example: 7
            includeImages:
              type: boolean
              default: true
              description: "Whether to include event images"
              example: true
            userEmail:
              type: string
              format: email
              description: "Demo recipient email address"
              example: "<EMAIL>"
            userName:
              type: string
              description: "Demo recipient name"
              example: "John Doe"
            templateId:
              type: string
              description: "ID of the template to use (defaults to standard newsletter)"
              example: "newsletter_v1"
            filters:
              type: object
              properties:
                minPopularity:
                  type: integer
                  minimum: 0
                  maximum: 6
                  description: "Minimum popularity score for included events"
                categories:
                  type: array
                  items:
                    type: string
                    enum: ["Concert", "Festival", "Conference", "Workshop", "Exhibition", "Sports", "Theater", "Community"]
                  description: "Event categories to include"
                priceRange:
                  type: object
                  properties:
                    min:
                      type: number
                      format: float
                      minimum: 0
                    max:
                      type: number
                      format: float
                      minimum: 0

    EmailPreviewResponse:
      type: object
      properties:
        subject:
          type: string
          description: "Email subject line"
          example: "🎭 Local Events This Week in Boston, MA"
        htmlContent:
          type: string
          description: "HTML version of the email"
        plainContent:
          type: string
          description: "Plain text version of the email"
        from:
          type: object
          properties:
            name:
              type: string
              example: "Local Events"
            email:
              type: string
              format: email
              example: "<EMAIL>"
        to:
          type: object
          properties:
            name:
              type: string
              example: "John Doe"
            email:
              type: string
              format: email
              example: "<EMAIL>"
        metadata:
          type: object
          properties:
            marketId:
              type: string
              format: uuid
            marketName:
              type: string
              example: "Boston, MA"
            eventCount:
              type: integer
              example: 7
            featuredEventId:
              type: string
              format: uuid
            generationTime:
              type: string
              format: date-time
            fileSize:
              type: object
              properties:
                html:
                  type: integer
                  description: "HTML content size in bytes"
                plain:
                  type: integer
                  description: "Plain text content size in bytes"
            validation:
              $ref: '#/components/schemas/EmailValidationResult'

    EmailValidationRequest:
      type: object
      required:
        - htmlContent
      properties:
        htmlContent:
          type: string
          description: "HTML content to validate"
        plainContent:
          type: string
          description: "Plain text content to validate"
        subject:
          type: string
          description: "Email subject to validate"

    EmailValidationResponse:
      type: object
      properties:
        isValid:
          type: boolean
          description: "Overall validation status"
        validation:
          $ref: '#/components/schemas/EmailValidationResult'

    EmailValidationResult:
      type: object
      properties:
        isValid:
          type: boolean
          description: "Whether the email passes all validation checks"
        errors:
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
          description: "Validation errors that prevent sending"
        warnings:
          type: array
          items:
            $ref: '#/components/schemas/ValidationWarning'
          description: "Validation warnings that should be reviewed"
        checks:
          type: object
          properties:
            hasRequiredFields:
              type: boolean
            hasValidLinks:
              type: boolean
            hasAltText:
              type: boolean
            isAccessible:
              type: boolean
            isMobileOptimized:
              type: boolean

    ValidationError:
      type: object
      properties:
        code:
          type: string
          example: "MISSING_REQUIRED_FIELD"
        message:
          type: string
          example: "Subject line is required"
        field:
          type: string
          example: "subject"
        severity:
          type: string
          enum: ["error", "warning"]

    ValidationWarning:
      type: object
      properties:
        code:
          type: string
          example: "LARGE_IMAGE_SIZE"
        message:
          type: string
          example: "Image size exceeds recommended 150KB"
        field:
          type: string
          example: "image_url"
        recommendation:
          type: string
          example: "Consider optimizing image size for better email performance"

    Template:
      type: object
      properties:
        id:
          type: string
          description: "Template identifier"
          example: "newsletter_v1"
        name:
          type: string
          description: "Human-readable template name"
          example: "Newsletter Template v1"
        description:
          type: string
          description: "Template description"
        category:
          type: string
          enum: ["newsletter", "promotional", "announcement", "custom"]
        version:
          type: string
          example: "1.0.0"
        isDefault:
          type: boolean
          description: "Whether this is the default template"
        supportedOptions:
          type: array
          items:
            type: string
          description: "List of supported customization options"
          example: ["includeImages", "eventLimit", "customColors"]
        previewUrl:
          type: string
          format: uri
          description: "URL to template preview image"

    CreateMarketRequest:
      type: object
      required:
        - area_name
        - area_address
        - area_radius
      properties:
        area_name:
          type: string
          minLength: 2
          maxLength: 100
          example: "Boston, MA"
        area_address:
          type: string
          minLength: 5
          maxLength: 200
          example: "123 Main St, Boston, MA 02101"
        area_radius:
          type: number
          format: float
          minimum: 1
          maximum: 100
          example: 25.0
        coordinates:
          $ref: '#/components/schemas/Coordinates'

    UpdateMarketRequest:
      type: object
      properties:
        area_name:
          type: string
          minLength: 2
          maxLength: 100
        area_address:
          type: string
          minLength: 5
          maxLength: 200
        area_radius:
          type: number
          format: float
          minimum: 1
          maximum: 100
        isActive:
          type: boolean
        coordinates:
          $ref: '#/components/schemas/Coordinates'

    Coordinates:
      type: object
      properties:
        latitude:
          type: number
          format: float
          minimum: -90
          maximum: 90
          example: 42.3601
        longitude:
          type: number
          format: float
          minimum: -180
          maximum: 180
          example: -71.0589

    MarketListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Market'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    EventListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Event'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    PaginationInfo:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
          example: 1
        limit:
          type: integer
          minimum: 1
          example: 20
        total:
          type: integer
          minimum: 0
          example: 150
        totalPages:
          type: integer
          minimum: 0
          example: 8
        hasNext:
          type: boolean
          example: true
        hasPrev:
          type: boolean
          example: false

    ErrorResponse:
      type: object
      required:
        - error
      properties:
        error:
          type: object
          properties:
            code:
              type: string
              description: "Error code for programmatic handling"
              example: "MARKET_NOT_FOUND"
            message:
              type: string
              description: "Human-readable error message"
              example: "The specified market could not be found"
            details:
              type: object
              description: "Additional error context"
            timestamp:
              type: string
              format: date-time
              description: "When the error occurred"
            requestId:
              type: string
              description: "Unique request identifier for debugging"
              example: "req_123456789"

    HealthResponse:
      type: object
      properties:
        status:
          type: string
          enum: ["healthy", "degraded", "unhealthy"]
          example: "healthy"
        timestamp:
          type: string
          format: date-time
        version:
          type: string
          example: "1.0.0"
        services:
          type: object
          properties:
            database:
              type: string
              enum: ["healthy", "degraded", "unhealthy"]
            firebase:
              type: string
              enum: ["healthy", "degraded", "unhealthy"]
            email:
              type: string
              enum: ["healthy", "degraded", "unhealthy"]

