# API Reference

## Overview
This section contains comprehensive API documentation for the Titan platform, including REST endpoints, authentication, and integration examples.

## API Documentation

### Core API Reference
- **[OpenAPI Specification](api-reference.yaml)** - Complete API specification in OpenAPI 3.0 format

## API Overview

### Base URLs
- **Development**: `https://rm-titan-dev.web.app/api`
- **Production**: `https://rm-titan-prod.web.app/api`

### Authentication
All API endpoints require authentication via Firebase Auth tokens:
```javascript
const token = await firebase.auth().currentUser.getIdToken();
const response = await fetch('/api/endpoint', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

### Response Format
All API responses follow a consistent structure:
```json
{
  "success": true,
  "data": {},
  "error": null,
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "req_12345"
}
```

## Core Endpoints

### Authentication
```
POST /api/auth/login           # User login
POST /api/auth/logout          # User logout
POST /api/auth/refresh         # Refresh token
GET  /api/auth/profile         # User profile
```

### User Management
```
GET    /api/users              # List users
POST   /api/users              # Create user
GET    /api/users/:id          # Get user by ID
PUT    /api/users/:id          # Update user
DELETE /api/users/:id          # Delete user
```

### Campaign Management
```
GET    /api/campaigns          # List campaigns
POST   /api/campaigns          # Create campaign
GET    /api/campaigns/:id      # Get campaign by ID
PUT    /api/campaigns/:id      # Update campaign
DELETE /api/campaigns/:id      # Delete campaign
```

### Analytics
```
GET    /api/analytics/overview     # Dashboard overview
GET    /api/analytics/campaigns    # Campaign analytics
GET    /api/analytics/users        # User analytics
POST   /api/analytics/events       # Track events
```

## Error Handling

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

### Error Response Format
```json
{
  "success": false,
  "data": null,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "req_12345"
}
```

## Rate Limiting

### Rate Limits
- **Authenticated Users**: 1000 requests per hour
- **Anonymous Users**: 100 requests per hour
- **Bulk Operations**: 50 requests per hour

### Rate Limit Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## API Versioning

### Version Strategy
- **Current Version**: v1
- **Version Header**: `API-Version: v1`
- **URL Versioning**: `/api/v1/endpoint`

### Deprecation Policy
- **Minimum Support**: 6 months
- **Deprecation Notice**: 3 months advance notice
- **Breaking Changes**: New major version

## SDK & Libraries

### JavaScript SDK
```javascript
import { TitanAPI } from '@titan/sdk';

const api = new TitanAPI({
  baseUrl: 'https://rm-titan-prod.web.app/api',
  apiKey: 'your-api-key'
});

// Usage
const users = await api.users.list();
const user = await api.users.create({ name: 'John Doe' });
```

### TypeScript Types
```typescript
interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Campaign {
  id: string;
  title: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  createdAt: Date;
  updatedAt: Date;
}
```

## Integration Examples

### React Integration
```javascript
import { useEffect, useState } from 'react';
import { TitanAPI } from '@titan/sdk';

function UserList() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchUsers() {
      try {
        const response = await TitanAPI.users.list();
        setUsers(response.data);
      } catch (error) {
        console.error('Failed to fetch users:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchUsers();
  }, []);

  if (loading) return <div>Loading...</div>;

  return (
    <ul>
      {users.map(user => (
        <li key={user.id}>{user.name}</li>
      ))}
    </ul>
  );
}
```

### Node.js Integration
```javascript
const { TitanAPI } = require('@titan/sdk');

const api = new TitanAPI({
  baseUrl: process.env.TITAN_API_URL,
  apiKey: process.env.TITAN_API_KEY
});

async function createUser(userData) {
  try {
    const user = await api.users.create(userData);
    console.log('User created:', user.data);
    return user.data;
  } catch (error) {
    console.error('Failed to create user:', error);
    throw error;
  }
}
```

## Webhooks

### Webhook Configuration
```javascript
// Configure webhook endpoint
POST /api/webhooks
{
  "url": "https://your-app.com/webhook",
  "events": ["user.created", "campaign.updated"],
  "secret": "your-webhook-secret"
}
```

### Webhook Payload
```json
{
  "event": "user.created",
  "data": {
    "id": "user_123",
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "signature": "sha256=..."
}
```

## Testing

### Test Environment
- **Base URL**: `https://rm-titan-dev.web.app/api`
- **Test Data**: Automatically reset daily
- **Rate Limits**: Relaxed for testing

### API Testing Tools
```bash
# Using curl
curl -X GET "https://rm-titan-dev.web.app/api/users" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Using Postman
# Import the OpenAPI spec for automatic collection generation
```

## Security

### Authentication Security
- **JWT Tokens**: Short-lived access tokens
- **Refresh Tokens**: Secure refresh mechanism
- **Token Rotation**: Automatic token rotation
- **Rate Limiting**: DDoS protection

### Data Security
- **Input Validation**: All inputs validated
- **Output Sanitization**: XSS prevention
- **SQL Injection**: Parameterized queries
- **Access Control**: Role-based permissions

## Monitoring & Analytics

### API Metrics
- **Response Time**: Average response time
- **Error Rate**: 4xx/5xx error percentage
- **Throughput**: Requests per second
- **Availability**: Uptime percentage

### Health Checks
```
GET /api/health
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0",
  "uptime": 86400
}
```

## Best Practices

### API Design
- **RESTful**: Follow REST conventions
- **Idempotent**: Safe to retry operations
- **Stateless**: No server-side session state
- **Cacheable**: Implement appropriate caching

### Error Handling
- **Consistent Format**: Standard error responses
- **Meaningful Messages**: Clear error descriptions
- **Proper Status Codes**: HTTP status codes
- **Logging**: Comprehensive error logging

### Performance
- **Pagination**: Limit response size
- **Filtering**: Allow data filtering
- **Caching**: Implement response caching
- **Compression**: Enable gzip compression

## Resources

### Internal Documentation
- [Developer Guide](../developer-guide/) - Development practices
- [Architecture Guide](../architecture/) - System architecture
- [Deployment Guide](../deployment/) - Deployment procedures

### External Resources
- [OpenAPI Specification](https://swagger.io/specification/)
- [REST API Best Practices](https://restfulapi.net/)
- [HTTP Status Codes](https://httpstatuses.com/)
- [JWT Tokens](https://jwt.io/)

## Support

For API support:
1. Check the API documentation
2. Review integration examples
3. Test in development environment
4. Contact the API team
5. Create support ticket if needed
