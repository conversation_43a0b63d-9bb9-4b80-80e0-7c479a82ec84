# Auto-Commit Guide (auto-commit.mjs)

## Overview
The `auto-commit.mjs` script is an advanced Node.js utility that automates git commit workflows with intelligent AI-enhanced commit messages, Jira integration, and interactive user prompts.

## Prerequisites

### Required Dependencies
- `child_process` - For executing git commands
- `fs` - File system operations
- `https` - For API requests
- `readline` - User input handling
- `path` - Path utilities

### Required Configuration
- `extEnv.json` - External configuration file containing:
  - `jiraUrl` - Jira instance URL
  - `jiraEmail` - Jira user email
  - `jiraApiToken` - Jira API token
  - `openAiKey` - OpenAI API key for AI-enhanced descriptions

## Usage

```bash
./auto-commit.mjs
```

or

```bash
node auto-commit.mjs
```

## Configuration

### extEnv.json Structure
```json
{
  "jiraUrl": "https://your-company.atlassian.net",
  "jiraEmail": "<EMAIL>",
  "jiraApiToken": "your-jira-api-token",
  "openAiKey": "your-openai-api-key"
}
```

### Setting Up Jira Integration
1. **Generate API Token**: Go to Jira → Profile → Security → API tokens
2. **Configure Permissions**: Ensure the user has project access
3. **Test Connection**: Verify API token works with your Jira instance

### Setting Up OpenAI Integration
1. **Get API Key**: Create account at OpenAI and generate API key
2. **Configure Permissions**: Ensure API key has text completion access
3. **Set Usage Limits**: Configure appropriate usage limits

## Core Features

### 1. Interactive User Prompts
```javascript
// Yes/No confirmation
const confirmed = await ask('Do you want to proceed?');

// Text input
const message = await prompt('Enter commit message');

// Option selection
const choice = await select('Choose option', ['Option 1', 'Option 2']);
```

### 2. Git Operations
```javascript
// Execute git commands safely
const status = git('git status --porcelain');
const branch = git('git rev-parse --abbrev-ref HEAD');
```

### 3. Change Detection
```javascript
// Get changed files with status
const files = getChangedFiles();
// Returns: [{ status: 'M', filename: 'src/app.js' }]

// Get file diffs
const diffs = getDiffs();
// Returns: Combined staged and unstaged diffs
```

### 4. Jira Integration
```javascript
// Fetch assignable users from Jira project
const users = await getJiraUsers('PROJECT_KEY');
// Returns: Array of user objects with display names and account IDs
```

### 5. AI-Enhanced Descriptions
```javascript
// Generate AI-enhanced ticket descriptions
const description = await generateAIDescription(
  summary,
  userDescription,
  changedFiles,
  branchName
);
```

## Workflow Process

### 1. Initial Setup
- Load configuration from `extEnv.json`
- Verify git repository status
- Check for uncommitted changes

### 2. Change Analysis
- Detect modified, added, and deleted files
- Generate git diffs for context
- Analyze branch information

### 3. User Interaction
- Prompt for commit message or ticket summary
- Allow selection of Jira assignees
- Confirm actions before execution

### 4. AI Enhancement (Optional)
- Generate comprehensive ticket descriptions
- Include acceptance criteria
- Add technical implementation notes

### 5. Commit Execution
- Stage appropriate files
- Create commit with enhanced message
- Optionally create or update Jira tickets

## Advanced Features

### Git Status Handling
```javascript
function getChangedFiles() {
  const status = git('git status --porcelain');
  return status.split('\n').map(line => ({
    status: line.substring(0, 2).trim(),
    filename: line.substring(3)
  }));
}
```

### Diff Management
```javascript
function getDiffs() {
  const staged = git('git diff --staged');
  const unstaged = git('git diff');
  
  let diff = '';
  if (staged) diff += "STAGED:\n" + staged + "\n\n";
  if (unstaged) diff += "UNSTAGED:\n" + unstaged;
  
  // Truncate large diffs
  const maxLength = 50000;
  if (diff.length > maxLength) {
    diff = diff.substring(0, maxLength) + "\n\n[Truncated...]";
  }
  
  return diff;
}
```

### Jira API Integration
```javascript
async function getJiraUsers(projectKey) {
  const url = `${jiraUrl}/rest/api/3/user/assignable/search?project=${projectKey}`;
  const auth = Buffer.from(`${jiraEmail}:${jiraApiToken}`).toString('base64');
  
  // HTTPS request implementation
  // Returns array of assignable users
}
```

### AI Description Generation
```javascript
async function generateAIDescription(summary, userDescription, files, branch) {
  const prompt = `
    Create a comprehensive Jira ticket description:
    
    Summary: ${summary}
    Description: ${userDescription}
    Branch: ${branch}
    Files: ${files.map(f => f.status + ' ' + f.filename).join('\n')}
    
    Include:
    1. Problem statement
    2. Technical implementation
    3. Acceptance criteria
    4. Technical notes
  `;
  
  // OpenAI API call implementation
}
```

## Error Handling

### Git Command Failures
```javascript
function git(command) {
  try {
    return execSync(command, { encoding: 'utf-8' }).trim();
  } catch (error) {
    console.error('Error executing git command: ' + command);
    console.error(error.message);
    process.exit(1);
  }
}
```

### Configuration Loading
```javascript
try {
  const configContent = fs.readFileSync('extEnv.json', 'utf8');
  config = JSON.parse(configContent);
} catch (error) {
  console.warn('⚠️ Failed to load extEnv.json:', error.message);
}
```

### API Request Failures
```javascript
// Graceful handling of Jira API failures
const users = await getJiraUsers(projectKey).catch(() => []);

// Fallback for OpenAI failures
const description = await generateAIDescription(...args) || userDescription;
```

## Best Practices

### 1. Security
- Never commit `extEnv.json` to version control
- Use environment variables in CI/CD
- Rotate API tokens regularly
- Implement proper error handling for API failures

### 2. Git Workflow
- Always review changes before committing
- Use meaningful commit messages
- Stage files selectively
- Verify branch state before operations

### 3. Jira Integration
- Use descriptive ticket summaries
- Include relevant technical details
- Assign tickets appropriately
- Link commits to tickets

### 4. AI Enhancement
- Provide clear context in prompts
- Review AI-generated content
- Customize prompts for your team's needs
- Have fallbacks for API failures

## Examples

### Basic Commit Workflow
```bash
# Run the script
./auto-commit.mjs

# Script detects changes and prompts:
# Files changed: src/app.js (modified), README.md (added)
# Enter commit message: Add user authentication feature
# Generate AI description? (y/n): y
# 🤖 Enhancing description with AI...
# Commit created successfully!
```

### Jira Integration Example
```bash
# When Jira is configured:
# Create Jira ticket? (y/n): y
# Enter ticket summary: Implement user authentication
# Select assignee:
# 1. John Developer
# 2. Jane Smith
# Select option (1-2): 1
# Ticket created: AUTH-123
```

## Troubleshooting

### Common Issues

1. **Git authentication**
   ```bash
   # Configure git credentials
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
   ```

2. **Jira API errors**
   ```bash
   # Test Jira connection
   curl -u <EMAIL>:api_token \
     https://your-company.atlassian.net/rest/api/3/myself
   ```

3. **OpenAI API errors**
   ```bash
   # Test OpenAI connection
   curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.openai.com/v1/models
   ```

4. **Permission errors**
   ```bash
   # Make script executable
   chmod +x auto-commit.mjs
   ```

## Customization

### Custom Prompts
Modify the AI prompt template to match your team's standards:
```javascript
const customPrompt = `
  Create a ticket description following our team standards:
  - Start with user story format
  - Include technical approach
  - Add testing requirements
  - Reference design documents
`;
```

### Custom Git Commands
Extend git operations for your workflow:
```javascript
// Add custom git hooks
function setupGitHooks() {
  git('git config core.hooksPath .githooks');
}

// Custom commit message format
function formatCommitMessage(type, scope, message) {
  return `${type}(${scope}): ${message}`;
}
```

## Integration with Development Workflow

### Pre-commit Hooks
```bash
# .githooks/pre-commit
#!/bin/bash
node auto-commit.mjs --pre-commit-check
```

### CI/CD Integration
```yaml
# GitHub Actions example
- name: Enhanced Commit
  run: |
    export OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
    export JIRA_API_TOKEN=${{ secrets.JIRA_API_TOKEN }}
    node auto-commit.mjs --ci-mode
```
