# Developer Guide: Adding Helpers and Templates

This guide walks developers through adding custom Handlebars helpers and email templates to the Local Events system.

## Table of Contents

1. [Overview](#overview)
2. [Project Structure](#project-structure)
3. [Adding Custom Helpers](#adding-custom-helpers)
4. [Creating Email Templates](#creating-email-templates)
5. [Testing Your Work](#testing-your-work)
6. [Best Practices](#best-practices)
7. [Common Patterns](#common-patterns)
8. [Troubleshooting](#troubleshooting)

## Overview

The Local Events system uses Handlebars templating engine for generating dynamic email content. The system provides:

- **Custom Helpers**: JavaScript functions that extend Handlebars functionality
- **Email Templates**: Structured layouts for different email types
- **Template Components**: Reusable template partials
- **Testing Suite**: Automated validation for templates and helpers

## Project Structure

```
src/domain/localEvents/
├── templates/
│   ├── helpers/
│   │   └── index.js              # Custom Handlebars helpers
│   ├── layouts/                  # Base email layouts
│   ├── partials/                 # Reusable template components
│   ├── components/               # Email UI components
│   └── README.md                 # Template documentation
├── emails/
│   └── localEventsEmailPreview.js  # Email generation logic
├── constants/
│   └── index.js                  # Configuration constants
├── emails/
│   └── localEventsEmailPreview.js  # Email generation logic
└── TESTING.md                    # Testing documentation
```

## Adding Custom Helpers

### Step 1: Define Your Helper

Edit `src/domain/localEvents/templates/helpers/index.js`:

```javascript
const helpers = {
    // Existing helpers...
    
    // Your new helper
    your_helper_name: function(param1, param2, options) {
        // Helper logic here
        if (!param1) return '';
        
        // Access context data via 'this'
        const context = this;
        
        // Return formatted result
        return `Formatted: ${param1}`;
    }
};

export default helpers;
```

### Step 2: Helper Types and Examples

#### Basic Value Helpers
```javascript
// Format currency
format_currency: function(amount, currency = 'USD') {
    if (!amount) return '$0';
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
},

// Generate random ID
random_id: function() {
    return 'id_' + Math.random().toString(36).substr(2, 9);
}
```

#### Conditional Block Helpers
```javascript
// Check if value is within range
if_in_range: function(value, min, max, options) {
    const numValue = parseFloat(value);
    if (numValue >= min && numValue <= max) {
        return options.fn(this);
    }
    return options.inverse(this);
}
```

Usage in templates:
```handlebars
{{#if_in_range price 20 100}}
    <span class="affordable">Affordable pricing!</span>
{{else}}
    <span class="premium">Premium event</span>
{{/if_in_range}}
```

#### Array Processing Helpers
```javascript
// Filter array by property
filter_by: function(array, property, value) {
    if (!Array.isArray(array)) return [];
    return array.filter(item => item[property] === value);
},

// Sort array by property
sort_by: function(array, property, direction = 'asc') {
    if (!Array.isArray(array)) return [];
    return array.sort((a, b) => {
        const aVal = a[property];
        const bVal = b[property];
        if (direction === 'desc') {
            return bVal > aVal ? 1 : -1;
        }
        return aVal > bVal ? 1 : -1;
    });
}
```

### Step 3: Register Your Helper

Your helper is automatically registered when added to the `helpers` object. The system imports all helpers from the index file.

## Creating Email Templates

### Step 1: Create Template Structure

Templates follow this hierarchy:
- **Layouts**: Overall email structure (header, body, footer)
- **Partials**: Reusable sections (event cards, buttons)
- **Components**: Specific UI elements

### Step 2: Layout Templates

Create a new layout in `templates/layouts/`:

```handlebars
<!-- templates/layouts/newsletter.hbs -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{subject}}</title>
    <!--[if mso]>
    <style type="text/css">
        table, td { border-collapse: collapse; }
    </style>
    <![endif]-->
</head>
<body style="margin: 0; padding: 0; background-color: #f4f4f4;">
    {{> email-header market=market}}
    
    <div style="max-width: 600px; margin: 0 auto; background: white;">
        {{{body}}}
    </div>
    
    {{> email-footer unsubscribe_url=unsubscribe_url}}
</body>
</html>
```

### Step 3: Partial Templates

Create reusable components in `templates/partials/`:

```handlebars
<!-- templates/partials/event-card.hbs -->
<div class="event-card" style="border: 1px solid #ddd; margin: 10px 0; padding: 15px;">
    {{#if image_url}}
    <img src="{{image_url}}" alt="{{name}}" style="width: 100%; max-width: 200px;">
    {{/if}}
    
    <h3 style="margin: 10px 0;">{{name}}</h3>
    
    <p><strong>📍 {{venue_name}}</strong></p>
    <p>🗓 {{format_date start_date "MMMM DD, YYYY"}}</p>
    <p>🕒 {{format_time start_date "h:mm A"}}</p>
    
    {{#if minimum_price}}
    <p>💰 {{format_currency minimum_price}}
    {{#if maximum_price}}
         - {{format_currency maximum_price}}
    {{/if}}
    </p>
    {{/if}}
    
    {{#if description}}
    <p>{{truncate description 150}}</p>
    {{/if}}
    
    {{#if ticket_url}}
    <a href="{{add_utm_params ticket_url 'email' 'newsletter' 'local-events'}}" 
       style="display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none;">
        Get Tickets
    </a>
    {{/if}}
</div>
```

### Step 4: Component Templates

Create specific UI components:

```handlebars
<!-- templates/components/cta-button.hbs -->
<div style="text-align: center; margin: 20px 0;">
    <a href="{{url}}" 
       style="display: inline-block; 
              padding: 12px 24px; 
              background-color: {{bg_color}}; 
              color: {{text_color}}; 
              text-decoration: none; 
              border-radius: 4px;
              font-weight: bold;">
        {{text}}
    </a>
</div>
```

### Step 5: Using Templates

In your email generation code:

```javascript
import Handlebars from 'handlebars';
import helpers from './templates/helpers/index.js';

// Register helpers
Object.keys(helpers).forEach(name => {
    Handlebars.registerHelper(name, helpers[name]);
});

// Register partials
Handlebars.registerPartial('event-card', eventCardTemplate);
Handlebars.registerPartial('cta-button', ctaButtonTemplate);

// Compile and render
const template = Handlebars.compile(layoutTemplate);
const html = template({
    subject: 'Local Events This Week',
    market: marketData,
    events: eventData,
    unsubscribe_url: 'https://example.com/unsubscribe'
});
```

## Testing Your Work

### Step 1: Write Unit Tests for Helpers

Create tests in `tests/unit/templates/helpers.test.js`:

```javascript
import { describe, it, expect } from 'vitest';
import helpers from '../../../templates/helpers/index.js';

describe('Custom Helpers', () => {
    describe('your_helper_name', () => {
        it('should format value correctly', () => {
            const result = helpers.your_helper_name('test input');
            expect(result).toBe('Formatted: test input');
        });
        
        it('should handle null input', () => {
            const result = helpers.your_helper_name(null);
            expect(result).toBe('');
        });
    });
});
```

### Step 2: Write Snapshot Tests for Templates

Add template tests in `tests/snapshot/templateRenderer.test.js`:

```javascript
import { describe, it, expect } from 'vitest';
import { renderTemplate } from '../../../templates/templateRenderer.js';

describe('Template Rendering', () => {
    it('should render newsletter layout correctly', async () => {
        const data = {
            subject: 'Test Newsletter',
            market: { area_name: 'Boston, MA' },
            events: [/* test event data */]
        };
        
        const result = await renderTemplate('newsletter', data);
        expect(result).toMatchSnapshot();
    });
});
```

### Step 3: Run Tests

```bash
# Run all tests
npm test

# Run specific helper tests
npm test -- --grep "your_helper_name"

# Run with coverage
npm run test:coverage

# Update snapshots if intentional changes
npm test -- --update-snapshots
```

## Best Practices

### Helper Development

1. **Keep It Simple**: Helpers should do one thing well
2. **Handle Null Values**: Always check for null/undefined inputs
3. **Return Safe Values**: Return empty strings for invalid inputs
4. **Use TypeScript JSDoc**: Document parameters and return types

```javascript
/**
 * Formats an event price range
 * @param {Object} event - Event object with pricing
 * @param {number} event.minimum_price - Minimum price
 * @param {number} event.maximum_price - Maximum price
 * @returns {string} Formatted price string
 */
format_event_pricing: function(event) {
    if (!event || typeof event !== 'object') return '';
    
    const { minimum_price, maximum_price } = event;
    
    if (!minimum_price && !maximum_price) return 'Free';
    if (!maximum_price || minimum_price === maximum_price) {
        return `$${minimum_price}`;
    }
    
    return `$${minimum_price} - $${maximum_price}`;
}
```

### Template Development

1. **Mobile-First Design**: Templates should work on mobile devices
2. **Email Client Compatibility**: Test across different email clients
3. **Accessibility**: Include alt text, proper contrast, semantic HTML
4. **Performance**: Optimize images and minimize inline styles

```handlebars
<!-- Good: Accessible and responsive -->
<img src="{{image_url}}" 
     alt="{{name}} event image"
     style="width: 100%; max-width: 300px; height: auto;"
     width="300">

<!-- Good: Responsive table layout -->
<table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
    <tr>
        <td style="padding: 20px;">
            Content here
        </td>
    </tr>
</table>
```

### Testing

1. **Test Edge Cases**: Null values, empty arrays, special characters
2. **Snapshot Management**: Review changes before updating snapshots
3. **Performance Testing**: Test with large datasets
4. **Cross-platform Testing**: Test in different environments

## Common Patterns

### Date and Time Formatting
```javascript
// Helper for relative dates
time_ago: function(date) {
    if (!date) return '';
    const now = new Date();
    const eventDate = new Date(date);
    const diffInHours = Math.abs(now - eventDate) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
        return 'Today';
    } else if (diffInHours < 48) {
        return 'Tomorrow';
    } else {
        const days = Math.floor(diffInHours / 24);
        return `In ${days} days`;
    }
}
```

### Conditional Content
```handlebars
{{#if events}}
    {{#each events}}
        {{> event-card this}}
    {{/each}}
{{else}}
    <p>No events found in this area.</p>
{{/if}}
```

### Dynamic Styling
```javascript
// Helper for dynamic CSS classes
event_status_class: function(event) {
    if (!event) return '';
    
    if (event.cancelled) return 'event-cancelled';
    if (event._isPostponed) return 'event-postponed';
    if (event._isActive) return 'event-active';
    
    return 'event-inactive';
}
```

## Troubleshooting

### Common Issues

1. **Helper Not Found**: Ensure helper is exported in index.js
2. **Template Not Rendering**: Check partial registration
3. **Styles Not Working**: Use inline styles for email compatibility
4. **Tests Failing**: Check mock data matches expected structure

### Debugging Tips

1. **Use Debug Helper**: Add temporary debug output
```javascript
debug_context: function() {
    console.log('Template context:', this);
    return '';
}
```

2. **Validate Data Structure**: Check incoming data format
```javascript
validate_event: function(event) {
    const required = ['name', 'start_date', 'venue_name'];
    const missing = required.filter(field => !event[field]);
    
    if (missing.length > 0) {
        console.warn('Missing event fields:', missing);
    }
    
    return missing.length === 0;
}
```

3. **Test in Isolation**: Test helpers separately from templates
```bash
# Test specific helper
npm test -- --grep "format_currency"
```

### Performance Optimization

1. **Cache Heavy Operations**: Store computed values
2. **Minimize Helper Calls**: Compute once, use multiple times
3. **Optimize Images**: Use appropriate sizes and formats
4. **Lazy Load Content**: Only load what's needed

```javascript
// Cache expensive operations
const memoize = (fn) => {
    const cache = new Map();
    return function(...args) {
        const key = JSON.stringify(args);
        if (cache.has(key)) {
            return cache.get(key);
        }
        const result = fn.apply(this, args);
        cache.set(key, result);
        return result;
    };
};

const expensive_helper = memoize(function(data) {
    // Expensive computation here
    return processedData;
});
```

## Next Steps

1. **Review existing helpers** in `templates/helpers/index.js`
2. **Study template examples** in the templates directory
3. **Run the test suite** to understand expected behavior
4. **Check the email preview** functionality for integration examples
5. **Read the API documentation** for available data structures

For more information, see:
- [Template Architecture Documentation](../src/domain/localEvents/templates/README.md)
- [Testing Guide](../src/domain/localEvents/TESTING.md)
- [Email Preview Documentation](../src/domain/localEvents/README_EMAIL_PREVIEW.md)
