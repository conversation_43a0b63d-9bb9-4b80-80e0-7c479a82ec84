# Developer Guide

## Overview
This guide provides comprehensive information for developers working with the Titan platform. It covers development tools, workflows, best practices, and advanced techniques.

## Development Tools

### Essential Tools
- **[Auto-Commit Guide](auto-commit-guide.md)** - AI-enhanced git workflow with Jira integration
- **[Test Runner Guide](test-runner-guide.md)** - Interactive test execution and reporting
- **[Helpers & Templates](developer-guide-helpers-templates.md)** - Development utilities and templates

### Development Process
- **[Feedback & Iteration](feedback-and-iteration.md)** - Development process and feedback loops

## Development Workflow

### 1. Setting Up Your Environment
```bash
# Clone repository
git clone <repository-url>
cd titan

# Install dependencies
pnpm install

# Set up environment variables
node createEnv.js rm-titan-dev path base
node createEnv.js rm-titan-dev path functions
```

### 2. Feature Development Process
1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Develop with Hot Reload**
   ```bash
   npm run start-dev
   ```

3. **Write Tests**
   ```bash
   # Unit tests
   npm run test:unit

   # Integration tests  
   npm run test:integration

   # E2E tests
   npm run test:e2e
   ```

4. **Use AI-Enhanced Commits**
   ```bash
   ./auto-commit.mjs
   ```

### 3. Code Quality & Testing
```bash
# Run all tests interactively
node runTests.mjs

# Run specific test suites
npm run test:unit
npm run test:contract
npm run test:e2e
```

### 4. Deployment
```bash
# Deploy to development
./deploy.sh dev

# Deploy to production
./deploy.sh prod
```

## Code Organization

### Domain-Driven Structure
```
src/
├── domain/
│   ├── users/
│   │   ├── components/
│   │   ├── services/
│   │   ├── hooks/
│   │   └── types/
│   └── campaigns/
│       ├── components/
│       ├── services/
│       ├── hooks/
│       └── types/
├── shared/
│   ├── components/
│   ├── hooks/
│   ├── services/
│   └── utils/
└── global/
    ├── components/
    ├── styles/
    └── constants/
```

### File Naming Conventions
- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Services**: camelCase (e.g., `userService.ts`)
- **Hooks**: camelCase with `use` prefix (e.g., `useUserData.ts`)
- **Types**: PascalCase (e.g., `UserTypes.ts`)
- **Tests**: Same as source with `.test.tsx` or `.spec.tsx`

## Development Best Practices

### Code Quality
- **TypeScript**: Use strict typing
- **ESLint**: Follow configured rules
- **Prettier**: Consistent code formatting
- **Jest**: Comprehensive test coverage

### Git Workflow
- **Branches**: Use feature branches
- **Commits**: Use conventional commit messages
- **Pull Requests**: Require review before merge
- **Auto-Commit**: Leverage AI for enhanced commit messages

### Testing Strategy
- **Unit Tests**: Test individual components/functions
- **Integration Tests**: Test service interactions
- **E2E Tests**: Test complete user workflows
- **Contract Tests**: Test API contracts

## Advanced Topics

### Performance Optimization
- **Code Splitting**: Implement lazy loading
- **Bundle Analysis**: Use webpack-bundle-analyzer
- **Memoization**: Use React.memo and useMemo
- **Image Optimization**: Implement responsive images

### Security Considerations
- **Environment Variables**: Use Google Cloud Secret Manager
- **Authentication**: Implement proper auth flows
- **API Security**: Validate all inputs
- **CORS**: Configure properly for production

### Monitoring & Debugging
- **React DevTools**: Component debugging
- **Network Tab**: API request monitoring
- **Error Boundaries**: Graceful error handling
- **Logging**: Structured logging for debugging

## Tools Deep Dive

### Auto-Commit Tool
The auto-commit tool provides:
- AI-enhanced commit messages
- Jira integration
- Change analysis
- Interactive prompts

```bash
# Run auto-commit
./auto-commit.mjs

# Features:
# - Analyzes code changes
# - Generates descriptive commit messages
# - Integrates with Jira tickets
# - Provides interactive confirmations
```

### Test Runner
The test runner offers:
- Interactive test selection
- TestOps integration
- User-specific reporting
- Parallel execution

```bash
# Run test runner
node runTests.mjs

# Features:
# - Select specific test suites
# - Run with TestOps reporting
# - User attribution for results
# - Detailed execution reports
```

### Environment Management
Environment setup includes:
- Google Cloud Secret Manager integration
- Multi-environment support
- Automatic secret rotation
- Secure credential handling

```bash
# Set up environments
node createEnv.js rm-titan-dev path base
node createEnv.js rm-titan-dev path functions
```

## Troubleshooting

### Common Development Issues

1. **Build Failures**
   ```bash
   # Clear cache and reinstall
   pnpm store prune
   pnpm install
   npm run build
   ```

2. **Test Failures**
   ```bash
   # Update snapshots
   npm run test:update-snapshots
   
   # Clear test cache
   npm run test:clear-cache
   ```

3. **Environment Issues**
   ```bash
   # Regenerate environment files
   npm run build-dev-env
   npm run build-dev-functions-env
   ```

4. **Git Issues**
   ```bash
   # Reset to clean state
   git stash
   git reset --hard origin/main
   ```

### Performance Issues
- Use React DevTools Profiler
- Implement code splitting
- Optimize bundle size
- Monitor network requests

### Debugging Tips
- Use browser debugging tools
- Implement proper error boundaries
- Add structured logging
- Use TypeScript for better error detection

## Resources

### Documentation
- [Architecture Guide](../architecture/) - System design
- [Deployment Guide](../deployment/) - Deployment procedures
- [API Reference](../api/) - Backend integration
- [User Guides](../user-guides/) - End-user documentation

### External Resources
- [React Documentation](https://react.dev)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Playwright Documentation](https://playwright.dev)

## Getting Help

1. **Check Documentation** - Start with relevant docs sections
2. **Review Code Examples** - Look at existing implementations
3. **Run Diagnostics** - Use built-in troubleshooting tools
4. **Ask Team** - Contact development team for support
5. **Create Issues** - Document bugs and feature requests
