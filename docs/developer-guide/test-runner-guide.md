# Test Runner Guide (runTests.mjs)

## Overview
The `runTests.mjs` script is a Node.js utility designed to manage and execute test plans for the Titan project. It features interactive test selection, integration with TestOps, and user-specific reporting to Qase.

## Prerequisites

### Required Dependencies
- `inquirer` - For interactive user prompts
- `child_process` - For running test commands
- `fs` - File system operations

### Required Files
- `tests.json` - A JSON configuration file listing available tests

## Usage

```bash
node runTests.mjs
```

## Configuration

### tests.json Structure
```json
[
  {
    "name": "Unit Tests",
    "project": "my-project",
    "command": "npm run test:unit",
    "config": "configs/unit.config.js"
  },
  {
    "name": "Integration Tests",
    "project": "my-project",
    "command": "npm run test:integration",
    "config": "configs/integration.config.js"
  }
]
```

## Core Features

### 1. Test Configuration Validation
```javascript
async function validateTestConfig() {
  const testConfigRaw = await fs.readFile(CONFIG_PATH, 'utf-8');
  const testConfig = JSON.parse(testConfigRaw);

  for (const test of testConfig) {
    // Validate paths and commands
  }
  return testConfig;
}
```

### 2. Interactive Test Selection
```javascript
const choices = testConfig.map(test => ({ name: test.name, value: test.project }));
const { selectedTests } = await inquirer.prompt([{
  type: 'checkbox',
  name: 'selectedTests',
  message: 'Select the tests to run:',
  choices
}]);
```

### 3. Test Execution
```javascript
async function executeTests(testConfig, selectedTests, useTestOps) {
  for (const project of selectedTests) {
    // Run each selected test with spawn
  }
}
```

### 4. TestOps Integration
```javascript
const env = { ...process.env, QASE_MODE: 'testops' };
const childProcess = spawn('npx', ['test-command'], { env });
```

### 5. User Reporting
```javascript
async function promptUserName() {
  const { userName } = await inquirer.prompt({
    type: 'input',
    name: 'userName',
    message: 'Enter your name for reporting:'
  });
  return userName;
}
```

## Error Handling
- Validates test paths and configurations
- Interactive error handling by prompting the user

## Examples

### Running All Tests
```bash
node runTests.mjs

# Select "All" when prompted
```

### Running Specific Tests
```bash
node runTests.mjs

# Select specific tests from the list when prompted
```

## Troubleshooting

### Common Issues

1. **Configuration Errors**
   ```bash
   Error loading test configuration: Invalid JSON
   ```

2. **Missing Test Scripts**
   ```bash
   Error: No command found for test "Unit Tests"
   ```

3. **Execution Failures**
   ```bash
   Error executing test command: npm run test:unit
   ```

## Best Practices

### Test Management
- Regularly update `tests.json` with accurate configurations
- Ensure test paths and commands are correct

### Using TestOps
- Enable TestOps for enhanced test visibility
- Customize test reporting with user input

### User Interaction
- Utilize interactive prompts for flexibility
- Collect user information for detailed reporting

## Advanced Customization

### Adding New Tests
1. Update `tests.json` with the new test plan
2. Ensure validation and configurations are correct

### Modifying Test Commands
- Alter test commands in `tests.json` to suit project needs
- Use absolute paths for all configurations

## Potential Integrations

### CI/CD Integration
- Automate test runs on code changes
- Utilize TestOps for real-time feedback

### Reporting with Qase
- Integrate Qase reporting for detailed test analytics
- Configure scripts to report user-specific results
