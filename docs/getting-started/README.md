# Getting Started with Titan

## Welcome to Titan
Titan is a comprehensive platform designed for modern web development with React, TypeScript, and cloud-first architecture. This guide will help you get up and running quickly.

## Prerequisites

### Required Software
- **Node.js** (v18+) - JavaScript runtime
- **pnpm** - Package manager
- **Git** - Version control
- **Firebase CLI** - For deployment and cloud functions
- **Google Cloud SDK** - For secret management

### Recommended Tools
- **VS Code** - IDE with excellent TypeScript support
- **Playwright** - For end-to-end testing
- **Docker** - For containerized development

## Initial Setup

### 1. Clone and Install
```bash
git clone <repository-url>
cd titan
pnpm install
```

### 2. Environment Configuration
```bash
# Create your external environment configuration
cp extEnv.json.example extEnv.json
# Edit extEnv.json with your Google Cloud credentials

# Generate environment files
npm run build-dev-env
npm run build-dev-functions-env
```

### 3. Verify Installation
```bash
# Run tests to verify setup
node runTests.mjs

# Start development server
npm run start-dev
```

## Project Structure

```
titan/
├── src/                    # React application source
├── functions/             # Cloud functions
├── playwright/           # End-to-end tests
├── docs/                 # Documentation
├── scripts/              # Utility scripts
├── public/               # Static assets
└── configs/              # Configuration files
```

## Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/your-feature

# Start development server
npm run start-dev

# Make your changes...

# Use auto-commit for consistent commits
./auto-commit.mjs
```

### 2. Testing
```bash
# Run all tests interactively
node runTests.mjs

# Or run specific test types
npm run test:unit
npm run test:e2e
```

### 3. Deployment
```bash
# Deploy to development
./deploy.sh dev

# Deploy to production (from main branch)
./deploy.sh prod
```

## Key Concepts

### Domain-Driven Design
Titan follows domain-driven design principles:
- **Domains** - Business logic areas (e.g., users, campaigns)
- **Components** - Reusable UI components
- **Services** - Business logic and API interactions

### Configuration Management
- **Environment Variables** - Stored in Google Cloud Secret Manager
- **Feature Flags** - Dynamic feature toggling
- **Build Configuration** - Webpack/Vite configuration

### Testing Strategy
- **Unit Tests** - Jest for individual components
- **Integration Tests** - API and service testing
- **E2E Tests** - Playwright for user workflows

## Next Steps

1. **Read the [Developer Guide](../developer-guide/)** for detailed development practices
2. **Explore the [Architecture](../architecture/)** to understand system design
3. **Review [Deployment](../deployment/)** procedures for production readiness
4. **Check [API Reference](../api/)** for backend integration

## Common Tasks

### Adding a New Feature
1. Create domain structure in `src/domain/`
2. Implement components and services
3. Add tests for new functionality
4. Update documentation
5. Deploy to dev environment for testing

### Fixing Bugs
1. Identify the issue location
2. Write failing tests
3. Implement fix
4. Verify tests pass
5. Deploy fix

### Performance Optimization
1. Use React DevTools for profiling
2. Implement code splitting
3. Optimize bundle size
4. Monitor production metrics

## Getting Help

- **Documentation** - Check relevant docs sections
- **Code Examples** - Look in existing domains
- **Team Support** - Contact development team
- **Issues** - Check GitHub issues for known problems

## Troubleshooting

### Common Issues

1. **Environment Variables Missing**
   ```bash
   # Regenerate environment files
   npm run build-dev-env
   ```

2. **Build Failures**
   ```bash
   # Clear cache and reinstall
   pnpm store prune
   pnpm install
   ```

3. **Test Failures**
   ```bash
   # Update test snapshots
   npm run test:update-snapshots
   ```

4. **Deployment Issues**
   ```bash
   # Check deployment logs
   firebase functions:log
   ```

## Resources

- [React Documentation](https://react.dev)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Google Cloud Documentation](https://cloud.google.com/docs)
- [Playwright Documentation](https://playwright.dev)
