# Deployment Documentation

## Overview
This section covers deployment procedures, environment management, and operational practices for the Titan platform.

## Deployment Guides

### Core Deployment Documentation
- **[Deployment Script Guide](deployment-script-guide.md)** - Comprehensive automated deployment
- **[Environment Setup Guide](environment-setup-guide.md)** - Environment configuration and secret management

## Deployment Strategy

### Environment Overview
Titan supports multiple deployment environments:
- **Development** - Feature development and testing
- **Staging** - Pre-production validation
- **Production** - Live user environment

### Deployment Methods
- **Blue-Green Deployment** - Zero-downtime releases
- **Canary Deployment** - Gradual rollout
- **Feature Flags** - Runtime configuration
- **Rollback Strategy** - Quick recovery

## Quick Start

### Prerequisites
```bash
# Required tools
firebase login
gcloud auth login
pnpm install

# Environment setup
node createEnv.js rm-titan-dev path base
node createEnv.js rm-titan-dev path functions
```

### Development Deployment
```bash
# Deploy to development environment
./deploy.sh dev

# The script will:
# 1. Prompt for deployer identification
# 2. Allow branch selection
# 3. Build and deploy the application
# 4. Deploy cloud functions
# 5. Update hosting configuration
```

### Production Deployment
```bash
# Deploy to production (must be on main branch)
./deploy.sh prod

# Production deployment:
# 1. Automatically uses main branch
# 2. Performs additional safety checks
# 3. Enables production optimizations
# 4. Sends deployment notifications
```

## Environment Configuration

### Secret Management
Titan uses Google Cloud Secret Manager for environment variables:

```bash
# Create secrets with proper labels
gcloud secrets create SECRET_NAME --data-file=secret.txt
gcloud secrets update SECRET_NAME --update-labels=path=base

# Generate environment files
node createEnv.js rm-titan-dev path base      # Creates .env
node createEnv.js rm-titan-dev path functions # Creates functions/.env
node createEnv.js rm-titan-dev path test      # Creates playwright/.env
```

### Environment Variables
Different environments require different variable sets:
- **Base** - Main application variables
- **Functions** - Cloud function specific variables
- **Test** - Testing environment variables

## Deployment Process

### 1. Pre-Deployment Checks
```bash
# Verify environment
npm run build
npm run test
npm run lint

# Check deployment readiness
firebase functions:config:get
firebase projects:list
```

### 2. Deployment Execution
```bash
# Interactive deployment
./deploy.sh dev

# Automated deployment (CI/CD)
DEPLOYER="CI/CD" ./deploy.sh dev
```

### 3. Post-Deployment Verification
```bash
# Verify deployment
firebase functions:log --only=api
firebase hosting:channel:list

# Test endpoints
curl https://your-app.web.app/api/health
```

## Monitoring & Observability

### Deployment Tracking
The deployment script provides comprehensive tracking:
- **Deployment ID** - Unique identifier for each deployment
- **Action Logging** - All deployment actions are logged
- **Error Tracking** - Deployment errors are captured
- **Success Metrics** - Deployment success/failure rates

### Monitoring Setup
```bash
# View deployment logs
firebase functions:log --only=deployment-tracker

# Monitor application health
firebase functions:log --only=api

# Check hosting status
firebase hosting:channel:list
```

## Rollback Procedures

### Automatic Rollback
```bash
# Rollback to previous version
firebase hosting:rollback

# Rollback specific functions
firebase functions:rollback FUNCTION_NAME
```

### Manual Rollback
```bash
# Revert to specific commit
git revert COMMIT_SHA
./deploy.sh prod
```

## CI/CD Integration

### GitHub Actions Example
```yaml
name: Deploy
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: pnpm install
      - name: Deploy
        run: |
          echo "${{ secrets.FIREBASE_SERVICE_ACCOUNT }}" > service-account.json
          export GOOGLE_APPLICATION_CREDENTIALS=service-account.json
          ./deploy.sh prod
```

### Environment Variables for CI/CD
```bash
# Required environment variables
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
FIREBASE_TOKEN=firebase-token
DEPLOYER=CI/CD

# Optional variables
DEPLOYMENT_NOTIFICATION_WEBHOOK=webhook-url
SLACK_WEBHOOK=slack-webhook-url
```

## Security Considerations

### Access Control
- **Service Accounts** - Least privilege access
- **API Keys** - Secure storage in Secret Manager
- **Environment Isolation** - Separate credentials per environment
- **Audit Logging** - All deployments are logged

### Security Checklist
- [ ] Service account has minimal required permissions
- [ ] Secrets are stored in Secret Manager
- [ ] Environment variables are not hardcoded
- [ ] Deployment tracking is enabled
- [ ] Rollback procedures are tested

## Troubleshooting

### Common Deployment Issues

1. **Authentication Errors**
   ```bash
   # Re-authenticate
   firebase login
   gcloud auth login
   ```

2. **Build Failures**
   ```bash
   # Clear cache and rebuild
   pnpm store prune
   pnpm install
   npm run build
   ```

3. **Function Deployment Failures**
   ```bash
   # Check function logs
   firebase functions:log --only=FUNCTION_NAME
   
   # Redeploy specific function
   firebase deploy --only=functions:FUNCTION_NAME
   ```

4. **Environment Variable Issues**
   ```bash
   # Regenerate environment files
   node createEnv.js rm-titan-dev path base
   node createEnv.js rm-titan-dev path functions
   ```

### Diagnostic Commands
```bash
# Check deployment status
firebase projects:list
firebase functions:list
firebase hosting:channel:list

# Verify configuration
firebase functions:config:get
gcloud secrets list --project=PROJECT_ID

# Test connectivity
curl -I https://your-app.web.app
```

## Best Practices

### Deployment Safety
- **Always test in development** before production
- **Use feature flags** for risky changes
- **Monitor post-deployment** for issues
- **Have rollback plan** ready

### Performance Optimization
- **Minimize bundle size** before deployment
- **Optimize images** and assets
- **Enable caching** for static resources
- **Use CDN** for global distribution

### Monitoring
- **Set up alerts** for deployment failures
- **Monitor error rates** post-deployment
- **Track performance metrics** continuously
- **Log deployment events** for audit trail

## Resources

### Internal Documentation
- [Architecture Guide](../architecture/) - System architecture
- [Developer Guide](../developer-guide/) - Development practices
- [API Reference](../api/) - Backend integration

### External Resources
- [Firebase Documentation](https://firebase.google.com/docs)
- [Google Cloud Documentation](https://cloud.google.com/docs)
- [GitHub Actions](https://docs.github.com/en/actions)
- [Docker Documentation](https://docs.docker.com)

## Support

For deployment issues:
1. Check the deployment logs
2. Review the troubleshooting section
3. Verify environment configuration
4. Contact the DevOps team
5. Create incident report if needed
