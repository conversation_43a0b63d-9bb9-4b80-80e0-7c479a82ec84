# Deployment Script Guide (deploy.sh)

## Overview
The `deploy.sh` script is a comprehensive bash script designed to deploy the Titan project to different environments with robust error handling, deployment tracking, and interactive branch selection.

## Prerequisites

### Required Tools
The script automatically checks for the following tools:
- `git` - Version control
- `pnpm` - Package manager
- `node` - JavaScript runtime
- `firebase` - Firebase CLI tools

### Required Files
- `deploy-tracker.js` - Deployment tracking script (must be executable)
- `@playwright/test` - Must be installed in node_modules
- `extEnv.json` - External environment configuration

## Usage

```bash
./deploy.sh {prod|dev}
```

### Arguments
- `prod` - Deploy to production environment (rm-titan-prod)
- `dev` - Deploy to development environment (rm-titan-dev)

## Features

### 1. Environment Configuration
- **Production**: Uses `rm-titan-prod` project and `main` branch
- **Development**: Uses `rm-titan-dev` project with interactive branch selection

### 2. Branch Selection (Dev Environment)
When deploying to dev, you can choose:
1. `develop` branch (default)
2. Release branch (lists available release branches)
3. Custom branch name

### 3. Git Status Management
- Checks for uncommitted changes
- Offers to stash changes before deployment
- Verifies branch existence before proceeding

### 4. Deployment Tracking
The script integrates with a deployment tracking system:
- Logs all deployment actions
- Tracks function deployments
- Records errors and success states
- Generates deployment IDs for tracking

### 5. Error Handling
- Exits on any error (except function deployments)
- Comprehensive error logging
- Graceful handling of missing dependencies

## Deployment Tracking Functions

### `log_action(action, details)`
Logs deployment actions with structured data:
```bash
log_action "branch_selected" "{\"branch\": \"develop\", \"method\": \"default\"}"
```

### `log_error(error_msg, details)`
Logs deployment errors:
```bash
log_error "Branch does not exist" "{\"branch\": \"feature-xyz\"}"
```

### `track_function(name, status, details)`
Tracks individual function deployments:
```bash
track_function "user-service" "success" "{\"duration\": \"45s\"}"
```

### `finalize_deployment(success, message)`
Finalizes deployment tracking:
```bash
finalize_deployment true "Deployment completed successfully"
```

## Interactive Prompts

### 1. Deployer Identification
```
Who are you? Enter Name:
```

### 2. Branch Selection (Dev Only)
```
Select branch for deployment:
1) develop (default)
2) release branch
3) Enter custom branch name
Enter choice [1-3]:
```

### 3. Stash Confirmation
```
Warning: You have uncommitted changes in your working directory.
Do you want to stash them before proceeding? (yes/no):
```

### 4. Branch Verification
```
Warning: Branch 'feature-xyz' does not exist locally or remotely.
Do you want to continue anyway? (yes/no):
```

## Error Scenarios

### Missing Dependencies
```bash
Error: firebase is not installed.
```

### Missing Files
```bash
Error: deploy-tracker.js is not found. Please create the file first.
```

### Invalid Environment
```bash
Invalid environment specified. Must be 'prod' or 'dev'.
```

## Best Practices

1. **Always verify your branch** before deployment
2. **Commit or stash changes** before running the script
3. **Use meaningful branch names** for tracking
4. **Monitor deployment logs** for issues
5. **Test in dev environment** before production deployment

## Example Deployment Flow

```bash
# Development deployment
./deploy.sh dev

# Script prompts:
Who are you? Enter Name: John Developer
Select branch for deployment:
1) develop (default)
2) release branch  
3) Enter custom branch name
Enter choice [1-3]: 1

# Deployment proceeds with tracking enabled
```

## Troubleshooting

### Common Issues
1. **Permission denied**: Make sure the script is executable
   ```bash
   chmod +x deploy.sh
   ```

2. **Missing deploy-tracker.js**: Ensure the tracking script exists
   ```bash
   ls -la deploy-tracker.js
   ```

3. **Firebase authentication**: Verify Firebase CLI is logged in
   ```bash
   firebase login
   ```

4. **Git authentication**: Ensure git credentials are configured
   ```bash
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
   ```

## Integration with CI/CD

The script can be integrated into CI/CD pipelines:
- Set environment variables for non-interactive mode
- Use deployment tracking for monitoring
- Integrate with alerting systems for error notifications
