# Environment Setup Guide (createEnv.js)

## Overview
The `createEnv.js` script is a Node.js utility that integrates with Google Cloud Secret Manager to fetch secrets and generate environment files for different deployment contexts.

## Prerequisites

### Required Dependencies
- `@google-cloud/secret-manager` - Google Cloud Secret Manager client library
- `fs` - File system operations
- `path` - Path utilities

### Required Files
- `extEnv.json` - External environment configuration containing Google Cloud credentials

### Google Cloud Setup
1. **Service Account**: Create a service account with Secret Manager access
2. **Credentials**: Download the service account key JSON file
3. **extEnv.json**: Configure the path to credentials for each project

## Usage

```bash
node createEnv.js <projectId> <labelKey> <labelValue>
```

### Parameters
- `projectId` - Google Cloud project ID (e.g., "rm-titan-dev", "rm-titan-prod")
- `labelKey` - Secret label key to filter by (e.g., "path")
- `labelValue` - Secret label value to filter by (e.g., "base", "functions", "test")

## Configuration

### extEnv.json Structure
```json
{
  "rm-titan-dev": "/path/to/dev-service-account.json",
  "rm-titan-prod": "/path/to/prod-service-account.json"
}
```

### Secret Manager Labels
Secrets in Google Cloud Secret Manager should be labeled with:
- `path=base` - For main application environment variables
- `path=functions` - For cloud functions environment variables
- `path=test` - For test environment variables

## Output Files

### File Locations
- `labelValue=base` → `.env` (root directory)
- `labelValue=functions` → `functions/.env`
- `labelValue=test` → `playwright/.env`

### File Format
```bash
#env for <projectId> - <labelValue>
SECRET_NAME_1=secret_value_1
SECRET_NAME_2=secret_value_2
API_KEY=your_api_key_here
```

## Core Functions

### `listSecretsWithLabel(projectId, labelKey, labelValue)`
Retrieves all secrets from Google Cloud Secret Manager that match the specified label.

**Parameters:**
- `projectId` - Google Cloud project ID
- `labelKey` - Label key to filter by
- `labelValue` - Label value to filter by

**Returns:** Array of secret objects

### `accessSecretVersion(secretName)`
Accesses the latest version of a specific secret.

**Parameters:**
- `secretName` - Full secret name (projects/PROJECT_ID/secrets/SECRET_NAME)

**Returns:** Secret value as string

### `writeEnvFile(secrets, projectId)`
Writes secrets to the appropriate environment file based on `labelValue`.

**Parameters:**
- `secrets` - Array of secret objects
- `projectId` - Google Cloud project ID

## Examples

### Development Environment Setup
```bash
# Create main .env file for development
node createEnv.js rm-titan-dev path base

# Create functions/.env for cloud functions
node createEnv.js rm-titan-dev path functions

# Create playwright/.env for tests
node createEnv.js rm-titan-dev path test
```

### Production Environment Setup
```bash
# Create main .env file for production
node createEnv.js rm-titan-prod path base

# Create functions/.env for production functions
node createEnv.js rm-titan-prod path functions
```

## Error Handling

### Missing Project Configuration
```bash
Error: Your extEnv file does not contain an entry for rm-titan-dev. Please add and retry.
```

### Missing Command Line Arguments
```bash
Error: Please provide a project ID, label key, and label value as command-line arguments
```

### Google Cloud Authentication Issues
```bash
Error fetching and writing secrets: Error: Could not load the default credentials
```

## Security Best Practices

1. **Never commit extEnv.json** to version control
2. **Use least privilege** for service account permissions
3. **Rotate service account keys** regularly
4. **Audit secret access** in Google Cloud Console
5. **Use different service accounts** for different environments

## Integration with Package Scripts

Add these scripts to your `package.json`:

```json
{
  "scripts": {
    "build-dev-env": "node createEnv.js rm-titan-dev path base",
    "build-dev-functions-env": "node createEnv.js rm-titan-dev path functions",
    "build-prod-env": "node createEnv.js rm-titan-prod path base",
    "build-prod-functions-env": "node createEnv.js rm-titan-prod path functions"
  }
}
```

## Troubleshooting

### Common Issues

1. **Invalid credentials**
   ```bash
   # Verify service account file exists
   ls -la /path/to/service-account.json
   
   # Check file permissions
   chmod 600 /path/to/service-account.json
   ```

2. **Missing secrets**
   ```bash
   # List all secrets in project
   gcloud secrets list --project=rm-titan-dev
   
   # Check secret labels
   gcloud secrets describe SECRET_NAME --project=rm-titan-dev
   ```

3. **Permission denied**
   ```bash
   # Grant Secret Manager access
   gcloud projects add-iam-policy-binding rm-titan-dev \
     --member="serviceAccount:SERVICE_ACCOUNT_EMAIL" \
     --role="roles/secretmanager.secretAccessor"
   ```

4. **Network issues**
   ```bash
   # Test Google Cloud connectivity
   gcloud auth application-default login
   gcloud secrets list --project=rm-titan-dev
   ```

## Secret Management Workflow

### Adding New Secrets
1. Create secret in Google Cloud Console
2. Add appropriate labels (`path=base`, `path=functions`, etc.)
3. Run the script to update environment files
4. Restart your application to pick up new variables

### Updating Existing Secrets
1. Update secret value in Google Cloud Console
2. Re-run the script to fetch updated values
3. Restart your application

### Environment-Specific Secrets
- Use different projects for different environments
- Label secrets appropriately for automatic filtering
- Maintain separate service accounts for each environment

## Monitoring and Auditing

### Google Cloud Audit Logs
Monitor secret access through Google Cloud's audit logs:
- Secret Manager API calls
- Service account authentication
- Permission changes

### Script Logging
Add logging to track script execution:
```javascript
console.log(`Fetching secrets for ${projectId} with label ${labelKey}=${labelValue}`);
console.log(`Found ${secrets.length} secrets`);
console.log('.env file created successfully');
```
