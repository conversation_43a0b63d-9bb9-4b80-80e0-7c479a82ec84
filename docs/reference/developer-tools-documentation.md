# Developer Tools Documentation

## package.json

### Purpose
The `package.json` file serves as the manifest for the project. It includes metadata such as the project's name, version, private status, license, and keywords. The file also declares dependencies, scripts, ESLint configuration, and browser compatibility.

### Key Dependencies
- **@analytics/google-analytics**: For integrating Google Analytics.
- **@babel/preset-react**: For supporting React with Babel.
- **@google-cloud/pubsub**: For Google Cloud Pub/Sub messaging.
- **@hookform/resolvers**: Resolvers for React Hook Form.
- **firebase-admin**: Admin access to Firebase services.

### Scripts
- **start-dev**: Builds the environment and starts the Vite server.
- **build**: Builds the project for production.
- **deploy:blue-green**: Deploys using the blue-green strategy.
- **docker:build**: Builds Docker images.

## deploy.sh

### Purpose
Used to deploy the project to different environments (prod/dev). It checks for necessary tools, initializes deployment tracking, and handles git branch selection.

### Key Functions
- **command_exists**: Checks if required commands are available.
- **log_action**: Logs deployment actions.
- **track_function**: Tracks specific deployment functions.

### Usage
Run with the argument `prod` or `dev` to specify the environment.

## createEnv.js

### Purpose
Interacts with Google Cloud Secret Manager to fetch secrets and write them to `.env` files specific to the environment.

### Key Functions
- **listSecretsWithLabel**: Lists secrets with a specified label.
- **accessSecretVersion**: Accesses the latest version of a secret.
- **writeEnvFile**: Writes secrets to an environment file.

### Usage
Run with `node createEnv.js <projectId> <labelKey> <labelValue>`.

## auto-commit.mjs

### Purpose
Automates commits by managing git changes and integrating Jira for task management.

### Key Features
- Loads environment configuration.
- Provides an interactive prompt for user confirmation.
- Connects with Jira for user selection.

### Usage
Uses `git` internally to handle commit actions and AI-enhanced descriptions.

## runTests.mjs

### Purpose
Facilitates running tests using a test configuration file with support for TestOps and Qase integration.

### Key Features
- Validates test configurations.
- Prompts for test selection.
- Executes tests and reports results.

### Usage
Executes tests based on user selection and configuration in `tests.json`.
