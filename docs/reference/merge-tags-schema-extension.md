# MergeTags Schema Extension

This document describes the extension of the MergeTags collection schema with new fields for better organization, data sourcing, and audit trails.

## Overview

The MergeTags collection has been extended with the following new fields:

### New Required Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `category` | `MergeTagCategory` | Categorizes merge tags by functionality | `'recipient'`, `'address'`, `'campaign'` |
| `dataSource` | `MergeTagDataSource` | Identifies the source of the merge tag data | `'recipient'`, `'mailingAddress'`, `'computed'` |
| `createdBy` | `string` | User ID who created the merge tag | `'user123'`, `'system-migration'` |
| `isSystem` | `boolean` | Whether this is a system-defined merge tag | `true`, `false` |

### New Optional Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `path` | `string` | Firestore dot-path notation for nested data access | `'user.profile.firstName'` |
| `updatedAt` | `Timestamp` | When the merge tag was last updated | Auto-managed by Firestore |

## Type Definitions

### MergeTagCategory

Categories for organizing merge tags by functionality:

- `'recipient'` - Recipient personal information (name, email, phone)
- `'address'` - Address and location data
- `'campaign'` - Campaign-specific data
- `'account'` - Account-level information
- `'custom'` - Custom fields
- `'system'` - System-generated data
- `'computed'` - Runtime computed values

### MergeTagDataSource

Data sources that provide merge tag values:

- `'recipient'` - Direct recipient data
- `'mailingAddress'` - Mailing address collection
- `'campaign'` - Campaign data
- `'account'` - Account settings
- `'customFields'` - Custom field definitions
- `'computed'` - Runtime computed values
- `'external'` - External API data

## Migration

### Running the Migration

The migration script is located at `scripts/migrations/migrate-merge-tags.js` and can be run with the following commands:

```bash
# Dry run to see what would be changed
node scripts/migrations/migrate-merge-tags.js --dry-run

# Run the actual migration with default batch size (100)
node scripts/migrations/migrate-merge-tags.js

# Run with custom batch size
node scripts/migrations/migrate-merge-tags.js --batch-size=50

# Validate migration results
node scripts/migrations/migrate-merge-tags.js --validate
```

### Migration Logic

The migration script automatically determines appropriate defaults based on existing merge tag data:

1. **Category Detection**: Analyzes the `id` and `name` fields to categorize tags
   - Personal info tags → `'recipient'`
   - Address tags → `'address'`
   - Campaign tags → `'campaign'`
   - Account tags → `'account'`
   - Date/time tags → `'system'`
   - Everything else → `'custom'`

2. **Data Source Mapping**: Sets data source based on category
   - `'recipient'` category → `'recipient'` data source
   - `'address'` category → `'mailingAddress'` data source
   - `'campaign'` category → `'campaign'` data source
   - `'account'` category → `'account'` data source
   - `'system'` category → `'computed'` data source

3. **Path Generation**: Creates Firestore dot-path notation where applicable
   - Recipients: `recipients.{tagId}`
   - Addresses: `mailingAddress.{tagId}`
   - Campaigns: `campaign.{tagId}`
   - Accounts: `account.{tagId}`

4. **System Flag**: Marks common system tags as `isSystem: true`
   - `currentDate`, `currentTime`, `unsubscribeLink`, `trackingPixel`
   - `recipientId`, `campaignId`, `accountId`

### Default Values

For existing merge tags that don't match any patterns:

```javascript
{
  category: 'custom',
  dataSource: 'recipient',
  createdBy: 'system-migration',
  isSystem: false,
  updatedAt: serverTimestamp()
}
```

## Validation Rules

### Firestore Path Validation

The `path` field must follow Firestore dot-path notation:

- **Valid**: `firstName`, `user.profile.firstName`, `user_data.contact_info`
- **Invalid**: `1user.name`, `.user.name`, `user..name`, `user.first-name`

**Regex**: `/^[a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*$/`

### Required Field Validation

- `id`: Minimum 3 characters
- `name`: Minimum 3 characters  
- `sample`: Minimum 2 characters
- `tempateType`: Non-empty array
- `templateSubType`: Non-empty array
- `category`: Non-empty string
- `dataSource`: Non-empty string
- `createdBy`: Non-empty string

## TypeScript Interfaces

The following TypeScript interfaces are available in `src/dataTypes/MergeTag.ts`:

- `MergeTag` - Complete merge tag document
- `CreateMergeTagInput` - For creating new merge tags
- `UpdateMergeTagInput` - For updating existing merge tags
- `MergeTagFilters` - For query filtering
- `MergeTagMigrationDefaults` - Migration helper

## Testing

Unit tests are provided in `src/domain/templates/data/__tests__/validation.test.js` covering:

- Valid merge tag validation
- Required field validation
- Firestore path validation
- Boolean and default value handling
- Timestamp field validation

Run tests with:
```bash
npm test src/domain/templates/data/__tests__/validation.test.js
```

## Examples

### Creating a New Merge Tag

```javascript
import { mergeTagSchema } from '../domain/templates/data/validation';

const newMergeTag = {
  id: 'customerFirstName',
  name: 'Customer First Name',
  sample: 'John',
  tempateType: ['email', 'print'],
  templateSubType: ['newsletter', 'postcard'],
  category: 'recipient',
  dataSource: 'recipient', 
  path: 'recipients.name.firstName',
  createdBy: 'user123',
  isSystem: false
};

// Validate before saving
const validatedTag = mergeTagSchema.parse(newMergeTag);
```

### Querying by Category

```javascript
import { collection, query, where, getDocs } from 'firebase/firestore';

// Get all recipient-related merge tags
const recipientTagsQuery = query(
  collection(db, 'MergeTags'),
  where('category', '==', 'recipient')
);

// Get all system merge tags
const systemTagsQuery = query(
  collection(db, 'MergeTags'), 
  where('isSystem', '==', true)
);
```

## Migration Checklist

- [ ] Backup MergeTags collection
- [ ] Run migration in dry-run mode
- [ ] Review proposed changes
- [ ] Run actual migration
- [ ] Validate migration results
- [ ] Update application code to use new fields
- [ ] Deploy updated validation schema
- [ ] Update documentation

## Rollback Procedure

If rollback is needed:

1. Restore from backup
2. Remove new field references from application code
3. Revert validation schema changes
4. Redeploy application

## Support

For issues with the migration or new schema:

1. Check migration logs for errors
2. Validate a sample of documents using `--validate` flag
3. Review TypeScript interfaces for correct usage
4. Run unit tests to verify validation logic
