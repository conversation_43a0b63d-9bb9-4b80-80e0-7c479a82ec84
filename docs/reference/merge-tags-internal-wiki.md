# Merge Tags System - Internal Wiki

## Overview

The enhanced merge tags system provides a comprehensive solution for dynamic content insertion across all communication channels (email, SMS, print, etc.). This replaces the legacy merge tag implementation with a modern, feature-rich system.

## Architecture

### Components Overview

```
src/domain/mergeTags/
├── components/                 # React components for UI
│   ├── MergeTagPicker.tsx     # Main picker component
│   ├── MergeTagToolbarButton.tsx  # Toolbar integration
│   └── index.ts               # Exports
├── data/                      # Data layer & React Query hooks
│   ├── mergeTags.ts          # CRUD operations
│   └── index.ts              # Exports
├── hooks/                     # Custom hooks
│   └── useMergeTagPicker.ts  # State management
├── utils/                     # Utility functions
│   ├── unlayerUtils.js       # Unlayer integration
│   └── analyticsAdapter.ts   # Analytics tracking
├── mergeTagRegistry.js        # Core registry service
└── examples/                  # Integration examples
    └── UnlayerIntegrationExample.tsx
```

### Feature Flag Integration

The system is controlled by the `enhanced-merge-tags` feature flag:

- **Flag Name**: `enhanced-merge-tags`
- **Rollout Strategy**: Gradual rollout by account ID
- **Fallback**: Legacy merge tag system

## Implementation Guide

### 1. Basic Unlayer Integration

```jsx
import { MergeTagToolbarButton } from '../domain/mergeTags/components';

function EmailTemplateEditor() {
  const editorRef = useRef();
  
  const handleInsertTag = (tag, insertText) => {
    const unlayer = editorRef.current?.editor;
    unlayer?.insertHTML(`<span class="merge-tag">${insertText}</span>`);
  };

  return (
    <div>
      <div className="toolbar">
        <MergeTagToolbarButton
          templateType="email"
          onInsertTag={handleInsertTag}
        />
      </div>
      <EmailEditor ref={editorRef} />
    </div>
  );
}
```

### 2. Feature Flag Usage

```jsx
import useFeatureFlag from '../hooks/useFeatureFlag';

function TemplateEditor() {
  const { isFeatureEnabled } = useFeatureFlag('enhanced-merge-tags');

  return (
    <div>
      {isFeatureEnabled ? (
        <MergeTagToolbarButton templateType="email" />
      ) : (
        <LegacyMergeTagButton />
      )}
    </div>
  );
}
```

### 3. Custom Integration

```jsx
import { MergeTagPicker } from '../domain/mergeTags/components';

function CustomEditor() {
  const [isPickerOpen, setIsPickerOpen] = useState(false);

  return (
    <>
      <button onClick={() => setIsPickerOpen(true)}>
        Insert Merge Tag
      </button>
      
      <MergeTagPicker
        isOpen={isPickerOpen}
        onClose={() => setIsPickerOpen(false)}
        onTagSelect={(tag, insertText) => {
          // Handle tag insertion
          insertIntoEditor(insertText);
          setIsPickerOpen(false);
        }}
        templateType="email"
      />
    </>
  );
}
```

## Rollout Strategy

### Phase 1: Internal Testing (Current)
- **Accounts**: Development team accounts only
- **Features**: Full functionality enabled
- **Monitoring**: Error tracking, performance metrics

### Phase 2: Beta Testing (Planned)
- **Accounts**: Select customer accounts (5-10)
- **Features**: Core functionality with analytics
- **Duration**: 2 weeks
- **Success Criteria**: <1% error rate, positive user feedback

### Phase 3: Gradual Rollout (Planned)
- **Week 1**: 10% of accounts
- **Week 2**: 25% of accounts  
- **Week 3**: 50% of accounts
- **Week 4**: 100% of accounts
- **Monitoring**: Real-time error rates, performance impact

### Phase 4: Legacy Cleanup (Planned)
- **Timeline**: 1 month after full rollout
- **Actions**: Remove legacy code, update documentation
- **Validation**: Ensure no legacy dependencies remain

## Migration Guide

### Template Migration

Existing templates need migration from legacy syntax to new enhanced syntax:

**Legacy Format:**
```html
Hello [firstName], welcome to [companyName]!
```

**New Format:**
```html
Hello {{firstName}}, welcome to {{companyName}}!
```

**Migration Script:**
```javascript
// Automated migration function
function migrateTemplate(templateContent) {
  return templateContent.replace(/\[(\w+)\]/g, '{{$1}}');
}
```

### Custom Tag Migration

Custom tags need to be registered in the new system:

```javascript
import mergeTagRegistry from '../domain/mergeTags/mergeTagRegistry';

// Migrate legacy custom tag
mergeTagRegistry.register({
  id: 'customField',
  name: 'Custom Field',
  sample: 'Sample Value',
  category: 'custom',
  dataSource: 'recipient',
  path: 'customFields.customField',
  templateType: ['email', 'sms'],
  isLegacyMigration: true
});
```

## Analytics & Monitoring

### Key Metrics

1. **Usage Metrics**
   - Tag insertion frequency
   - Most used tags
   - Template type breakdown
   - Error rates

2. **Performance Metrics**
   - Component load times
   - Search response times
   - Tag resolution times

3. **User Experience**
   - Picker open/close rates
   - Search success rates
   - Tag copy success rates

### Monitoring Dashboards

- **Real-time**: Firebase Analytics
- **Historical**: Custom dashboard (planned)
- **Alerts**: Error rate >1%, Performance degradation >200ms

## Testing Strategy

### Unit Tests
- Component rendering and interaction
- Hook state management
- Utility function behavior
- Registry operations

### Integration Tests  
- Unlayer editor integration
- Feature flag behavior
- Analytics event firing

### E2E Tests (Playwright)
- Complete user workflows
- Cross-browser compatibility
- Performance benchmarks

## Troubleshooting

### Common Issues

1. **Tags Not Appearing in Picker**
   ```javascript
   // Check registry status
   console.log(mergeTagRegistry.isReady());
   console.log(mergeTagRegistry.getAllTags());
   ```

2. **Unlayer Integration Not Working**
   ```javascript
   // Verify Unlayer reference
   console.log(editorRef.current?.editor);
   
   // Check insertion method
   unlayer.insertHTML(content);  // Preferred
   unlayer.insertText(content);  // Alternative
   ```

3. **Feature Flag Not Working**
   ```javascript
   // Check flag status
   const { isFeatureEnabled, isLoading } = useFeatureFlag('enhanced-merge-tags');
   console.log({ isFeatureEnabled, isLoading });
   ```

### Performance Issues

1. **Slow Picker Loading**
   - Check registry initialization
   - Verify React Query cache status
   - Monitor network requests

2. **Search Performance**
   - Search is debounced (300ms)
   - Large tag sets are virtualized
   - Consider pagination for >1000 tags

## Security Considerations

### Data Protection
- Tag samples contain no real user data
- Analytics events are anonymized
- No PII in error logs

### XSS Prevention
- All tag content is properly escaped
- HTML insertion uses safe methods
- User input is sanitized

## Support & Contact

- **Technical Lead**: [Name]
- **Product Owner**: [Name] 
- **Support Channel**: #merge-tags-support
- **Documentation**: This wiki + component README files

## Changelog

### v2.0.0 (Current)
- Enhanced picker component
- Unlayer integration
- Feature flag support
- Analytics tracking
- Full TypeScript support

### v1.0.0 (Legacy)
- Basic merge tag functionality
- Limited editor support
- No feature flags
- Minimal analytics
