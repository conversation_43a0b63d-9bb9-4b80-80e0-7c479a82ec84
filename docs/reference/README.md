# Reference Documentation

## Overview
This section contains quick reference materials, internal documentation, and technical specifications for the Titan platform.

## Available References

### Developer Tools
- **[Developer Tools Reference](developer-tools-documentation.md)** - Comprehensive overview of all developer tools

### Internal Documentation
- **[Merge Tags Wiki](merge-tags-internal-wiki.md)** - Internal merge tags documentation
- **[Merge Tags Schema](merge-tags-schema-extension.md)** - Schema extensions for merge tags

## Reference Categories

### 🔧 Developer Tools Reference
Quick reference for all development tools:
- Build and configuration tools
- Deployment and infrastructure scripts
- Testing and quality assurance utilities
- Development workflow automation

### 📋 Internal References
Internal team documentation:
- Merge tags system documentation
- Schema definitions and extensions
- Internal APIs and utilities
- Team-specific processes

### 📊 Data References
Data structures and schemas:
- Database schema documentation
- API data models
- Configuration file formats
- Environment variable references

## Quick Reference Guides

### Essential Commands
```bash
# Environment setup
npm run build-dev-env              # Create development .env
npm run build-dev-functions-env    # Create functions .env

# Development
npm run start-dev                  # Start development server
npm run build                      # Build for production

# Testing
node runTests.mjs                  # Interactive test runner

# Deployment
./deploy.sh dev                    # Deploy to development
./deploy.sh prod                   # Deploy to production

# Git workflow
./auto-commit.mjs                  # AI-enhanced commits
```

### Configuration Files
```
Project Configuration Files:
├── package.json              # Project manifest
├── babel.config.js          # Babel configuration
├── jest.config.js           # Jest test configuration
├── playwright.config.ts     # Playwright E2E tests
├── postcss.config.js        # PostCSS configuration
├── tailwind.config.js       # Tailwind CSS configuration
├── tsconfig.json            # TypeScript configuration
├── vite.config.js           # Vite build configuration
└── tests.json               # Test runner configuration
```

### Environment Variables
```bash
# Development Environment
NODE_ENV=development
VITE_APP_ENV=development
FIREBASE_PROJECT_ID=rm-titan-dev

# Production Environment
NODE_ENV=production
VITE_APP_ENV=production
FIREBASE_PROJECT_ID=rm-titan-prod

# API Configuration
API_BASE_URL=https://api.titan.com
API_KEY=your-api-key
AUTH_DOMAIN=titan.firebaseapp.com
```

### Directory Structure
```
titan/
├── docs/                     # Documentation
│   ├── getting-started/     # Getting started guides
│   ├── developer-guide/     # Developer documentation
│   ├── architecture/        # System architecture
│   ├── deployment/          # Deployment guides
│   ├── api/                 # API documentation
│   ├── user-guides/         # User guides
│   └── reference/           # Reference materials
├── src/                      # Source code
│   ├── domain/              # Domain-specific code
│   ├── shared/              # Shared utilities
│   └── global/              # Global components
├── functions/               # Cloud functions
├── playwright/              # E2E tests
├── public/                  # Static assets
└── scripts/                 # Utility scripts
```

## API Reference Summary

### Authentication Endpoints
```
POST /api/auth/login         # User login
POST /api/auth/logout        # User logout
POST /api/auth/refresh       # Refresh token
GET  /api/auth/profile       # User profile
```

### Core Endpoints
```
GET    /api/users            # List users
POST   /api/users            # Create user
GET    /api/campaigns        # List campaigns
POST   /api/campaigns        # Create campaign
GET    /api/analytics        # Get analytics
```

### Response Format
```json
{
  "success": true,
  "data": {},
  "error": null,
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "req_12345"
}
```

## Database Schema Reference

### User Schema
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user' | 'viewer';
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
  preferences: UserPreferences;
}
```

### Campaign Schema
```typescript
interface Campaign {
  id: string;
  title: string;
  description: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  settings: CampaignSettings;
  metrics: CampaignMetrics;
}
```

### Analytics Schema
```typescript
interface AnalyticsEvent {
  id: string;
  eventType: string;
  userId?: string;
  campaignId?: string;
  timestamp: Date;
  properties: Record<string, any>;
  sessionId: string;
}
```

## Configuration Reference

### Build Configuration
```javascript
// vite.config.js
export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          utils: ['lodash', 'moment']
        }
      }
    }
  }
});
```

### Test Configuration
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  transform: {
    '^.+\\.(ts|tsx)$': 'babel-jest'
  },
  moduleNameMapping: {
    '\\.(css|less|scss)$': 'identity-obj-proxy'
  }
};
```

## Deployment Reference

### Environment Targets
```bash
# Development
PROJECT_ID=rm-titan-dev
FIREBASE_PROJECT=rm-titan-dev
API_URL=https://rm-titan-dev.web.app/api

# Production
PROJECT_ID=rm-titan-prod
FIREBASE_PROJECT=rm-titan-prod
API_URL=https://rm-titan-prod.web.app/api
```

### Deployment Commands
```bash
# Full deployment
./deploy.sh dev

# Functions only
firebase deploy --only functions

# Hosting only
firebase deploy --only hosting

# Specific function
firebase deploy --only functions:functionName
```

## Error Codes Reference

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

### Application Error Codes
- `AUTH_001` - Invalid credentials
- `AUTH_002` - Token expired
- `VAL_001` - Validation error
- `VAL_002` - Required field missing
- `SYS_001` - System error
- `SYS_002` - Database error

## Performance Benchmarks

### Load Times
- **First Contentful Paint**: < 1.5s
- **Time to Interactive**: < 3.0s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1

### API Response Times
- **Authentication**: < 200ms
- **Data Queries**: < 500ms
- **Complex Operations**: < 2000ms
- **File Uploads**: < 5000ms

## Security Reference

### Authentication
- **JWT Token Expiry**: 1 hour
- **Refresh Token Expiry**: 7 days
- **Session Timeout**: 24 hours
- **Password Requirements**: 8+ chars, mixed case, numbers

### API Security
- **Rate Limiting**: 1000 requests/hour
- **CORS Origins**: Configured domains only
- **SSL/TLS**: Required for all endpoints
- **API Key Rotation**: Every 90 days

## Monitoring Reference

### Key Metrics
- **Uptime**: 99.9%
- **Error Rate**: < 0.1%
- **Response Time**: < 500ms avg
- **Throughput**: 1000 requests/minute

### Alerting Thresholds
- **High Error Rate**: > 1%
- **Slow Response**: > 2000ms
- **High Load**: > 80% CPU
- **Low Disk Space**: < 10% available

## Best Practices Summary

### Code Quality
- Use TypeScript for type safety
- Follow ESLint rules
- Write comprehensive tests
- Document complex logic

### Performance
- Implement lazy loading
- Optimize bundle size
- Use efficient algorithms
- Cache frequently accessed data

### Security
- Validate all inputs
- Use HTTPS everywhere
- Implement proper authentication
- Regular security audits

### Deployment
- Test thoroughly before production
- Use feature flags for risky changes
- Monitor post-deployment metrics
- Have rollback procedures ready

## Resources

### Internal Documentation
- [Getting Started](../getting-started/) - Onboarding guide
- [Developer Guide](../developer-guide/) - Development practices
- [Architecture](../architecture/) - System design
- [API Reference](../api/) - Backend integration

### External Resources
- [React Documentation](https://react.dev)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Google Cloud Documentation](https://cloud.google.com/docs)

## Support

For reference questions:
1. Check the relevant reference section
2. Search the documentation
3. Review code examples
4. Contact the development team
5. Update documentation if needed
