{"swagger": "2.0", "info": {"title": "ReminderMedia Titan Server", "description": "Server Structure for Titan Functions", "version": "1.0.0"}, "host": "us-central1-rm-titan-dev.cloudfunctions.net", "schemes": ["https"], "produces": ["application/json"], "securityDefinitions": {"api_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "key", "in": "query"}, "rmc_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"}, "print_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-print-service-account-dev%40rm-titan-dev.iam.gserviceaccount.com"}, "crm_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-crm-service-account-dev%40rm-titan-dev.iam.gserviceaccount.com"}, "mercury_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-mercury-service-account-dev%40rm-titan-dev.iam.gserviceaccount.com"}, "landing_pages_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-landing-pages-servie-accoun%40rm-titan-dev.iam.gserviceaccount.com"}}, "tags": [{"name": "Enterprise"}, {"name": "Workflows"}, {"name": "Accounts"}, {"name": "Events"}, {"name": "Areas"}, {"name": "RMC"}, {"name": "Algolia"}, {"name": "Analytics"}, {"name": "Recipient Manager"}], "paths": {"/getWorkflow": {"get": {"tags": ["Workflows"], "summary": "Get Workflow", "description": "Get Workflow", "parameters": [{"name": "accountId", "in": "query", "description": "The ID of the account document to fetch.", "required": true, "type": "string"}, {"name": "workflowId", "in": "query", "description": "The ID of the workflow document to fetch.", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "getWorkflow", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/getWorkflow"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/getWorkflowExecution": {"get": {"tags": ["Workflows"], "summary": "Get Workflow Execution", "description": "Get Workflow Execution", "parameters": [{"name": "accountId", "in": "query", "description": "The ID of the account document to fetch.", "required": true, "type": "string"}, {"name": "leadId", "in": "query", "description": "The ID of the lead document to fetch.", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "getWorkflowExecution", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/getWorkflowExecution"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/getAccount": {"get": {"tags": ["Accounts"], "summary": "Get Account", "description": "Get Account", "parameters": [{"name": "accountId", "in": "query", "description": "The ID of the account document to fetch.", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "getAccount", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/getAccount"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/getEventsByArea": {"get": {"tags": ["Events"], "summary": "Get all events for a specific area", "description": "Retrieves all events associated with a given area UUID, with optional filtering capabilities", "operationId": "getEventsByArea", "parameters": [{"name": "area_uuid", "in": "query", "description": "Unique identifier of the area", "required": true, "type": "string"}, {"name": "limit", "in": "query", "description": "Maximum number of events to return (max: 500)", "required": false, "type": "integer", "default": 100}, {"name": "active_only", "in": "query", "description": "Filter to show only active events", "required": false, "type": "string", "default": "false"}, {"name": "start_date", "in": "query", "description": "Filter events starting on or after this date (ISO 8601 format)", "required": false, "type": "string"}, {"name": "end_date", "in": "query", "description": "Filter events ending on or before this date (ISO 8601 format)", "required": false, "type": "string"}, {"name": "popularity_min", "in": "query", "description": "Minimum popularity score (0-6)", "required": false, "type": "integer"}, {"name": "page_token", "in": "query", "description": "Token for pagination (use next_page_token from previous response)", "required": false, "type": "string"}], "responses": {"200": {"description": "Successful response", "schema": {"$ref": "#/definitions/EventsByAreaResponse"}}, "400": {"description": "Bad request - Missing or invalid parameters", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "404": {"description": "Area not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "405": {"description": "Method not allowed", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/getEventsByArea"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/getLiveEvents": {"get": {"tags": ["Events"], "summary": "Get Live Events", "description": "Retrieves active events from the LiveEvents collection with blocked tag filtering. Supports both paginated and non-paginated responses.", "operationId": "getLiveEvents", "parameters": [{"name": "paginate", "in": "query", "description": "Enable pagination mode", "required": false, "type": "boolean", "default": false}, {"name": "page", "in": "query", "description": "Page number (only used when paginate=true)", "required": false, "type": "integer", "minimum": 1, "default": 1}, {"name": "limit", "in": "query", "description": "Number of events per page (only used when paginate=true)", "required": false, "type": "integer", "minimum": 1, "maximum": 1000, "default": 100}], "responses": {"200": {"description": "Successful response - returns either paginated or non-paginated response based on the paginate parameter", "schema": {"type": "object"}}, "405": {"description": "Method not allowed", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/getLiveEvents"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/searchAreas": {"get": {"tags": ["Areas"], "summary": "Search for areas by name or location", "description": "Search for event areas/markets either by name prefix or by geographic proximity", "operationId": "searchAreas", "parameters": [{"name": "name", "in": "query", "description": "Area name to search for (prefix match)", "required": false, "type": "string"}, {"name": "lat", "in": "query", "description": "Latitude for geographic search (requires lng)", "required": false, "type": "number"}, {"name": "lng", "in": "query", "description": "Longitude for geographic search (requires lat)", "required": false, "type": "number"}, {"name": "radius_km", "in": "query", "description": "Search radius in kilometers (for geographic search)", "required": false, "type": "number", "default": 50}, {"name": "limit", "in": "query", "description": "Maximum number of areas to return", "required": false, "type": "integer", "default": 20}], "responses": {"200": {"description": "Successful response", "schema": {"$ref": "#/definitions/AreaSearchResponse"}}, "400": {"description": "Bad request - Invalid parameters or missing required parameter combinations", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "405": {"description": "Method not allowed", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/searchAreas"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/getAllMarkets": {"get": {"tags": ["Events"], "summary": "Get all event markets", "description": "Retrieves all event markets with optional filtering, sorting, and pagination support", "operationId": "getAllMarkets", "parameters": [{"name": "active_only", "in": "query", "description": "Filter to show only active markets", "required": false, "type": "boolean", "default": false}, {"name": "paginate", "in": "query", "description": "Enable pagination mode", "required": false, "type": "boolean", "default": false}, {"name": "page", "in": "query", "description": "Page number (only used when paginate=true)", "required": false, "type": "integer", "minimum": 1, "default": 1}, {"name": "limit", "in": "query", "description": "Number of markets per page (max: 500)", "required": false, "type": "integer", "minimum": 1, "maximum": 500, "default": 100}, {"name": "min_events", "in": "query", "description": "Minimum number of events a market must have", "required": false, "type": "integer", "minimum": 0}, {"name": "sort_by", "in": "query", "description": "Field to sort by", "required": false, "type": "string", "enum": ["name", "event_count", "created"], "default": "name"}, {"name": "order", "in": "query", "description": "Sort order", "required": false, "type": "string", "enum": ["asc", "desc"], "default": "asc"}, {"name": "search", "in": "query", "description": "Search term for market names", "required": false, "type": "string"}], "responses": {"200": {"description": "Successful response", "schema": {"$ref": "#/definitions/MarketsResponse"}}, "405": {"description": "Method not allowed", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/getAllMarkets"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/getMarketStats": {"get": {"tags": ["Events"], "summary": "Get market statistics", "description": "Returns aggregate statistics about all event markets", "operationId": "getMarketStats", "responses": {"200": {"description": "Successful response", "schema": {"$ref": "#/definitions/MarketStatsResponse"}}, "405": {"description": "Method not allowed", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/getMarketStats"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/updateAccount": {"post": {"summary": "Updates account", "description": "Update account", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "updateAccount", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/updateAccount"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/rmcAccountSync": {"post": {"tags": ["RMC"], "summary": "Invoke rmcAccountSync", "description": "Invoke the rmcAccountSync function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "rmcAccountSync", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/rmcAccountSync"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/ext-firestore-algolia-search-executeFullIndexOperation": {"patch": {"tags": ["Algolia"], "summary": "Invoke ext-firestore-algolia-search-executeFullIndexOperation", "description": "Invoke the ext-firestore-algolia-search-executeFullIndexOperation function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "ext-firestore-algolia-search-executeFullIndexOperation", "x-google-backend": {"address": "https://us-central1-rm-titan-prod.cloudfunctions.net/ext-firestore-algolia-search-executeFullIndexOperation"}, "security": [{"api_key": [], "rmc_token": []}]}}, "/ext-firestore-algolia-search-users-executeFullIndexOperation": {"patch": {"tags": ["Algolia"], "summary": "Invoke ext-firestore-algolia-search-users-executeFullIndexOperation", "description": "Invoke the ext-firestore-algolia-search-users-executeFullIndexOperation function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "ext-firestore-algolia-search-users-executeFullIndexOperation", "x-google-backend": {"address": "https://us-central1-rm-titan-prod.cloudfunctions.net/ext-firestore-algolia-search-users-executeFullIndexOperation"}, "security": [{"api_key": [], "rmc_token": []}]}}, "/ext-firestore-algolia-search-txf1-executeFullIndexOperation": {"patch": {"tags": ["Algolia"], "summary": "Invoke ext-firestore-algolia-search-txf1-executeFullIndexOperation", "description": "Invoke the ext-firestore-algolia-search-txf1-executeFullIndexOperation function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "ext-firestore-algolia-search-txf1-executeFullIndexOperation", "x-google-backend": {"address": "https://us-central1-rm-titan-prod.cloudfunctions.net/ext-firestore-algolia-search-txf1-executeFullIndexOperation"}, "security": [{"api_key": [], "rmc_token": []}]}}, "/RMCLogIn": {"patch": {"tags": ["RMC"], "summary": "Invoke RMCLogIn", "description": "Invoke the RMCLogIn function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "RMCLogIn", "x-google-backend": {"address": "https://us-central1-rm-titan-prod.cloudfunctions.net/RMCLogIn"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}]}}, "/assignToEnterprise": {"get": {"tags": ["Enterprise"], "summary": "Invoke assignEnterprise", "description": "Assigns an account to an enterprise by adding the \"enterprise\" field to an account document. The enterprise field is an object with \"label\" and \"value\" properties.", "parameters": [{"name": "accountId", "in": "query", "description": "The ID of the account document to be assigned to an enterprise.", "required": true, "type": "string"}, {"name": "enterprise", "in": "query", "description": "The ID of the enterprise to which the account should be assigned.", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response that returns the \"enterprise\" label and value.", "schema": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"type": "string"}}}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "assignToEnterprise", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/assignToEnterprise"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/shortURLTraffic": {"get": {"tags": ["Analytics"], "summary": "Get traffic data", "description": "Get traffic data.", "parameters": [{"name": "lpId", "in": "query", "description": "The landing page id", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response that returns the \"shortURLTraffic\" label and value.", "schema": {"type": "object", "properties": {"data": {"type": "object", "$ref": "#/definitions/shortURLTrafficData"}, "success": {"type": "boolean"}, "message": {"type": "string"}}}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/shortURLTraffic"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/lpAnalytics": {"get": {"tags": ["Analytics"], "summary": "Get landing page analytics", "description": "Get landing page analytics.", "parameters": [{"name": "lpId", "in": "query", "description": "The landing page id", "required": true, "type": "string"}, {"name": "eventType", "in": "query", "description": "The event type to filter by", "required": false, "type": "string"}], "responses": {"200": {"description": "A successful response that returns the landing page analytics.", "schema": {"type": "object", "properties": {"data": {"type": "object", "$ref": "#/definitions/AnalyticsData"}, "success": {"type": "boolean"}, "message": {"type": "string"}}}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "lpAnalytics", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/lpAnalytics"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/checkEnterpriseAssociation": {"get": {"tags": ["Enterprise"], "summary": "Invoke checkEnterpriseAssociation", "description": "Checks enterprise association between an account and an enterprise.", "parameters": [{"name": "accountId", "in": "query", "description": "The ID of the account document to check for association with an enterprise.", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response that returns the enterprise and associated details.", "schema": {"type": "object", "properties": {"name": {"type": "string"}, "account_id": {"type": "string"}, "breadcrumbs": {"type": "string"}, "dam_owner_ids": {"type": "array", "items": {"type": "string"}}, "enterprise_id": {"type": "string"}, "enterprise_name": {"type": "string"}, "enterprise": {"type": "object", "$ref": "#/definitions/Enterprise"}, "restrictions": {"type": "array", "items": {"type": "string"}}}}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "checkEnterpriseAssociation", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/checkEnterpriseAssociation"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/enterpriseList": {"get": {"tags": ["Enterprise"], "summary": "Invoke enterpriseList", "description": "Retrieves the full list of enterprises.", "responses": {"200": {"description": "A successful response with the list of enterprises.", "schema": {"type": "array", "items": {"$ref": "#/definitions/Enterprise"}}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "enterpriseList", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/enterpriseList"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/recordRecipientVisit": {"post": {"tags": ["Recipient Manager"], "summary": "Records recipient visit", "description": "Used to track lead sources", "responses": {"200": {"description": "A successful response", "schema": {"type": "object"}}}, "operationId": "recordRecipientVisit", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/recordRecipientVisit"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/lead": {"post": {"tags": ["Recipient Manager"], "summary": "Recipient Creation from a lead", "description": "Used by landing pages to create a recipient", "responses": {"200": {"description": "A successful response", "schema": {"type": "object"}}}, "operationId": "lead", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/lead"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/syncAccountManager": {"post": {"tags": ["RMC"], "summary": "Invoke syncAccountManager", "description": "Invoke the syncAccountManager function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "parameters": [{"name": "accountId", "in": "query", "description": "The ID of the account document to check for given recipient group.", "required": true, "type": "string"}, {"name": "email", "in": "query", "description": "The email address to check for auth record and user record", "required": true, "type": "string"}, {"name": "userId", "in": "query", "description": "The user ID to check for auth record and user record", "required": true, "type": "string"}, {"name": "action", "in": "query", "description": "The action to perform on the user record", "required": true, "type": "string", "enum": ["add", "remove"]}], "operationId": "syncAccountManager", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/syncAccountManager"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/updateUserEmail": {"post": {"tags": ["RMC"], "summary": "Invoke updateUserEmail", "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}], "description": "Invoke the updateUserEmail function, to update Auth and User email", "operationId": "updateUserEmail", "parameters": [{"name": "newEmail", "in": "query", "description": "The new email address", "required": true, "type": "string"}, {"name": "oldEmail", "in": "query", "description": "The old email address", "required": true, "type": "string"}, {"name": "userId", "in": "query", "description": "The user ID", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}, "400": {"description": "Bad request - Missing or invalid parameters", "schema": {"type": "object", "properties": {"status": {"type": "string", "example": "failure"}, "message": {"type": "string", "example": "Operation failed"}}}}, "401": {"description": "Unauthorized - Invalid or missing auth token", "schema": {"type": "object", "properties": {"status": {"type": "string", "example": "failure"}, "message": {"type": "string", "example": "Operation failed"}}}}, "403": {"description": "Cannot update email for internal users.", "schema": {"type": "object", "properties": {"status": {"type": "string", "example": "failure"}, "message": {"type": "string", "example": "Operation failed"}}}}, "404": {"description": "User not found.", "schema": {"type": "object", "properties": {"status": {"type": "string", "example": "failure"}, "message": {"type": "string", "example": "Operation failed"}}}}, "409": {"description": "The new email address is already in use by another account.", "schema": {"type": "object", "properties": {"status": {"type": "string", "example": "failure"}, "message": {"type": "string", "example": "Operation failed"}}}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "properties": {"status": {"type": "string", "example": "failure"}, "message": {"type": "string", "example": "Operation failed"}}}}}, "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/updateUserEmail"}}}, "/recipients": {"get": {"tags": ["Recipient Manager"], "summary": "Collect a group of recipients by productId or recipientGroupId", "description": "Returns the valid recipients for a given productId or recipientGroupId.", "parameters": [{"name": "accountId", "in": "query", "description": "The ID of the account document to check for given recipient group.", "required": true, "type": "string"}, {"name": "recipientGroupId", "in": "query", "description": "The recipient group id to check for", "required": false, "type": "string"}, {"name": "productId", "in": "query", "description": "The rm product id to verify the recipient group against", "required": false, "type": "string"}, {"name": "method", "in": "query", "description": "The method is either 'address' or 'email'", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response with the list of recipients in a recipient group", "schema": {"type": "object"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "recipients", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/recipients"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/emailPreProcess": {"post": {"tags": ["Recipient Manager"], "summary": "Validates email addresses associated with the collection of recipient group paths", "description": "TODO: Add description", "responses": {"200": {"description": "A successful response with the list of recipients in a recipient group", "schema": {"type": "object"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "emailPreProcess", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/emailPreProcess"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/recipientsByCampaign": {"get": {"tags": ["Recipient Manager"], "summary": "Collect a group of recipients by productId campaignId", "description": "Returns the valid recipients for a given productId campaignId.", "parameters": [{"name": "accountId", "in": "query", "description": "The ID of the account document to check for given recipient group.", "required": true, "type": "string"}, {"name": "campaignId", "in": "query", "description": "The campaign id to check for", "required": false, "type": "string"}, {"name": "productId", "in": "query", "description": "The rm product id to verify the campaignId against", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response with the list of recipients", "schema": {"type": "object"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "recipientsByCampaign", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/recipientsByCampaign"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/removeFromEnterprise": {"post": {"tags": ["Enterprise"], "summary": "Invoke removeFromEnterprise", "description": "Invoke the removeFromEnterprise function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "removeFromEnterprise", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/removeFromEnterprise"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/shortLivedTokenForCustomers": {"post": {"tags": ["RMC"], "summary": "Invoke shortLivedTokenForCustomers", "description": "Invoke the shortLivedTokenForCustomers function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "shortLivedTokenForCustomers", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/shortLivedTokenForCustomers"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/shortLivedTokenForAdmins": {"post": {"tags": ["RMC"], "summary": "Invoke shortLivedTokenForAdmins", "description": "Invoke the shortLivedTokenForAdmins function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "shortLivedTokenForAdmins", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/shortLivedTokenForAdmins"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/recipient": {"post": {"tags": ["Recipient Manager"], "summary": "Recipient Creation from a lead", "description": "Used by Mercury to create a new recipient from a lead", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "createRecipient", "x-google-backend": {"address": "https://us-central1-rm-titan-prod.cloudfunctions.net/recipient"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}, "get": {"tags": ["Recipient Manager"], "summary": "Get recipient details", "description": "Used to retrieve details of a recipient", "responses": {"200": {"description": "A successful response", "schema": {"type": "object"}}}, "operationId": "recipient", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/recipient"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/recipientGroup": {"get": {"tags": ["Recipient Manager"], "summary": "Invoke recipientGroup", "description": "Invoke the recipientGroup function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "recipientGroup", "x-google-backend": {"address": "https://us-central1-rm-titan-prod.cloudfunctions.net/recipientGroup"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}]}}, "/recipientGroups": {"get": {"tags": ["Recipient Manager"], "summary": "Invoke recipientGroups", "description": "Invoke the recipientGroups function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "recipientGroups", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/recipientGroups"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}, "post": {"tags": ["Recipient Manager"], "summary": "Invoke recipientGroups via POST", "description": "Invoke the recipientGroups function using a POST request.", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "recipientGroupsPost", "x-google-backend": {"address": "https://us-central1-rm-titan-prod.cloudfunctions.net/recipientGroups"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/recipientGroupSummary": {"get": {"tags": ["Recipient Manager"], "summary": "Invoke recipientGroup<PERSON>um<PERSON><PERSON>", "description": "Invoke the recipientGroupSummary function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "recipientGroupSummary", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/recipientGroupSummary"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/shortenUrl": {"post": {"tags": ["<PERSON> Shortening"], "summary": "Invoke shortenUrl", "description": "Creates a shortened URL and generates a QR code for it. Accepts either a Landing Page ID (`lpId`) or a Long URL (`longUrl`) & (`accountId`). Alsways send (`productId`). Values shown in (`linkData`) are optional and those shown are used to build tracking into links. You may add any key -> value to linkData and it will be stored", "operationId": "shortenUrl", "produces": ["application/json"], "consumes": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Data required to shorten a URL. You must provide either `lpId` or `longUrl`, but not both.", "required": true, "schema": {"type": "object", "properties": {"lpId": {"type": "string", "description": "Landing Page ID to generate a short link for.", "example": "0abd8967-553b-4101-aeeb-d94e16cb6e25"}, "longUrl": {"type": "string", "description": "Long URL to shorten.", "example": "https://google.com"}, "accountId": {"type": "string", "description": "Account ID", "example": "1677765"}, "productId": {"type": "string", "description": "Product ID associated with the short link.", "example": "4"}, "linkData": {"type": "object", "description": "Metadata for the short link.", "properties": {"source": {"type": "string", "description": "Traffic source.", "example": "qr_code"}, "medium": {"type": "string", "description": "Marketing medium.", "example": "facebook"}, "campaign": {"type": "string", "description": "Campaign name.", "example": "price lookup"}, "content": {"type": "string", "description": "Content description.", "example": "testing"}, "page_uuid": {"type": "string", "description": "Page UUID.", "example": "0abd8967-553b-4101-aeeb-d94e16cb6e25"}, "recipient_uuid": {"type": "string", "description": "Recipient UUID.", "example": "0065e424-59b1-47e4-b062-165facfd9aca"}, "mailing_id": {"type": "integer", "description": "Mailing ID.", "example": 822137}}}}, "required": ["productId"], "description": "You must provide either `lpId` or `longUrl && AccountId`, but not both. `productId` required all the time"}}], "responses": {"200": {"description": "A successful response containing the shortened URL and associated data.", "schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the operation was successful.", "example": true}, "data": {"type": "object", "description": "Details of the shortened URL and metadata."}}}}, "400": {"description": "Bad request due to invalid or missing parameters.", "schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates the operation was not successful.", "example": false}, "error": {"type": "string", "description": "Description of the error.", "example": "You must provide either `lpId` or `longUrl`, but not both."}}}}}, "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/shortenUrl"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/trackClick": {"patch": {"summary": "Invoke trackClick", "description": "Invoke the trackClick function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/trackEvent": {"post": {"tags": ["Analytics"], "summary": "Write analytics data", "description": "Write Analytics data", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "trackEvent", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/trackEvent"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/recipientGroupAssignments": {"post": {"tags": ["Recipient Manager"], "summary": "Invoke recipientGroupAssignments", "description": "Invoke the recipientGroupAssignments function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "recipientGroupAssignments", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/recipientGroupAssignments"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/linkDomains": {"get": {"tags": ["<PERSON> Shortening"], "summary": "Invoke linkDomains", "description": "Invoke the linkDomains function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "linkDomains", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/linkDomains"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/createRecipientShortUrl": {"patch": {"summary": "Invoke createRecipientShortUrl", "description": "Invoke the createRecipientShortUrl function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "createRecipientShortUrl", "x-google-backend": {"address": "https://us-central1-rm-titan-prod.cloudfunctions.net/createRecipientShortUrl"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/linkData": {"get": {"tags": ["<PERSON> Shortening"], "summary": "Invoke linkData", "description": "Get data based on a link shortCode", "operationId": "linkData", "parameters": [{"in": "query", "name": "shortCode", "description": "The shortCode used to fetch associated link data.", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response containing the requested link data.", "schema": {"type": "string"}}}, "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/linkData"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/lpCodes": {"get": {"tags": ["<PERSON> Shortening"], "summary": "Invoke lpCodes", "description": "Get all codes associated with a specific `lpId`.", "operationId": "lpCodes", "parameters": [{"in": "query", "name": "lpId", "description": "The Landing Page ID to filter the codes by.", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response containing the requested data.", "schema": {"type": "string"}}}, "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/lpCodes"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/domainUpdate": {"get": {"summary": "Invoke domainUpdate", "tags": ["<PERSON> Shortening"], "description": "Invoke the domainUpdate function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "domainUpdate", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/domainUpdate"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/heartbeat": {"get": {"summary": "Invoke heartbeat", "tags": ["Monitoring"], "description": "Invoke the heartbeat function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "heartbeat", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/heartbeat"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/recipientGroupsForPrint": {"post": {"summary": "Invoke recipientGroupsForPrint", "tags": ["Recipient Manager"], "description": "Invoke the recipientGroupsForPrint function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "recipientGroupsForPrint", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/recipientGroupsForPrint"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/analyticsData": {"get": {"summary": "Invoke analyticsData", "tags": ["<PERSON> Shortening"], "description": "Invoke the analyticsData function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "analyticsData", "x-google-backend": {"address": "https://us-central1-rm-titan-prod.cloudfunctions.net/analyticsData"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/featureFlag": {"post": {"summary": "Invoke featureFlag", "tags": ["Accounts"], "description": "Invoke the featureFlag function", "parameters": [{"in": "body", "name": "body", "description": "Required parameters for invoking the featureFlag function", "required": true, "schema": {"type": "object", "properties": {"flag": {"type": "string", "description": "The flag identifier for the feature flag."}, "accountId": {"type": "string", "description": "The account ID to check access for the feature flag."}}, "required": ["flag", "accountId"]}}], "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "featureFlag", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/featureFlag"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/api": {"patch": {"summary": "Invoke api", "description": "Invoke the api function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/recipientsForLegacy": {"get": {"summary": "Invoke recipientsForLegacy", "description": "Invoke the recipientsForLegacy function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "recipientsForLegacy", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/recipientsForLegacy"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/createAccount": {"post": {"tags": ["Recipient Manager"], "summary": "Create Account", "description": "Used in the migration process to create an account if we find one is missing.", "responses": {"200": {"description": "A successful response", "schema": {"type": "object"}}}, "operationId": "createAccount", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/createAccount"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/migratedRecipient": {"post": {"tags": ["Recipient Manager"], "summary": "Create Recipient", "description": "Used in the migration process to create a recipient if we find one is missing.", "responses": {"200": {"description": "A successful response", "schema": {"type": "object"}}}, "operationId": "migratedRecipient", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/migratedRecipient"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/migratedRecipientGroup": {"post": {"tags": ["Recipient Manager"], "summary": "Create Recipient Group", "description": "Used in the migration process to create a recipientGroup if we find one is missing.", "responses": {"200": {"description": "A successful response", "schema": {"type": "object"}}}, "operationId": "migratedRecipientGroup", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/migratedRecipientGroup"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/cyclrWebhook": {"patch": {"summary": "Invoke cyclrWebhook", "description": "Invoke the cyclrWebhook function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "cyclrWebhook", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/cyclrWebhook"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/getAccountIntegrations": {"patch": {"summary": "Invoke getAccountIntegrations", "description": "Invoke the getAccountIntegrations function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "getAccountIntegrations", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/getAccountIntegrations"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/recipientGroupCounts": {"get": {"summary": "Invoke recipientGroupCounts", "description": "Invoke the recipientGroupCounts function", "parameters": [{"name": "accountId", "in": "query", "description": "The ID of the account document to check for given recipient group.", "required": true, "type": "string"}], "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "recipientGroupCounts", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/recipientGroupCounts"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/getImagesFromBucket": {"patch": {"summary": "Invoke getImagesFromBucket", "description": "Invoke the getImagesFromBucket function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "getImagesFromBucket", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/getImagesFromBucket"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/accountPlans": {"patch": {"summary": "Invoke accountPlans", "description": "Invoke the accountPlans function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "accountPlans", "x-google-backend": {"address": "https://us-central1-rm-titan-dev.cloudfunctions.net/accountPlans"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}, "/uploadPublishedTemplate": {"patch": {"summary": "Invoke uploadPublishedTemplate", "description": "Invoke the uploadPublishedTemplate function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "uploadPublishedTemplate", "x-google-backend": {"address": "https://us-central1-rm-titan-prod.cloudfunctions.net/uploadPublishedTemplate"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}}, "definitions": {"Enterprise": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "damId": {"type": "string"}, "defaultLetter": {"type": "string"}, "disclaimer": {"type": "string"}, "domain": {"type": "string"}, "partner": {"type": "string"}, "photoUrl": {"type": "string"}, "shortDisclaimer": {"type": "string"}, "shortName": {"type": "string"}, "parent": {"type": "string"}, "restrictions": {"type": "array", "items": {"type": "string"}}, "tags": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string"}}}, "shortURLTrafficData": {"type": "object", "properties": {"totalVisits": {"type": "integer", "example": 4}, "uniqueVisits": {"type": "integer", "example": 4}, "qrScans": {"type": "integer", "example": 4}}}, "AnalyticsData": {"type": "object", "properties": {"totalVisits": {"type": "integer", "example": 4}, "uniqueVisits": {"type": "integer", "example": 4}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "failure"}, "message": {"type": "string", "example": "Operation failed"}}}, "OGEvent": {"type": "object", "properties": {"uuid": {"type": "string", "description": "Unique identifier for the event"}, "name": {"type": "string", "description": "Name of the event"}, "description": {"type": "string", "description": "Detailed description of the event"}, "start_date": {"type": "string", "description": "Event start date and time (ISO 8601)"}, "end_date": {"type": "string", "description": "Event end date and time (ISO 8601)"}, "instance_date": {"type": "string", "description": "Instance date for recurring events"}, "venue": {"type": "object", "properties": {"name": {"type": "string"}, "city": {"type": "string"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}}}, "urls": {"type": "object", "properties": {"source": {"type": "string"}, "ticket": {"type": "string"}, "image": {"type": "string"}}}, "popularity_score": {"type": "integer", "description": "Event popularity score (0-6)"}, "cancelled": {"type": "string", "description": "Cancellation status"}, "is_active": {"type": "boolean", "description": "Whether the event is currently active"}, "areas": {"type": "array", "items": {"type": "string"}, "description": "List of area UUIDs this event belongs to"}, "recurring_event_uuid": {"type": "string", "description": "UUID of the parent recurring event"}, "umbrella_event_uuid": {"type": "string", "description": "UUID of the parent umbrella event"}}}, "OGArea": {"type": "object", "properties": {"uuid": {"type": "string", "description": "Unique identifier for the area"}, "name": {"type": "string", "description": "Name of the area/market"}, "latitude": {"type": "number", "description": "Area center latitude"}, "longitude": {"type": "number", "description": "Area center longitude"}, "radius": {"type": "number", "description": "Area radius in kilometers"}, "event_count": {"type": "integer", "description": "Number of events in this area"}, "distance_km": {"type": "number", "description": "Distance from search center in kilometers (only in location search)"}}}, "EventsByAreaResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Whether the request was successful"}, "area": {"$ref": "#/definitions/OGArea"}, "events": {"type": "array", "items": {"$ref": "#/definitions/OGEvent"}}, "count": {"type": "integer", "description": "Number of events returned in this response"}, "total_in_area": {"type": "integer", "description": "Total number of events in this area"}, "filters_applied": {"type": "object", "properties": {"active_only": {"type": "boolean"}, "start_date": {"type": "string"}, "end_date": {"type": "string"}, "popularity_min": {"type": "integer"}}}, "pagination": {"type": "object", "properties": {"has_more": {"type": "boolean", "description": "Whether there are more results available"}, "next_page_token": {"type": "string", "description": "Token to retrieve the next page of results"}, "limit": {"type": "integer", "description": "Maximum number of results per page"}}}}}, "AreaSearchResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "areas": {"type": "array", "items": {"$ref": "#/definitions/OGArea"}}, "count": {"type": "integer", "description": "Number of areas returned"}, "total_found": {"type": "integer", "description": "Total number of areas found (only in location search)"}, "search_type": {"type": "string", "description": "Type of search performed: 'name' or 'location'"}, "search_term": {"type": "string", "description": "The search term used (only in name search)"}, "search_center": {"type": "object", "description": "Search center coordinates (only in location search)", "properties": {"latitude": {"type": "number"}, "longitude": {"type": "number"}}}, "search_radius_km": {"type": "number", "description": "Search radius in kilometers (only in location search)"}}}, "MarketsResponse": {"type": "object", "required": ["success", "markets", "count", "filters_applied"], "properties": {"success": {"type": "boolean", "description": "Indicates if the request was successful", "example": true}, "markets": {"type": "array", "description": "Array of market objects", "items": {"$ref": "#/definitions/Market"}}, "count": {"type": "integer", "description": "Number of markets returned", "example": 50}, "pagination": {"$ref": "#/definitions/MarketsPagination", "description": "Pagination information (only present when paginate=true)"}, "filters_applied": {"$ref": "#/definitions/MarketsFilters"}}}, "Market": {"type": "object", "properties": {"id": {"type": "string", "description": "Firestore document ID", "example": "abc123def456"}, "uuid": {"type": "string", "description": "Market UUID", "example": "6ed26762-fa7f-4457-9310-be486e1cb969"}, "name": {"type": "string", "description": "Market name", "example": "Worcester, MA-CT"}, "address": {"type": "string", "description": "Market central address", "example": "455 Main Street, Worcester, MA 01608"}, "location": {"$ref": "#/definitions/MarketLocation"}, "radius": {"type": "number", "description": "Market radius in kilometers", "example": 20}, "timezone": {"type": "string", "description": "Market timezone", "example": "America/New_York"}, "utc_offset_hours": {"type": "integer", "description": "UTC offset in hours (standard time)", "example": -5}, "utc_dst_offset_hours": {"type": "integer", "description": "UTC offset in hours (daylight saving time)", "example": -4}, "event_count": {"type": "integer", "description": "Number of events in this market", "example": 41}, "isActive": {"type": "boolean", "description": "Whether the market is active", "example": true}, "metadata": {"$ref": "#/definitions/MarketMetadata"}}}, "MarketLocation": {"type": "object", "properties": {"latitude": {"type": "number", "format": "double", "description": "Market center latitude", "example": 42.262592}, "longitude": {"type": "number", "format": "double", "description": "Market center longitude", "example": -71.802292}, "geopoint": {"type": "object", "description": "Geopoint data", "properties": {"lat": {"type": "number", "format": "double", "example": 42.262592}, "lon": {"type": "number", "format": "double", "example": -71.802292}}}}}, "MarketMetadata": {"type": "object", "properties": {"airtableId": {"type": "string", "description": "Airtable record ID", "example": "recA6omMMDE2iKDYl"}, "baseId": {"type": "string", "description": "Airtable base ID", "example": "appf64PoFO85E2VQe"}, "createdTime": {"type": "string", "format": "date-time", "description": "Creation timestamp", "example": "2023-11-01T20:12:06.000Z"}, "lastModified": {"type": "string", "format": "date-time", "description": "Last modification timestamp", "example": "2025-06-19T06:30:20.413Z"}, "lastSyncTime": {"type": "string", "format": "date-time", "description": "Last sync timestamp", "example": "2025-06-19T06:30:19.875Z"}}}, "MarketsPagination": {"type": "object", "required": ["page", "limit", "totalPages", "totalCount", "hasNext", "has<PERSON>rev"], "properties": {"page": {"type": "integer", "description": "Current page number", "example": 1}, "limit": {"type": "integer", "description": "Number of items per page", "example": 50}, "totalPages": {"type": "integer", "description": "Total number of pages", "example": 3}, "totalCount": {"type": "integer", "description": "Total number of markets", "example": 150}, "hasNext": {"type": "boolean", "description": "Indicates if there is a next page", "example": true}, "hasPrev": {"type": "boolean", "description": "Indicates if there is a previous page", "example": false}}}, "MarketsFilters": {"type": "object", "properties": {"active_only": {"type": "boolean", "description": "Whether only active markets were returned", "example": true}, "min_events": {"type": "integer", "description": "Minimum event count filter applied", "example": 10}, "search": {"type": "string", "description": "Search term used", "example": "Boston"}, "sort_by": {"type": "string", "description": "Field used for sorting", "example": "name"}, "order": {"type": "string", "description": "Sort order applied", "example": "asc"}}}, "MarketStatsResponse": {"type": "object", "required": ["success", "statistics", "generated_at"], "properties": {"success": {"type": "boolean", "description": "Indicates if the request was successful", "example": true}, "statistics": {"$ref": "#/definitions/MarketStatistics"}, "generated_at": {"type": "string", "format": "date-time", "description": "Timestamp when statistics were generated", "example": "2025-06-19T10:30:00.000Z"}}}, "MarketStatistics": {"type": "object", "properties": {"total_markets": {"type": "integer", "description": "Total number of markets in the system", "example": 150}, "active_markets": {"type": "integer", "description": "Number of active markets", "example": 145}, "inactive_markets": {"type": "integer", "description": "Number of inactive markets", "example": 5}, "total_events_across_all_markets": {"type": "integer", "description": "Sum of all events across all markets", "example": 15000}, "markets_with_events": {"type": "integer", "description": "Number of markets that have at least one event", "example": 140}, "markets_without_events": {"type": "integer", "description": "Number of markets with no events", "example": 10}, "average_events_per_market": {"type": "integer", "description": "Average number of events per market", "example": 100}, "markets_by_timezone": {"type": "object", "additionalProperties": {"type": "integer"}, "description": "Count of markets grouped by timezone", "example": {"America/New_York": 45, "America/Chicago": 30, "America/Los_Angeles": 25}}, "top_markets_by_event_count": {"type": "array", "description": "Top 10 markets by event count", "items": {"$ref": "#/definitions/TopMarket"}}}}, "TopMarket": {"type": "object", "properties": {"name": {"type": "string", "description": "Market name", "example": "New York, NY"}, "uuid": {"type": "string", "description": "Market UUID", "example": "123e4567-e89b-12d3-a456-************"}, "event_count": {"type": "integer", "description": "Number of events in this market", "example": 500}}}, "UpdateAccountPlansRequest": {"type": "object", "required": ["accountId"], "properties": {"accountId": {"type": "string", "description": "The ID of the account to update", "example": "12345"}, "addedPlan": {"type": "object", "description": "Plan to add to the account", "properties": {"plan_id": {"type": "string", "description": "The ID of the plan to add", "example": "plan_789"}, "active_at": {"type": "string", "format": "date-time", "description": "Timestamp when the plan becomes active", "example": "2024-01-15T10:30:00Z"}}}, "removedPlan": {"type": "string", "description": "The ID of the plan to remove from the account", "example": "plan_456"}}}, "GetAccountPlansResponse": {"type": "object", "properties": {"data": {"type": "array", "description": "Array of account plans", "items": {"$ref": "#/definitions/AccountPlan"}}, "count": {"type": "integer", "description": "Total number of plans found", "example": 3}, "accountId": {"type": "string", "description": "The account ID that was queried", "example": "12345"}, "actions": {"type": "array", "description": "List of actions performed", "items": {"type": "string"}, "example": ["getAccountPlans"]}, "message": {"type": "string", "description": "Optional message when no plans are found", "example": "No plans found for the account"}}}, "UpdateAccountPlansResponse": {"type": "object", "properties": {"accountId": {"type": "string", "description": "The account ID that was updated", "example": "12345"}, "actions": {"type": "array", "description": "List of actions performed", "items": {"type": "string"}, "example": ["setAccountPlan plan_789", "removeAccountPlan plan_456"]}}}, "AccountPlan": {"type": "object", "properties": {"id": {"type": "string", "description": "The plan document ID", "example": "plan_123"}, "plan_id": {"type": "string", "description": "The plan identifier", "example": "plan_123"}, "account_id": {"type": "string", "description": "The associated account ID", "example": "12345"}, "createdAt": {"type": "string", "format": "date-time", "description": "Creation timestamp", "example": "2024-01-10T08:00:00Z"}, "activeAt": {"type": "string", "format": "date-time", "description": "Active timestamp", "example": "2024-01-15T10:30:00Z"}, "isActive": {"type": "boolean", "description": "Whether the plan is currently active", "example": true}, "updatedAt": {"type": "string", "format": "date-time", "description": "Last update timestamp", "example": "2024-01-20T14:45:00Z"}, "by": {"type": "string", "description": "Source of the update", "example": "crm_update"}, "assignments": {"type": "array", "description": "Array of recipient group assignments", "items": {"$ref": "#/definitions/Assignment"}}}}, "Assignment": {"type": "object", "properties": {"id": {"type": "string", "description": "The assignment document ID", "example": "assign_abc123"}, "group": {"type": "string", "description": "The recipient group name", "example": "Marketing Team"}, "meta": {"type": "object", "description": "Additional metadata for the assignment", "additionalProperties": true}, "createdAt": {"type": "string", "format": "date-time", "description": "Assignment creation timestamp", "example": "2024-01-12T09:30:00Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Assignment last update timestamp", "example": "2024-01-18T11:20:00Z"}}}}}