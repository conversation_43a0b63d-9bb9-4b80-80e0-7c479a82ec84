{"titan": {"dev": {"securityDefinitions": {"api_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "key", "in": "query"}, "rmc_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"}, "print_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-print-service-account-dev%40rm-titan-dev.iam.gserviceaccount.com"}, "crm_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-crm-service-account-dev%40rm-titan-dev.iam.gserviceaccount.com"}, "mercury_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-mercury-service-account-dev%40rm-titan-dev.iam.gserviceaccount.com"}, "landing_pages_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-landing-pages-servie-accoun%40rm-titan-dev.iam.gserviceaccount.com"}}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}, "stage": {"securityDefinitions": {"api_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "key", "in": "query"}, "rmc_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"}, "print_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-print-service-account-dev%40rm-titan-dev.iam.gserviceaccount.com"}, "crm_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-crm-service-account-dev%40rm-titan-dev.iam.gserviceaccount.com"}, "mercury_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-mercury-service-account-dev%40rm-titan-dev.iam.gserviceaccount.com"}, "landing_pages_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-landing-pages-servie-accoun%40rm-titan-dev.iam.gserviceaccount.com"}}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}, "prod": {"securityDefinitions": {"api_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "key", "in": "query"}, "rmc_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"}, "print_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-print-service-account-prod%40rm-titan-prod.iam.gserviceaccount.com"}, "crm_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-crm-service-account-prod%40rm-titan-prod.iam.gserviceaccount.com"}, "mercury_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-mercury-service-account-pro%40rm-titan-prod.iam.gserviceaccount.com"}, "landing_pages_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-landing-pages-service-accou%40rm-titan-prod.iam.gserviceaccount.com"}}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "print_token": []}, {"api_key": [], "crm_token": []}, {"api_key": [], "mercury_token": []}, {"api_key": [], "landing_pages_token": []}]}}}