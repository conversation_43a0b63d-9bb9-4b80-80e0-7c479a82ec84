const axios = require('axios');
// run from command line: node sendWebhookWithInterval
async function sendWebhook(url, data) {
    try {
        const response = await axios.post(url, data);
        console.log("Webhook sent successfully!");
        console.log("Response:", response.data);
    } catch (error) {
        console.error("Failed to send webhook:", error.message);
    }
}


function sendWebhookWithInterval(url, data, intervalInMinutes) {
    sendWebhook(url, data); 
    setInterval(() => {
        sendWebhook(url, data);
    }, intervalInMinutes * 60 * 1000); 
}


const webhookUrl = 'https://us-central1-rm-campaigns.cloudfunctions.net/facebookLeadSubscription';
const sampleData = {
    event: "mock_event",
    data: {
        key1: "value1",
        key2: "value2"
    }
};
const timeIntervalInMinutes = 3;

sendWebhookWithInterval(webhookUrl, sampleData, timeIntervalInMinutes);