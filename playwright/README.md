# Requirements for playwright testing

**ATTENTION!! You CANNOT deploy the gateway without `./playwright/.env.dev`**

### Contact <PERSON> for ignored files required or help with anything below:

```
├── playwright
│   + ── configs
│   │   + ── .auth
│   │   │    + ── jwt-token.json
│   │   │    + ── titan-admin-user-indexeddb.json
│   │   │    + ── titan-admin-user.json
+ ── .env.dev
+ ── .env.local
+ ── .env.prod
```
### example .env used

```
RM_TITAN_API_KEY="FakeKeyabcdefg"
RM_TITAN_AUDIENCE_CLAIM="rm-mercury-dev-api-gateway-0z8cmf1x5f71q.apigateway.rm-mercury-dev.cloud.goog"
RM_TITAN_BASE_URI="https://rm-mercury-dev-api-gateway-4jk4mipy.uc.gateway.dev/"
RM_TITAN_DISABLE_SSL_KEY="false"
RM_TITAN_PRIVATE_KEY="-----<PERSON><PERSON>IN PRIVATE KEY-----\nFakePrivateKey\n-----END PRIVATE KEY-----\n"
RM_TITAN_SERVICE_ACCOUNT_EMAIL="<EMAIL>"
RM_TITAN_FRONT_END_URL="localhost:3000"
```
## Running Tests:

1. First time running playwright e2e tests, install the default browsers:

`npx playwright install`

2. To find and create test suites, see `playwright.config.ts`
    1. Example:
```
// Titan UI Test Automation
    {
      name: "titan-ui-smoke",
      testMatch: "**/titan/ui/smoke/*",
      use: {
        ...devices["Desktop Chrome"],
        storageState: "tests/configs/.auth/titan-admin-user.json",
        baseURL: baseUrl,
      },
      fullyParallel: false,
      dependencies: ["titan-setup"],
    },
```

3. Run the test project with setting the env: 
    1. Example: `NODE_ENV=local npx playwright test --project=titan-ui-smoke`