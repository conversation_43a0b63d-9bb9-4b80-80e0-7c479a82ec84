{
  "compilerOptions": {
    "esModuleInterop": true,
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@apis/*": ["apis/*"],
      "@configs/*": ["configs/*"],
      "@fixtures/*": ["fixtures/*"],
      "@models/*": ["models/*"],
      "@pages/*": ["pages/*"],
      "@playwright.config/*": ["playwright.config.ts"],
      "@root/*": ["../*"],
      "@test-data/*": ["test-data/*"],
      "@test-seeds/*": ["test-seeds/*"],
      "@tests/*": ["tests/*"],
      "@utils/*": ["utils/*"],
    }
  }
}
