import { expect, Locator, <PERSON>, BrowserContext } from "@playwright/test";
import { TestUser } from "@models/interfaces";

export class TitanAccountsListPage {
  readonly page: Page;
  readonly context: BrowserContext;
  readonly testAccountId: string;

  // Locator Properties
  // Page Properties
  readonly accountsListHeader: Locator;
  readonly accountsSearchBox: Locator;
  readonly accountsListPagination: Locator;
  readonly accountsListPaginationLink: Locator;

  // Account Line Items
  readonly accountLineItems: Locator;
  readonly accountItem: Locator;
  readonly accountName: Locator;
  readonly accountDisplayName: Locator;
  readonly accountGroup: Locator;
  readonly accountId: Locator;
  readonly accountCreatedAt: Locator;
  readonly accountStatus: Locator;
  readonly accountStatusIcon: Locator;
  readonly viewAccountDetailsLink: Locator;
  readonly clearAccountsSearchBoxButton: Locator;
  readonly registerNewPhoneNumberButton: Locator;

  // Register new phone modal
  readonly registerNewPhoneModal: Locator;
  readonly registerNewPhoneModalHeader: Locator;
  readonly registerNewPhoneModalFilterBySelect: Locator;
  readonly registerNewPhoneModalAreaCodeInput: Locator;
  readonly registerNewPhoneModalFindNumberButton: Locator;
  readonly registerNewPhoneModalCloseButton: Locator;

  constructor(page: Page, context: BrowserContext, testAccountId: string) {
    this.page = page;
    this.context = context;
    this.testAccountId = testAccountId;

    this.accountsListHeader = page.getByRole("heading", { name: "Accounts" });
    this.accountsSearchBox = page.getByPlaceholder("Search...");
    this.clearAccountsSearchBoxButton = page.getByRole("button", {
      name: "Clear the search query",
    });
    this.accountsListPagination = page.getByTestId("accounts-list-pagination");
    this.accountsListPaginationLink = page.locator(
      `css=[data-testid="accounts-list-pagination"] li a`
    );

    this.accountLineItems = page.locator('[data-testid^="account-item"]');
    this.accountItem = page.getByTestId(`account-item-${this.testAccountId}`);
    this.accountName = page.getByTestId(`account-name-${this.testAccountId}`);
    this.accountDisplayName = page.getByTestId(
      `account-display-name-${this.testAccountId}`
    );
    this.accountGroup = page.getByTestId(`account-group-${this.testAccountId}`);
    this.accountId = page.getByTestId(`account-id-${this.testAccountId}`);
    this.accountCreatedAt = page.getByTestId(
      `account-created-${this.testAccountId}`
    );
    this.accountStatus = page.getByTestId(
      `account-status-${this.testAccountId}`
    );
    this.accountStatusIcon = page.getByTestId(
      `account-status-icon-${this.testAccountId}`
    );
    this.registerNewPhoneNumberButton = page.getByTestId(
      `register-new-phone-number-button-${this.testAccountId}`
    );
    this.viewAccountDetailsLink = page.getByTestId(
      `view-account-details-link-${this.testAccountId}`
    );

    this.registerNewPhoneModal = page.getByRole("dialog", {
      name: "Register New Phone Number",
    });
    this.registerNewPhoneModalHeader = page.getByRole("heading", {
      name: "Register New Phone Number",
    });
    this.registerNewPhoneModalFilterBySelect = page.getByTestId(
      "register-new-phone-number-select"
    );
    this.registerNewPhoneModalAreaCodeInput = page.getByLabel("Area Code", {
      exact: true,
    });
    this.registerNewPhoneModalFindNumberButton = page.getByRole("button", {
      name: "Find a Number",
    });
    this.registerNewPhoneModalCloseButton =
      this.registerNewPhoneModal.getByRole("button", { name: "Close" });
  }

  async goto() {
    await this.page.goto("./accounts/list");
  }

  async waitForURL(
    expectedURL: string,
    options?: {
      timeout?: number;
      waitUntil?: "load" | "domcontentloaded" | "networkidle" | "commit";
    }
  ) {
    await this.page.waitForURL(expectedURL, options);
  }

  async expectURL(expectedURL: string) {
    await expect(this.page).toHaveURL(expectedURL);
  }

  async expectAccountItemsExists() {
    await this.accountLineItems.first().waitFor();
    await expect(this.accountLineItems).toHaveCount(20);
    await this.accountsListPaginationLink.first().waitFor({ state: "visible" });
    await expect(this.accountsListPaginationLink).toHaveCount(5);
  }

  async searchForAccount(userToSearch: TestUser, searchBy: "id" | "name") {
    let count: number;
    await this.accountsSearchBox.click();
    if (searchBy === "id") {
      await this.accountsSearchBox.fill(userToSearch.accountId);
      await this.page.waitForTimeout(1000);
      await expect(this.accountsSearchBox).toHaveValue(userToSearch.accountId);
      count = await this.accountLineItems.count();
      expect(count).toBe(1);
    } else {
      await this.accountsSearchBox.fill(userToSearch.name);
      await this.page.waitForTimeout(1000);
      await expect(this.accountsSearchBox).toHaveValue(userToSearch.name);
      count = await this.accountLineItems.count();
      expect(count).toBeGreaterThanOrEqual(1);
    }
  }

  async clearAccountsSearchBox() {
    await expect(this.clearAccountsSearchBoxButton).toBeVisible();
    await this.clearAccountsSearchBoxButton.click();
  }

  async expectSearchBoxCleared() {
    await expect(this.accountsSearchBox).toHaveValue("");
    await this.page.waitForTimeout(1000);
    await expect(this.accountLineItems).toHaveCount(20);
  }

  async verifySearchedAccountLineItem(testUser: TestUser) {
    expect(this.accountName).toContainText(testUser.name);
    if (testUser.displayName != null) {
      expect(this.accountDisplayName).toContainText(
        `Display Name: ${testUser.displayName}`
      );
    }
    if (testUser.group != null) {
      expect(this.accountGroup).toContainText(`Group: ${testUser.group}`);
    }
    expect(this.accountId).toContainText(`Account ID${testUser.accountId}`);
    expect(this.accountCreatedAt).toContainText(
      `Account Created${testUser.createdAt}`
    );
    if (testUser.status === "Inactive") {
      expect(this.accountItem).toContainText("Inactive");
      expect(this.accountStatusIcon).toHaveClass(/ion-close-circled/);
    } else {
      expect(this.accountItem).toContainText("Active");
      expect(this.accountStatusIcon).toHaveClass(/ion-checkmark-circled/);
    }
    await expect(this.accountsListPaginationLink).toHaveCount(1);
    await expect(this.registerNewPhoneNumberButton).toBeVisible();
    await expect(this.viewAccountDetailsLink).toHaveAttribute(
      "href",
      /\/account\/info/
    );
  }

  async openRegisterNewPhoneModalModal() {
    expect(this.registerNewPhoneNumberButton).toBeVisible();
    await this.registerNewPhoneNumberButton.click();
  }

  async expectRegisterNewPhoneModalOpened() {
    await expect(this.registerNewPhoneModalHeader).toBeVisible();
    await expect(this.registerNewPhoneModalFilterBySelect).toBeVisible();
    await expect(this.registerNewPhoneModalAreaCodeInput).toBeVisible();
    await expect(this.registerNewPhoneNumberButton).toBeVisible();
    await expect(this.registerNewPhoneModalHeader).toHaveText(
      /Register New Phone Number/
    );
    await expect(
      this.registerNewPhoneModalFilterBySelect.locator("option")
    ).toHaveCount(2);
    await expect(
      this.registerNewPhoneModalFilterBySelect.locator("option")
    ).toHaveText(["Area Code", "State"]);
    await expect(this.registerNewPhoneModalAreaCodeInput).toHaveValue("");
  }

  async closeRegisterNewPhoneModal() {
    await expect(this.registerNewPhoneModalCloseButton).toBeVisible();
    await this.registerNewPhoneModalCloseButton.click();
  }

  async verifyRegisterNewPhoneModalClosed() {
    await expect(this.registerNewPhoneModalHeader).not.toBeVisible();
    await expect(this.registerNewPhoneModalFilterBySelect).not.toBeVisible();
    await expect(this.registerNewPhoneModalAreaCodeInput).not.toBeVisible();
    await expect(this.registerNewPhoneNumberButton).not.toBeVisible();
  }
}
