import { expect, Locator, Page, BrowserContext } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';

export class TitanLoginPage {
  readonly page: Page;
  readonly context: BrowserContext;

  // Locator Properties
  // Login Form
  readonly loginFormLocator: Locator;
  readonly emailLabelLocator: Locator;
  readonly emailInputLocator: Locator;
  readonly passwordLabelLocator: Locator;
  readonly passwordInputLocator: Locator;
  readonly loginSubmitButtonLocator: Locator;
  // Other
  readonly loginRmMainLogo: Locator;
  readonly loginMainHeader: Locator;
  readonly loginSubHeader:Locator;
  readonly loginCtaLink: Locator;
  // Main Dashboard after login
  readonly mainDashboardHeader: Locator;

  constructor(page: Page, context: BrowserContext) {
    this.page = page;
    this.context = context;
    this.loginFormLocator = page.getByTestId('login-form');
    this.emailLabelLocator = page.getByTestId('login-form-email-label')
    this.emailInputLocator = page.getByTestId('login-form-email-input');
    this.passwordLabelLocator = page.getByTestId('login-form-password-label');
    this.passwordInputLocator = page.getByTestId('login-from-password-input');
    this.loginSubmitButtonLocator = page.getByTestId('login-form-submit-button');
    this.loginRmMainLogo = this.page.getByTestId('login-rm-main-logo')
    this.loginMainHeader = page.getByTestId('login-main-header');
    this.loginSubHeader = page.getByTestId('login-sub-header');
    this.loginCtaLink = page.getByTestId('login-cta-link');
    this.mainDashboardHeader = page.getByRole('heading', { name: 'Ask Me A Question?' });
  }

  async goto() {
    await this.page.goto(`./auth/signin`);
  }

  async waitForURL(expectedURL: string, options?: { timeout?: number, waitUntil?: "load" | "domcontentloaded" | "networkidle" | "commit" }) {
    await this.page.waitForURL(expectedURL, options);
  }

  async expectURL(expectedURL: string) {
    await expect(this.page).toHaveURL(expectedURL);
  }

  async takeScreenshot(path: string) {
    await this.page.screenshot({ path });
  }

  async expectToHaveScreenshot(name: string) {
    await expect(this.page).toHaveScreenshot(name);
  }

  async saveStorageState(authFilePath: string) {
    await this.page.context().storageState({ path: authFilePath });
  }

  async verifyMainDashboardHeader() {
    await this.mainDashboardHeader.isVisible();
  }

  async copyIndexedDBToLocalStorage() {
    await this.page.evaluate(() => {
      const indexedDB = window.indexedDB;
      const request = indexedDB.open('firebaseLocalStorageDb');

      request.onsuccess = function (event: any) {
        const db = event.target.result;
        const transaction = db.transaction(['firebaseLocalStorage'], 'readonly');
        const objectStore = transaction.objectStore('firebaseLocalStorage');
        const getAllKeysRequest = objectStore.getAllKeys();
        const getAllValuesRequest = objectStore.getAll();

        getAllKeysRequest.onsuccess = function (event: any) {
          const keys = event.target.result;

          getAllValuesRequest.onsuccess = function (event: any) {
            const values = event.target.result;

            for (let i = 0; i < keys.length; i++) {
              const key = keys[i];
              const value = values[i];
              localStorage.setItem(key, JSON.stringify(value));
            }
          }
        }
      }

      request.onerror = function (event: any) {
        console.error('Error opening IndexedDB database:', event.target.error);
      }
    });
  }
}
