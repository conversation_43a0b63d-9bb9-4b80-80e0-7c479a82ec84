const QaseReporter = require('playwright-qase-reporter').default;
const fs = require('fs');
const path = require('path');

class CustomQaseReporter extends QaseReporter {
  async onEnd(result) {
    const runId = process.env.QASE_TESTOPS_RUN_ID;
    if (runId) {
      const filePath = path.join(__dirname, '../../qase_run_id.txt');
      fs.writeFileSync(filePath, runId, 'utf8');
      console.log(`✅ Qase Run ID written to qase_run_id.txt: ${runId}`);
    }

    // Call the base reporter's onEnd if it exists
    if (super.onEnd) {
      await super.onEnd(result);
    }
  }
}

module.exports = { default: CustomQaseReporter };
