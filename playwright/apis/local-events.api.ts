import { APIRequestContext, APIResponse, Fixtures } from '@playwright/test';
import config from '@configs/env/config';
import {
  GetAllMarketsResponse,
  GetMarketStatsResponse,
  GetEventsByAreaResponse,
  GetLiveEventsResponse,
  SearchAreasResponse,
  ErrorResponse
} from '@test-data/local-events/contract-schema';
const apiKey = config.RM_TITAN_API_KEY;
export class LocalEventsAPI {
  private request: APIRequestContext;
  private baseURL: string;

  constructor(
    request: APIRequestContext,
    baseURL: string = config.RM_TITAN_BASE_URI,
  ) {
    this.request = request;
    this.baseURL = baseURL;
  }

  /**
   * Get all markets with optional filtering and pagination
   */
  async getAllMarkets(
    authToken: string,
    params: {
      active_only?: boolean;
      paginate?: boolean;
      page?: number;
      limit?: number;
      min_events?: number;
      sort_by?: "name" | "event_count" | "created";
      order?: "asc" | "desc";
      search?: string;
    } = {}
  ): Promise<APIResponse> {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    searchParams.append('key', apiKey);

    const url = `${this.baseURL}/getAllMarkets${searchParams.toString() ? "?" + searchParams.toString() : ""}`;

    return this.request.get(url, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });
  }

  /**
   * Get market statistics
   */
  async getMarketStats(authToken: string): Promise<APIResponse> {
    return this.request.get(`${this.baseURL}/getMarketStats?key=${apiKey}`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });
  }

  /**
   * Get events for a specific area
   */
  async getEventsByArea(
    authToken: string,
    params: {
      area_uuid: string;
      limit?: number;
      active_only?: boolean;
      start_date?: string;
      end_date?: string;
      popularity_min?: number;
      page_token?: string;
    }): Promise<APIResponse> {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    searchParams.append('key', apiKey); 

    const url = `${this.baseURL}/getEventsByArea?${searchParams.toString()}`;

    return this.request.get(url, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });
  }

  /**
   * Get all live events with optional pagination
   */
  async getLiveEvents(
    authToken: string,
    params: {
      paginate?: boolean;
      limit?: number;
      page?: number;
    } = {}
  ): Promise<APIResponse> {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    searchParams.append('key', apiKey);

    const url = `${this.baseURL}/getLiveEvents${searchParams.toString() ? "?" + searchParams.toString() : ""}`;

    return this.request.get(url, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });
  }

  /**
   * Search areas by name or location
   */
  async searchAreas(
    authToken: string,
    params: {
      name?: string;
      lat?: number;
      lng?: number;
      radius_km?: number;
      limit?: number;
    }): Promise<APIResponse> {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    searchParams.append('key', apiKey);

    const url = `${this.baseURL}/searchAreas?${searchParams.toString()}`;

    return this.request.get(url, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
      },
    });
  }

  /**
   * Helper method to validate response structure
   */
  static validateResponseStructure(
    response: any,
    expectedFields: string[], 
    index?: number
  ): void {
    expectedFields.forEach((field) => {
      if (!(field in response)) {
        throw new Error(`Missing required field: ${field}${index !== undefined ? ` in event at index ${index}` : ''}`);
      }
    });
  }

  /**
   * Helper method to validate market object structure
   */
  static validateMarketStructure(market: any): void {
    const requiredFields = [
      "id",
      "uuid",
      "name",
      "location",
      "timezone",
      "event_count",
      "isActive",
    ];
    this.validateResponseStructure(market, requiredFields);

    // Validate nested location object
    if (market.location) {
      const locationFields = ["latitude", "longitude"];
      this.validateResponseStructure(market.location, locationFields);
    }
  }

  /**
   * Helper method to validate event object structure
   */
  static validateEventsByAreaEventStructure(event: any, index?: number): void {
    const requiredFields = [
      "id",
      "uuid",
      "name",
      "description",
      "start_date",
      "event_dates",
      "event_type",
      "urls",
      "popularity_score",
      "_isActive",
      "area_uuid",
      "venue",
      "cancelled",
    ];
    this.validateResponseStructure(event, requiredFields, index);
  }

  /**
   * Helper method to validate event object structure
   */
  static validateLiveEventsEventStructure(event: any, index?: number): void {
    const requiredFields = [
      "id",
      "uuid",
      "name",
      "description",
      "start_date",
      "event_dates",
      "image_url",
      "area_uuid",
      "venue_name",
      "venue_city",
      "venue_latitude",
      "venue_longitude",
      "source_url",
      "ticket_url",
      "cancelled",
    ];
    this.validateResponseStructure(event, requiredFields, index);
  }

  /**
   * Helper method to validate pagination structure
   */
  static validatePaginationStructure(pagination: any): void {
    const requiredFields = ["limit"];
    this.validateResponseStructure(pagination, requiredFields);
  }
}
