import { test } from '@fixtures/base.api';
import { APIResponse, expect } from "@playwright/test";
import { faker } from '@faker-js/faker';
import config from "@configs/env/config";

const apiKey = config.RM_TITAN_API_KEY;

test.describe("titan gateway smoke test suite", () => {

  /**
   * RMC Gateway Client
   * ----------------------------------------------
   */
  test("should make a POST request to /rmcAccountSync using RMC token", async ({ 
    request, auth 
  }) => {
    const response = await request.post(`rmcAccountSync`, {
      data: {
        accountId: "****************",
        accountCreatedAt: "2024-08-15T09:00:00+00:00",
        accountUpdatedAt: "2024-08-15T09:30:00+00:00",
        accountPlans: [],
        displayName: "Smoke Test Account",
        name: "<PERSON><PERSON> The Bear",
        slug: "smokey-the-bear",
        timezone: { timezone: "America/New_York" },
        marketUuid: "**********",
        groupId: "****************9999",
        groupName: "Smoke Test Group",
      },
      params: {
        key: apiKey,
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());

    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);
  });

  test("should make post request to /shortLivedTokenForCustomers using RMC token", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForCustomers`, {
      data: {
        accountId: "102",
        email: "<EMAIL>"
      },
      params: {
        key: apiKey,
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);
  });

  /**
   * Print Gateway Client
   * ---------------------------------------------------
   */
  test("should make a GET request to /enterpriseList using Print token", async ({
    request, auth 
  }) => {
    const response = await request.get(`enterpriseList`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.print.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());

    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);
  });

  test("should make a GET request to /checkEnterpriseAssociation using Print token", async ({
    request, auth
  }) => {
    const response = await request.get(`checkEnterpriseAssociation`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.print.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
        accountId: "996808",
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());

    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);
  });

  test("should make a GET request to /assignToEnterprise using Print token", async ({
    request, auth
  }) => {
    const response = await request.get(`assignToEnterprise`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.print.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
        accountId: "996808",
        enterprise: "UoeKaBIRne0XXBWwPCTO",
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());

    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);
  });

  /**
   *  CRM Gateway Client
   * ---------------------------------------------------
   * */

  test("should make a GET request to /enterpriseList using CRM Token", async ({
    request, auth
  }) => {
    const response = await request.get(`enterpriseList`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.crm.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());

    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);
  });

  test("should make a GET request to /checkEnterpriseAssociation using CRM Token", async ({
    request, auth
  }) => {
    test.skip(process.env.NODE_ENV === 'prod', 'Skipping test in production.');
    const response = await request.get(`checkEnterpriseAssociation`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.crm.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
        accountId: "996808",
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());

    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);;
  });

  test("should make a GET request to /assignToEnterprise using CRM Token", async ({
    request, auth
  }) => {
    const response = await request.get(`assignToEnterprise`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.crm.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
        accountId: "996808",
        enterprise: "UoeKaBIRne0XXBWwPCTO",
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);
  });

  /**
   *  Landing Pages Gateway Client
   * ---------------------------------------------------
   * */
  
  test("should make a POST request to /shortenUrl using LP Token", async ({
    request, auth
  }) => {
    test.skip(process.env.NODE_ENV === 'prod', 'Skipping in production.');
    const response = await request.post(`shortenUrl`, {
      data: {
        lpId: "2fe6a76f-e108-46ec-a41f-0d018d4ae161", // Note: if failed, ensure lp exists => using for the sake of time.
        productId: "9",
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.landingPages.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);

    await check500(response);
    
  });

  test("should make a GET request to /domainUpdate using LP Token", async ({
    request, auth
  }) => {
    const response = await request.get(`domainUpdate`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.landingPages.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
        accountId: "4378522",
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);

    await check500(response);
  });

  test("should make a POST request to /lpCodes using LP Token", async ({
    request, auth
  }) => {
    test.skip(process.env.NODE_ENV === 'prod', 'Skipping test in production.');
    const response = await request.get(`lpCodes`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.landingPages.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
        lpId: "2fe6a76f-e108-46ec-a41f-0d018d4ae161", // Note: if failed, ensure lp exists => using for the sake of time.
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);

    await check500(response);
  });

  test("should make a GET request to /lpAnalytics using LP Token", async ({
    request, auth
  }) => {
    test.skip(process.env.NODE_ENV === 'prod', 'Skipping test in production.');
    const response = await request.get(`lpAnalytics`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.landingPages.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
        lpId: "2fe6a76f-e108-46ec-a41f-0d018d4ae161", // Note: if failed, ensure lp exists => using for the sake of time.
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(404);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);

    await check500(response);
  });
  
  test("should make a GET request to /linkData using LP Token", async ({
    request, auth
  }) => {
    test.skip(process.env.NODE_ENV === 'prod', 'Skipping test in production.');
    const response = await request.get(`linkData}`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.landingPages.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
        shortCode: '62RSrKYRJ',
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);

    await check500(response);
  });

  test("should make a GET request to public path /trackClick using LP Token", async ({
    request
  }) => {
      test.skip(process.env.NODE_ENV === 'prod', 'Skipping test in production.');
      const publicUrl = 'https://us-central1-rm-titan-dev.cloudfunctions.net';
      const shortCode = '62RSrKYRJ';

      const response = await request.get(`${publicUrl}/trackClick/${shortCode}`, {
    });
    
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.statusText()).toBeTruthy();
    expect(response.status()).toBeTruthy();
    expect(response.status()).not.toBe(401);
    expect(response.status()).not.toBe(405);
    expect(response.status()).not.toBeGreaterThanOrEqual(500);
    
  });
});

async function check500(response: APIResponse) {
  if(response.status() === 500) {
    return false;
  } else {
    const resJson = await response.json();
    console.dir(resJson, {depth: null});
  }
}