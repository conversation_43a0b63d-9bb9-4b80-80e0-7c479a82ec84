import { test } from "@fixtures/base.api";
import { expect } from "@playwright/test";
import config from "@configs/env/config";
// import Ajv from "ajv";
import { seedRecipientGroup } from "@utils/recipients/seedRecipientGroup";
import { seedRecipientAndMailingAddress } from "@utils/recipients/seedRecipientAndMailingAddress";
import { deleteRecipient } from "@utils/recipients/deleteRecipientRecursively";
import { deleteRecipientGroup } from "@utils/recipients/deleteRecipientGroupRecursively";
import {
  RecipientName,
  TestAddress,
  TestRecipient,
  TestRecipientAndAddress,
} from "@models/interfaces";
import { RecipientFilePaths } from "@models/enums";
import { Timestamp } from "firebase/firestore";
import recipientsByCampaignScenarios from "@test-data/recipient-apis/recipients-by-campaign/recipients-by-campaign-scenarios.json";
import { recipientsByCampaignResponseContract } from "@test-data/recipient-apis/recipients-by-campaign/contract-schema";
import { validateRecipientsByCampaignResponse } from "@utils/recipients/validateRecipientsByCampaignResponse";
import { faker } from "@faker-js/faker";
import { wait } from "@utils/helpers/wait";
import { startFirebaseDb } from "@utils/helpers/startFirebaseDb";
import { waitForCloudFunctionProcessing } from "@utils/helpers/waitForCloudFunctionProcessing";
import { doc } from "firebase/firestore";

const db = startFirebaseDb();
const key = config.RM_TITAN_API_KEY;
const skipCleanup = process.env.SKIP_CLEANUP === "true";

// The schema is used to validate the response structure and types
// matches the response from Titan that Printing expects
const responseSchema = recipientsByCampaignResponseContract;

const accountId = "4378522";
let recipientGroupId: string;
let campaignId = "133448";
let recipients: TestRecipientAndAddress[] = [];

// All tests depend on the same recipient group state
// I'm setting mode to serial to avoid overlapping requests
// If not serial, each test worker would seed its own recipient group
test.describe.configure({ mode: "serial" });

test.describe("Recipients By Campaign", () => {
  let apiResponse: any;

  // Will only run once when in serial mode
  test.beforeAll(async ({ request, auth }) => {
    // I'm seeding the recipient group first and returning the the groupId and campaignId
    console.log(`🌱 Seeding Recipient Group`);
    const groupResult = await seedRecipientGroup({ 
      accountId,
      productGroup: "postcards",
      campaignId
    });
    recipientGroupId = groupResult.recipientGroupId;
    campaignId = groupResult.campaignId;

    // I'm generating the recipient data from the business rules
    // defined in the recipientsByCampaignScenarios.json file
    // This file contains the test scenarios for the recipients
    console.log(`🔄 Generating Recipient Data`);
    recipients = await generateRecipientData(accountId);

    // I'm waiting for group Cloud Functions to process the group
    // before seeding the recipient and address data
    await wait(
      2000,
      "Waiting for recipient group Cloud Functions to process..."
    );

    // I'm seeding the recipient group with recipients and address data
    await seedRecipientAndMailingAddress(recipients, RecipientFilePaths.RECIPIENTS_BY_CAMPAIGN_PATH);

    // I'm waiting for the recipient Cloud Functions to process all the recipients and address data
    for (const recipient of recipients) {
      const recipientId = recipient.testRecipient.recipientId;
      const storePath = `Account/${accountId}/Recipients/${recipientId}/UserEnteredMailingAddress/0`;
      const docRef = doc(db, storePath);
      await waitForCloudFunctionProcessing(
        docRef, 
        "processingStatus", 
        "DONE", 
        15, 
        1000
      );
    }

    console.log(`📞 Calling Recipients By Campaign Endpoint`);
    const response = await request.get(`recipientsByCampaign`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.print.signedToken}`,
        "Content-Type": "application/json",
      },
      params: {
        accountId,
        method: "address",
        productId: 8,
        campaignId,
        key,
      },
    });

    expect(response.status()).toBe(200);
    apiResponse = await response.json();
  });

  test.afterAll(async () => {
    if (skipCleanup) {
      console.log('⏭️  Skipping test cleanup (SKIP_CLEANUP=true)');
      return;
    }
    // Test Cleanup: Delete the recipient group and all recipients
    // Delete the recipients
    console.log("🧹 Cleaning up test data: Deleting recipients and recipient group");
    for (const recipient of recipients) {
      await deleteRecipient(accountId, recipient.testRecipient.recipientId);
    }
    // Delete the recipient group
    await deleteRecipientGroup(accountId, recipientGroupId);
    console.log("✅ Test data cleanup completed");
  });


  test("should verify response body structure", async () => {
    // Validate the response body structure against the schema contract
    console.log(
      "📜 Validating response body structure against the schema contract for print preprocessing..."
    );
    validateRecipientsByCampaignResponse(apiResponse);
  });

  test("should handle missing names and salutations appropriately", async () => {
    const responseRecipients = apiResponse.recipientGroup;

    for (const responseRecipient of responseRecipients) {
      test.step(`Should replace empty name and salutations fields with "Current Resident"`, () => {
        // If name fields are empty, salutations must be "Current Resident"
        if (!responseRecipient.first_name && !responseRecipient.last_name) {
          expect(responseRecipient.first_name).toBe("Current Resident");
          expect(responseRecipient.mailing_salutation).toBe("Current Resident");
          expect(responseRecipient.letter_salutation).toBe("Current Resident");
        }
      });

      test.step(`Should ensure name fields present when salutations are empty`, () => {
        // If salutations are empty, name fields must not be empty
        if (!responseRecipient.mailing_salutation || !responseRecipient.letter_salutation) {
          expect(responseRecipient.first_name || responseRecipient.last_name).toBeTruthy();
        }
      });

      test.step(`Should ensure at least one name or "Current Resident" salutation is present`, () => {
        // Ensure we never have a case where both names and salutations are empty
        expect(
          responseRecipient.first_name ||
          responseRecipient.last_name ||
          responseRecipient.mailing_salutation === "Current Resident"
        ).toBeTruthy();
      });
    }

    console.log(
      "✅ All recipients have either valid names or Current Resident salutations for missing names"
    );
  });
});

async function generateRecipientData(
  accountId: string
): Promise<TestRecipientAndAddress[]> {
  let recipientAndAddresses: TestRecipientAndAddress[] = [];

  for (const scenario of recipientsByCampaignScenarios) {
    const scenarioName = scenario.scenarioName;
    const firstName = scenario.recipient.firstName;
    const lastName = scenario.recipient.lastName;
    const addressScenario = scenario.recipient.address;
    const recipientId = faker.string.uuid();
    const name: RecipientName = {
      firstName,
      lastName,
    };
    const testRecipient: TestRecipient = {
      recipientId: recipientId,
      accountId: accountId,
      isActive: true,
      mailingsPaused: false,
      createdAt: Timestamp.now(),
      recipientGroupIds: [recipientGroupId],
      name,
      salutation: {
        letterSalutation: "",
        mailingSalutation: "",
      },
    };

    // Address Data
    const testAddress: TestAddress = {
      address1: addressScenario.address1,
      address2: addressScenario.address2 || "",
      city: addressScenario.city,
      state: addressScenario.state,
      postalCode: addressScenario.postalCode,
      type: "home",
      createdAt: Timestamp.now(),
    };

    recipientAndAddresses.push({ scenarioName, testRecipient, testAddress });
  }

  return recipientAndAddresses;
}
