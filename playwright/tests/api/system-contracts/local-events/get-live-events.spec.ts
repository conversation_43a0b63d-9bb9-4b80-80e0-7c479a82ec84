import { test, expect } from '@fixtures/base.api';
import { LocalEventsAPI } from '@apis/local-events.api';
import { GetLiveEventsResponse } from '@test-data/local-events/contract-schema';

test.describe('getLiveEvents API endpoint', () => {
  const defaultParameters = {
    limit: 50,
    paginate: true,
    page: 1
  };
  
  test.beforeEach(async () => {
    test.skip(process.env.NODE_ENV === 'prod', 'Skipping tests in production');
  });

  test('should return live events with default parameters', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getLiveEvents(authToken, defaultParameters);
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetLiveEventsResponse = await response.json();
    
    // Validate response structure
    expect(responseBody.success).toBe(true);
    expect(Array.isArray(responseBody.events)).toBe(true);
    expect(typeof responseBody.pagination).toBe('object');
    expect(responseBody.pagination.page).toBe(1);
    expect(responseBody.pagination.limit).toBe(50);
    expect(responseBody.pagination.totalCount).toBeGreaterThan(0);
    expect(responseBody.pagination.totalPages).toBeGreaterThan(0);
    expect(responseBody.pagination.hasNext).toBe(true);
    expect(responseBody.pagination.hasPrev).toBe(false); // First page should not have previous

    // Validate first event structure
    if (responseBody.events.length > 0) {
      const firstEvent = responseBody.events[0];
      LocalEventsAPI.validateLiveEventsEventStructure(firstEvent);
      
      expect(firstEvent.id).toBeTruthy();
      expect(firstEvent.uuid).toBeTruthy();
      expect(firstEvent.name).toBeTruthy();
      expect(firstEvent.start_date).toBeTruthy();
      expect(firstEvent.end_date).toBeTruthy();
      expect(firstEvent.area_uuid).toBeTruthy();
      expect(firstEvent.cancelled).toBe(null); // Should not be cancelled
    }
  });

  test('should support pagination', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const limit = localEventsTestData.pagination.small_limit;
    
    const response = await localEventsApi.getLiveEvents(
      authToken, {
        paginate: true,
        limit,
        page: 1
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetLiveEventsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.events.length).toBeLessThanOrEqual(limit);
    expect(responseBody.pagination).toBeDefined();
    
    if (responseBody.pagination) {
      expect(responseBody.pagination.page).toBe(1);
      expect(responseBody.pagination.limit).toBe(limit);
      expect(typeof responseBody.pagination.totalCount).toBe('number');
      expect(typeof responseBody.pagination.totalPages).toBe('number');
      expect(typeof responseBody.pagination.hasNext).toBe('boolean');
      expect(typeof responseBody.pagination.hasPrev).toBe('boolean');
      
      expect(responseBody.pagination.hasPrev).toBe(false); // First page should not have previous
    }
  });

  test('should retrieve last page', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    // First get total count to determine valid page number
    const firstPageResponse = await localEventsApi.getLiveEvents(
      authToken,
      {
        paginate: true,
        limit: localEventsTestData.pagination.medium_limit,
        page: 1
      }
    );
    
    expect(firstPageResponse.status()).toBe(200);
    
    const firstPageBody: GetLiveEventsResponse = await firstPageResponse.json();
    
    if (firstPageBody.pagination && firstPageBody.pagination.totalPages > 1) {
      const lastPage = firstPageBody.pagination.totalPages;
      
      const lastPageResponse = await localEventsApi.getLiveEvents(
        authToken, {
          paginate: true,
          limit: localEventsTestData.pagination.medium_limit,
          page: lastPage
        }
      );
      
      expect(lastPageResponse.status()).toBe(200);
      
      const lastPageBody: GetLiveEventsResponse = await lastPageResponse.json();
      
      expect(lastPageBody.success).toBe(true);
      expect(lastPageBody.pagination?.hasNext).toBe(false); // Last page should not have next
      expect(lastPageBody.pagination?.hasPrev).toBe(true); // Last page should have previous
    }
  });

  test.skip('should handle invalid page numbers gracefully', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getLiveEvents(
      authToken, {
        paginate: true,
        limit: 10,
        page: 99999 // Very large page number
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetLiveEventsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    // Should return empty results or handle gracefully
    expect(Array.isArray(responseBody.events)).toBe(true);
  });

  test('should handle large limit values', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getLiveEvents(
      authToken, {
        paginate: true,
        limit: localEventsTestData.pagination.max_limit, // Maximum limit defined in test data (500)
        page: 1
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetLiveEventsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.events.length).toBeLessThanOrEqual(1000); // Server-side limit
  });

  test.skip('should sort events by start date consistently', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getLiveEvents(
      authToken, {
        paginate: true,
        limit: 20,
        page: 1
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetLiveEventsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    
    // Events should be sorted by start date (descending - most recent first)
    for (let i = 1; i < responseBody.events.length; i++) {
      const currentEventDate = new Date(responseBody.events[i].start_date._seconds);
      const previousEventDate = new Date(responseBody.events[i - 1].start_date._seconds);

      expect(currentEventDate.getTime()).toBeLessThanOrEqual(previousEventDate.getTime());
    }
  });

  test('should filter blocked tags from search terms', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getLiveEvents(
      authToken, 
      {
        paginate: true,
        limit: localEventsTestData.pagination.max_limit,
        page: 1
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetLiveEventsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    
    // Validate that blocked tags are properly filtered
    responseBody.events.forEach(event => {
      if (event._searchTerms && Array.isArray(event._searchTerms)) {
        // This is structural validation - we can't easily test specific blocked tags
        expect(Array.isArray(event._searchTerms)).toBe(true);
        
        // Search terms should be strings
        event._searchTerms.forEach(term => {
          expect(typeof term).toBe('string');
        });
      }
    });
  });

  test('should validate event data completeness', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getLiveEvents(
      authToken, 
      defaultParameters
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetLiveEventsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    
    responseBody.events.forEach((event, index) => {
      // All events should be active
      // expect(event._isActive).toBe(true);
      
      // Required fields
      expect.soft(event.id, `Event ${event.id} at index ${index} is missing id`).toBeTruthy();
      expect.soft(event.uuid, `Event ${event.id} at index ${index} is missing uuid`).toBeTruthy();
      expect.soft(event.name, `Event ${event.id} at index ${index} is missing name`).toBeTruthy();
      expect.soft(event.start_date, `Event ${event.id} at index ${index} is missing start_date`).toBeTruthy();
      expect.soft(event.area_uuid, `Event ${event.id} at index ${index} is missing area_uuid`).toBeTruthy();

      // Date should be valid
      expect.soft(event.start_date._seconds, `Event ${event.id} at index ${index} is missing start_date`).toBeDefined();
      const startDate = new Date(event.start_date._seconds);
      expect.soft(startDate).toBeInstanceOf(Date);
      expect.soft(!isNaN(startDate.getTime()), `Event ${event.id} at index ${index} has an invalid start_date of ${startDate}`).toBe(true);

      // Venue structure
      expect.soft(event.venue_name).toBeDefined();
      expect.soft(typeof event.venue_name, `Event ${event.id} at index ${index} has an invalid venue_name`).toBe('string');

      // Flags should be array if present
      if (event.flags !== undefined) {
        expect(Array.isArray(event.flags)).toBe(true);
      }
      
      // Popularity score should be number if present
      if (event.popularity_score !== undefined) {
        expect(typeof event.popularity_score).toBe('number');
        expect(event.popularity_score).toBeGreaterThanOrEqual(0);
      }
    });
  });

  test('should handle method not allowed', async ({ request, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await request.post('getLiveEvents', 
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: { limit: 10 }
      }
    );
    
    expect(response.status()).toBe(405);
    
    const responseBody = await response.json();
    expect(responseBody.code).toBe(405);
    expect(responseBody.message).toContain('http method is not allowed');
  });

  test('should validate cache headers', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getLiveEvents(
      authToken,
      defaultParameters
    );

    expect(response.status()).toBe(200);
    
    const cacheControl = response.headers()['cache-control'];
    expect(cacheControl).toBeDefined();
    expect(cacheControl).toContain('public');
    expect(cacheControl).toContain('max-age=300'); // 5 minutes
  });

  test('should handle concurrent requests efficiently', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const startTime = Date.now();
    
    const requests = Array(5).fill(null).map(() => 
      localEventsApi.getLiveEvents(authToken, defaultParameters)
    );

    const responses = await Promise.all(requests);
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    responses.forEach(response => {
      expect(response.status()).toBe(200);
    });
    
    const bodies = await Promise.all(responses.map(r => r.json()));
    
    // All responses should be successful
    bodies.forEach(body => {
      expect(body.success).toBe(true);
      expect(body.events.length).toBeGreaterThan(0);
    });
    
    // Should complete reasonably quickly due to caching
    expect(totalTime).toBeLessThan(10000); // Less than 10 seconds
  });

  test('should validate pagination consistency', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const limit = localEventsTestData.pagination.small_limit;
    
    // Get first two pages
    const page1Response = await localEventsApi.getLiveEvents(
      authToken, {
        paginate: true,
        limit,
        page: 1
      }
    );
    
    const page2Response = await localEventsApi.getLiveEvents(
      authToken, {
        paginate: true,
        limit,
        page: 2
      }
    );
    
    expect(page1Response.status()).toBe(200);
    expect(page2Response.status()).toBe(200);
    
    const page1Body: GetLiveEventsResponse = await page1Response.json();
    const page2Body: GetLiveEventsResponse = await page2Response.json();
    
    expect(page1Body.success).toBe(true);
    expect(page2Body.success).toBe(true);
    
    if (page1Body.events.length > 0 && page2Body.events.length > 0) {
      // No overlapping events between pages
      const page1Ids = new Set(page1Body.events.map(e => e.id));
      const page2Ids = new Set(page2Body.events.map(e => e.id));
      
      const overlap = Array.from(page1Ids).filter(id => page2Ids.has(id));
      expect(overlap.length).toBe(0);
      
      // Pagination info should be consistent
      if (page1Body.pagination && page2Body.pagination) {
        expect(page1Body.pagination.totalCount).toBe(page2Body.pagination.totalCount);
        expect(page1Body.pagination.totalPages).toBe(page2Body.pagination.totalPages);
        expect(page1Body.pagination.limit).toBe(page2Body.pagination.limit);
      }
    }
  });

  test('should handle zero limit gracefully', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getLiveEvents(
      authToken, {
        paginate: true,
        limit: 0
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetLiveEventsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    // Should either return empty results or apply a minimum limit
    expect(Array.isArray(responseBody.events)).toBe(true);
  });

  test('should validate memory usage with large datasets', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    // This test ensures the endpoint can handle large responses
    const response = await localEventsApi.getLiveEvents(
      authToken, { 
        paginate: true,
        page: 1,
        limit: 1000 
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetLiveEventsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.events.length).toBeLessThanOrEqual(1000);
    
    // Validate that all events have the required structure even with large datasets
    responseBody.events.forEach((event, index) => {
      // Required fields
      expect(event.id, `Event at index ${index} is missing id`).toBeTruthy();
      expect(event.uuid, `Event at index ${index} is missing uuid`).toBeTruthy();
      expect(event.name, `Event at index ${index} is missing name`).toBeTruthy();
      expect(event.start_date, `Event at index ${index} is missing start_date`).toBeTruthy();

      // Spot check every 10th event to avoid performance issues in testing
      if (index % 10 === 0) {
        LocalEventsAPI.validateLiveEventsEventStructure(event, index);
      }
    });
  });
});
