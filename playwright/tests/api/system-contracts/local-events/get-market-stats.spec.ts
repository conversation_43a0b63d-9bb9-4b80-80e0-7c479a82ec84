import { test, expect } from '@fixtures/base.api';
import { GetMarketStatsResponse } from '@test-data/local-events/contract-schema';

test.describe('getMarketStats API endpoint', () => {
  
  test.beforeEach(async () => {
    test.skip(process.env.NODE_ENV === 'prod', 'Skipping tests in production');
  });

  test('should return comprehensive market statistics', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getMarketStats(authToken);
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetMarketStatsResponse = await response.json();
    
    // Validate response structure
    expect(responseBody.success).toBe(true);
    expect(responseBody.statistics).toBeDefined();
    expect(responseBody.generated_at).toBeDefined();
    
    const stats = responseBody.statistics;
    
    // Validate all required statistics fields
    expect(typeof stats.total_markets).toBe('number');
    expect(typeof stats.active_markets).toBe('number');
    expect(typeof stats.inactive_markets).toBe('number');
    expect(typeof stats.total_events_across_all_markets).toBe('number');
    expect(typeof stats.markets_with_events).toBe('number');
    expect(typeof stats.markets_without_events).toBe('number');
    expect(typeof stats.average_events_per_market).toBe('number');
    
    // Validate data consistency
    expect(stats.total_markets).toBe(stats.active_markets + stats.inactive_markets);
    expect(stats.total_markets).toBe(stats.markets_with_events + stats.markets_without_events);
    expect(stats.total_markets).toBeGreaterThan(0);
    expect(stats.active_markets).toBeGreaterThan(0);
    expect(stats.total_events_across_all_markets).toBeGreaterThan(0);
    
    // Validate markets by timezone
    expect(typeof stats.markets_by_timezone).toBe('object');
    expect(Object.keys(stats.markets_by_timezone).length).toBeGreaterThan(0);
    
    // All timezone counts should sum to total markets
    const timezoneTotal = Object.values(stats.markets_by_timezone).reduce((sum, count) => sum + count, 0);
    expect(timezoneTotal).toBe(stats.total_markets - 1); // Exclude _lastSync market
    
    // Validate top markets
    expect(Array.isArray(stats.top_markets_by_event_count)).toBe(true);
    expect(stats.top_markets_by_event_count.length).toBeLessThanOrEqual(10);
    
    stats.top_markets_by_event_count.forEach((market, index) => {
      expect(market.name).toBeTruthy();
      expect(market.uuid).toBeTruthy();
      expect(typeof market.event_count).toBe('number');
      expect(market.event_count).toBeGreaterThan(0);
      
      // Markets should be sorted by event count (descending)
      if (index > 0) {
        expect(market.event_count).toBeLessThanOrEqual(stats.top_markets_by_event_count[index - 1].event_count);
      }
    });
  });

  test('should validate timezone data', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getMarketStats(authToken);
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetMarketStatsResponse = await response.json();
    const timezones = responseBody.statistics.markets_by_timezone;
    
    // Should include common US timezones
    const commonTimezones = [
      'America/New_York',
      'America/Chicago', 
      'America/Denver',
      'America/Los_Angeles'
    ];
    
    commonTimezones.forEach(timezone => {
      if (timezone in timezones) {
        expect(timezones[timezone]).toBeGreaterThan(0);
      }
    });
    
    // All timezone counts should be positive integers
    Object.values(timezones).forEach(count => {
      expect(Number.isInteger(count)).toBe(true);
      expect(count).toBeGreaterThan(0);
    });
  });

  test('should handle method not allowed', async ({ request }) => {
    const response = await request.post('getMarketStats');
    
    expect(response.status()).toBe(405);
    
    const responseBody = await response.json();
    expect(responseBody.code).toBe(405);
    expect(responseBody.message).toContain('http method is not allowed');
  });

  test('should validate cache headers', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getMarketStats(authToken);
    
    expect(response.status()).toBe(200);
    
    const cacheControl = response.headers()['cache-control'];
    expect(cacheControl).toBeDefined();
    expect(cacheControl).toContain('public');
    expect(cacheControl).toContain('max-age=3600'); // 1 hour
  });

  test('should maintain consistency across multiple calls', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;

  console.log('Starting consistency test for getMarketStats API...');

  const responses = await Promise.all([
    localEventsApi.getMarketStats(authToken),
    localEventsApi.getMarketStats(authToken),
    localEventsApi.getMarketStats(authToken)
  ]);

  responses.forEach((response, index) => {
    console.log(`Response ${index + 1} status:`, response.status());
    expect(response.status()).toBe(200);
  });

  const bodies = await Promise.all(responses.map(r => r.json()));

  console.log('Response bodies received:');
  bodies.forEach((body, index) => {
    console.log(`Response ${index + 1}:`, JSON.stringify(body, null, 2));
  });

  // Compare statistics across responses
  const baseStats = bodies[0].statistics;
  console.log('Base statistics from first response:', JSON.stringify(baseStats, null, 2));

  bodies.slice(1).forEach((body, index) => {
    const currentStats = body.statistics;
    console.log(`Comparing statistics for response ${index + 2}:`, JSON.stringify(currentStats, null, 2));

    try {
      expect(currentStats.total_markets).toBe(baseStats.total_markets);
      expect(currentStats.active_markets).toBe(baseStats.active_markets);
      expect(currentStats.total_events_across_all_markets).toBe(baseStats.total_events_across_all_markets);
    } catch (error) {
      console.error(`Mismatch detected in response ${index + 2}:`, error.message);
      console.error('Base stats:', JSON.stringify(baseStats, null, 2));
      console.error('Current stats:', JSON.stringify(currentStats, null, 2));
      throw error; // Re-throw the error to fail the test
    }
  });

  console.log('Consistency test completed successfully.');
  });

  test('should validate statistics mathematical relationships', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getMarketStats(authToken);
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetMarketStatsResponse = await response.json();
    const stats = responseBody.statistics;
    
    // Average events per market should be reasonable
    const expectedAverage = Math.round(stats.total_events_across_all_markets / stats.total_markets);
    expect(stats.average_events_per_market).toBe(expectedAverage);
    
    // Markets with events should be less than or equal to total markets
    expect(stats.markets_with_events).toBeLessThanOrEqual(stats.total_markets);
    
    // Active markets should generally have most of the events (business logic validation)
    expect(stats.active_markets).toBeGreaterThan(0);
    
    // Top markets should have reasonable event counts
    if (stats.top_markets_by_event_count.length > 0) {
      const topMarket = stats.top_markets_by_event_count[0];
      expect(topMarket.event_count).toBeGreaterThan(stats.average_events_per_market);
    }
  });

  test('should handle concurrent requests efficiently', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const startTime = Date.now();
    
    const requests = Array(10).fill(null).map(() => localEventsApi.getMarketStats(authToken));
    const responses = await Promise.all(requests);
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    // All requests should complete successfully
    responses.forEach(response => {
      expect(response.status()).toBe(200);
    });
    
    // Due to caching, concurrent requests should be relatively fast
    expect(totalTime).toBeLessThan(10000); // Less than 10 seconds for 10 concurrent requests
    
    const bodies = await Promise.all(responses.map(r => r.json()));
    
    // All responses should be identical (due to caching)
    bodies.forEach(body => {
      expect(body.success).toBe(true);
      expect(body.statistics.total_markets).toBe(bodies[0].statistics.total_markets);
    });
  });
});
