import { test, expect } from '@fixtures/base.api';
import { LocalEventsAPI } from '@apis/local-events.api';
import { GetAllMarketsResponse } from '@test-data/local-events/contract-schema';

test.describe('getAllMarkets API endpoint', () => {
  
  test.beforeEach(async () => {
    // Skip tests in production to avoid data pollution
    test.skip(process.env.NODE_ENV === 'prod', 'Skipping tests in production');
  });

  test('should return all markets with default parameters', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getAllMarkets(authToken);
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetAllMarketsResponse = await response.json();
    
    // Validate response structure
    expect(responseBody.success).toBe(true);
    expect(Array.isArray(responseBody.markets)).toBe(true);
    expect(typeof responseBody.count).toBe('number');
    expect(responseBody.count).toBeGreaterThan(0);
    expect(responseBody.filters_applied).toBeDefined();
    
    // Validate filters applied
    expect(responseBody.filters_applied.active_only).toBe(false);
    expect(responseBody.filters_applied.sort_by).toBe('name');
    expect(responseBody.filters_applied.order).toBe('asc');
    
    // Validate market structure
    if (responseBody.markets.length > 0) {
      // Validate the market[0] structure
      const firstMarket = responseBody.markets[0];
      expect(firstMarket.id).toBe('_lastSync');
      expect(firstMarket.location.geopoint).toBeNull();
      expect(firstMarket.event_count).toBe(0);
      expect(firstMarket.metadata).toEqual({});

      const firstActualMarket = responseBody.markets[1];
      LocalEventsAPI.validateMarketStructure(firstActualMarket);
      
      expect(firstActualMarket.id).toBeTruthy();
      expect(firstActualMarket.uuid).toBeTruthy();
      expect(firstActualMarket.name).toBeTruthy();
      expect(firstMarket.location).toBeDefined();
      expect(typeof firstActualMarket.event_count).toBe('number');
      expect(typeof firstActualMarket.isActive).toBe('boolean');
    }
  });

  test('should filter markets by active status', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getAllMarkets(authToken, { active_only: true });
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetAllMarketsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.filters_applied.active_only).toBe(true);
    
    // All returned markets should be active
    responseBody.markets.forEach(market => {
      expect(market.isActive).toBe(true);
    });
  });

  test('should support pagination', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const limit = 5;
    const response = await localEventsApi.getAllMarkets(
      authToken,
      { 
        paginate: true, 
        limit, 
        page: 1 
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetAllMarketsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.markets.length).toBeLessThanOrEqual(limit);
    expect(responseBody.pagination).toBeDefined();
    
    if (responseBody.pagination) {
      expect(responseBody.pagination.page).toBe(1);
      expect(responseBody.pagination.limit).toBe(limit);
      expect(typeof responseBody.pagination.totalCount).toBe('number');
      expect(typeof responseBody.pagination.totalPages).toBe('number');
      expect(typeof responseBody.pagination.hasNext).toBe('boolean');
      expect(typeof responseBody.pagination.hasPrev).toBe('boolean');
    }
  });

  test('should filter markets by minimum events', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const minEvents = 50;
    const response = await localEventsApi.getAllMarkets(authToken, { min_events: minEvents });
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetAllMarketsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.filters_applied.min_events).toBe(minEvents);
    
    // All returned markets should have at least minEvents
    responseBody.markets.forEach(market => {
      expect(market.event_count).toBeGreaterThanOrEqual(minEvents);
    });
  });

  test('should support search by market name', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const searchTerm = 'New York';
    const response = await localEventsApi.getAllMarkets(authToken, { search: searchTerm });
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetAllMarketsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.filters_applied.search).toBe(searchTerm);
    
    // All returned markets should contain the search term
    responseBody.markets.forEach(market => {
      expect(market.name.toLowerCase()).toContain(searchTerm.toLowerCase());
    });
  });

  test('should support different sort orders', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;

    console.log('Starting sort order test for getAllMarkets API...');

    // Test ascending order by name
    console.log('Testing ascending order by name...');
    const ascResponse = await localEventsApi.getAllMarkets(authToken, {
      sort_by: 'name',
      order: 'asc',
      limit: 10,
    });

    console.log('Ascending order response status:', ascResponse.status());
    expect(ascResponse.status()).toBe(200);

    const ascBody: GetAllMarketsResponse = await ascResponse.json();
    expect(ascBody.success).toBe(true);

    // Verify ascending order
    for (let i = 2; i < ascBody.markets.length; i++) {
      const currentMarket = ascBody.markets[i];
      const previousMarket = ascBody.markets[i - 1];

      // Perform Firestore-compatible comparison
      const isSorted = currentMarket.name.localeCompare(previousMarket.name, 'en', { sensitivity: 'base' }) >= 0;
      expect(isSorted, `Failed at index ${i}, currentMarket.name: ${currentMarket.name}, previousMarket.name: ${previousMarket.name}`).toBe(true);
    }

    console.log('Name sorted by ascending test completed successfully.');

    // Test descending order by event count
    console.log('Testing descending order by event count...');
    const descResponse = await localEventsApi.getAllMarkets(authToken, {
      sort_by: 'event_count',
      order: 'desc',
      limit: 10,
    });

    expect(descResponse.status()).toBe(200);

    const descBody: GetAllMarketsResponse = await descResponse.json();
    expect(descBody.success).toBe(true);

    // Verify descending order by event count
    for (let i = 1; i < descBody.markets.length; i++) {
      const currentMarket = descBody.markets[i];
      const previousMarket = descBody.markets[i - 1];

      expect(currentMarket.event_count <= previousMarket.event_count).toBe(true);
    }

    console.log('Descending order test completed successfully.');
  });

  test('should handle large limit values', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getAllMarkets(authToken, { limit: 500 });
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetAllMarketsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.markets.length).toBeLessThanOrEqual(500);
  });

  test('should return empty results for invalid search', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getAllMarkets(
      authToken,
      { search: localEventsTestData.search.name.invalid }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetAllMarketsResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.markets.length).toBe(0);
    expect(responseBody.count).toBe(0);
  });

  test('should handle method not allowed', async ({ request, auth }) => {
    const response = await request.post('getAllMarkets', 
      {
        headers: { 
          Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    expect(response.status()).toBe(405);
    
    const responseBody = await response.json();
    expect(responseBody.code).toBe(405);
    expect(responseBody.message).toContain('http method is not allowed');
  });

  test('should validate cache headers', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getAllMarkets(authToken);
    
    expect(response.status()).toBe(200);
    
    const cacheControl = response.headers()['cache-control'];
    expect(cacheControl).toBeDefined();
    expect(cacheControl).toContain('public');
    expect(cacheControl).toContain('max-age=300'); // 5 minutes
  });

  test('should handle concurrent requests', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const requests = Array(5).fill(null).map(() => 
      localEventsApi.getAllMarkets(authToken, { limit: 10 })
    );
    
    const responses = await Promise.all(requests);
    
    responses.forEach(response => {
      expect(response.status()).toBe(200);
    });
    
    const bodies = await Promise.all(responses.map(r => r.json()));
    
    // All responses should be successful and consistent
    bodies.forEach(body => {
      expect(body.success).toBe(true);
      expect(body.markets.length).toBeGreaterThan(0);
    });
  });
});
