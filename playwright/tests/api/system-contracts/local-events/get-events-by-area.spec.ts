import { test, expect } from '@fixtures/base.api';
import { LocalEventsAPI } from '@apis/local-events.api';
import { GetEventsByAreaResponse } from '@test-data/local-events/contract-schema';

test.describe('getEventsByArea API endpoint', () => {
  
  test.beforeEach(async () => {
    test.skip(process.env.NODE_ENV === 'prod', 'Skipping tests in production');
  });

  test('should return events for a valid area', async ({ 
    localEventsApi, 
    localEventsTestData, 
    auth 
  }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getEventsByArea(
      authToken, 
      { area_uuid: localEventsTestData.markets.valid.area_uuid }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetEventsByAreaResponse = await response.json();
    
    // Validate response structure
    expect(responseBody.success).toBe(true);
    expect(responseBody.area).toBeDefined();
    expect(Array.isArray(responseBody.events)).toBe(true);
    expect(typeof responseBody.count).toBe('number');
    expect(typeof responseBody.total_in_area).toBe('number');
    expect(responseBody.filters_applied).toBeDefined();
    
    // Validate area information
    expect(responseBody.area.uuid).toBe(localEventsTestData.markets.valid.area_uuid);
    expect(responseBody.area.name).toBeTruthy();
    expect(typeof responseBody.area.latitude).toBe('number');
    expect(typeof responseBody.area.longitude).toBe('number');
    expect(typeof responseBody.area.event_count).toBe('number');
    
    // Validate events
    if (responseBody.events.length > 0) {
      const firstEvent = responseBody.events[0];
      LocalEventsAPI.validateEventsByAreaEventStructure(firstEvent);
      
      expect(firstEvent.area_uuid).toBe(localEventsTestData.markets.valid.area_uuid);
      expect(firstEvent.id).toBeTruthy();
      expect(firstEvent.uuid).toBeTruthy();
      expect(firstEvent.name).toBeTruthy();
      expect(firstEvent.start_date).toBeTruthy();
    }
  });

  test('should return 404 for invalid area UUID', async ({ 
    localEventsApi, 
    localEventsTestData, 
    auth 
  }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getEventsByArea(
      authToken, 
      { area_uuid: localEventsTestData.markets.invalid.area_uuid }
    );
    
    expect(response.status()).toBe(404);
    
    const responseBody = await response.json();
    expect(responseBody.success).toBe(false);
    expect(responseBody.error).toBe('Area not found');
    expect(responseBody.area_uuid).toBe(localEventsTestData.markets.invalid.area_uuid);
  });

  test('should return 400 for missing area_uuid', async ({ localEventsApi, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getEventsByArea(authToken, {} as any);
    
    expect(response.status()).toBe(400);
    
    const responseBody = await response.json();
    expect(responseBody.success).toBe(false);
    expect(responseBody.error).toBe('Missing required parameter: area_uuid');
  });

  test('should support pagination with page tokens', async ({ localEventsApi, localEventsTestData, auth }) => {
    const limit = localEventsTestData.pagination.small_limit;
    const authToken = auth.tokens.rmc.signedToken;
    
    // Get first page
    const firstPageResponse = await localEventsApi.getEventsByArea(
      authToken, {
        area_uuid: localEventsTestData.markets.large.area_uuid,
        limit
      }
    );
    
    expect(firstPageResponse.status()).toBe(200);
    
    const firstPageBody: GetEventsByAreaResponse = await firstPageResponse.json();
    
    expect(firstPageBody.success).toBe(true);
    expect(firstPageBody.events.length).toBeLessThanOrEqual(limit);
    
    if (firstPageBody.pagination?.has_more && firstPageBody.pagination.next_page_token) {
      // Get second page using page token
      const secondPageResponse = await localEventsApi.getEventsByArea(
        authToken, {
          area_uuid: localEventsTestData.markets.large.area_uuid,
          limit,
          page_token: firstPageBody.pagination.next_page_token
        }
      );
      
      expect(secondPageResponse.status()).toBe(200);
      
      const secondPageBody: GetEventsByAreaResponse = await secondPageResponse.json();
      
      expect(secondPageBody.success).toBe(true);
      expect(secondPageBody.events.length).toBeLessThanOrEqual(limit);
      
      // Verify no duplicate events between pages
      const firstPageIds = new Set(firstPageBody.events.map(e => e.id));
      const secondPageIds = new Set(secondPageBody.events.map(e => e.id));
      
      const overlap = Array.from(firstPageIds).filter(id => secondPageIds.has(id));
      expect(overlap.length).toBe(0); // No duplicates
      
      // All events should be from the same area
      secondPageBody.events.forEach(event => {
        expect(event.area_uuid).toBe(localEventsTestData.markets.large.area_uuid);
      });
    }
  });

  test('should filter by active events only', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getEventsByArea(
      authToken, {
        area_uuid: localEventsTestData.markets.valid.area_uuid,
        active_only: true,
        limit: 10
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetEventsByAreaResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.filters_applied.active_only).toBe(true);
    
    // All returned events should be active
    responseBody.events.forEach(event => {
      expect(event._isActive).toBe(true);
    });
  });

  test('should filter by date range', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    // const startDate = localEventsTestData.filters.date_ranges.current.start;
    // const endDate = localEventsTestData.filters.date_ranges.current.end;
    const today = new Date();
    const startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // Format as YYYY-MM-DD
    const endDate = new Date(today.getTime() - 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // Today - 14 days

    const response = await localEventsApi.getEventsByArea(
      authToken, {
        area_uuid: localEventsTestData.markets.large.area_uuid,
        start_date: startDate,
        end_date: endDate,
        limit: 20
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetEventsByAreaResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.filters_applied.start_date).toBe(startDate);
    expect(responseBody.filters_applied.end_date).toBe(endDate);
    
    // Validate events are within date range
    const startDateTime = new Date(startDate).getTime();
    const endDateTime = new Date(endDate).getTime();

    expect(responseBody.events.length).toBeGreaterThan(0);
    expect(responseBody.events.length).toBeLessThanOrEqual(20);
    
    responseBody.events.forEach(event => {
      const eventDate = new Date(event.start_date.seconds).getTime();
      expect(eventDate).toBeGreaterThanOrEqual(startDateTime);
      expect(eventDate).toBeLessThanOrEqual(endDateTime);
    });
  });

  test('should filter by popularity score', async ({ localEventsApi, localEventsTestData, auth }) => {
    const minPopularity = localEventsTestData.filters.popularity.min_medium;
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getEventsByArea(
      authToken, {
        area_uuid: localEventsTestData.markets.large.area_uuid,
        popularity_min: minPopularity,
        limit: 10
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetEventsByAreaResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.filters_applied.popularity_min).toBe(minPopularity);
    
    // All returned events should meet minimum popularity
    responseBody.events.forEach(event => {
      if (event.popularity_score !== undefined) {
        expect(event.popularity_score).toBeGreaterThanOrEqual(minPopularity);
      }
    });
  });

  test('should handle invalid date formats', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getEventsByArea(
      authToken, {
        area_uuid: localEventsTestData.markets.valid.area_uuid,
        start_date: 'invalid-date',
        end_date: '2025-13-45' // Invalid date
      }
    );
    
    // Should still return 200 but may not filter properly
    expect(response.status()).toBe(200);
    
    const responseBody = await response.json();
    expect(responseBody.success).toBe(true);
  });

  test('should handle large limit values', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getEventsByArea(
      authToken, {
        area_uuid: localEventsTestData.markets.large.area_uuid,
        limit: localEventsTestData.pagination.max_limit
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetEventsByAreaResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(responseBody.events.length).toBeLessThanOrEqual(localEventsTestData.pagination.max_limit);
  });

  test('should validate blocked tags are removed', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getEventsByArea(
      authToken, {
        area_uuid: localEventsTestData.markets.valid.area_uuid,
        limit: 20
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetEventsByAreaResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    expect(typeof responseBody.filters_applied.blocked_tags_removed).toBe('boolean');
    
    // Search terms should be filtered if blocked tags were removed
    responseBody.events.forEach(event => {
      if (event._searchTerms && Array.isArray(event._searchTerms)) {
        // This is more of a structural validation since we don't know which tags are blocked
        expect(Array.isArray(event._searchTerms)).toBe(true);
      }
    });
  });

  test('should handle invalid page tokens gracefully', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getEventsByArea(
      authToken, {
        area_uuid: localEventsTestData.markets.valid.area_uuid,
        page_token: 'invalid-token-123',
        limit: 5
      }
    );
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetEventsByAreaResponse = await response.json();
    
    // Should return results as if no page token was provided
    expect(responseBody.success).toBe(true);
    expect(responseBody.events.length).toBeGreaterThan(0);
  });

  test('should validate event data structure', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getEventsByArea(
      authToken, {
      area_uuid: localEventsTestData.markets.valid.area_uuid,
      limit: 5
    });
    
    expect(response.status()).toBe(200);
    
    const responseBody: GetEventsByAreaResponse = await response.json();
    
    expect(responseBody.success).toBe(true);
    
    responseBody.events.forEach((event, index) => {
      // Validate required fields
      expect(event.id, `Event ID is missing at index ${index}`).toBeTruthy();
      expect(event.uuid, `Event UUID is missing at index ${index}`).toBeTruthy();
      expect(event.name, `Event Name is missing at index ${index}`).toBeTruthy();
      expect(event.start_date, `Event Start Date is missing at index ${index}`).toBeTruthy();
      expect(event.area_uuid, `Event Area UUID is missing at index ${index}`).toBe(localEventsTestData.markets.valid.area_uuid);

      // Validate venue structure
      expect(event.venue, `Event Venue is missing at index ${index}`).toBeDefined();
      expect(typeof event.venue).toBe('object');
      expect(typeof event.venue.name).toBe('string');

      // Validate URLs structure
      expect(event.urls).toBeDefined();
      expect(typeof event.urls).toBe('object');
      
      // Validate prices structure
      expect(event.prices).toBeDefined();
      expect(typeof event.prices).toBe('object');
      
      // Validate flags if present
      if (event.flags) {
        expect(Array.isArray(event.flags)).toBe(true);
      }
    });
  });

  test('should handle method not allowed', async ({ request, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await request.post('getEventsByArea', 
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          area_uuid: '2cca7581-b3e3-42ad-9c1b-48bdf0fb5333' // New York City, NY - All 5 Boroughs
        }
      }
    );
    
    expect(response.status()).toBe(405);
    
    const responseBody = await response.json();
    expect(responseBody.code).toBe(405);
    expect(responseBody.message).toContain('http method is not allowed');
  });

  test('should validate cache headers', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const response = await localEventsApi.getEventsByArea(
      authToken, {
        area_uuid: localEventsTestData.markets.valid.area_uuid
      }
    );
    
    expect(response.status()).toBe(200);
    
    const cacheControl = response.headers()['cache-control'];
    expect(cacheControl).toBeDefined();
    expect(cacheControl).toContain('public');
    expect(cacheControl).toContain('max-age=300'); // 5 minutes
  });

  test('should test pagination consistency across multiple pages', async ({ localEventsApi, localEventsTestData, auth }) => {
    const authToken = auth.tokens.rmc.signedToken;
    const limit = 100;
    let allEventIds: string[] = [];
    let currentPageToken: string | undefined;
    let pageCount = 0;
    const maxPages = 50; // Limit to prevent infinite loops
    let totalEventsRetrieved = 0; // Accumulate the total number of events retrieved
    let totalInArea: number | undefined; // Store the total_in_area value from the response

    while (pageCount < maxPages) {
      const response = await localEventsApi.getEventsByArea(
        authToken, {
          area_uuid: localEventsTestData.markets.large2.area_uuid,
          limit,
          page_token: currentPageToken
        }
      );

      expect(response.status()).toBe(200);

      const responseBody: GetEventsByAreaResponse = await response.json();
      expect(responseBody.success).toBe(true);

      // Store total_in_area from the first response
      if (totalInArea === undefined) {
        totalInArea = responseBody.total_in_area;
        expect(typeof totalInArea).toBe('number');
      }

      // Add current page event IDs
      const currentPageIds = responseBody.events.map(e => e.id);

      // Check for duplicates across pages
      currentPageIds.forEach(id => {
        expect(allEventIds.includes(id)).toBe(false);
      });

      allEventIds.push(...currentPageIds);
      totalEventsRetrieved += currentPageIds.length; // Accumulate the total number of events retrieved

      // Check if there are more pages
      if (!responseBody.pagination?.has_more || !responseBody.pagination.next_page_token) {
        break;
      }

      currentPageToken = responseBody.pagination.next_page_token;
      pageCount++;
    }

    // Should have retrieved some events
    expect(allEventIds.length).toBeGreaterThan(0);

    // All IDs should be unique
    const uniqueIds = new Set(allEventIds);
    expect(uniqueIds.size).toBe(allEventIds.length);

    // Verify total events retrieved matches total_in_area
    expect(totalEventsRetrieved).toBe(totalInArea);
  });
});
