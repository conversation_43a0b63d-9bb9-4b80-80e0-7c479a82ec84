import { expect, test} from "@playwright/test";
import config from "@configs/env/config";
import testData from "@test-data/astrid-address-checker-scenarios/astrid-address-checker-scenarios.json";

const key = config.ASTRID_API_KEY;
const url = config.ASTRID_API_URL;

test.describe("Astrid Address Checker", () => {

  for (const { scenarioName, address, expectedResponseBody } of testData) {

    test(`Should confirm address deliverability for scenario: ${scenarioName}`, async ({ request }) => {
      console.log(`📞 Calling Astrid Address Checker`);
      const response = await request.post(`${url}/address`, {
        headers: {
          "Authorization": `Bearer ${key}`,
          "Content-Type": "application/json",
        },
        data: address,
      });

      const expectedStatusCode = scenarioName === "MISSING_ADDRESS1" ? 400 : 200;
      expect(response.status()).toBe(expectedStatusCode);
      const responseBody = await response.json();

      const actual = {
        status: responseBody.status,
        codes: responseBody.codes ?? [],
        address1: responseBody.address1 ?? "",
        address2: responseBody.address2 ?? "",
        city: responseBody.city ?? "",
        state: responseBody.state ?? "",
        zip: responseBody.zip ?? "",
      };

      const expected = {
        status: expectedResponseBody.status,
        codes: expectedResponseBody.codes ?? [],
        address1: expectedResponseBody.address1 ?? "",
        address2: expectedResponseBody.address2 ?? "",
        city: expectedResponseBody.city ?? "",
        state: expectedResponseBody.state ?? "",
        zip: expectedResponseBody.zip ?? "",
      };

      console.log(`🧪 Testing the response body`);
      expect(actual).toEqual(expected);
    });
  }
});
