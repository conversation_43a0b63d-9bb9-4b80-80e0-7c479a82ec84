import { test } from "@fixtures/base.api";
import { expect } from "@playwright/test";
import config from "@configs/env/config";
import Ajv from "ajv";
import { seedRecipientGroup } from "@utils/recipients/seedRecipientGroup";
import { seedRecipientAndMailingAddress } from "@utils/recipients/seedRecipientAndMailingAddress";
import { deleteRecipient } from "@utils/recipients/deleteRecipientRecursively";
import { deleteRecipientGroup } from "@utils/recipients/deleteRecipientGroupRecursively";
import {
  RecipientName,
  TestAddress,
  TestRecipient,
  TestRecipientAndAddress,
} from "@models/interfaces";
import { RecipientFilePaths } from "@models/enums";
import { Timestamp } from "firebase/firestore";
import recipientsScenarios from "@test-data/recipient-apis/recipients/recipients-scenarios.json";
import { recipientsResponseContract } from "@test-data/recipient-apis/recipients/contract-schema";
import { validateRecipientsResponse } from "@utils/recipients/validateRecipientsResponse";
import { faker } from "@faker-js/faker";
import { wait } from "@utils/helpers/wait";
import { startFirebaseDb } from "@utils/helpers/startFirebaseDb";
import { waitForCloudFunctionProcessing } from "@utils/helpers/waitForCloudFunctionProcessing";
import { 
  collection,
  doc,
  getDocs,
  query,
  where, 
} from "firebase/firestore";

const db = startFirebaseDb();
const key = config.RM_TITAN_API_KEY;
const skipCleanup = process.env.SKIP_CLEANUP === "true";
const skipSeeds = process.env.SKIP_SEEDS === "true";

// The schema is used to validate the response structure and types
// matches the response from Titan that Digital expects
const responseSchema = recipientsResponseContract;

const accountId = "4378522";
let recipientGroupId: string;
let campaignId: string;
let recipients: TestRecipientAndAddress[] = [];

// All tests depend on the same recipient group state
// I'm setting mode to serial to avoid overlapping requests
// If not serial, each test worker would seed its own recipient group
test.describe.configure({ mode: "serial" });

test.describe("Recipients", () => {
  let apiResponse: any;

  // Will only run once when in serial mode
  test.beforeAll(async ({ request, auth }) => {
    // I'm seeding the recipient group first and returning the the groupId and campaignId
    console.log(`🌱 Seeding Recipient Group`);
    const groupResult = await seedRecipientGroup({ 
      accountId,
      productGroup: "digital",
      campaignId, 
    });
    recipientGroupId = groupResult.recipientGroupId;
    campaignId = groupResult.campaignId;

    // I'm generating the recipient data from the business rules
    // defined in the recipients-scenarios.json file
    // This file contains the test scenarios for the recipients
    console.log(`🔄 Generating Recipient Data`);
    recipients = await generateRecipientData(accountId);

    // I'm waiting for group Cloud Functions to process the group
    // before seeding the recipient and address data
    await wait(
      2000,
      "Waiting for recipient group Cloud Functions to process..."
    );

    // I'm seeding the recipient group with recipients and address data
    await seedRecipientAndMailingAddress(recipients, RecipientFilePaths.RECIPIENTS_PATH);

    // I'm waiting for the recipient Cloud Functions to process all the recipients and email data
    for (const recipient of recipients) {
      const recipientId = recipient.testRecipient.recipientId;
      const docId = await waitForEmailAddressFirestoreDocId(
        accountId,
        recipientId,
      );
      
      if (docId) {
        const storePath = `Account/${accountId}/Recipients/${recipientId}/EmailAddresses/0`;
        const docRef = doc(db, storePath);
        await waitForCloudFunctionProcessing(
          docRef, 
          "processingStatus", 
          "DONE", 
          15, 
          1000
        );
      } else {
        console.warn(`⚠️ No email address document found for recipient ${recipientId}. Skipping wait for Cloud Function processing.`);
      }
    }

    console.log(`📞 Calling Recipients Endpoint`);
    const response = await request.get(`recipients`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.print.signedToken}`,
        "Content-Type": "application/json",
      },
      params: {
        accountId,
        method: "email",
        productId: 2,
        key,
      },
    });

    expect(response.status()).toBe(200);
    apiResponse = await response.json();
  });

  test.afterAll(async () => {
    if (skipSeeds) {
      console.log('⏭️  Skipping test seeds (SKIP_SEEDS=true)');
      return;
    }
    if (skipCleanup) {
      console.log('⏭️  Skipping test cleanup (SKIP_CLEANUP=true)');
      return;
    }
    // Test Cleanup: Delete the recipient group and all recipients
    // Delete the recipients
    console.log("🧹 Cleaning up test data: Deleting recipients and recipient group");
    for (const recipient of recipients) {
      await deleteRecipient(accountId, recipient.testRecipient.recipientId);
    }
    // Delete the recipient group
    await deleteRecipientGroup(accountId, recipientGroupId);
    console.log("✅ Test data cleanup completed");
  });

  test("should verify response body structure", async () => {
    // Validate the response body structure against the schema contract
    console.log(
      "📜 Validating response body structure against the system's schema contract..."
    );
    
    validateRecipientsResponse(apiResponse);
  });
});

async function generateRecipientData(
  accountId: string
): Promise<TestRecipientAndAddress[]> {
  let recipientAndAddresses: TestRecipientAndAddress[] = [];

  for (const scenario of recipientsScenarios) {
    const scenarioName = scenario.scenarioName;
    const firstName = scenario.recipient.firstName;
    const lastName = scenario.recipient.lastName;
    const email = scenario.recipient.email;
    const addressScenario = scenario.recipient.address;
    const recipientId = faker.string.uuid();
    const name: RecipientName = {
      firstName,
      lastName,
    };
    const testRecipient: TestRecipient = {
      recipientId: recipientId,
      accountId: accountId,
      isActive: true,
      mailingsPaused: false,
      createdAt: Timestamp.now(),
      recipientGroupIds: [recipientGroupId],
      name,
      salutation: {
        letterSalutation: "",
        mailingSalutation: "",
      },
      email,
    };

    // Address Data
    const testAddress: TestAddress = {
      address1: addressScenario.address1,
      address2: addressScenario.address2 || "",
      city: addressScenario.city,
      state: addressScenario.state,
      postalCode: addressScenario.postalCode,
      type: "home",
      createdAt: Timestamp.now(),
    };

    recipientAndAddresses.push({ scenarioName, testRecipient, testAddress });
  }

  return recipientAndAddresses;
}

// export function validateRecipientsResponse(apiResponse: any): void {
//   const ajv = new Ajv();
//     const validate = ajv.compile(responseSchema);
//     const isValid = validate(apiResponse);

//     console.log(`📊 Response validation result: ${isValid ? "✅ Valid" : "❌ Not Valid"}`);

//     if (!isValid) {
//       console.error("❌ Schema Validation Errors:", validate.errors);
//     } else {
//       console.log("✅ Schema validation passed");
//       console.log("📋 Response structure:", {
//         message: apiResponse.message,
//         count: apiResponse.count,
//         recipients: apiResponse.recipients.map((r) => ({
//           id: r.id,
//           first_name: r.first_name,
//           last_name: r.last_name,
//           recipient_group_id: r.recipient_group_id,
//           mailings_paused: r.mailings_paused,
//           mailing_salutation: r.mailing_salutation,
//           letter_salutation: r.letter_salutation,
//           email: r.email,
//         })),
//       });
//     }

//     if (!isValid) {
//       const errorMessages = validate.errors
//         ?.map((err) => {
//           const path = err.dataPath || "(root)";
//           const actualValue = path
//             .split("/")
//             .filter(Boolean)
//             .reduce(
//               (obj, key) => (obj && key in obj ? obj[key] : undefined),
//               apiResponse
//             );

//           return `→ ${path} ${err.message}. Received: ${JSON.stringify(actualValue)}`;
//         })
//         .join("\n");

//       expect(
//         isValid,
//         `❌ API response failed schema validation:\n\n${errorMessages}\n`
//       ).toBe(true);
//     } else {
//       expect(isValid).toBe(true);
//     }
//     expect(apiResponse.count).toBeGreaterThan(0);
// }

async function waitForEmailAddressFirestoreDocId(
  accountId: string,
  recipientId: string,
  maxAttempts: number = 10,
  intervalMs: number = 1000,
): Promise<string> {
  const subcollectionPath = `Account/${accountId}/Recipients/${recipientId}/EmailAddresses`;
  const subcollectionRef = collection(db, subcollectionPath);

  let attempts = 0;
  while (attempts < maxAttempts) {
    const q = query(subcollectionRef, where("isPrimary", "==", true));
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      console.log(`✅ Found document ID: ${doc.id}`);
      return doc.id; 
    }
    console.log(`⏳ Waiting for document in subcollection: ${subcollectionPath} (Attempt ${attempts + 1})`);
    await new Promise(resolve => setTimeout(resolve, intervalMs));
    attempts++;
  }

  throw new Error(`❌ Document not found in subcollection after ${maxAttempts} attempts: ${subcollectionPath}`);
}
