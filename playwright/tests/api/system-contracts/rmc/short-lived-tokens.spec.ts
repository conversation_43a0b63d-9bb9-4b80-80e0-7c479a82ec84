import { test } from "@fixtures/base.api";
import { expect } from "@playwright/test";
import { decodeSignedJwt } from "@utils/helpers/generate-jwt";
import config from "@configs/env/config";

const apiKey = config.RM_TITAN_API_KEY;
const testAccountId = config.RMC_TEST_ACCOUNT_ID;
const kathyTyndall = "102";
const kathyTyndallEmail = "<EMAIL>";
const testAccountEmail = config.RMC_TEST_ACCOUNT_EMAIL;
const expiredRmcClientToken = config.RM_TITAN_EXPIRED_RMC_CLIENT_TOKEN;
const incorrectApiKey = 'xIxaxyXXXmyDFd57ngNMeh0xOi3JETjMWSXDSRkl';
const nonExistentAccountId = '********';
const nonExistentEmail = '<EMAIL>';

/**
 * Short Lived Tokens: Customers Test Suite
 * -------------------------------------------------------------
 */
test.describe("Titan Short Lived Tokens For Customers", () => {

  // Positive
  test("should simulate RMC POST to /shortLivedTokenForCustomers and return a valid token", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForCustomers`, {
      data: {
        accountId: kathyTyndall,
        email: kathyTyndallEmail,
      },
      params: {
        key: apiKey,
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    const resJson = await response.json();

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(resJson.code).not.toBeDefined();
    expect(resJson.message).not.toBeDefined();
    expect(response.status()).toBe(200);
    expect(response.statusText()).toBe('OK');
    expect(resJson.token).toBeTruthy();

    const decodedToken = await decodeSignedJwt(resJson.token);
    expect(decodedToken).toBeTruthy();
    
    const diffCheck = await getDifferenceInHours(decodedToken.exp, decodedToken.iat);
    expect(diffCheck).toBe(1);
  });

  // Negative - Erroneous & Missing POST Body Data
  test.fixme("should return 404 (missing accountId)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForCustomers`, {
      data: {
        accountId: "",
      },
      params: {
        key: apiKey,
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(404);
  });

  test.fixme("should return 404 (non-existent accountId)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForCustomers`, {
      data: {
        accountId: nonExistentAccountId,
      },
      params: {
        key: apiKey,
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(404);
  });

  // Negative - Erroneous & Missing API Key
  test("should return 400 (incorrect API key)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForCustomers`, {
      data: {
        accountId: "102",
      },
      params: {
        key: incorrectApiKey,
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(400);
  });

  test("should return 401 (missing API key)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForCustomers`, {
      data: {
        accountId: "102",
      },
      params: {
        key: '',
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(401);
  });

  // Negative - Missing & Expired Tokens
  test("should return 401 (missing auth token)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForCustomers`, {
      data: {
        accountId: "102",
      },
      params: {
        key: '',
      },
      headers: {
        Authorization: `Bearer `,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(401);
  });

  test("should return 401 (expired auth token)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForCustomers`, {
      data: {
        accountId: "102",
      },
      params: {
        key: '',
      },
      headers: {
        Authorization: `Bearer ${expiredRmcClientToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(401);
  });  
});

/**
 * Short Lived Tokens: Admin Test Suite
 * -------------------------------------------------------------
 */
test.describe('Short Lived Tokens For Admins', () => {

  // Positive
  test("should simulate RMC POST to /shortLivedTokenForAdmins and return a valid yeet token", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForAdmins`, {
      data: {
        accountId: testAccountId,
        email: testAccountEmail,
      },
      params: {
        key: apiKey,
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    const resJson = await response.json();

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(resJson.code).not.toBeDefined();
    expect(resJson.message).not.toBeDefined();
    expect(response.status()).toBe(200);
    expect(response.statusText()).toBe('OK');
    expect(resJson.token).toBeTruthy();

    const decodedToken = await decodeSignedJwt(resJson.token);
    expect(decodedToken).toBeTruthy();
    expect(decodedToken.uid).toBe('f5eM1NPOHsVeAhqAb6Z4Jrphqen2');
    
    const diffCheck = await getDifferenceInHours(decodedToken.exp, decodedToken.iat);
    expect(diffCheck).toBe(1);
  });

  // Negative - Erroneous & Missing POST Body Data
  // Account ID
  test("should return 200 & token (Admins non-existent accountID)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForAdmins`, {
      data: {
        accountId: nonExistentAccountId,
        email: testAccountEmail,
      },
      params: {
        key: apiKey,
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(200);
    const resJson = await response.json();

    console.log('RES JSON: ', resJson);
    expect(resJson.token).toBeTruthy();
  });

  // Email
  test("should return 404 (Admins missing email)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForAdmins`, {
      data: {
        accountId: testAccountId,
        email: "",
      },
      params: {
        key: apiKey,
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(404);
  });

  test("should return 404 (Admins non-existent email)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForAdmins`, {
      data: {
        accountId: testAccountId,
        email: nonExistentEmail,
      },
      params: {
        key: apiKey,
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(404);
  });

  test("should return 401 (Admins missing API key)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForAdmins`, {
      data: {
        accountId: testAccountId,
        email: testAccountEmail,
      },
      params: {
        key: '',
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(401);
  });

  test("should return 400 (Admins incorrect API key)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForAdmins`, {
      data: {
        accountId: testAccountId,
        email: testAccountEmail,
      },
      params: {
        key: incorrectApiKey,
      },
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(400);
  });

  test("should return 401 (Admins missing auth token)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForAdmins`, {
      data: {
        accountId: testAccountId,
        email: testAccountEmail,
      },
      params: {
        key: apiKey,
      },
      headers: {
        Authorization: `Bearer `,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(401);
  });

  test("should return 401 (Admins expired auth token)", async ({
    request, auth
  }) => {
    const response = await request.post(`shortLivedTokenForAdmins`, {
      data: {
        accountId: testAccountId,
        email: testAccountEmail,
      },
      params: {
        key: apiKey,
      },
      headers: {
        Authorization: `Bearer ${expiredRmcClientToken}`,
        ContentType: "application/json",
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    
    expect(response.status()).toBe(401);
  });
});

async function getDifferenceInHours(exp: number, iat: number) {
  const start = new Date(iat * 1000);
  const end = new Date(exp * 1000);
  const msBetween = end.getTime() - start.getTime();
  const differenceInHours = msBetween / (1000 * 60 * 60);
  return differenceInHours;
}
