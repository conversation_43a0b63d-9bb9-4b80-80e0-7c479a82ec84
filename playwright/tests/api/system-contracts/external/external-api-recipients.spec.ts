import { test } from "@fixtures/base.api";
import { APIRequestContext, expect } from "@playwright/test";
import config from "@configs/env/config";

const apiKey = config.RM_TITAN_EXTERNAL_API_KEY;
const apiVersion = "/api/v1";

test.describe("External API: Recipient Group routes", () => {
  let createdGroup: any;
  let groupEdits: any;
  let editedGroup: any;
  let createdRecipient: any;
  let recipientEdits: any;
  let editedRecipient: any;

  test("Test 1: should POST a Recipient Group", async ({
    request,
    recipientGroup,
  }) => {
    const response = await request.post(`${apiVersion}/recipient-groups`, {
      data: {
        name: recipientGroup.name,
        description: recipientGroup.description,
      },
      headers: {
        "X-API-Key": apiKey,
        "Content-Type": "application/json",
      },
    });

    const resJson = await response.json();

    expect(response.status()).toBe(201);
    expect(resJson.data.id).toBeTruthy();
    expect(resJson.data.name).toBeTruthy();
    expect(resJson.data.description).toBeTruthy();
    expect(resJson.data.updatedAt).toBeTruthy();
    expect(resJson.data.createdAt).toBeTruthy();

    createdGroup = resJson;
  });

  test("Test 2: should get Groups and verify created Group", async ({
    request,
  }) => {
    const resJson = await getGroups(request);
    const group = await assertGroupsResponseAndReturnExpectedGroup(
      resJson,
      createdGroup
    );

    expect(group.id).toBe(createdGroup.data.id);
    expect(group.name).toBe(createdGroup.data.name);
    expect(group.description).toBe(createdGroup.data.description);
    expect(group.createdAt).toBe(createdGroup.data.createdAt);
    expect(group.updatedAt).toBe(createdGroup.data.updatedAt);
  });

  test("Test 3: should edit the Group", async ({ request, recipientGroup }) => {
    groupEdits = await recipientGroup.generateNewPostBody();
    const response = await request.put(
      `${apiVersion}/recipient-groups/${createdGroup.data.id}`,
      {
        data: groupEdits,
        headers: {
          "X-API-Key": apiKey,
          "Content-Type": "application/json",
        },
      }
    );

    const resJson = await response.json();

    expect(response.status()).toBe(200);
    expect(resJson.data.id).toBeTruthy();
    expect(resJson.data.name).toBeTruthy();
    expect(resJson.data.description).toBeTruthy();
    expect(resJson.data.updatedAt).toBeTruthy();
    expect(resJson.data.createdAt).toBeTruthy();

    editedGroup = resJson;
  });

  test("Test 4: should verify edited Group", async ({ request }) => {
    const resJson = await getGroups(request);
    const group = await assertGroupsResponseAndReturnExpectedGroup(
      resJson,
      editedGroup
    );

    expect(group.id).toBe(editedGroup.data.id);
    expect(group.name).toBe(editedGroup.data.name);
    expect(group.description).toBe(editedGroup.data.description);
    expect(group.createdAt).toBe(editedGroup.data.createdAt);
    expect(group.updatedAt).toBe(editedGroup.data.updatedAt);
  });

  test("Test 5: should POST a Recipient", async ({ request, recipient }) => {
    const response = await request.post(`${apiVersion}/recipients`, {
      data: {
        name: {
          firstName: recipient.firstName,
          lastName: recipient.lastName
        },
        recipientGroupIds: [editedGroup.data.id],
      },
      headers: {
        "X-API-Key": apiKey,
        "Content-Type": "application/json",
      },
    });

    const resJson = await response.json();

    expect(response.status()).toBe(201);
    expect(resJson.data.id).toBeTruthy();
    expect(resJson.data.name.firstName).toBeTruthy();
    expect(resJson.data.name.lastName).toBeTruthy();
    expect(resJson.data.recipientGroupIds).toBeTruthy();
  

    createdRecipient = resJson;
  });

  test("Test 6: should verify created Recipient", async ({ request }) => {
    const resJson = await getRecipients(request);
    const recipient = await assertRecipientsResponseAndReturnExpectedRecipient(
      resJson,
      createdRecipient
    );

    expect(recipient.id).toBe(createdRecipient.data.id);
    expect(recipient.name.firstName).toBe(createdRecipient.data.name.firstName);
    expect(recipient.name.lastName).toBe(createdRecipient.data.name.lastName);
    expect(recipient.createdAt).toBe(createdRecipient.data.createdAt);
    expect(recipient.updatedAt).toBe(createdRecipient.data.updatedAt);
  });

  test("Test 7: should edit the Recipient", async ({ request, recipient }) => {
    recipientEdits = await recipient.generateNewPostBody();
    const response = await request.put(
      `${apiVersion}/recipients/${createdRecipient.data.id}`,
      {
        data: recipientEdits,
        headers: {
          "X-API-Key": apiKey,
          "Content-Type": "application/json",
        },
      }
    );

    const resJson = await response.json();

    expect(response.status()).toBe(200);
    expect(resJson.data.id).toBeTruthy();
    expect(resJson.data.name.firstName).toBeTruthy();
    expect(resJson.data.name.lastName).toBeTruthy();
    expect(resJson.data.updatedAt).toBeTruthy();
    expect(resJson.data.createdAt).toBeTruthy();

    editedRecipient = resJson;
  });

  test("Test 8: should verify edited Recipient", async ({ request }) => {
    const resJson = await getRecipients(request);
    const recipient = await assertRecipientsResponseAndReturnExpectedRecipient(
      resJson,
      editedRecipient
    );

    expect(recipient.id).toBe(editedRecipient.data.id);
    expect(recipient.firstName).toBe(editedRecipient.data.firstName);
    expect(recipient.lastName).toBe(editedRecipient.data.lastName);
    expect(recipient.createdAt).toBe(editedRecipient.data.createdAt);
    expect(recipient.updatedAt).toBe(editedRecipient.data.updatedAt);
  });

  test("Test 9: should delete the Recipient", async ({ request }) => {
    const response = await request.delete(
      `${apiVersion}/recipients/${editedRecipient.data.id}`,
      {
        headers: {
          "X-API-Key": apiKey,
          "Content-Type": "application/json",
        },
      }
    );

    const resJson = await response.json();

    expect(response.status()).toBe(200);
    expect(resJson.message).toBe("Recipient has been successfully deleted.");
  });

  test("Test 10: should verify Recipient deletion", async ({ request }) => {
    const resJson = await getRecipients(request);
    const recipient = await assertRecipientsResponseAndReturnExpectedRecipient(
      resJson,
      editedRecipient
    );
    expect(recipient).toBeFalsy();
  });

  test("Test 11: should delete the Group", async ({ request }) => {
    const response = await request.delete(
      `${apiVersion}/recipient-groups/${editedGroup.data.id}`,
      {
        headers: {
          "X-API-Key": apiKey,
          "Content-Type": "application/json",
        },
      }
    );

    const resJson = await response.json();

    expect(response.status()).toBe(200);
    expect(resJson.message).toBe(
      "Recipient Group has been successfully deleted."
    );
  });

  test("Test 12: should verify Group deletion", async ({ request }) => {
    const resJson = await getGroups(request);
    const group = await assertGroupsResponseAndReturnExpectedGroup(
      resJson,
      editedGroup
    );
    expect(group).toBeFalsy();
  });
});

// Helper functions
async function getGroups(request: APIRequestContext) {
  const response = await request.get(`${apiVersion}/recipient-groups`, {
    params: {
      sort_order: "desc",
      sort_by: "updatedAt",
    },
    headers: {
      "X-API-Key": apiKey,
      "Content-Type": "application/json",
    },
  });

  return await response.json();
}

async function getRecipients(request: APIRequestContext) {
  const response = await request.get(`${apiVersion}/recipients`, {
    params: {
      sort_order: "desc",
      sort_by: "updatedAt",
    },
    headers: {
      "X-API-Key": apiKey,
      "Content-Type": "application/json",
    },
  });

  return await response.json();
}

async function assertGroupsResponseAndReturnExpectedGroup(
  resJson: any,
  expectedGroup: any
) {
  expect(resJson.status).toBe(200);
  expect(resJson.data).toBeTruthy();
  expect(resJson.pagination).toBeTruthy();
  expect(resJson.pagination.currentPage).toBeTruthy();
  expect(resJson.pagination.totalPages).toBeTruthy();
  expect(resJson.pagination.totalRecords).toBeTruthy();
  expect(resJson.pagination.pageSize).toBeTruthy();

  return resJson.data.find(
    (group: any) => group.description === expectedGroup.data.description
  );
}

async function assertRecipientsResponseAndReturnExpectedRecipient(
  resJson: any,
  expectedRecipient: any
) {
  expect(resJson.status).toBe(200);
  expect(resJson.data).toBeTruthy();
  expect(resJson.pagination).toBeTruthy();
  expect(resJson.pagination.currentPage).toBeTruthy();
  expect(resJson.pagination.totalPages).toBeTruthy();
  expect(resJson.pagination.totalRecords).toBeTruthy();
  expect(resJson.pagination.pageSize).toBeTruthy();

  return resJson.data.find(
    (recipient: any) => recipient.id === expectedRecipient.data.id
  );
}
