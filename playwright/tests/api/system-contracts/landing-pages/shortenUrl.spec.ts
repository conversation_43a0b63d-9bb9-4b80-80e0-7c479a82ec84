import { test } from '@fixtures/base.api';
import { APIResponse, expect } from "@playwright/test";
import { faker } from '@faker-js/faker';
import config from "@configs/env/config";

const apiKey = config.RM_TITAN_API_KEY;
const fakeUuid = faker.string.uuid();
const testAccountId = "4378522";
const testLongUrl = `https://tylerarmstrong.landingpages.qa-ms5.remindermedia.dev/fake-market-analysis?p=${fakeUuid}`;

let returnedShortUrl: string;
let returnedQrCodes: any;
let returnedPageUuid: string;
let returnedDomain: string;

test("Should return a valid shortened URL when a user creates a landing page", async ({ request, auth }) => {
  const payload = {
      lpId: fakeUuid,
      longUrl: testLongUrl,
      productId: "9",
      accountId: testAccountId,
  };

  const response = await request.post("shortenUrl", {
      data: payload,
      headers: {
          Authorization: `Bearer ${auth.tokens.landingPages.signedToken}`,
          "Content-Type": "application/json",
      },
      params: {
          key: apiKey,
      },
  });

  console.log(`RES STATUS CODE: ${response.status()}`);
  console.log(`RES STATUS TEXT: ${response.statusText()}`);
  console.log(`FAKE UUID: ${fakeUuid}`);

  expect(response.status()).toBe(200);

  let resJson: any;
  try {
      resJson = await response.json();
  } catch (error) {
      throw new Error(`Failed to parse JSON response: ${error.message}`);
  }

  console.log(`Response JSON:, ${resJson}`);

  expect(resJson.success).toBe(true);
  expect(resJson.data).toBeDefined();

  const { exists, shortId, longUrl, shortUrl, qrCodes, productId, accountId, clicks, domain, linkData } = resJson.data;

  expect(exists).toBe(false);
  expect(shortId).toBeDefined();
  expect(longUrl).toBe(payload.longUrl);
  expect(shortUrl).toBeDefined();
  expect(qrCodes.png).toBeDefined();
  expect(qrCodes.svg).toBeDefined();
  expect(qrCodes.pdf).toBeDefined();
  expect(productId).toBe("9");
  expect(accountId).toBe(payload.accountId);
  expect(clicks).toBe(0);
  expect(domain).toBeDefined();
  expect(linkData.page_uuid).toBe(fakeUuid);

  returnedShortUrl = shortUrl;
  returnedQrCodes = qrCodes;
  returnedPageUuid = linkData.page_uuid;
  returnedDomain = domain;
});

test("Should return a valid shortened URL when a user updates their landing page", async ({ request, auth }) => {
  const payload = {
      lpId: fakeUuid,
      longUrl: testLongUrl,
      productId: "9",
      accountId: testAccountId,
  };

  const response = await request.post("shortenUrl", {
      data: payload,
      headers: {
          Authorization: `Bearer ${auth.tokens.landingPages.signedToken}`,
          "Content-Type": "application/json",
      },
      params: {
          key: apiKey,
      },
  });

  console.log(`RES STATUS CODE: ${response.status()}`);
  console.log(`RES STATUS TEXT: ${response.statusText()}`);
  console.log(`FAKE UUID: ${fakeUuid}`);

  expect(response.status()).toBe(200);

  const resJson = await parseResponse(response);

  expect(resJson.success).toBe(true);
  expect(resJson.data).toBeDefined();

  const { exists, shortId, longUrl, shortUrl, qrCodes, productId, accountId, clicks, domain, linkData } = resJson.data;

  expect(exists).toBe(true);
  expect(shortId).toBeDefined();
  expect(longUrl).toBe(testLongUrl);
  expect(shortUrl).toBe(returnedShortUrl);
  expect(qrCodes.png).toBe(returnedQrCodes.png);
  expect(qrCodes.svg).toBe(returnedQrCodes.svg);
  expect(qrCodes.pdf).toBe(returnedQrCodes.pdf);
  expect(productId).toBe("9");
  expect(accountId).toBe(payload.accountId);
  expect(clicks).toBe(0);
  expect(domain).toBe(returnedDomain);
  expect(linkData.page_uuid).toBe(fakeUuid);
});

test("Should return valid qrCodes when a user downloads landing pages QR Code files", async ({
    request, auth
  }) => {
    const response = await request.get(`lpCodes`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.landingPages.signedToken}`,
        "Content-Type": "application/json",
      },
      params: {
        key: apiKey,
        lpId: fakeUuid,
      },
    });

    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    console.log(`FAKE UUID: ${fakeUuid}`);
    
    expect(response.status()).toBe(200);

    const resJson = await parseResponse(response);

    expect(resJson.success).toBe(true);
    expect(resJson.data).toBeDefined();

    const { longUrl, shortUrl, clicks, productId, qrCodes, page_uuid, traffic, } = resJson.data[0];

    expect(longUrl).toBe(testLongUrl);
    expect(shortUrl).toBe(returnedShortUrl);
    expect(clicks).toBe(0);
    expect(productId).toBe("9");
    expect(qrCodes.png).toBe(returnedQrCodes.png);
    expect(qrCodes.svg).toBe(returnedQrCodes.svg);
    expect(qrCodes.pdf).toBe(returnedQrCodes.pdf);
    expect(page_uuid).toBe(returnedPageUuid);
    expect(traffic).toEqual([]);
  });

test("Should return success message for updating codes when a user edits their landing page", async ({
    request, auth
  }) => {
    const response = await request.get(`domainUpdate`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.landingPages.signedToken}`,
        ContentType: "application/json",
      },
      params: {
        key: apiKey,
        accountId: testAccountId,
      },
    });
    console.log("RES STATUS CODE: ", response.status());
    console.log("RES STATUS TEXT: ", response.statusText());
    console.log(`FAKE UUID: ${fakeUuid}`);
    
    expect(response.status()).toBe(200);

    const resJson = await parseResponse(response);

    expect(resJson.success).toBe(true);
    expect(resJson.data).toBeDefined();

    const { message, count, } = resJson.data;

    expect(message).toBe(`Updating ${count} codes`);
  });

  async function parseResponse(response: APIResponse) {
    let resJson: any;
    try {
      resJson = await response.json();
    } catch (error) {
      throw new Error(`Failed to parse JSON response: ${error.message}`);
    }

    return resJson;
  }