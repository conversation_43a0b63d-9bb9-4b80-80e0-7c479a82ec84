// Playwright
import { test } from "@fixtures/base.api";
import { expect } from "@playwright/test";
// Configs
import config from "@configs/env/config";
// Firebase
import { startFirebaseDb } from "@utils/helpers/startFirebaseDb";
import { doc, getDoc, writeBatch } from "firebase/firestore";
// BM Recipient Scenarios
import brandedMagazinesBadAddressScenarios from "@test-data/cyclr-recipient-scenarios/mailing-addresses/branded-magazines/bm-recipient.bad-addresses.json";
import brandedMagazinesGoodAddressScenarios from "@test-data/cyclr-recipient-scenarios/mailing-addresses/branded-magazines/bm-recipient.good-and-exclusive-addresses.json";
import brandedMagazinesGoodAndNotExclusiveAddressScenarios from "@test-data/cyclr-recipient-scenarios/mailing-addresses/branded-magazines/bm-recipient.good-not-exclusive-addresses.json";
import brandedMagazinesIncompleteAddressScenarios from "@test-data/cyclr-recipient-scenarios/mailing-addresses/branded-magazines/bm-recipient.incomplete-addresses.json";
import brandedMagazinesSalutationMailingAddressScenarios from "@test-data/cyclr-recipient-scenarios/mailing-addresses/branded-magazines/bm-recipient.will-send-with-missing-salutations.json";
// PC Recipient Scenarios
import postcardsBadAddressScenarios from "@test-data/cyclr-recipient-scenarios/mailing-addresses/postcards/pc-recipient.bad-addresses.json";
import postcardsGoodAddressScenarios from "@test-data/cyclr-recipient-scenarios/mailing-addresses/postcards/pc-recipient.good-and-exclusive-addresses.json";
import postcardsGoodAndNotExclusiveAddressScenarios from "@test-data/cyclr-recipient-scenarios/mailing-addresses/postcards/pc-recipient.good-not-exclusive-addresses.json";
import postcardsIncompleteAddressScenarios from "@test-data/cyclr-recipient-scenarios/mailing-addresses/postcards/pc-recipient.incomplete-addresses.json";
import postcardsSalutationMailingAddressScenarios from "@test-data/cyclr-recipient-scenarios/mailing-addresses/postcards/pc-recipient.will-send-with-missing-salutations.json";
// Digital Recipient Scenarios
import invalidEmailScenarios from "@test-data/cyclr-recipient-scenarios/email-addresses/digital-recipient.invalid-emails.json";
import validEmailScenarios from "@test-data/cyclr-recipient-scenarios/email-addresses/digital-recipient.valid-emails.json";
// Models
import { CyclrMockSource, Scenario } from "@models/cyclr-interfaces";
// Helpers & Utilities
import { wait } from "@utils/helpers/wait";
import {
  pollAndCompareAddressDeliverability,
  pollAndCompareEmailDeliverability,
} from "@utils/recipients/pollFirestoreAndCompareDeliverability";
import { createCyclrMockRecipient } from "@utils/cyclr/createMockCyclrRecipient";
import { validateRecipientsByCampaignResponse } from "@utils/recipients/validateRecipientsByCampaignResponse";
import { validateRecipientsResponse } from "@utils/recipients/validateRecipientsResponse";
import { deleteRecipientGroup } from "@utils/recipients/deleteRecipientGroupRecursively";
import { deleteRecipient } from "@utils/recipients/deleteRecipientRecursively";

const apiKey = config.CYCLR_TEST_API_KEY;
const key = config.RM_TITAN_API_KEY;
const testAccountId = config.RMC_TEST_ACCOUNT_ID || "4378522";
const postcardGroupId = process.env.POSTCARD_GROUP_ID;
const brandedMagazineGroupId = process.env.BM_GROUP_ID;
const digitalGroupId = process.env.DIGITAL_GROUP_ID;
const verbose = process.env.VERBOSE === "true";

const brandedMagazineScenarios = [
  ...(brandedMagazinesBadAddressScenarios as Scenario[]),
  ...(brandedMagazinesGoodAddressScenarios as Scenario[]),
  ...(brandedMagazinesGoodAndNotExclusiveAddressScenarios as Scenario[]),
  ...(brandedMagazinesIncompleteAddressScenarios as Scenario[]),
  ...(brandedMagazinesSalutationMailingAddressScenarios as Scenario[]),
];
const postcardScenarios = [
  ...(postcardsBadAddressScenarios as Scenario[]),
  ...(postcardsGoodAddressScenarios as Scenario[]),
  ...(postcardsGoodAndNotExclusiveAddressScenarios as Scenario[]),
  ...(postcardsIncompleteAddressScenarios as Scenario[]),
  ...(postcardsSalutationMailingAddressScenarios as Scenario[]),
];
const digitalScenarios = [
  ...(invalidEmailScenarios as Scenario[]),
  ...(validEmailScenarios as Scenario[]),
];

const db = startFirebaseDb();

const brandedMagazineDeliverabilityTests: {
  scenario: Scenario;
  recipientId: string;
}[] = [];
const postcardDeliverabilityTests: {
  scenario: Scenario;
  recipientId: string;
}[] = [];
const digitalDeliverabilityTests: {
  scenario: Scenario;
  recipientId: string;
}[] = [];

test.afterAll(async () => {
  if (process.env.SKIP_CLEANUP === "true") { 
    console.log("⏭️  Skipping cleanup (SKIP_CLEANUP=true)");
    return;
  } else {
    console.log("🧹 Cleaning up recipients and recipient groups...");

    const batch = writeBatch(db);

    // Clean up recipients and recipient groups
    if (postcardGroupId) {
      console.log("Deleting Postcard Group:", postcardGroupId);
      await deleteRecipientGroup(testAccountId, postcardGroupId);
    }
    if (brandedMagazineGroupId) {
      console.log("Deleting Branded Magazine Group:", brandedMagazineGroupId);
      await deleteRecipientGroup(testAccountId, brandedMagazineGroupId);
    }
    if (digitalGroupId) {
      console.log("Deleting Digital Group:", digitalGroupId);
      // const digitalGroupPath = `Account/${testAccountId}/RecipientGroups/${digitalGroupId}`;
      // await deleteDocumentRecursively(digitalGroupPath);
      await deleteRecipientGroup(testAccountId, digitalGroupId);
    }
    console.log("✅ Recipient Group Cleanup completed.");

    // Clean up recipients
    console.log("🧹 Cleaning up recipients...");
    for (const { recipientId } of [
      ...brandedMagazineDeliverabilityTests,
      ...postcardDeliverabilityTests,
      ...digitalDeliverabilityTests,
    ]) {
      console.log("Deleting Recipient:", recipientId);
      await deleteRecipient(testAccountId, recipientId);
    }
    await batch.commit();
    console.log("✅ Recipient Cleanup completed.");
  }
});

test.describe("POST /api/v1/recipients via Cyclr payloads", async () => {
  const source: CyclrMockSource = "QA Ninja";

  // Postcard Tests
  for (const scenario of postcardScenarios) {
    test(`Post Recipient - Postcards - ${scenario.scenarioName}`, async ({
      request,
    }) => {
      const recipientData = createCyclrMockRecipient(
        scenario,
        postcardGroupId,
        source
      );
      console.log("Postcards Scenario:", scenario.scenarioName);
      const response = await request.post(`recipients`, {
        headers: {
          "X-API-Key": apiKey,
          "Content-Type": "application/json",
        },
        data: recipientData,
      });

      // Status code assertion
      expect(response.status()).toBe(201);

      const body = await response.json();

      // Basic response structure
      expect(body).toHaveProperty("status", 201);
      expect(body).toHaveProperty("message", "Recipient created successfully.");
      expect(body).toHaveProperty("data");

      // Required fields
      expect(body.data).toHaveProperty("id");
      expect(body.data).toHaveProperty("accountId");
      expect(body.data).toHaveProperty("recipientGroupIds");
      expect(body.data).toHaveProperty("isActive", true);
      expect(body.data).toHaveProperty("createdAt");
      expect(body.data).toHaveProperty("updatedAt");
      expect(body.data).toHaveProperty("mailingsPaused", false);

      // Deep assertion for recipient fields
      console.log(
        "Validating recipient fields for Postcards scenario:",
        scenario.scenarioName
      );
      deepAssertResponseRecipientFields(body.data, scenario);

      brandedMagazineDeliverabilityTests.push({
        scenario,
        recipientId: body.data.id,
      });
    });
  }

  // Branded Magazine Tests
  for (const scenario of brandedMagazineScenarios) {
    test(`Post Recipient - Branded Magazines - ${scenario.scenarioName}`, async ({
      request,
    }) => {
      const recipientData = createCyclrMockRecipient(
        scenario,
        brandedMagazineGroupId,
        source
      );
      const response = await request.post(`recipients`, {
        headers: {
          "X-API-Key": apiKey,
          "Content-Type": "application/json",
        },
        data: recipientData,
      });

      expect(response.status()).toBe(201);

      const body = await response.json();

      // Basic response structure
      expect(body).toHaveProperty("status", 201);
      expect(body).toHaveProperty("message", "Recipient created successfully.");
      expect(body).toHaveProperty("data");

      // Required fields
      expect(body.data).toHaveProperty("id");
      expect(body.data).toHaveProperty("accountId");
      expect(body.data).toHaveProperty("recipientGroupIds");
      expect(body.data).toHaveProperty("isActive", true);
      expect(body.data).toHaveProperty("createdAt");
      expect(body.data).toHaveProperty("updatedAt");
      expect(body.data).toHaveProperty("mailingsPaused", false);
      expect(body.data).toHaveProperty("source", source);

      // Deep assertion for recipient fields
      console.log(
        "Validating recipient fields for Branded Magazines scenario:",
        scenario.scenarioName
      );
      deepAssertResponseRecipientFields(body.data, scenario);

      postcardDeliverabilityTests.push({ scenario, recipientId: body.data.id });
    });
  }

  // Digital Tests
  for (const scenario of digitalScenarios) {
    test(`Post Recipient - Digital - ${scenario.scenarioName}`, async ({
      request,
    }) => {
      const recipientData = createCyclrMockRecipient(
        scenario,
        digitalGroupId,
        source
      );
      console.log("Digital Scenario:", scenario.scenarioName);
      const response = await request.post(`recipients`, {
        headers: {
          "X-API-Key": apiKey,
          "Content-Type": "application/json",
        },
        data: recipientData,
      });

      expect(response.status()).toBe(201);

      const body = await response.json();

      // Basic response structure
      expect(body).toHaveProperty("status", 201);
      expect(body).toHaveProperty("message", "Recipient created successfully.");
      expect(body).toHaveProperty("data");

      // Required fields
      expect(body.data).toHaveProperty("id");
      expect(body.data).toHaveProperty("accountId");
      expect(body.data).toHaveProperty("recipientGroupIds");
      expect(body.data).toHaveProperty("isActive", true);
      expect(body.data).toHaveProperty("createdAt");
      expect(body.data).toHaveProperty("updatedAt");
      expect(body.data).toHaveProperty("mailingsPaused", false);
      expect(body.data).toHaveProperty("source", source);

      // Deep assertion for recipient fields
      console.log(
        "Validating recipient fields for Digital scenario:",
        scenario.scenarioName
      );
      deepAssertResponseRecipientFields(body.data, scenario);

      digitalDeliverabilityTests.push({ scenario, recipientId: body.data.id });
    });
  }

  test("Should validate print and digital recipient deliverability calculations", async () => {
    await wait(
      5000,
      "Giving Cloud Functions some seconds to process recipients..."
    );

    for (const {
      scenario,
      recipientId,
    } of brandedMagazineDeliverabilityTests) {
      await test.step(`Validate Branded Magazines Deliverability - ${scenario.scenarioName}`, async () => {
        const storePath = `Account/${testAccountId}/Recipients/${recipientId}`;
        const testRecipientDocRef = doc(db, storePath);

        const snapshot = await getDoc(testRecipientDocRef);

        await pollAndCompareAddressDeliverability(
          scenario.scenarioName,
          storePath,
          testRecipientDocRef,
          scenario["expectedDeliverability"],
          20
        );
      });
    }

    for (const { scenario, recipientId } of postcardDeliverabilityTests) {
      await test.step(`Validate Postcards Deliverability - ${scenario.scenarioName}`, async () => {
        const storePath = `Account/${testAccountId}/Recipients/${recipientId}`;
        const testRecipientDocRef = doc(db, storePath);

        await pollAndCompareAddressDeliverability(
          scenario.scenarioName,
          storePath,
          testRecipientDocRef,
          scenario["expectedDeliverability"],
          20
        );
      });
    }

    for (const { scenario, recipientId } of digitalDeliverabilityTests) {
      await test.step(`Validate Digital Deliverability - ${scenario.scenarioName}`, async () => {
        const storePath = `Account/${testAccountId}/Recipients/${recipientId}`;
        const testRecipientDocRef = doc(db, storePath);

        await pollAndCompareEmailDeliverability(
          scenario.scenarioName,
          storePath,
          testRecipientDocRef,
          scenario["expectedDeliverability"],
          20
        );
      });
    }
  });

  test("should verify cyclr synced postcards recipients return to /recipientsByCampaign endpoint", async ({
    auth,
    titanBaseApi,
  }) => {
    const response = await titanBaseApi.get(`recipientsByCampaign`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        "Content-Type": "application/json",
      },
      params: {
        accountId: testAccountId,
        method: "address",
        productId: 8,
        campaignId: "133448",
        key,
      },
    });

    expect(response.status()).toBe(200);
    const apiResponse = await response.json();

    validateRecipientsByCampaignResponse(apiResponse);
  });

  test("should verify cyclr synced branded magazines recipients return to /recipientsByCampaign endpoint", async ({
    auth,
    titanBaseApi,
  }) => {
    const response = await titanBaseApi.get(`recipientsByCampaign`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        "Content-Type": "application/json",
      },
      params: {
        accountId: testAccountId,
        method: "address",
        productId: 12,
        campaignId: "133247",
        key,
      },
    });

    expect(response.status()).toBe(200);
    const apiResponse = await response.json();

    validateRecipientsByCampaignResponse(apiResponse);

    expect(apiResponse.count).toBeGreaterThan(0);
    expect(apiResponse.recipientGroup.length).toBeGreaterThan(0);
  });

  test("should verify cyclr synced digital recipients return to /recipients endpoint", async ({
    auth,
    titanBaseApi,
  }) => {
    const response = await titanBaseApi.get(`recipients`, {
      headers: {
        Authorization: `Bearer ${auth.tokens.rmc.signedToken}`,
        "Content-Type": "application/json",
      },
      params: {
        accountId: testAccountId,
        method: "email",
        productId: 2,
        key,
      },
    });

    expect(response.status()).toBe(200);
    const apiResponse = await response.json();

    validateRecipientsResponse(apiResponse);

    expect(apiResponse.count).toBeGreaterThan(0);
    expect(apiResponse.recipients.length).toBeGreaterThan(0);
  });
});

/**
 * Asserts that the recipient fields in the response body match the expected values from the scenario.
 * @param bodyData - The response body data containing the recipient information.
 * @param scenario - The scenario object containing the expected recipient data.
 */
function deepAssertResponseRecipientFields(bodyData: any, scenario: Scenario) {
  // Name validation
  if ("firstName" in scenario.recipient) {
    if (verbose) {
      console.log("Validating firstName:", scenario.recipient.firstName);
    }
    expect
      .soft(bodyData.name)
      .toHaveProperty("firstName", scenario.recipient.firstName || "");
  } else if (verbose) {
    console.log(
      "No firstName provided in scenario, defaulting to empty string."
    );
  }

  if ("lastName" in scenario.recipient) {
    if (verbose) {
      console.log("Validating lastName:", scenario.recipient.lastName);
    }
    expect
      .soft(bodyData.name)
      .toHaveProperty("lastName", scenario.recipient.lastName || "");
  } else if (verbose) {
    console.log(
      "No lastName provided in scenario, defaulting to empty string."
    );
  }

  if ("middle" in scenario.recipient) {
    if (verbose) {
      console.log("Validating middle name:", scenario.recipient.middle);
    }
    expect
      .soft(bodyData.name)
      .toHaveProperty("middle", scenario.recipient.middle || "");
  } else if (verbose) {
    console.log(
      "No middle name provided in scenario, defaulting to empty string."
    );
  }

  if ("nickname" in scenario.recipient) {
    if (verbose) {
      console.log("Validating nickname:", scenario.recipient.nickname);
    }
    expect
      .soft(bodyData.name)
      .toHaveProperty("nickname", scenario.recipient.nickname || "");
  } else if (verbose) {
    console.log(
      "No nickname provided in scenario, defaulting to empty string."
    );
  }

  if ("prefix" in scenario.recipient) {
    if (verbose) {
      console.log("Validating prefix:", scenario.recipient.prefix);
    }
    expect
      .soft(bodyData.name)
      .toHaveProperty("prefix", scenario.recipient.prefix || "");
  } else if (verbose) {
    console.log("No prefix provided in scenario, defaulting to empty string.");
  }

  if ("suffix" in scenario.recipient) {
    if (verbose) {
      console.log("Validating suffix:", scenario.recipient.suffix);
    }
    expect
      .soft(bodyData.name)
      .toHaveProperty("suffix", scenario.recipient.suffix || "");
  } else if (verbose) {
    console.log("No suffix provided in scenario, defaulting to empty string.");
  }

  // Address validation if present
  if (scenario.recipient.addresses) {
    if (verbose) {
      console.log("Validating addresses:", scenario.recipient.addresses);
    }
    expect
      .soft(
        bodyData,
        "Response is missing 'userEnteredMailingAddresses' property"
      )
      .toHaveProperty("userEnteredMailingAddresses");
    scenario.recipient.addresses.forEach((address, index) => {
      expect.soft(bodyData.userEnteredMailingAddresses[index]).toMatchObject({
        address1: address.address1,
        address2: address.address2 || "",
        city: address.city,
        state: address.state,
        postalCode: address.postalCode,
      });
    });
  } else if (verbose) {
    console.log(
      "No addresses provided in scenario, skipping address validation."
    );
  }

  // Email Addresses validation
  if (scenario.recipient.emails) {
    if (verbose) {
      console.log("Validating email addresses:", scenario.recipient.emails);
    }
    expect
      .soft(bodyData, "Response is missing 'emailAddresses' property")
      .toHaveProperty("emailAddresses");
    const emails = Object.entries(scenario.recipient.emails);
    emails.forEach(([label, email], index) => {
      expect.soft(bodyData.emailAddresses[index]).toEqual(
        expect.objectContaining({
          email,
          label,
          primary: index === 0,
        })
      );
    });
  } else if (verbose) {
    console.log(
      "No email addresses provided in scenario, skipping email validation."
    );
  }

  // Phone Numbers validation
  if (scenario.recipient.phones) {
    if (verbose) {
      console.log("Validating phone numbers:", scenario.recipient.phones);
    }
    expect
      .soft(bodyData, "Response is missing 'phoneNumbers' property")
      .toHaveProperty("phoneNumbers");
    const phoneEntries = Object.entries(scenario.recipient.phones);
    phoneEntries.forEach(([label, data], index) => {
      const expectedNumber = typeof data === "string" ? data : data.number;
      const expectedExtension =
        typeof data === "string" ? "" : data.extension || "";
      const expectedPrimary =
        typeof data === "string" ? index === 0 : data.primary || index === 0;
      expect.soft(bodyData.phoneNumbers[index]).toEqual(
        expect.objectContaining({
          phoneNumber: expectedNumber,
          phoneExtension: expectedExtension,
          label,
          primary: expectedPrimary,
        })
      );
    });
  } else if (verbose) {
    console.log(
      "No phone numbers provided in scenario, skipping phone validation."
    );
  }

  // Salutation validation
  if (scenario.recipient.salutation) {
    if (verbose) {
      console.log("Validating salutation:", scenario.recipient.salutation);
    }
    expect.soft(bodyData, "Response is missing 'salutation' property")
      .toHaveProperty;
    expect
      .soft(bodyData, "Response is missing 'salutation' property")
      .toHaveProperty("salutation");
    expect.soft(bodyData.salutation).toEqual(
      expect.objectContaining({
        letterSalutation: scenario["expectedSalutations"].letterSalutation,
        mailingSalutation: scenario["expectedSalutations"].mailingSalutation,
        company: scenario.recipient.salutation.company || "",
        jobTitle: scenario.recipient.salutation.jobTitle || "",
      })
    );
  }

  // Notes validation
  if (scenario.recipient.notes) {
    if (verbose) {
      console.log("Validating notes:", scenario.recipient.notes);
    }
    expect
      .soft(bodyData, "Response is missing 'notes' property")
      .toHaveProperty("notes");
    expect.soft(bodyData.notes).toHaveLength(scenario.recipient.notes.length);
    scenario.recipient.notes.forEach((note, index) => {
      expect(bodyData.notes[index]).toMatchObject({
        value: note.value,
      });
    });
  } else if (verbose) {
    console.log("No notes provided in scenario, skipping notes validation.");
  }

  // Significant Dates validation
  if (scenario.recipient.dates) {
    if (verbose) {
      console.log("Validating significant dates:", scenario.recipient.dates);
    }
    expect(
      bodyData,
      "Response is missing 'significantDates' property"
    ).toHaveProperty("significantDates");
    expect
      .soft(bodyData.significantDates)
      .toHaveLength(scenario.recipient.dates.length);
    scenario.recipient.dates.forEach((date, index) => {
      expect.soft(bodyData.significantDates[index]).toMatchObject({
        label: date.label,
        date: date.date,
      });
    });
  } else if (verbose) {
    console.log(
      "No significant dates provided in scenario, skipping dates validation."
    );
  }

  // Social Media Accounts validation
  if (scenario.recipient.social) {
    if (verbose) {
      console.log(
        "Validating social media accounts:",
        scenario.recipient.social
      );
    }
    expect
      .soft(bodyData, "Response is missing 'socialMediaAccounts' property")
      .toHaveProperty("significantDates");
    expect
      .soft(bodyData.socialMediaAccounts)
      .toHaveLength(scenario.recipient.social.length);
    scenario.recipient.social.forEach((social, index) => {
      expect.soft(bodyData.socialMediaAccounts[index]).toMatchObject(social);
    });
  } else if (verbose) {
    console.log(
      "No social media accounts provided in scenario, skipping social validation."
    );
  }

  // Websites validation
  if (scenario.recipient.websites) {
    if (verbose) {
      console.log("Validating websites:", scenario.recipient.websites);
    }
    expect.soft(bodyData, "Response is missing 'websites' property")
      .toHaveProperty;
    expect(bodyData, "Response is missing 'websites' property").toHaveProperty(
      "websites"
    );
    expect
      .soft(bodyData.websites)
      .toHaveLength(scenario.recipient.websites.length);
    scenario.recipient.websites.forEach((web, index) => {
      expect.soft(bodyData.websites[index]).toMatchObject({
        label: web.label,
        url: web.url,
      });
    });
  } else if (verbose) {
    console.log(
      "No websites provided in scenario, skipping websites validation."
    );
  }

  // External IDs validation
  if (scenario.recipient.externalIds) {
    if (verbose) {
      console.log("Validating external IDs:", scenario.recipient.externalIds);
    }
    expect(
      bodyData,
      "Response is missing 'externalIds' property"
    ).toHaveProperty("externalIds");
    expect
      .soft(bodyData.externalIds)
      .toHaveLength(scenario.recipient.externalIds.length);
    scenario.recipient.externalIds.forEach((eid, index) => {
      expect.soft(bodyData.externalIds[index]).toMatchObject(eid);
    });
  } else if (verbose) {
    console.log(
      "No external IDs provided in scenario, skipping external IDs validation."
    );
  }
}
