import { test as setup, expect } from '@playwright/test';
import { seedRecipientGroup } from '@utils/recipients/seedRecipientGroup';
import { RecipientGroupData } from '@models/interfaces';
import { wait } from '@utils/helpers/wait';

setup('Seed recipient Groups and product assignments', async () => {
  
  // Seed groups once for all tests
  console.log('🌱 Seeding Test Groups');
  
  const accountId = '4378522';
  const postcardGroup: RecipientGroupData = {
    name: 'Postcard Group',
    accountId,
    productGroup: 'postcards',
    campaignId: '133448'
  }
  const brandedMagazinesGroup: RecipientGroupData = {
    name: 'Branded Magz Group',
    accountId,
    productGroup: 'branded-magazines',
    campaignId: '133247'
  }
  const digitalProductsGroup: RecipientGroupData = {
    name: 'Digital Products Group',
    accountId,
    productGroup: 'digital',
  }
  
  const [pcGroup, bmGroup, digitalGroup] = await Promise.all([
    seedRecipientGroup(postcardGroup),
    seedRecipientGroup(brandedMagazinesGroup),
    seedRecipientGroup(digitalProductsGroup)
  ]);

  // Store IDs for tests
  process.env.POSTCARD_GROUP_ID = pcGroup.recipientGroupId;
  process.env.BM_GROUP_ID = bmGroup.recipientGroupId;
  process.env.DIGITAL_GROUP_ID = digitalGroup.recipientGroupId;
});
