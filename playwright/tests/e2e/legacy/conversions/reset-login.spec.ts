import { expect, test, <PERSON>, <PERSON>rowser } from "@playwright/test";
import * as path from 'path';
import crmUsers from '@configs/user-auth/crm-users.json';
import contacts from '@test-data/legacy/conversions/contacts.json';

let page: Page;

test.describe('resets login for conversions', () => {

  test.beforeAll(async ({ browser }) => {

    page = await browser.newPage();
    await page.goto('https://crm.remindermedia.net/account/login');
    await page.getByPlaceholder('Username').click();
    await page.getByPlaceholder('Username').fill(crmUsers[0].tyler.username);
    await page.getByPlaceholder('Password').click();
    await page.getByPlaceholder('Password').fill(crmUsers[0].tyler.password);
    await page.getByRole('button', { name: 'Log On' }).click();
  });

  test.afterAll(async () => {
    await page.close();
  });

  contacts.forEach(contactId => {
    test(`should reset login for contact ID ${contactId}`, async () => {
      await page.goto(`https://crm.remindermedia.net/dg/plugins/remindermedia/login_rmconnect.php?account_id=${contactId}&redirect_route_name=account-bound.tear-out-cards.index`);
      await expect(page.locator('body')).toContainText('Tear Out Card 1');
      console.log(`Finished contact id: ${contactId}`);
    });
  });
});
