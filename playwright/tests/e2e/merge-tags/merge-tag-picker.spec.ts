import { test, expect } from "@playwright/test";

test.describe("Merge Tag Picker - E2E Tests", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to a page with merge tag picker (adjust URL as needed)
    await page.goto("/templates/email/create");
    
    // Wait for the page to load
    await page.waitForLoadState("networkidle");
    
    // Ensure feature flag is enabled for this test
    await page.evaluate(() => {
      window.localStorage.setItem('test-feature-flag-enhanced-merge-tags', 'true');
    });
  });

  test("should display merge tag toolbar button in email editor", async ({ page }) => {
    // Look for the merge tag toolbar button
    const mergeTagButton = page.locator('[data-testid="merge-tag-toolbar-button"]').or(
      page.getByRole("button", { name: /insert merge tag/i })
    );
    
    await expect(mergeTagButton).toBeVisible();
    
    // Verify button has proper icon
    const tagIcon = mergeTagButton.locator('svg');
    await expect(tagIcon).toBeVisible();
  });

  test("should open merge tag picker when toolbar button is clicked", async ({ page }) => {
    // Click the merge tag toolbar button
    const mergeTagButton = page.getByRole("button", { name: /insert merge tag/i });
    await mergeTagButton.click();
    
    // Verify the picker modal opens
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.getByText("Merge Tags")).toBeVisible();
    
    // Verify picker components are present
    await expect(page.getByPlaceholder("Search merge tags...")).toBeVisible();
    await expect(page.getByText("Recipient")).toBeVisible(); // Category
    await expect(page.getByText("Campaign")).toBeVisible(); // Category
  });

  test("should search and filter merge tags", async ({ page }) => {
    // Open the picker
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Search for a specific tag
    const searchInput = page.getByPlaceholder("Search merge tags...");
    await searchInput.fill("firstName");
    
    // Wait for search results
    await page.waitForTimeout(500); // Account for debounce
    
    // Verify search results show only matching tags
    await expect(page.getByText("First Name")).toBeVisible();
    
    // Verify non-matching tags are filtered out
    await expect(page.getByText("Last Name")).not.toBeVisible();
    
    // Clear search and verify all tags return
    await searchInput.clear();
    await page.waitForTimeout(500);
    await expect(page.getByText("Last Name")).toBeVisible();
  });

  test("should display tag categories in accordion format", async ({ page }) => {
    // Open the picker
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Verify category headers are visible
    const recipientCategory = page.getByRole("button", { name: /recipient/i });
    const campaignCategory = page.getByRole("button", { name: /campaign/i });
    
    await expect(recipientCategory).toBeVisible();
    await expect(campaignCategory).toBeVisible();
    
    // Test collapsing/expanding categories
    await recipientCategory.click();
    
    // Verify category content is hidden/shown
    // Note: Adjust selectors based on actual implementation
    const recipientTags = page.locator('[data-category="recipient"]');
    await expect(recipientTags.first()).not.toBeVisible();
    
    // Expand again
    await recipientCategory.click();
    await expect(recipientTags.first()).toBeVisible();
  });

  test("should show sample data preview for each tag", async ({ page }) => {
    // Open the picker
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Look for a specific tag with sample data
    const firstNameTag = page.getByText("First Name").first();
    await expect(firstNameTag).toBeVisible();
    
    // Verify sample data is displayed
    const sampleData = page.locator('[data-testid="tag-sample"]').or(
      page.locator('.tag-sample')
    );
    await expect(sampleData.first()).toBeVisible();
  });

  test("should copy tag to clipboard when clicked", async ({ page }) => {
    // Grant clipboard permissions
    await page.context().grantPermissions(['clipboard-read', 'clipboard-write']);
    
    // Open the picker
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Click on a merge tag
    const firstNameTag = page.getByText("First Name").first();
    await firstNameTag.click();
    
    // Verify copy feedback is shown
    await expect(page.getByText("Copied!")).toBeVisible({ timeout: 2000 });
    
    // Verify clipboard content
    const clipboardContent = await page.evaluate(() => {
      return navigator.clipboard.readText();
    });
    
    expect(clipboardContent).toContain("{{firstName}}");
  });

  test("should filter tags by template type", async ({ page }) => {
    // Navigate to SMS template editor (adjust URL as needed)
    await page.goto("/templates/sms/create");
    await page.waitForLoadState("networkidle");
    
    // Open the picker
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Verify only SMS-compatible tags are shown
    // This test depends on having tags with different template type compatibility
    const tags = page.locator('.tag-item');
    const tagCount = await tags.count();
    
    // Should have fewer tags than email template (this is implementation-dependent)
    expect(tagCount).toBeGreaterThan(0);
    
    // Verify template type indicator
    await expect(page.getByText("SMS Template")).toBeVisible();
  });

  test("should close picker when close button is clicked", async ({ page }) => {
    // Open the picker
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Click close button
    const closeButton = page.getByRole("button", { name: /close/i });
    await closeButton.click();
    
    // Verify modal is closed
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test("should close picker when clicking outside modal", async ({ page }) => {
    // Open the picker
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Click outside the modal (on backdrop)
    await page.locator('.modal-backdrop').click({ position: { x: 10, y: 10 } });
    
    // Verify modal is closed
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test("should support keyboard navigation", async ({ page }) => {
    // Open the picker
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Focus search input
    const searchInput = page.getByPlaceholder("Search merge tags...");
    await searchInput.focus();
    
    // Use Tab to navigate through tags
    await page.keyboard.press("Tab");
    await page.keyboard.press("Tab");
    
    // Use Enter to select a tag
    await page.keyboard.press("Enter");
    
    // Verify copy feedback appears
    await expect(page.getByText("Copied!")).toBeVisible({ timeout: 2000 });
  });

  test("should handle empty search results gracefully", async ({ page }) => {
    // Open the picker
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Search for something that doesn't exist
    const searchInput = page.getByPlaceholder("Search merge tags...");
    await searchInput.fill("nonexistenttagxyz");
    await page.waitForTimeout(500);
    
    // Verify "no results" message is shown
    await expect(page.getByText("No merge tags found")).toBeVisible();
    
    // Verify helpful message
    await expect(page.getByText("Try a different search term")).toBeVisible();
  });

  test("should show loading state while tags are being fetched", async ({ page }) => {
    // Intercept the API call to add delay
    await page.route('**/mergeTags**', async route => {
      // Add a delay to simulate slow loading
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });
    
    // Open the picker
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    
    // Verify loading state is shown
    await expect(page.locator('.loading-spinner')).toBeVisible();
    await expect(page.getByText("Loading merge tags...")).toBeVisible();
    
    // Wait for loading to complete
    await expect(page.locator('.loading-spinner')).not.toBeVisible({ timeout: 5000 });
    await expect(page.getByText("Recipient")).toBeVisible();
  });

  test("should handle API errors gracefully", async ({ page }) => {
    // Mock API failure
    await page.route('**/mergeTags**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });
    
    // Open the picker
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    
    // Verify error state is shown
    await expect(page.getByText("Failed to load merge tags")).toBeVisible();
    await expect(page.getByRole("button", { name: /retry/i })).toBeVisible();
    
    // Test retry functionality
    await page.route('**/mergeTags**', route => route.continue());
    await page.getByRole("button", { name: /retry/i }).click();
    
    // Verify tags load after retry
    await expect(page.getByText("Recipient")).toBeVisible({ timeout: 5000 });
  });
});
