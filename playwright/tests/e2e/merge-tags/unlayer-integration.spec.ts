import { test, expect } from "@playwright/test";

test.describe("Unlayer Merge Tag Integration - E2E Tests", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to email template editor with Unlayer
    await page.goto("/templates/email/editor");
    
    // Wait for the page and Unlayer to load
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000); // Allow Unlayer to initialize
    
    // Ensure feature flag is enabled
    await page.evaluate(() => {
      window.localStorage.setItem('test-feature-flag-enhanced-merge-tags', 'true');
    });
  });

  test("should insert merge tag into Unlayer editor", async ({ page }) => {
    // Wait for Unlayer editor to be ready
    await expect(page.locator('#unlayer-editor, .unlayer-editor')).toBeVisible({ timeout: 10000 });
    
    // Click merge tag toolbar button
    const mergeTagButton = page.getByRole("button", { name: /insert merge tag/i });
    await mergeTagButton.click();
    
    // Wait for picker modal
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Select a merge tag (e.g., firstName)
    const firstNameTag = page.getByText("First Name").first();
    await firstNameTag.click();
    
    // Close picker (if it doesn't auto-close)
    const isPickerVisible = await page.locator('[role="dialog"]').isVisible();
    if (isPickerVisible) {
      await page.getByRole("button", { name: /close/i }).click();
    }
    
    // Verify tag was inserted into Unlayer editor
    // Note: This depends on how Unlayer exposes the content
    const editorContent = await page.locator('.unlayer-editor iframe').contentFrame();
    if (editorContent) {
      await expect(editorContent.locator('text="{{firstName}}"')).toBeVisible();
    }
  });

  test("should render preview with resolved merge tag values", async ({ page }) => {
    // Insert a merge tag first
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await page.getByText("First Name").first().click();
    
    // Look for preview button/mode
    const previewButton = page.getByRole("button", { name: /preview/i });
    if (await previewButton.isVisible()) {
      await previewButton.click();
      
      // In preview mode, merge tags should be resolved with sample data
      await expect(page.locator('text="John"')).toBeVisible(); // Sample firstName value
    }
  });

  test("should support fallback syntax in Unlayer", async ({ page }) => {
    // Manually insert a tag with fallback syntax
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Use the search to find a tag
    const searchInput = page.getByPlaceholder("Search merge tags...");
    await searchInput.fill("firstName");
    await page.waitForTimeout(500);
    
    // Click the tag
    await page.getByText("First Name").first().click();
    
    // Verify the tag format supports fallback
    // This test might need to be adapted based on actual implementation
    const editorContent = await page.locator('.unlayer-editor iframe').contentFrame();
    if (editorContent) {
      // Check if fallback syntax is supported
      await expect(editorContent.locator('text*="{{firstName"')).toBeVisible();
    }
  });

  test("should handle multiple merge tags in same template", async ({ page }) => {
    // Insert multiple merge tags
    const tagsToInsert = ["First Name", "Last Name", "Email"];
    
    for (const tagName of tagsToInsert) {
      // Open picker
      await page.getByRole("button", { name: /insert merge tag/i }).click();
      await expect(page.locator('[role="dialog"]')).toBeVisible();
      
      // Search and select tag
      const searchInput = page.getByPlaceholder("Search merge tags...");
      await searchInput.fill(tagName);
      await page.waitForTimeout(300);
      
      await page.getByText(tagName).first().click();
      
      // Wait a moment between insertions
      await page.waitForTimeout(500);
    }
    
    // Verify all tags are present in the editor
    const editorContent = await page.locator('.unlayer-editor iframe').contentFrame();
    if (editorContent) {
      await expect(editorContent.locator('text="{{firstName}}"')).toBeVisible();
      await expect(editorContent.locator('text="{{lastName}}"')).toBeVisible();
      await expect(editorContent.locator('text="{{emailAddress}}"')).toBeVisible();
    }
  });

  test("should maintain merge tags when switching between design and HTML modes", async ({ page }) => {
    // Insert a merge tag
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await page.getByText("First Name").first().click();
    
    // Switch to HTML mode (if available)
    const htmlModeButton = page.getByRole("button", { name: /html/i }).or(
      page.getByRole("tab", { name: /html/i })
    );
    
    if (await htmlModeButton.isVisible()) {
      await htmlModeButton.click();
      
      // Verify merge tag syntax is preserved in HTML
      await expect(page.locator('text="{{firstName}}"')).toBeVisible();
      
      // Switch back to design mode
      const designModeButton = page.getByRole("button", { name: /design/i }).or(
        page.getByRole("tab", { name: /design/i })
      );
      
      if (await designModeButton.isVisible()) {
        await designModeButton.click();
        
        // Verify tag is still there
        const editorContent = await page.locator('.unlayer-editor iframe').contentFrame();
        if (editorContent) {
          await expect(editorContent.locator('text="{{firstName}}"')).toBeVisible();
        }
      }
    }
  });

  test("should track analytics when tags are inserted via Unlayer", async ({ page }) => {
    // Mock analytics endpoint to capture events
    const analyticsEvents = [];
    await page.route('**/analytics/**', (route) => {
      const requestData = route.request().postDataJSON();
      analyticsEvents.push(requestData);
      route.fulfill({ status: 200, body: '{}' });
    });
    
    // Insert a merge tag
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await page.getByText("First Name").first().click();
    
    // Wait for analytics event to be sent
    await page.waitForTimeout(1000);
    
    // Verify analytics event was captured
    expect(analyticsEvents.length).toBeGreaterThan(0);
    
    const event = analyticsEvents.find(e => e.tagId === 'firstName');
    expect(event).toBeDefined();
    expect(event.insertionSource).toContain('unlayer');
  });

  test("should handle Unlayer editor not being available gracefully", async ({ page }) => {
    // Mock scenario where Unlayer fails to load
    await page.addInitScript(() => {
      window.unlayer = undefined;
    });
    
    // Navigate to editor page
    await page.goto("/templates/email/editor");
    
    // Try to insert merge tag
    const mergeTagButton = page.getByRole("button", { name: /insert merge tag/i });
    if (await mergeTagButton.isVisible()) {
      await mergeTagButton.click();
      await expect(page.locator('[role="dialog"]')).toBeVisible();
      await page.getByText("First Name").first().click();
      
      // Should show error message or graceful fallback
      await expect(page.getByText("Editor not available")).toBeVisible();
    }
  });

  test("should preserve merge tag formatting in email export", async ({ page }) => {
    // Insert merge tags
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await page.getByText("First Name").first().click();
    
    // Export or save the template
    const saveButton = page.getByRole("button", { name: /save/i }).or(
      page.getByRole("button", { name: /export/i })
    );
    
    if (await saveButton.isVisible()) {
      await saveButton.click();
      
      // Verify merge tags are preserved in the saved content
      // This would depend on the actual save/export implementation
      await expect(page.getByText("Template saved successfully")).toBeVisible();
    }
  });

  test("should show merge tag validation errors in Unlayer", async ({ page }) => {
    // Try to insert an invalid or non-existent merge tag
    // This might require manually typing invalid syntax
    
    // Focus on Unlayer editor
    const editorFrame = page.locator('.unlayer-editor iframe').contentFrame();
    if (editorFrame) {
      await editorFrame.locator('body').click();
      
      // Type invalid merge tag syntax
      await page.keyboard.type('{{invalidTag}}');
      
      // Look for validation warning
      await expect(page.locator('.merge-tag-warning')).toBeVisible();
    }
  });

  test("should support undo/redo with merge tag insertions", async ({ page }) => {
    // Insert a merge tag
    await page.getByRole("button", { name: /insert merge tag/i }).click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await page.getByText("First Name").first().click();
    
    // Use undo (Ctrl+Z or Cmd+Z)
    await page.keyboard.press(process.platform === 'darwin' ? 'Meta+z' : 'Control+z');
    
    // Verify tag was removed
    const editorContent = await page.locator('.unlayer-editor iframe').contentFrame();
    if (editorContent) {
      await expect(editorContent.locator('text="{{firstName}}"')).not.toBeVisible();
    }
    
    // Use redo (Ctrl+Y or Cmd+Shift+Z)
    await page.keyboard.press(process.platform === 'darwin' ? 'Meta+Shift+z' : 'Control+y');
    
    // Verify tag is back
    if (editorContent) {
      await expect(editorContent.locator('text="{{firstName}}"')).toBeVisible();
    }
  });
});
