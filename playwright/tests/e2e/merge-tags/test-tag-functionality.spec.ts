import { test, expect } from "@playwright/test";

test.describe("Merge Tag Test Functionality - E2E", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the merge tags page
    await page.goto("/merge-tags");
    
    // Wait for the page to load
    await page.waitForLoadState("networkidle");
  });

  test("should display Test Tag button for each merge tag", async ({ page }) => {
    // Wait for the table to load
    await expect(page.locator("table")).toBeVisible();
    
    // Check that Test Tag buttons are present
    const testButtons = page.locator('[title="Test Tag"]');
    await expect(testButtons.first()).toBeVisible();
    
    // Verify the buttons have the play icon
    const playIcons = page.locator('[title="Test Tag"] svg');
    await expect(playIcons.first()).toBeVisible();
  });

  test("should open Test Tag modal when Test Tag button is clicked", async ({ page }) => {
    // Wait for the table to load
    await expect(page.locator("table")).toBeVisible();
    
    // Click the first Test Tag button
    const firstTestButton = page.locator('[title="Test Tag"]').first();
    await firstTestButton.click();
    
    // Check that the Test Tag modal opens
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.getByText("Test Merge Tag")).toBeVisible();
    
    // Verify modal content
    await expect(page.getByText("Select Tag to Test")).toBeVisible();
    await expect(page.getByText("Sample Recipient Data")).toBeVisible();
    await expect(page.getByRole("button", { name: "Test Tag" })).toBeVisible();
    await expect(page.getByRole("button", { name: "Test with Custom Data" })).toBeVisible();
  });

  test("should allow selection of different sample recipient data", async ({ page }) => {
    // Wait for the table to load and click Test Tag button
    await expect(page.locator("table")).toBeVisible();
    await page.locator('[title="Test Tag"]').first().click();
    
    // Wait for modal to open
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Check sample data options
    const sampleSelect = page.locator('select').filter({ hasText: 'Basic - Complete data' });
    await expect(sampleSelect).toBeVisible();
    
    // Verify all sample options are available
    await sampleSelect.click();
    await expect(page.getByText("Basic - Complete data")).toBeVisible();
    await expect(page.getByText("Minimal - Limited data")).toBeVisible();
    await expect(page.getByText("Empty - Missing fields")).toBeVisible();
    await expect(page.getByText("Arrays - Multiple values")).toBeVisible();
  });

  test("should execute tag test and display results", async ({ page }) => {
    // Wait for the table to load and click Test Tag button
    await expect(page.locator("table")).toBeVisible();
    await page.locator('[title="Test Tag"]').first().click();
    
    // Wait for modal to open
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Select a tag and sample data (defaults should be fine)
    // Click Test Tag button
    await page.getByRole("button", { name: "Test Tag" }).click();
    
    // Wait for test to complete and results to show
    await expect(page.getByText("Test Results")).toBeVisible({ timeout: 10000 });
    
    // Verify result sections are present
    await expect(page.getByText("Tag Information")).toBeVisible();
    await expect(page.getByText("Resolution Results")).toBeVisible();
    await expect(page.getByText("Sample Recipient Data")).toBeVisible();
  });

  test("should display success toast when tag test completes", async ({ page }) => {
    // Wait for the table to load and click Test Tag button
    await expect(page.locator("table")).toBeVisible();
    await page.locator('[title="Test Tag"]').first().click();
    
    // Wait for modal to open
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Click Test Tag button
    await page.getByRole("button", { name: "Test Tag" }).click();
    
    // Wait for success toast
    await expect(page.getByText("Tag test completed successfully!")).toBeVisible({ timeout: 10000 });
  });

  test("should close modal when Close button is clicked", async ({ page }) => {
    // Wait for the table to load and click Test Tag button
    await expect(page.locator("table")).toBeVisible();
    await page.locator('[title="Test Tag"]').first().click();
    
    // Wait for modal to open
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Click Close button
    await page.getByRole("button", { name: "Close" }).click();
    
    // Verify modal is closed
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test("should handle different recipient sample data types", async ({ page }) => {
    // Wait for the table to load and click Test Tag button
    await expect(page.locator("table")).toBeVisible();
    await page.locator('[title="Test Tag"]').first().click();
    
    // Wait for modal to open
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    const sampleTypes = ["basic", "minimal", "empty", "arrays"];
    
    for (const sampleType of sampleTypes) {
      // Select sample type
      const sampleSelect = page.locator('select').filter({ hasText: /Basic|Minimal|Empty|Arrays/ });
      await sampleSelect.selectOption({ label: sampleType === "basic" ? "Basic - Complete data" :
                                               sampleType === "minimal" ? "Minimal - Limited data" :
                                               sampleType === "empty" ? "Empty - Missing fields" :
                                               "Arrays - Multiple values" });
      
      // Click Test Tag button
      await page.getByRole("button", { name: "Test Tag" }).click();
      
      // Wait for test results
      await expect(page.getByText("Test Results")).toBeVisible({ timeout: 10000 });
      
      // Verify sample data section shows the correct type
      const sampleDataSection = page.getByText(`Sample Recipient Data (${sampleType})`);
      await expect(sampleDataSection).toBeVisible();
      
      // Clear results for next iteration (if not last)
      if (sampleType !== sampleTypes[sampleTypes.length - 1]) {
        // Re-select a different sample to reset results
        continue;
      }
    }
  });

  test("should display error handling for invalid tag resolution", async ({ page }) => {
    // This test would require a tag that we know will fail
    // For now, we'll test the error display structure
    
    await expect(page.locator("table")).toBeVisible();
    await page.locator('[title="Test Tag"]').first().click();
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // The error section should be initially hidden
    await expect(page.getByText("Test Error")).not.toBeVisible();
    
    // If an error occurs, it should display in a red error box
    // This is more of a visual test to ensure the error UI is properly structured
  });

  test("should show loading state during tag test execution", async ({ page }) => {
    // Wait for the table to load and click Test Tag button
    await expect(page.locator("table")).toBeVisible();
    await page.locator('[title="Test Tag"]').first().click();
    
    // Wait for modal to open
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Click Test Tag button and immediately check for loading state
    await page.getByRole("button", { name: "Test Tag" }).click();
    
    // Check for loading state (button should show "Testing...")
    await expect(page.getByText("Testing...")).toBeVisible();
    
    // Loading spinner should be visible
    await expect(page.locator(".animate-spin")).toBeVisible();
    
    // Eventually the test should complete
    await expect(page.getByText("Test Results")).toBeVisible({ timeout: 10000 });
  });

  test("should maintain test results when switching between different tags", async ({ page }) => {
    // Wait for the table to load and click Test Tag button
    await expect(page.locator("table")).toBeVisible();
    await page.locator('[title="Test Tag"]').first().click();
    
    // Wait for modal to open
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Run initial test
    await page.getByRole("button", { name: "Test Tag" }).click();
    await expect(page.getByText("Test Results")).toBeVisible({ timeout: 10000 });
    
    // Change to a different tag
    const tagSelect = page.locator('select').first(); // Select Tag to Test dropdown
    const options = await tagSelect.locator('option').count();
    
    if (options > 1) {
      await tagSelect.selectOption({ index: 1 });
      
      // Run test with new tag
      await page.getByRole("button", { name: "Test Tag" }).click();
      await expect(page.getByText("Test Results")).toBeVisible({ timeout: 10000 });
      
      // Results should be updated with new tag information
      await expect(page.getByText("Tag Information")).toBeVisible();
    }
  });
});
