import { test, expect } from "@playwright/test";
import { MergeTagRegistry } from "../../../../src/domain/mergeTags/mergeTagRegistry.js";
import { createDataSourceMapper } from "../../../../src/domain/mergeTags/mappers/dataSourceMappers";

// Mock Firebase dependencies for unit testing
const mockFirebaseConfig = {
  onSnapshot: () => () => {},
  collection: () => {},
  db: {}
};

test.describe("Merge Tag Resolution - Edge Cases and Validation", () => {
  let registry: MergeTagRegistry;
  
  test.beforeEach(async () => {
    // Create a new registry instance for each test
    registry = new MergeTagRegistry();
    // Mock the Firestore listener to avoid actual connections
    registry.unsubscribe = () => {};
    registry.isInitialized = true;
    
    // Set up test tags
    registry.tags = [
      {
        id: "firstName",
        name: "First Name",
        sample: "<PERSON>",
        category: "recipient",
        dataSource: "recipient",
        path: "name.firstName",
        templateType: ["email", "sms"],
        templateSubType: [],
        isSystem: true
      },
      {
        id: "lastName",
        name: "Last Name", 
        sample: "Doe",
        category: "recipient",
        dataSource: "recipient",
        path: "name.lastName",
        templateType: ["email", "sms"],
        templateSubType: [],
        isSystem: true
      },
      {
        id: "emailAddress",
        name: "Email Address",
        sample: "<EMAIL>",
        category: "recipient",
        dataSource: "recipient",
        path: "emailAddresses[0].email",
        templateType: ["email"],
        templateSubType: [],
        isSystem: true
      },
      {
        id: "customField",
        name: "Custom Company",
        sample: "ACME Corp",
        category: "custom",
        dataSource: "customFields",
        path: "custom.company",
        templateType: ["email", "print"],
        templateSubType: [],
        isSystem: false
      }
    ];
    registry.updateGroupings();
  });

  test.describe("Basic Tag Resolution", () => {
    test("should resolve simple nested paths correctly", async () => {
      const testData = {
        name: {
          firstName: "John",
          lastName: "Doe"
        }
      };

      const firstNameResult = registry.resolveTag("name.firstName", testData);
      const lastNameResult = registry.resolveTag("name.lastName", testData);

      expect(firstNameResult).toBe("John");
      expect(lastNameResult).toBe("Doe");
    });

    test("should resolve deeply nested paths", async () => {
      const testData = {
        user: {
          profile: {
            personal: {
              details: {
                firstName: "Alice"
              }
            }
          }
        }
      };

      const result = registry.resolveTag("user.profile.personal.details.firstName", testData);
      expect(result).toBe("Alice");
    });

    test("should return null for non-existent paths", async () => {
      const testData = {
        name: {
          firstName: "John"
        }
      };

      const result = registry.resolveTag("name.middleName", testData);
      expect(result).toBeNull();
    });

    test("should return null for invalid data structures", async () => {
      const testData = null;
      const result = registry.resolveTag("name.firstName", testData);
      expect(result).toBeNull();
    });
  });

  test.describe("Array Handling Edge Cases", () => {
    test("should resolve array elements with index notation", async () => {
      const testData = {
        emailAddresses: [
          { email: "<EMAIL>", type: "personal" },
          { email: "<EMAIL>", type: "work" },
          { email: "<EMAIL>", type: "backup" }
        ]
      };

      const firstEmail = registry.resolveTag("emailAddresses.0.email", testData);
      const secondEmail = registry.resolveTag("emailAddresses.1.email", testData);
      const thirdEmail = registry.resolveTag("emailAddresses.2.email", testData);

      expect(firstEmail).toBe("<EMAIL>");
      expect(secondEmail).toBe("<EMAIL>");
      expect(thirdEmail).toBe("<EMAIL>");
    });

    test("should handle empty arrays gracefully", async () => {
      const testData = {
        emailAddresses: []
      };

      const result = registry.resolveTag("emailAddresses.0.email", testData);
      expect(result).toBeNull();
    });

    test("should handle out-of-bounds array access", async () => {
      const testData = {
        emailAddresses: [
          { email: "<EMAIL>", type: "personal" }
        ]
      };

      const result = registry.resolveTag("emailAddresses.5.email", testData);
      expect(result).toBeNull();
    });

    test("should handle non-array data when array access is attempted", async () => {
      const testData = {
        emailAddresses: "not-an-array"
      };

      const result = registry.resolveTag("emailAddresses.0.email", testData);
      expect(result).toBeNull();
    });

    test("should resolve primitive arrays", async () => {
      const testData = {
        tags: ["vip", "customer", "qualified"]
      };

      const firstTag = registry.resolveTag("tags.0", testData);
      const secondTag = registry.resolveTag("tags.1", testData);

      expect(firstTag).toBe("vip");
      expect(secondTag).toBe("customer");
    });
  });

  test.describe("Fallback Handling", () => {
    test("should apply fallback when path doesn't exist", async () => {
      const testData = {
        name: {
          firstName: "John"
        }
      };

      const context = {
        accountId: "test-account",
        timezone: "America/New_York"
      };

      const result = await registry.resolveTagValue("nonExistentTag", context, testData, "DEFAULT_VALUE");
      
      expect(result.value).toBe("DEFAULT_VALUE");
      expect(result.fallbackApplied).toBe(true);
      expect(result.error).toContain("not found");
    });

    test("should not apply fallback when value exists", async () => {
      const testData = {
        name: {
          firstName: "John"
        }
      };

      const context = {
        accountId: "test-account",
        timezone: "America/New_York"
      };

      const result = await registry.resolveTagValue("firstName", context, testData, "DEFAULT_VALUE");
      
      expect(result.value).toBe("John");
      expect(result.fallbackApplied).toBe(false);
    });

    test("should handle null and undefined fallbacks", async () => {
      const testData = {};

      const context = {
        accountId: "test-account",
        timezone: "America/New_York"
      };

      const nullFallback = await registry.resolveTagValue("nonExistentTag", context, testData, null);
      const undefinedFallback = await registry.resolveTagValue("nonExistentTag", context, testData, undefined);
      
      expect(nullFallback.value).toBeNull();
      expect(undefinedFallback.value).toBeUndefined();
    });
  });

  test.describe("Data Type Edge Cases", () => {
    test("should handle numeric values", async () => {
      const testData = {
        stats: {
          age: 25,
          score: 98.5,
          count: 0
        }
      };

      const age = registry.resolveTag("stats.age", testData);
      const score = registry.resolveTag("stats.score", testData);
      const count = registry.resolveTag("stats.count", testData);

      expect(age).toBe(25);
      expect(score).toBe(98.5);
      expect(count).toBe(0);
    });

    test("should handle boolean values", async () => {
      const testData = {
        flags: {
          isActive: true,
          isVerified: false
        }
      };

      const isActive = registry.resolveTag("flags.isActive", testData);
      const isVerified = registry.resolveTag("flags.isVerified", testData);

      expect(isActive).toBe(true);
      expect(isVerified).toBe(false);
    });

    test("should handle Date objects", async () => {
      const testDate = new Date("2023-01-15T10:30:00Z");
      const testData = {
        dates: {
          created: testDate,
          updated: null
        }
      };

      const created = registry.resolveTag("dates.created", testData);
      const updated = registry.resolveTag("dates.updated", testData);

      expect(created).toBe(testDate);
      expect(updated).toBeNull();
    });

    test("should handle special string values", async () => {
      const testData = {
        values: {
          empty: "",
          whitespace: "   ",
          special: "特殊文字",
          unicode: "🎉✨",
          json: '{"nested": "value"}'
        }
      };

      const empty = registry.resolveTag("values.empty", testData);
      const whitespace = registry.resolveTag("values.whitespace", testData);
      const special = registry.resolveTag("values.special", testData);
      const unicode = registry.resolveTag("values.unicode", testData);
      const json = registry.resolveTag("values.json", testData);

      expect(empty).toBe("");
      expect(whitespace).toBe("   ");
      expect(special).toBe("特殊文字");
      expect(unicode).toBe("🎉✨");
      expect(json).toBe('{"nested": "value"}');
    });
  });

  test.describe("Complex Data Structures", () => {
    test("should handle mixed nested objects and arrays", async () => {
      const testData = {
        user: {
          contacts: [
            {
              type: "email",
              values: ["<EMAIL>", "<EMAIL>"]
            },
            {
              type: "phone", 
              values: ["555-1234", "555-5678"]
            }
          ]
        }
      };

      const emailType = registry.resolveTag("user.contacts.0.type", testData);
      const firstEmail = registry.resolveTag("user.contacts.0.values.0", testData);
      const phoneType = registry.resolveTag("user.contacts.1.type", testData);
      const firstPhone = registry.resolveTag("user.contacts.1.values.0", testData);

      expect(emailType).toBe("email");
      expect(firstEmail).toBe("<EMAIL>");
      expect(phoneType).toBe("phone");
      expect(firstPhone).toBe("555-1234");
    });

    test("should handle circular references gracefully", async () => {
      const testData: any = {
        name: "John"
      };
      // Create circular reference
      testData.self = testData;

      const name = registry.resolveTag("name", testData);
      // This should not cause infinite recursion
      const circular = registry.resolveTag("self.name", testData);

      expect(name).toBe("John");
      expect(circular).toBe("John");
    });

    test("should handle very deeply nested structures", async () => {
      const testData = {
        level1: {
          level2: {
            level3: {
              level4: {
                level5: {
                  level6: {
                    level7: {
                      level8: {
                        level9: {
                          level10: {
                            deepValue: "found"
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      };

      const result = registry.resolveTag("level1.level2.level3.level4.level5.level6.level7.level8.level9.level10.deepValue", testData);
      expect(result).toBe("found");
    });
  });

  test.describe("Error Handling and Malformed Data", () => {
    test("should handle malformed path strings", async () => {
      const testData = {
        name: {
          firstName: "John"
        }
      };

      // Empty path
      const emptyPath = registry.resolveTag("", testData);
      expect(emptyPath).toBeNull();

      // Path with consecutive dots
      const consecutiveDots = registry.resolveTag("name..firstName", testData);
      expect(consecutiveDots).toBeNull();

      // Path starting with dot
      const startingDot = registry.resolveTag(".name.firstName", testData);
      expect(startingDot).toBeNull();

      // Path ending with dot
      const endingDot = registry.resolveTag("name.firstName.", testData);
      expect(endingDot).toBeNull();
    });

    test("should handle malformed recipient data", async () => {
      const malformedData = {
        name: null,
        emails: undefined,
        addresses: "not-an-array",
        nested: {
          circular: {}
        }
      };
      
      // Add circular reference
      malformedData.nested.circular = malformedData.nested;

      const nameResult = registry.resolveTag("name.firstName", malformedData);
      const emailResult = registry.resolveTag("emails.0.email", malformedData);
      const addressResult = registry.resolveTag("addresses.0.street", malformedData);

      expect(nameResult).toBeNull();
      expect(emailResult).toBeNull();
      expect(addressResult).toBeNull();
    });

    test("should handle extremely large datasets", async () => {
      // Create a large array
      const largeArray = Array.from({ length: 10000 }, (_, i) => ({
        id: i,
        value: `item-${i}`
      }));

      const testData = {
        items: largeArray
      };

      const firstItem = registry.resolveTag("items.0.value", testData);
      const middleItem = registry.resolveTag("items.5000.value", testData);
      const lastItem = registry.resolveTag("items.9999.value", testData);

      expect(firstItem).toBe("item-0");
      expect(middleItem).toBe("item-5000");
      expect(lastItem).toBe("item-9999");
    });
  });

  test.describe("Real-world Integration Scenarios", () => {
    test("should handle typical recipient data structure", async () => {
      const recipientData = {
        id: "recipient-123",
        name: {
          firstName: "John",
          lastName: "Doe",
          fullName: "John Doe"
        },
        emailAddresses: [
          { email: "<EMAIL>", type: "personal", isPrimary: true },
          { email: "<EMAIL>", type: "work", isPrimary: false }
        ],
        phoneNumbers: [
          { number: "(*************", type: "mobile", isPrimary: true }
        ],
        mailingAddresses: [
          {
            address1: "123 Main St",
            city: "New York",
            state: "NY",
            postalCode: "10001"
          }
        ],
        customFields: {
          company: "ACME Corporation",
          anniversary: "2020-01-15"
        },
        tags: ["vip", "customer"]
      };

      const firstName = registry.resolveTag("name.firstName", recipientData);
      const primaryEmail = registry.resolveTag("emailAddresses.0.email", recipientData);
      const workEmail = registry.resolveTag("emailAddresses.1.email", recipientData);
      const phoneNumber = registry.resolveTag("phoneNumbers.0.number", recipientData);
      const address = registry.resolveTag("mailingAddresses.0.address1", recipientData);
      const company = registry.resolveTag("customFields.company", recipientData);
      const firstTag = registry.resolveTag("tags.0", recipientData);

      expect(firstName).toBe("John");
      expect(primaryEmail).toBe("<EMAIL>");
      expect(workEmail).toBe("<EMAIL>");
      expect(phoneNumber).toBe("(*************");
      expect(address).toBe("123 Main St");
      expect(company).toBe("ACME Corporation");
      expect(firstTag).toBe("vip");
    });

    test("should handle missing data gracefully in email template context", async () => {
      const incompleteRecipient = {
        id: "recipient-456",
        name: {
          firstName: "Jane"
          // lastName missing
        },
        emailAddresses: [
          { email: "<EMAIL>", type: "personal" }
        ]
        // phoneNumbers and mailingAddresses missing
      };

      const firstName = registry.resolveTag("name.firstName", incompleteRecipient);
      const lastName = registry.resolveTag("name.lastName", incompleteRecipient);
      const email = registry.resolveTag("emailAddresses.0.email", incompleteRecipient);
      const phone = registry.resolveTag("phoneNumbers.0.number", incompleteRecipient);
      const address = registry.resolveTag("mailingAddresses.0.address1", incompleteRecipient);

      expect(firstName).toBe("Jane");
      expect(lastName).toBeNull();
      expect(email).toBe("<EMAIL>");
      expect(phone).toBeNull();
      expect(address).toBeNull();
    });

    test("should handle batch resolution of multiple tags", async () => {
      const recipientData = {
        name: { firstName: "Alice", lastName: "Smith" },
        emailAddresses: [{ email: "<EMAIL>" }],
        customFields: { company: "Tech Corp" }
      };

      const context = {
        accountId: "test-account",
        timezone: "America/New_York"
      };

      // Test resolving multiple tags
      const firstNameResult = await registry.resolveTagValue("firstName", context, recipientData, "Unknown");
      const emailResult = await registry.resolveTagValue("emailAddress", context, recipientData, "<EMAIL>");
      const customFieldResult = await registry.resolveTagValue("customField", context, recipientData, "No Company");

      expect(firstNameResult.value).toBe("Alice");
      expect(firstNameResult.fallbackApplied).toBe(false);
      
      expect(emailResult.value).toBe("<EMAIL>");
      expect(emailResult.fallbackApplied).toBe(false);
    });
  });

  test.describe("Performance and Memory", () => {
    test("should handle rapid successive calls efficiently", async () => {
      const testData = {
        name: { firstName: "John", lastName: "Doe" },
        emails: Array.from({ length: 100 }, (_, i) => ({ email: `test${i}@example.com` }))
      };

      const startTime = Date.now();
      
      // Make 1000 rapid calls
      for (let i = 0; i < 1000; i++) {
        const result = registry.resolveTag("name.firstName", testData);
        expect(result).toBe("John");
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (less than 1 second)
      expect(duration).toBeLessThan(1000);
    });

    test("should not leak memory with repeated object creation", async () => {
      // Create and resolve many different data objects
      for (let i = 0; i < 100; i++) {
        const testData = {
          iteration: i,
          name: { firstName: `User${i}` },
          data: Array.from({ length: 10 }, (_, j) => ({ value: `${i}-${j}` }))
        };
        
        const result = registry.resolveTag("name.firstName", testData);
        expect(result).toBe(`User${i}`);
      }
    });
  });

  test.afterEach(async () => {
    // Clean up registry
    if (registry && registry.destroy) {
      registry.destroy();
    }
  });
});
