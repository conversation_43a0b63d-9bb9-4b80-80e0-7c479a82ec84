// stateManagement.spec.js
import { test, expect } from "@playwright/test";
import { stateManagement } from "../../../../functions/recipients/userEnteredMailingAddress/stateManagement.js";
import { getInitialMailingAddressState } from "../../../../functions/recipients/userEnteredMailingAddress/getInitialMailingAddressState.js";
import { FieldValue } from "firebase-admin/firestore";
import { deliverabilityStatus } from "../../../../functions/constants/deliverabilityStatus.js";
import { mailingAddressStateCases } from "../../../../functions/constants/mailingAddressStateCases.js";
import { processingStatus } from "../../../../functions/constants/processingStatus.js";
const { GOOD_ADDRESS, INCOMPLETE_ADDRESS, WILL_SEND } = deliverabilityStatus;
const {
  SET_INCOMPLETE_ADDRESS,
  CHECK_SOURCE_MAILING_ADDRESS,
  SOURCE_CREATION_SUCCESS,
  SOURCE_CREATION_ERROR,
  VALIDATE_ADDRESS_COMPLETE,
  VALIDATE_ADDRESS_ERROR,
  CHECK_MAGAZINE_PRODUCT_ASSIGNMENT,
  CHECK_MAGAZINE_PRODUCT_ASSIGNMENT_ERROR,
  PROCESS_EXCLUSIVITY_SUCCESS,
  PROCESS_EXCLUSIVITY_ERROR,
  DONE,
} = mailingAddressStateCases;
// For testing purposes, override FieldValue.arrayUnion to simply return an array with the provided item.
//@ts-ignore
FieldValue.arrayUnion = (item) => [item];

const baseState = getInitialMailingAddressState();

test.describe("stateManagement Reducer", () => {
  test(`should handle ${SET_INCOMPLETE_ADDRESS} case`, () => {
    const action = { type: SET_INCOMPLETE_ADDRESS };
    const newState = stateManagement(baseState, action);

    expect(newState.addressDeliverability.code).toBe(
      deliverabilityStatus.INCOMPLETE_ADDRESS.code
    );
    expect(newState.addressDeliverability.message).toBe(
      deliverabilityStatus.INCOMPLETE_ADDRESS.message
    );
    expect(newState.addressDeliverability.status).toBe(
      deliverabilityStatus.INCOMPLETE_ADDRESS.status
    );
    expect(newState.finalStatus).toBe(DONE);
  });

  test(`should handle ${CHECK_SOURCE_MAILING_ADDRESS}`, () => {
    const payload = {
      addressData: { address1: "456 Elm St" },
      sourceMailingAddressId: "src123",
      skipAddressValidation: true,
      skipMailingAddressSourceCreation: false,
    };
    const action = { type: CHECK_SOURCE_MAILING_ADDRESS, payload };
    const newState = stateManagement(baseState, action);

    expect(newState.addressData.address1).toBe("456 Elm St");
    expect(newState.addressData.processingStatus).toBe(
      CHECK_SOURCE_MAILING_ADDRESS
    );
    expect(newState.addressDeliverability.code).toBe(INCOMPLETE_ADDRESS.code);
    expect(newState.rootRecipient.mailingAddresses).toEqual([{ id: "src123" }]);
    expect(newState.switches.skipAddressValidation).toBe(true);
    expect(newState.switches.skipMailingAddressSourceCreation).toBe(false);
  });

  test(`should handle ${SOURCE_CREATION_SUCCESS}`, () => {
    const payload = {
      sourceMailingAddressId: "src456",
    };
    const action = { type: SOURCE_CREATION_SUCCESS, payload };
    const newState = stateManagement(baseState, action);

    // With our override, FieldValue.arrayUnion returns an array with the new element.
    expect(newState.rootRecipient.mailingAddresses).toEqual([{ id: "src456" }]);
    expect(newState.addressData.processingStatus).toBe(SOURCE_CREATION_SUCCESS);
  });

  test(`should handle ${SOURCE_CREATION_ERROR}`, () => {
    const action = { type: SOURCE_CREATION_ERROR };
    const newState = stateManagement(baseState, action);
    // should not update current address state prior to error
    // because we are skipping to this step the default is empty string
    expect(newState.addressData.address1).toBe("");
    expect(newState.addressData.processingStatus).toBe(SOURCE_CREATION_ERROR);
    expect(newState.finalStatus).toBe(SOURCE_CREATION_ERROR);
  });

  test(`should handle ${VALIDATE_ADDRESS_COMPLETE}`, () => {
    const payload = {
      addressData: {
        address1: "123 Main St",
        processingStatus: VALIDATE_ADDRESS_COMPLETE,
      },
      addressDeliverability: {
        code: "GOOD",
        message: "Good address",
        status: "OK",
      },
      searchTags: { formattedMailingAddress: "123 MAIN ST" },
      overrideDeliverability: { address: false },
    };
    const action = { type: VALIDATE_ADDRESS_COMPLETE, payload };
    const newState = stateManagement(baseState, action);

    expect(newState.addressData.address1).toBe("123 Main St");
    expect(newState.addressData.processingStatus).toBe(
      VALIDATE_ADDRESS_COMPLETE
    );
    expect(newState.addressDeliverability.code).toBe("GOOD");
    expect(newState.searchTags.formattedMailingAddress).toBe("123 MAIN ST");
    expect(newState.overrideDeliverability.address).toBe(false);
  });

  test(`should handle ${VALIDATE_ADDRESS_ERROR}`, () => {
    const action = { type: VALIDATE_ADDRESS_ERROR };
    const newState = stateManagement(baseState, action);

    expect(newState.addressDeliverability.code).toBe(
      deliverabilityStatus.FAILED.code
    );
    expect(newState.addressDeliverability.message).toBe(
      deliverabilityStatus.FAILED.message
    );
    expect(newState.addressDeliverability.status).toBe(
      deliverabilityStatus.FAILED.status
    );
    expect(newState.finalStatus).toBe(VALIDATE_ADDRESS_ERROR);
  });

  test(`should handle ${CHECK_MAGAZINE_PRODUCT_ASSIGNMENT}`, () => {
    const payload = {
      addressDeliverability: {
        code: GOOD_ADDRESS.code,
        message: GOOD_ADDRESS.message,
        status: GOOD_ADDRESS.status,
      },
      switches: { skipProcessExclusivity: true },
    };
    const action = { type: CHECK_MAGAZINE_PRODUCT_ASSIGNMENT, payload };
    const newState = stateManagement(baseState, action);

    expect(newState.addressDeliverability.code).toBe("GA");
    expect(newState.addressDeliverability.message).toBe("Good Address");
    expect(newState.switches.skipProcessExclusivity).toBe(true);
  });

  test(`should handle ${CHECK_MAGAZINE_PRODUCT_ASSIGNMENT_ERROR}`, () => {
    const action = { type: CHECK_MAGAZINE_PRODUCT_ASSIGNMENT_ERROR };
    const newState = stateManagement(baseState, action);

    expect(newState.finalStatus).toBe(CHECK_MAGAZINE_PRODUCT_ASSIGNMENT_ERROR);
  });

  test(`should handle ${PROCESS_EXCLUSIVITY_SUCCESS}`, () => {
    const payload = {
      addressDeliverability: {
        code: WILL_SEND.code,
        message: WILL_SEND.message,
        status: WILL_SEND.status,
      },
    };
    const action = { type: PROCESS_EXCLUSIVITY_SUCCESS, payload };
    const newState = stateManagement(baseState, action);

    expect(newState.addressDeliverability.code).toBe("WS");
    expect(newState.addressDeliverability.message).toBe("Will Send");
  });

  test(`should handle ${PROCESS_EXCLUSIVITY_ERROR}`, () => {
    const action = { type: PROCESS_EXCLUSIVITY_ERROR };
    const newState = stateManagement(baseState, action);

    expect(newState.finalStatus).toBe(PROCESS_EXCLUSIVITY_ERROR);
  });

  test("should handle ERROR_PROCESS", () => {
    const action = { type: "ERROR_PROCESS" };
    const newState = stateManagement(baseState, action);

    expect(newState.finalStatus).toBe(processingStatus.ERROR_PROCESS);
  });

  test("should return the original state for an unknown action", () => {
    const action = { type: "UNKNOWN_ACTION" };
    const newState = stateManagement(baseState, action);

    expect(newState).toEqual(baseState);
  });
});
