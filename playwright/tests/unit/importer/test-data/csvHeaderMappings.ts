export const csvHeaderMappingScenarios = [
  {
    id: "ltm-purchased-list-maps-property-address",
    jira: ["RT-547"],
    description: "Purchased mailing list maps Property Address fields correctly",
    headers: [
      "Property Resident First Name", "Property Resident Last Name", "Email", "Phone number",
      "Property Address1", "Property Address2", "Property City", "Property State", "Property Zip",
      "Score", "Owner First Name", "Owner Last Name", "Owner Name", "Owner street", "Owner city",
      "Owner State", "Owner zip"
    ],
    expectedMappings: {
      "Property Resident First Name": "First Name",
      "Property Resident Last Name": "Last Name",
      "Email": "Email",
      "Phone number": "Phone Number",
      "Property Address1": "Address Line 1",
      "Property Address2": "Address Line 2",
      "Property City": "City",
      "Property State": "State",
      "Property Zip": "Zip",
      "Score": undefined,
      "Owner First Name": undefined, 
      "Owner Last Name": undefined, 
      "Owner Name": undefined,
      "Owner street": undefined, 
      "Owner city": undefined,
      "Owner State": undefined,
      "Owner zip": undefined     
    }
  },
  {
    id: "customer-csv-maps-state-field",
    jira: [],
    description: "Customer CSV maps 'STATE' header to 'State' field correctly",
    headers: [
      "IDCUSTOMER", "FIRSTNAME", "LASTNAME", "EMAIL", "BIRTHDAY", "ANNIVERSARY", "SPOUSENAME",
      "SPOUSEEMAIL", "SPOUSENUMBER", "SPOUSEBIRTHDAY", "CREATED", "UNSUBSCRIBED",
      "ASSIGNED_AGENT_FIRST", "ASSIGNED_AGENT_LAST", "MOBILE_PHONE", "HOME_PHONE",
      "SOURCE", "STATUS", "TAGS", "HOTNESS", "LAST_COMMENT",
      "ADDRESS1", "ADDRESS2", "CITY", "STATE", "ZIP",
      "OFFICEADDRESS1", "OFFICEADDRESS2", "OFFICECITY", "OFFICESTATE", "OFFICEZIP",
      "CUSTOMFIELD_INTRODATE", "CUSTOMFIELD_CLOSINGDATE"
    ],
    expectedMappings: {
      IDCUSTOMER: undefined,
      FIRSTNAME: "First Name",
      LASTNAME: "Last Name",
      EMAIL: "Email",
      BIRTHDAY: undefined,
      ANNIVERSARY: undefined,
      SPOUSENAME: undefined,
      SPOUSEEMAIL: undefined,
      SPOUSENUMBER: undefined,
      SPOUSEBIRTHDAY: undefined,
      CREATED: undefined,
      UNSUBSCRIBED: undefined,
      ASSIGNED_AGENT_FIRST: undefined,
      ASSIGNED_AGENT_LAST: undefined,
      MOBILE_PHONE: "Phone Number",
      HOME_PHONE: "Phone Number 2",
      SOURCE: undefined,
      STATUS: undefined,
      TAGS: undefined,
      HOTNESS: undefined,
      LAST_COMMENT: undefined,
      ADDRESS1: "Address Line 1",
      ADDRESS2: "Address Line 2",
      CITY: "City",
      STATE: "State",
      ZIP: "Zip",
      OFFICEADDRESS1: undefined,
      OFFICEADDRESS2: undefined, 
      OFFICECITY: undefined, 
      OFFICESTATE: undefined,
      OFFICEZIP: undefined,
      CUSTOMFIELD_INTRODATE: undefined,
      CUSTOMFIELD_CLOSINGDATE: undefined,      
    }
  },
  {
    id: 'crm-mailing-tool-maps-address-fields',
    jira: [],
    description: 'CRM mailing tool maps address and basic contact fields correctly',
    headers: [
      "FN", "Last Name", "DN", "AN", "A1", "C", "ST", "Z",
    ],
    expectedMappings: {
      FN: "First Name",
      "Last Name": "Last Name",
      DN: undefined, // Likely short for "Display Name" or "Dear Name" => "Letter Salutation"
      AN: undefined, // Likely short for "Address Name" or "Addressee Name" => "Mailing Salutation"
      A1: "Address Line 1",
      C: "City",
      ST: "State",
      Z: "Zip",    
    }
  },
  {
    id: 'import-maps-full-name-zip-and-contact-fields',
    jira: [],
    description: "Customer import maps Full Name, Zip, and contact fields correctly",
    headers: [
      "Full Name", "Address1", "City", "State", "Zip", "Em", "Phone",
    ],
    expectedMappings: {
      "Full Name": "Full Name",
      Address1: "Address Line 1",
      City: "City",
      State: "State",
      Zip: "Zip",
      Em: "Email",
      Phone: "Phone Number",    
    }
  }
];
