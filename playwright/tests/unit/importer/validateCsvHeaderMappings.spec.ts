import { test, expect } from "@playwright/test";
import { createHeaderMatcher } from "../../../../src/domain/recipients/utilities/csvImportHelpers.jsx";
import { expectedHeaders } from "../../../../src/domain/recipients/constants/constants.js";
import { csvHeaderMappingScenarios } from "./test-data/csvHeaderMappings.js";

const expectedLabels = expectedHeaders.map((h) => h.label);
const threshold = 0.47;

test.describe("CSV Header Mapping – Tests Real Production Scenarios", () => {
  for (const { id, jira, description, headers, expectedMappings } of csvHeaderMappingScenarios) {
    const titlePrefix = `[${id}${jira?.length ? ` | ${jira.join(", ")}` : ""}]`;

    test(`${titlePrefix} ${description}`, async () => {
      const matcher = createHeaderMatcher([...expectedLabels]);

      for (const header of headers) {
        await test.step(`Map header: "${header}"`, async () => {
          const expected = expectedMappings?.[header];
          const result = matcher(header);
          
          expect.soft(
            result?.item,
            `❌ Mapped "${header}" incorrectly to ${JSON.stringify(result?.item, null, 2)}`
          ).toBe(expected);

          if(result?.score) 
            expect.soft(
              result?.score,
              `Fuzzy match for "${header}" exceeded score threshold and mapped to ${JSON.stringify(result?.item, null, 2)}`
            ).toBeLessThanOrEqual(threshold);
        });
      }
    });
  }
});
