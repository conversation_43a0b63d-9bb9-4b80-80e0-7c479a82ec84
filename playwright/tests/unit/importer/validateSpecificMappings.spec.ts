import { test, expect } from "@playwright/test";
import { createHeaderMatcher } from "../../../../src/domain/recipients/utilities/csvImportHelpers.jsx";
import { expectedHeaders } from "../../../../src/domain/recipients/constants/constants";
import Fuse from "fuse.js";

const allExpectedHeaders = expectedHeaders.map((h) => h.label);

test.describe("Validate specificMappings Logic", () => {
  const specificMappings = [
    {
      aliases: [
        "name",
        "ownername",
        "ownerlabelname",
        "clients",
        "printedname",
        "owner1full",
      ],
      expected: "Full Name",
    },
    {
      aliases: ["owner1first", "ownerfirstname", "fn"],
      expected: "First Name",
    },
    {
      aliases: ["owner1last", "ownerlastname", "ln"],
      expected: "Last Name",
    },
    {
      aliases: [
        "email",
        "e-mail",
        "emailaddress",
        "e-mailaddress",
        "businessemail",
        "personalemail",
        "homememail",
        "em",
      ],
      expected: "Email",
    },
    {
      aliases: [
        "phone",
        "mobilephone",
        "phonemobile",
        "mobile",
        "businessmobile",
        "businessphone",
        "businessphonenumber",
        "homephone",
        "homephonenumber",
        "wirelessphone",
        "wireless",
        "cellphone",
        "cellular",
        "cell",
        "landline",
      ],
      expected: "Phone Number",
    },
    {
      aliases: [
        "a1",
        "address1",
        "homestreet",
        "streethome",
        "street",
        "streetname",
        "namestreet",
        "streetnumber",
        "numberstreet",
        "address",
        "streetaddress",
        "addressstreet",
        "addressline",
        "addressline1",
        "Addr1 Line 1",
      ],
      expected: "Address Line 1",
    },
    {
      aliases: ["a2", "address2"],
      expected: "Address Line 2",
    },
    {
      aliases: [
        "c",
        "homecity",
        "officecity",
        "businesscity",
        "propertycity",
        "mailingcity",
      ],
      expected: "City",
    },
    {
      aliases: [
        "st",
        "stateprovence",
        "stateprovidence",
        "homestate",
        "officestate",
        "businessstate",
        "propertystate",
        "mailingstate",
      ],
      expected: "State",
    },
    {
      aliases: [
        "zipcode",
        "z",
        "zippostalcode",
        "postalcode",
        "homepostalcode",
        "hpostalcode",
        "officepostalcode",
        "officezip",
        "officezipcode",
        "propertyzip",
        "mailingzip",
      ],
      expected: "Zip",
    },
    {
      aliases: [
        "zipcode4",
        "z4",
        "zippostalcode4",
        "postalcode4",
        "homepostalcode4",
        "hpostalcode4",
        "officepostalcode4",
        "officezip4",
        "officezipcode4",
        "propertyzip4",
        "mailingzip4",
        "zip4",
        "propertyzipplus4",
      ],
      expected: "Zip",
    },
  ];

  for (const { expected, aliases } of specificMappings) {
    for (const alias of aliases) {
      test(`"${alias}" should map to "${expected}"`, () => {
        const matcher = createHeaderMatcher([expected]);
        const result = matcher(alias);
        expect.soft(result?.item).toBe(expected);
      });
    }
  }

  test("should return undefined for unmatched header", () => {
    const matcher = createHeaderMatcher(["Full Name"]);
    const result = matcher("definitelynotarealheader");
    expect(result).toBeUndefined();
  });
});

test.describe("Validate Exact & Synonym Matches in createHeaderMatcher", () => {
  const synonymTestMatrix = [
    { input: "zip", expected: "Zip" },
    { input: "postal", expected: "Zip" },
    { input: "phone", expected: "Phone" },
    { input: "email", expected: "Email" },
    { input: "street", expected: "Street" },
  ];

  for (const { input, expected } of synonymTestMatrix) {
    test(`"${input}" should match directly or by synonym to "${expected}"`, () => {
      const matcher = createHeaderMatcher([expected]);
      const result = matcher(input);

      expect(result).toBeDefined();
      expect(result?.item).toBe(expected);
    });
  }
});

test.describe("Validate Fuzzy Matching in createHeaderMatcher", () => {
  const fuzzyTestMatrix = [
    { input: "emial", expected: "Email" },
    { input: "phoen number", expected: "Phone Number" },
    { input: "MOBILE_PHONE", expected: "Phone Number" },
    { input: "HOME_PHONE", expected: "Phone Number"},
    { input: "brithday", expected: "Birthday" },
    { input: "adress line 1", expected: "Address Line 1" },
    { input: "addr1", expected: "Address Line 1"},
    { input: "Addr1 Line 1", expected: "Address Line 1"},
    { input: "Street Address", expected: "Address Line 1"},
    { input: "adress line2", expected: "Address Line 2" },
    { input: "ful name", expected: "Full Name" },
    { input: "comapny", expected: "Company" },
    { input: "stat", expected: "State" },
    { input: "citty", expected: "City" },
    { input: "zipc", expected: "Zip" },
    { input: "STATE", expected: "State" },
  ];

  for (const { input, expected } of fuzzyTestMatrix) {
    test(`"${input}" should fuzzy match to "${expected}"`, () => {
      const matcher = createHeaderMatcher([expected]);
      const result = matcher(input);

      // If test fails, get debug info from Fuse.js
      if (!result || result.item !== expected) {
        const fuse = new Fuse(allExpectedHeaders, {
          ignoreLocation: true,
          isCaseSensitive: false,
          includeScore: true,
          threshold: 1,
          distance: 50,
        });
      
        const fuseResult = fuse.search(input);
        const debugMatch = fuseResult[0];
      
        console.log(
          `Fuzzy test failed for "${input}". Closest match: "${debugMatch?.item}" with score: ${debugMatch?.score}`
        );
      }

      expect(result?.item).toBe(expected);
    });
  }
});
