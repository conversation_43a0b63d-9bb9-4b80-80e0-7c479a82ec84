export enum JwtFilePaths {
  rmcClientJwtFilePath = "configs/.auth/rmc-client-jwt.json",
  printClientJwtFilePath = "configs/.auth/print-client-jwt.json",
  crmClientJwtFilePath = "configs/.auth/crm-client-jwt.json",
  lpClientJwtFilePath = "configs/.auth/lp-client-jwt.json",
}

export enum RecipientFilePaths {
  RECIPIENTS_BY_CAMPAIGN_PATH = "./playwright/test-data/recipient-apis/stored-recipients-data/seeded-recipients-by-campaign.json",
  RECIPIENTS_PATH = "./playwright/test-data/recipient-apis/stored-recipients-data/seeded-recipients.json",
}
