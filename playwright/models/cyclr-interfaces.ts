export interface CyclrName {
  firstName?: string;
  lastName?: string;
  middle?: string;
  nickname?: string;
  prefix?: string;
  suffix?: string;
  phoneticFirstName?: string;
  phoneticLastName?: string;
}

export interface CyclrSignificantOther {
  firstName?: string;
  lastName?: string;
}

export interface CyclrSalutation {
  company?: string;
  jobTitle?: string;
  letterSalutation?: string;
  mailingSalutation?: string;
}

export interface CyclrPhoneNumber {
  label?: string;
  phoneNumber?: string;
  phoneExtension?: string;
  primary?: boolean;
}

export interface CyclrEmailAddress {
  email: string;
  label: string;
  primary: boolean;
}

export interface CyclrMailingAddress {
  address1: string;
  address2: string;
  city: string;
  state: string;
  postalCode: string;
  label: string;
}

export interface CyclrNote {
  value: string;
}

export interface CyclrSignificantDate {
  label: string;
  date: string;
}

export interface CyclrSocialMedia {
  label: string;
  value: string;
}

export interface CyclrWebsite {
  label: string;
  url: string;
}

export interface CyclrRecipient {
  recipientGroupIds: string[];
  name: CyclrName;
  significantOther: CyclrSignificantOther;
  salutation: CyclrSalutation;
  emailAddresses: CyclrEmailAddress[];
  phoneNumbers: CyclrPhoneNumber[];
  userEnteredMailingAddresses: CyclrMailingAddress[];
  notes: CyclrNote[];
  significantDates: CyclrSignificantDate[];
  socialMediaAccounts: CyclrSocialMedia[];
  websites: CyclrWebsite[];
  tags: string[];
  source: string;
  externalIds: Array<{ label: string; id: string }>;
}

export interface ScenarioRecipient {
  firstName?: string;
  lastName?: string;
  middle?: string;
  nickname?: string;
  prefix?: string;
  suffix?: string;
  phoneticFirstName?: string;
  phoneticLastName?: string;
  significantOther?: {
    firstName?: string;
    lastName?: string;
  };
  salutation?: {
    company?: string;
    jobTitle?: string;
    letterSalutation?: string;
    mailingSalutation?: string;
  };
  emails?: Record<string, string>;
  phones?: Record<string, string | {
    number: string;
    extension?: string;
    primary?: boolean;
  }>;
  addresses?: Array<{
    address1: string;
    address2?: string;
    city: string;
    state: string;
    postalCode: string;
    label?: string;
  }>;
  notes?: Array<{ value: string }>;
  dates?: Array<{ label: string; date: string }>;
  social?: Array<{ label: string; value: string }>;
  websites?: Array<{ label: string; url: string }>;
  tags?: string[];
  externalIds?: Array<{ label: string; id: string }>;
}

export interface Scenario {
  scenarioName: string;
  recipient: ScenarioRecipient;
}

export type CyclrMockSource = "Follow Up Boss" | "Google Contacts" | "HighLevel" | "Apple Contacts" | "QA Ninja";
