import { Timestamp } from 'firebase/firestore';

export interface TestUser {
  accountId: string,
  name: string,
  displayName: string,
  group: string,
  createdAt: string,
  status: string,
}

export interface ApiConfig {
  apiKey: string;
}

export interface JwtConfig {
  privateKey: string;
  saEmail: string;
  expiryLength: number;
}

export interface JwtPayload {
  iat: number;
  exp: number;
  iss: string;
  aud: string;
  sub: string;
  email: string;
}

export interface RecipientName {
  firstName?: string;
  lastName?: string,
  fullName?: string;
  middle?: string;
  nickname?: string;
  phoneticFirstName?: string;
  phoneticLastName?: string;
  prefix?: string;
};

export interface TestRecipient {
  recipientId: string;
  accountId: string;
  isActive: boolean;
  mailingsPaused: boolean;
  createdAt: Timestamp;
  recipientGroupIds: [string];
  name: RecipientName;
  email?: string;
  phone?: string;
  salutation: {
    letterSalutation?: string;
    mailingSalutation?: string;
    company?: string;
    jobTitle?: string;
  },
  significantOther?: {
    fullName?: string;
    firstName?: string;
    lastName?: string;
    namePrefix?: string;
    middleName: string;
    nameSuffix?: string;
  },
  significantDates?: [string];
  websites?: [string];
  notes?: [string];
};

export interface TestAddress {
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  type: string;
  createdAt: Timestamp;
};

export interface TestEmail {
  email: string;
  isPrimary: boolean;
  label?: string;
}

export interface TestRecipientAndAddress {
  scenarioName?: string;
  expectedDeliverability?: string;
  testRecipient: TestRecipient;
  testAddress: TestAddress;
};

export interface RecipientFileData {
  scenarioName: string;
  expectedDeliverability: string;
  accountId: string;
  storePath: string;
  seededRecipient: TestRecipient;
  seededAddress: TestAddress | string;
};

export interface MailingAddressDeliverability {
  code: string;
  message: string;
  status: string;
}

export interface EmailDeliverability {
  code: string;
  emailRisk: string;
  emailStatus: string;
  reason: string | Array<string>;
}

export type ProductGroup = 'digital' | 'postcards' | 'branded-magazines';

export interface RecipientGroupData {
  name?: string; // Optional - will generate if not provided
  description?: string; // Optional - - will generate if not provided
  accountId: string; // Required
  campaignId?: string; // Optional - will generate if not provided
  productGroup: ProductGroup; // Required
}

export interface RecipientGroupResult {
  recipientGroupId: string;
  campaignId: string;
}