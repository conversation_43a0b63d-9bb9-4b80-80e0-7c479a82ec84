import Ajv from "ajv";
import { expect } from "@playwright/test";
import { recipientsByCampaignResponseContract } from "@test-data/recipient-apis/recipients-by-campaign/contract-schema";

export async function validateRecipientsByCampaignResponse(apiResponse: any) {
  const ajv = new Ajv();
    const validate = ajv.compile(recipientsByCampaignResponseContract);
    const isValid = validate(apiResponse);

    console.log(`📊 Response validation result: ${isValid ? "✅ Valid" : "❌ Not Valid"}`);

    if (!isValid) {
      const errorMessages = validate.errors
        ?.map((err) => {
          const path = err.dataPath || "(root)";
          const actualValue = path
            .split("/")
            .filter(Boolean)
            .reduce(
              (obj, key) => (obj && key in obj ? obj[key] : undefined),
              apiResponse
            );

          return `→ ${path} ${err.message}. Received: ${JSON.stringify(actualValue)}`;
        })
        .join("\n");

      expect(
        isValid,
        `❌ API response failed schema validation:\n\n${errorMessages}\n`
      ).toBe(true);
    } else {
      console.log("✅ Schema validation passed");
      console.log("📋 Response structure:", {
        message: apiResponse.message,
        count: apiResponse.count,
        recipientGroup: apiResponse.recipientGroup.map((r) => ({
          id: r.id,
          first_name: r.first_name,
          last_name: r.last_name,
          recipient_group_id: r.recipient_group_id,
          mailings_paused: r.mailings_paused,
          mailing_salutation: r.mailing_salutation,
          letter_salutation: r.letter_salutation,
          address1: r.address1,
          address2: r.address2,
          city: r.city,
          state: r.state,
          zip: r.zip,
        })),
      });
      expect(isValid).toBe(true);
    }
    expect(apiResponse.count).toBeGreaterThan(0);
}