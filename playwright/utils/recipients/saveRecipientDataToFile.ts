import fs from "fs";
import path from "path";
import { RecipientFileData } from "@models/interfaces";
import { RecipientFilePaths } from "@models/enums";

export async function saveRecipientDataToFile(data: RecipientFileData, filePath: RecipientFilePaths): Promise<void> {
  const testRecipientFilePath = path.join(filePath);
  const dirPath = path.dirname(testRecipientFilePath);

  // Ensure directory exists
  if (!fs.existsSync(dirPath)) {
    await fs.promises.mkdir(dirPath, { recursive: true });
    console.log(`📁 Created directory: ${dirPath}`);
  }

  const { seededAddress, ...rest } = data;
  const newEntry = {
    data: seededAddress === 'non' ? rest : data,
  };

  let existingData = [];
  try {
    const fileContents = await fs.promises.readFile(
      testRecipientFilePath,
      "utf-8"
    );
    existingData = JSON.parse(fileContents);
  } catch (err) {
    // File doesn't exist, create it with empty array
    await fs.promises.writeFile(
      testRecipientFilePath,
      JSON.stringify([], null, 2)
    );
    console.log(`📄 Created new file: ${testRecipientFilePath}`);
  }

  existingData.push(newEntry);
  await fs.promises.writeFile(
    testRecipientFilePath,
    JSON.stringify(existingData, null, 2)
  );
  console.log(`✅ Saved data to: ${testRecipientFilePath}`);
}
