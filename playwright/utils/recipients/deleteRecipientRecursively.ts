import { doc, collection, getDocs, writeBatch } from "firebase/firestore";
import { startFirebaseDb } from "@utils/helpers/startFirebaseDb";

const db = startFirebaseDb();

const SUBCOLLECTIONS = [
  "EmailAddresses",  
  "History",
  "UserEnteredMailingAddress",
  "Notes",
  "PhoneNumbers",
  "SignificantDates",
  "SocialMediaAccounts",
  "Websites",
  "ExternalIds",
  "RealEstate"
];

const NESTED_SUBCOLLECTIONS = {
  "EmailAddresses": ["History"],
  "UserEnteredMailingAddress": ["History"],
};

/**
 * Deletes all documents in a collection and its nested subcollections using batch operations.
 * @param path - The Firestore path to the parent document.
 * @param collectionName - The name of the subcollection to delete.
 * @param batch - Firestore batch object for efficient deletion.
 */
async function deleteCollection(path: string, collectionName: string, batch: any): Promise<void> {
  const collectionRef = collection(db, `${path}/${collectionName}`);
  const snapshot = await getDocs(collectionRef);

  for (const doc of snapshot.docs) {
    // If this is UserEnteredMailingAddress, we need to check each document for History
    if (collectionName === "UserEnteredMailingAddress") {
      const historyPath = `${path}/${collectionName}/${doc.id}/History`;
      const historyRef = collection(db, historyPath);
      const historySnapshot = await getDocs(historyRef);

      // Queue deletion for all history documents
      for (const historyDoc of historySnapshot.docs) {
        batch.delete(historyDoc.ref);
        console.log(`📤 Queued deletion for History: ${historyDoc.ref.path}`);
      }
    }

    // Check for other nested subcollections at collection level
    if (NESTED_SUBCOLLECTIONS[collectionName]) {
      for (const nestedCollection of NESTED_SUBCOLLECTIONS[collectionName]) {
        if (nestedCollection !== "History") { // Skip History as we handled it above
          await deleteCollection(`${path}/${collectionName}/${doc.id}`, nestedCollection, batch);
        }
      }
    }

    // Queue deletion for the document itself
    batch.delete(doc.ref);
    console.log(`📤 Queued deletion for ${collectionName}: ${doc.ref.path}`);
  }
}

/**
 * Deletes a recipient and its subcollections recursively using batch operations.
 * @param accountId - The account ID.
 * @param recipientId - The recipient ID.
 */
export async function deleteRecipient(accountId: string, recipientId: string): Promise<void> {
  const basePath = `Account/${accountId}/Recipients/${recipientId}`;
  const batch = writeBatch(db);

  try {
    // Delete all subcollections recursively
    for (const subcollection of SUBCOLLECTIONS) {
      await deleteCollection(basePath, subcollection, batch);
    }

    // Queue deletion for the recipient document itself
    const recipientRef = doc(db, basePath);
    batch.delete(recipientRef);
    console.log(`📤 Queued deletion for root recipient: ${basePath}`);

    // Commit the batch to execute all deletions
    await batch.commit();
    console.log(`✅ Successfully deleted recipient: ${basePath}`);
  } catch (error) {
    console.error(`❌ Error deleting recipient ${recipientId}:`, error);
    throw error;
  }
}
