import { doc, setDoc } from "firebase/firestore";
import { Timestamp } from "firebase/firestore";
import { ProductGroup, RecipientGroupData, RecipientGroupResult } from "@models/interfaces";
import { faker } from "@faker-js/faker";
import { startFirebaseDb } from "@utils/helpers/startFirebaseDb";

const db = startFirebaseDb();

const PRODUCT_GROUPS = {
  DIGITAL: "digital",
  POSTCARDS: "postcards",
  BRANDED_MAGAZINES: "branded-magazines",
} as const;

export async function seedRecipientGroup(data: RecipientGroupData): Promise<RecipientGroupResult> {
  const recipientGroupId = faker.string.uuid();
  const campaignId =
    data.campaignId ||
    faker.number.int({ min: 100000, max: 999999 }).toString();
  const productAssignments = handleAssignmentsForProductGroup(
    data.productGroup,
    campaignId
  );
  const planAssignments = getProductPlansForGroup(data.productGroup); 

  try {
    const recipientGroupPath = `Account/${data.accountId}/RecipientGroups/${recipientGroupId}`;
    const recipientGroupRef = doc(db, recipientGroupPath);

    // Match CreateRecipientGroup.tsx pattern
    const recipientGroupData = {
      name: data.name || `Recipient Group ${recipientGroupId}`,
      description:
        data.description ||
        `Recipient Group ${recipientGroupId}`,
      createdAt: Timestamp.now(),
      isActive: true,
      assignments: productAssignments,
      productPlans: planAssignments,
    };

    await setDoc(recipientGroupRef, recipientGroupData);
    console.log(`✅ Recipient Group seeded: ${recipientGroupPath}`);

    return { recipientGroupId, campaignId };
  } catch (error) {
    console.error("❌ Error seeding Recipient Group:", error);
    throw error;
  }
}

function handleAssignmentsForProductGroup(productGroup: ProductGroup, campaignId?: string): any {
  const generatedId = campaignId || faker.number.int({ min: 100000, max: 999999 }).toString();

  switch (productGroup) {
    case PRODUCT_GROUPS.POSTCARDS:
      if (generatedId && campaignId === "133448") {
        return {
          "8": {
            "0": {
              campaign: generatedId,
              campaignName: `Campaign 14`,
              productType: "Postcard Campaign",
              productTypeId: 8,
              status: "init",
            }
        }
      }
    } else {
      return {
        "8": {
          "0": {
            campaign: generatedId,
            campaignName: `Campaign ${generatedId}`,
            productType: "Postcard Campaign",
            productTypeId: 8,
            status: "init",
          }
        }
      }
    }

    case PRODUCT_GROUPS.BRANDED_MAGAZINES:
      if (generatedId && campaignId === "133247") {
        return {
          "12": {
            campaign: generatedId,
            campaignName: "Mabl Mag Test YSZdhwHGkq",
            productType: "Good to be Home",
            productTypeId: 6,
            status: "active"
          }
        }
      } else {
        return {
          "12": {
            campaign: generatedId,
            campaignName: `Campaign 14`,
            productType: "Magazine Campaign",
            productTypeId: 12,
            status: "init",
          }
        }
      };

    case PRODUCT_GROUPS.DIGITAL:
      return {
        "2": {
          deid: generatedId,
          failed: false,
          isEnabled: true,
        },
        "5": {
          enabled: true,
          failed: false,
          frequency: "bi-weekly",
          isEnabled: true,
          leid: generatedId,
        },
        "6": {
          bpid: generatedId,
          enabled: true,
          rules: "FREQ=WEEKLY;INTERVAL=2;BYDAY=MO",
        }
      };
    default:
      throw new Error("Invalid product group");
  }
}

function getProductPlansForGroup(productGroup: ProductGroup): string[] {
  switch (productGroup) {
    case PRODUCT_GROUPS.POSTCARDS:
      return ["8"];
    case PRODUCT_GROUPS.BRANDED_MAGAZINES:
      return ["12"];
    case PRODUCT_GROUPS.DIGITAL:
      return ["2", "5", "6"];
    default:
      throw new Error(`Invalid product group: ${productGroup}`);
  }
}
