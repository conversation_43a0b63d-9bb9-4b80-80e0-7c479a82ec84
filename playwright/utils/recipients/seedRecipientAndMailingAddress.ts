import {
  TestAddress,
  TestEmail,
  TestRecipientAndAddress,
  RecipientFileData,
} from "@models/interfaces";
import { RecipientFilePaths } from "@models/enums";
import { saveRecipientDataToFile } from "./saveRecipientDataToFile";
import { doc, setDoc } from "firebase/firestore";
import { startFirebaseDb } from "@utils/helpers/startFirebaseDb";

const db = startFirebaseDb();

export async function seedRecipientAndMailingAddress(
  testRecipientAndAddresses: TestRecipientAndAddress[],
  filePath: RecipientFilePaths,
): Promise<void> {
  for (const testRecipientAndAddress of testRecipientAndAddresses) {
    const { scenarioName, expectedDeliverability, testRecipient, testAddress } =
      testRecipientAndAddress;
    const accountId = testRecipient.accountId;
    const recipientStorePath = `Account/${testRecipient.accountId}/Recipients/${testRecipient.recipientId}`;
    const mailingDocStorePath = `Account/${testRecipient.accountId}/Recipients/${testRecipient.recipientId}/UserEnteredMailingAddress/0`;
    const emailDocStorePath = `Account/${testRecipient.accountId}/Recipients/${testRecipient.recipientId}/EmailAddresses/0`;

    const filePathToSave = filePath;

    try {
      // Save recipient to Firestore
      const recipientDocRef = doc(db, recipientStorePath);
      let testRecipientToSeed = { ...testRecipient };
      delete testRecipientToSeed.recipientId;
      let testRecipientEmailToSeed = testRecipientToSeed?.email;
      await setDoc(recipientDocRef, testRecipientToSeed);
      console.log(`✅ Recipient seeded: ${recipientStorePath}`);

      const recipientData: RecipientFileData = {
        scenarioName,
        expectedDeliverability,
        accountId,
        storePath: recipientStorePath,
        seededRecipient: testRecipientToSeed,
        seededAddress: testAddress,
      };

      // Check if the address is a string (indicating no address)
      // If it is a string, we skip the address seeding step
      if (typeof testAddress === "string") {
        console.log(
          '⏭️  Mailing Address Scenario "NON", skipping address seeding step...'
        );
      } else {
        // testAddress is a type of TestAddress, proceed to save to Firestore
        const mailingDocRef = doc(db, mailingDocStorePath);
        const addressData: TestAddress = {
          address1: testAddress.address1,
          address2: testAddress.address2,
          city: testAddress.city,
          state: testAddress.state,
          postalCode: testAddress.postalCode,
          type: testAddress.type,
          createdAt: testAddress.createdAt,
        };
        await setDoc(mailingDocRef, addressData);
        console.log(
          `📬 Root recipient mailing address seeded: ${recipientData.storePath}`
        );
        if (testRecipientEmailToSeed) {
          // Save email address if it exists
          const emailDocRef = doc(db, emailDocStorePath);
          const emailData: TestEmail = {
            email: testRecipientEmailToSeed,
            isPrimary: true,
            label: "Primary Email",
          };
          await setDoc(emailDocRef, emailData);
          console.log(
            `📧 Email address seeded for recipient: ${recipientData.storePath}`
          );
        }

        // Save test recipient and address data to a JSON file
        await saveRecipientDataToFile(recipientData, filePathToSave);
        console.log(`💾 Test data saved successfully`);
      }
    } catch (error) {
      console.error("❌ Error seeding Firestore:", error);
      throw error;
    }
  }
}
