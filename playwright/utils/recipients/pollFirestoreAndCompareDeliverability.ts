import { expect } from "@playwright/test";  
import { DocumentReference, getDoc } from "firebase/firestore";
import { MailingAddressDeliverability, EmailDeliverability } from "@models/interfaces";
import _ from "lodash";

/**
 * Polls Firestore for the `addressDeliverability` field on a recipient document and
 * asserts that it eventually matches the expected deliverability.
 *
 * This function is useful for waiting on asynchronous Firestore writes triggered by
 * background processing (e.g., Cloud Functions) before making an assertion.
 *
 * @param testRecipientDocRef - A reference to the recipient document in Firestore.
 * @param expectedDeliverability - The expected deliverability status of the test scenario.
 * @param scenarioName - A human-readable name for the test scenario (used for debug logging).
 * @param storePath - The Firestore document path (used for debug logging).
 * @param maxPollAttempts - Maximum number of polling attempts (default is 3).
 *
 * @throws Will fail the test if the actual deliverability value does not match the expected value after all attempts.
 */
export async function pollAndCompareAddressDeliverability(
  scenarioName: string,
  storePath: string,
  testRecipientDocRef: DocumentReference, 
  expectedDeliverability: MailingAddressDeliverability, 
  maxPollAttempts?: number
): Promise<void> {
  let attempts = 0;
  maxPollAttempts = maxPollAttempts || 3;
  let actual: MailingAddressDeliverability = null;

  while (!_.isEqual(actual, expectedDeliverability) && attempts < maxPollAttempts) {
    const snapshot = await getDoc(testRecipientDocRef);
    actual = snapshot.get('addressDeliverability');
    console.log('Snapshot Actual: ', actual);
    if(actual.message === 'Failed' || actual.code === 'FA' || actual.status === 'Service Failed') {
      throw new Error(`Address deliverability unexpectedly failed for scenario: ${scenarioName}.\nFirestore Address Deliverability: ${JSON.stringify(actual)}`);      
    }
    if (!_.isEqual(actual, expectedDeliverability)) await new Promise(r => setTimeout(r, 1000));
    console.log(`❓Polling DB: Attempt ${attempts + 1}`);
    attempts++;
  }

  if (!_.isEqual(actual, expectedDeliverability)) {
    console.log(`\n🧪 Scenario: ${scenarioName}`);
    console.log(`📄 Store Path: ${storePath}`);
  }

  expect(expectedDeliverability).toEqual(actual);
}

export async function pollAndCompareEmailDeliverability(
  scenarioName: string,
  storePath: string,
  testRecipientDocRef: DocumentReference, 
  expectedDeliverability: EmailDeliverability, 
  maxPollAttempts?: number
): Promise<void> {
  let attempts = 0;
  maxPollAttempts = maxPollAttempts || 3;
  let actual: EmailDeliverability = null;

  while (!_.isEqual(actual, expectedDeliverability) && attempts < maxPollAttempts) {
    const snapshot = await getDoc(testRecipientDocRef);
    const data = snapshot.data();
    console.log('Snapshot Data: ', data);
    actual = data ? data.emailDeliverability : null;
    if (!actual || actual === undefined) {
      actual = null;
    }

    if(actual) {
      actual.reason = actual.reason === "" ? [] : actual.reason;
    }
    
    if (!_.isEqual(actual, expectedDeliverability)) await new Promise(r => setTimeout(r, 1000));
    console.log(`❓Polling DB: Attempt ${attempts + 1}`);
    attempts++;
  }

  if (!_.isEqual(actual, expectedDeliverability)) {
    console.log(`\n🧪 Scenario: ${scenarioName}`);
    console.log(`\n📄 Store Path: ${storePath}`);
  }

  const expectedDeliverabilityString = JSON.stringify(expectedDeliverability, null, 2);
  const actualDeliverabilityString = JSON.stringify(actual, null, 2);

  expect
    .soft(
      actual,
      `Expected: ${actualDeliverabilityString}, Received: ${expectedDeliverabilityString}`
    )
    .toEqual(expectedDeliverability);
}
