import Ajv from "ajv";
import { expect } from "@playwright/test";
import { recipientsResponseContract } from "@test-data/recipient-apis/recipients/contract-schema";

export function validateRecipientsResponse(apiResponse: any): void {
  const ajv = new Ajv();
    const validate = ajv.compile(recipientsResponseContract);
    const isValid = validate(apiResponse);

    console.log(`📊 Response validation result: ${isValid ? "✅ Valid" : "❌ Not Valid"}`);

    if (!isValid) {
      console.error("❌ Schema Validation Errors:", validate.errors);
    } else {
      console.log("✅ Schema validation passed");
      console.log("📋 Response structure:", {
        message: apiResponse.message,
        count: apiResponse.count,
        recipients: apiResponse.recipients.map((r) => ({
          id: r.id,
          first_name: r.first_name,
          last_name: r.last_name,
          recipient_group_id: r.recipient_group_id,
          mailings_paused: r.mailings_paused,
          mailing_salutation: r.mailing_salutation,
          letter_salutation: r.letter_salutation,
          email: r.email,
        })),
      });
    }

    if (!isValid) {
      const errorMessages = validate.errors
        ?.map((err) => {
          const path = err.dataPath || "(root)";
          const actualValue = path
            .split("/")
            .filter(Boolean)
            .reduce(
              (obj, key) => (obj && key in obj ? obj[key] : undefined),
              apiResponse
            );

          return `→ ${path} ${err.message}. Received: ${JSON.stringify(actualValue)}`;
        })
        .join("\n");

      expect(
        isValid,
        `❌ API response failed schema validation:\n\n${errorMessages}\n`
      ).toBe(true);
    } else {
      expect(isValid).toBe(true);
    }
    expect(apiResponse.count).toBeGreaterThan(0);
}