import { getFirestore, doc, deleteDoc, collection, getDocs } from "firebase/firestore";
import { initializeApp } from "firebase/app";
import { startFirebaseDb } from "@utils/helpers/startFirebaseDb";

const db = startFirebaseDb();

const SUBCOLLECTIONS = [
  "History",
];

export async function deleteRecipientGroup(accountId: string, groupId: string): Promise<void> {
  const basePath = `Account/${accountId}/RecipientGroups/${groupId}`;

  try {
    // Delete all subcollections
    for (const subcollection of SUBCOLLECTIONS) {
      const collectionRef = collection(db, `${basePath}/${subcollection}`);
      const snapshot = await getDocs(collectionRef);

      for (const doc of snapshot.docs) {
        await deleteDoc(doc.ref);
        console.log(`📤 Deleted ${subcollection}: ${doc.ref.path}`);
      }
    }

    // Delete the group document itself
    const groupRef = doc(db, basePath);
    await deleteDoc(groupRef);
    console.log(`✅ Deleted recipient group: ${basePath}`);

  } catch (error) {
    console.error(`❌ Error deleting recipient ${groupId}:`, error);
    throw error;
  }
}
