import jwt from "jsonwebtoken";
import config from "../../configs/env/config";
import { JwtConfig, JwtPayload } from "../../models/interfaces";
import * as path from "path";
import * as fs from "fs";

const audienceClaim = config.RM_TITAN_AUDIENCE_CLAIM;
const expiresInSeconds = 3600; // 1-hour
const bearerTokensFilePath = path.join("./playwright/configs/.auth/client-bearer-tokens.json");
const jwtConfigs: Record<string, JwtConfig> = {
  rmc: {
    privateKey: config.RM_TITAN_PRIVATE_KEY,
    saEmail: config.RM_TITAN_SERVICE_ACCOUNT_EMAIL,
    expiryLength: expiresInSeconds,
  },
  print: {
    privateKey: config.RM_TITAN_PRINT_PRIVATE_KEY,
    saEmail: config.RM_TITAN_PRINT_SERVICE_ACCOUNT_EMAIL,
    expiryLength: expiresInSeconds,
  },
  crm: {
    privateKey: config.RM_TITAN_CRM_PRIVATE_KEY,
    saEmail: config.RM_TITAN_CRM_SERVICE_ACCOUNT_EMAIL,
    expiryLength: expiresInSeconds,
  },
  landingPages: {
    privateKey: config.RM_TITAN_LANDING_PAGES_PRIVATE_KEY,
    saEmail: config.RM_TITAN_LANDING_PAGES_SERVICE_ACCOUNT_EMAIL,
    expiryLength: expiresInSeconds,
  },
};

/**
 * Generates all signed JSON Web Tokens and saves them to a single file
 */
export async function generateAllAndSaveTokens() {
  if (!audienceClaim) {
    throw new Error("Cannot generate and save JWTs without RM_TITAN_AUDIENCE_CLAIM value");
  }

  const tokenData = {
    rmc: { signedToken: "" },
    crm: { signedToken: "" },
    print: { signedToken: "" },
    landingPages: { signedToken: "" },
  };

  for (const [client, config] of Object.entries(jwtConfigs)) {
    const payload: JwtPayload = getJwtPayload(config);
    const formattedKey = formatKey(config.privateKey);
    const signedJwt = await getSignedJwt(payload, formattedKey);
    tokenData[client] = { signedToken: signedJwt };
  }

  try {
    await fs.promises.mkdir(path.dirname(bearerTokensFilePath), { recursive: true });
    await fs.promises.writeFile(bearerTokensFilePath, JSON.stringify(tokenData, null, 2));
    console.log(`All signed JWTs saved successfully to ${bearerTokensFilePath}`);
    return tokenData;
  } catch (error) {
    console.error("Error saving JWTs to JSON file: ", error);
    throw error;
  }
}

function formatKey(key: string): string {
  return key.replace(/\\n/g, '\n');
}

function getJwtPayload(config: JwtConfig): JwtPayload {
  const issuedAt = Math.floor(Date.now() / 1000);
  const expiresAt = issuedAt + expiresInSeconds;

  return {
    iat: issuedAt,
    exp: expiresAt,
    iss: config.saEmail,
    aud: audienceClaim,
    sub: config.saEmail,
    email: config.saEmail,
  };
}

async function getSignedJwt(payload: JwtPayload, privateKey: string) {
  try {
    return jwt.sign(payload, privateKey, { algorithm: "RS256" });
  } catch (error) {
    console.error("Error signing JWT: ", error);
    // throw error;
  }
}

export async function decodeSignedJwt(signedJwt: string) {
  return jwt.decode(signedJwt, { json: true });
}

export async function verifySignedJwt(signedJwt: string, privateKey: string) {
  console.log('PRIV KEY: ', privateKey);
  try {
    return jwt.verify(signedJwt, privateKey, { algorithms: ["RS256"] });
  } catch (error) {
    console.log('Error Verifying Signature: ', error)
  }
}

// Alternately run `NODE_ENV=dev ts-node ./playwright/utils/helpers/generate-jwt.ts` for updating Postman
if (require.main === module) {
  generateAllAndSaveTokens();
}
