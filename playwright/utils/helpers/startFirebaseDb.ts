import { initializeApp } from "firebase/app";
import { getFirestore, Firestore } from "firebase/firestore";
import config from "@configs/env/config";

interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
}

let db: any;

export function startFirebaseDb(): Firestore {
  if (db) {
    return db;
  }

  const firebaseConfig: FirebaseConfig = {
    apiKey: config.RM_TITAN_API_KEY,
    authDomain: `rm-titan-${process.env.NODE_ENV}.firebaseapp.com`,
    projectId: `rm-titan-${process.env.NODE_ENV}`,
  }
  // Initialize Firebase
  const firebaseApp = initializeApp(firebaseConfig);
  db = getFirestore(firebaseApp); 

  console.log(`✅ Firebase initialized for environment: ${process.env.NODE_ENV}`);
  
  return db;

}
