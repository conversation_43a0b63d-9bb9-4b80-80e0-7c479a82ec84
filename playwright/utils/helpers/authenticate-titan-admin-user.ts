import { Page } from "@playwright/test";

export const authenticateTitanAdminUser = async (page: Page, authFilePath: string) => {
  await page.goto('./');

  const auth = JSON.parse(require('fs').readFileSync(authFilePath, 'utf8'));

  await page.evaluate(auth => {
    const indexedDB = window.indexedDB;
    const request = indexedDB.open('firebaseLocalStorageDb');

    request.onsuccess = function (event: any) {
      const db = event.target.result;
      const transaction = db.transaction(['firebaseLocalStorage'], 'readwrite');
      const objectStore = transaction.objectStore('firebaseLocalStorage', { keyPath: 'fbase_key' });
      const localStorage = auth.origins[0].localStorage;
      for (const element of localStorage) {
        const value = element.value;
        console.log('localStorage VALUE', value);
        objectStore.put(JSON.parse(value));
      }
    }
  }, auth);
}
