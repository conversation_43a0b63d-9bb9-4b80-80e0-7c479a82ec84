const path = require('path');
const fs = require('fs');
const csv = require('csv-parser');

async function getBatchReportContactIds(filePath) {
  return new Promise((resolve, reject) => {
    const contactIds = [];

    fs.createReadStream(filePath)
      .pipe(csv()) 
      .on('data', (row) => {
        if (row.contactId) {
          contactIds.push(row.contactId);
        }
      })
      .on('end', () => {
        // Write the contactIds array to a JSON file
        const outputFilePath = path.join(__dirname, '../../test-data/conversions/contacts.json'); 
        fs.writeFile(outputFilePath, JSON.stringify(contactIds, null, 2), (err) => {
          if (err) {
            return reject(err);
          }
          console.log(`Contact IDs have been written to ${outputFilePath}`);
          resolve(contactIds);
        });
      })
      .on('error', (error) => {
        reject(error);
      });
  });
}

getBatchReportContactIds(path.join(__dirname, '../../test-data/conversions/contact-batch-analysis-report.csv'))
  .then((contactIds) => {
    console.log('Contact IDs have been processed:', contactIds);
  })
  .catch((error) => {
    console.error('Error processing contact IDs:', error);
  });

module.exports = { getBatchReportContactIds };
