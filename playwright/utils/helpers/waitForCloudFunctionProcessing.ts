import { getDoc, DocumentReference } from "firebase/firestore";

export async function waitForCloudFunctionProcessing(
  docRef: DocumentReference,
  fieldToCheck: string,
  expectedValue: any,
  maxAttempts: number = 10,
  intervalMs: number = 1000
): Promise<void> {
  let attempts = 0;

  while (attempts < maxAttempts) {
    const snapshot = await getDoc(docRef);
    const data = snapshot.data();

    if (data && data[fieldToCheck] === expectedValue) {
      console.log(`✅ Cloud Function processing complete for ${fieldToCheck}`);
      return;
    }

    console.log(`⏳ Waiting for Cloud Function processing... Attempt ${attempts + 1}`);
    await new Promise((resolve) => setTimeout(resolve, intervalMs));
    attempts++;
  }

  throw new Error(`❌ Cloud Function processing did not complete within ${maxAttempts} attempts`);
}
