import { 
  Cyclr<PERSON><PERSON><PERSON><PERSON>, 
  CyclrEmailAddress, 
  CyclrPhoneNumber, 
  CyclrMailingAddress, 
  CyclrMockSource,
  Scenario 
} from "@models/cyclr-interfaces";

export function createCyclrMockRecipient(scenario: Scenario, recipientGroupId: string, source: CyclrMockSource): CyclrRecipient {
  const r = scenario.recipient;

  // Only create email addresses if they exist
  const emailAddresses: CyclrEmailAddress[] = r.emails 
    ? Object.entries(r.emails).map(([label, email], index) => ({
        email,
        label,
        primary: index === 0
      }))
    : [];

  // Only create mailing addresses if they exist
  const userEnteredMailingAddresses: CyclrMailingAddress[] = 
    r.addresses
        ? r.addresses.map(addr => ({
            address1: addr.address1,
            address2: addr.address2 || "",
            city: addr.city,
            state: addr.state,
            postalCode: addr.postalCode,
            label: addr.label || "Mailing"
          }))
      : [];

  // Only create phone numbers if they exist
  const phoneNumbers: CyclrPhoneNumber[] = r.phones
    ? Object.entries(r.phones).map(([label, data], index) => ({
        phoneNumber: typeof data === 'string' ? data : data.number,
        phoneExtension: typeof data === 'string' ? '' : (data.extension || ''),
        label,
        primary: typeof data === 'string' ? index === 0 : (data.primary || index === 0)
      }))
    : [];

  // Base recipient structure - only include fields that are present
  const recipient: Partial<CyclrRecipient> = {
    recipientGroupIds: [recipientGroupId],
  };

  // Only add name fields if they exist
  if (r.firstName || r.lastName || r.middle || r.nickname || r.prefix || r.suffix || r.phoneticFirstName || r.phoneticLastName) {
    recipient.name = {
      ...(r.firstName !== undefined && { firstName: r.firstName }),
      ...(r.lastName !== undefined && { lastName: r.lastName }),
      ...(r.middle !== undefined && { middle: r.middle }),
      ...(r.nickname !== undefined && { nickname: r.nickname }),
      ...(r.prefix !== undefined && { prefix: r.prefix }),
      ...(r.suffix !== undefined && { suffix: r.suffix }),
      ...(r.phoneticFirstName !== undefined && { phoneticFirstName: r.phoneticFirstName }),
      ...(r.phoneticLastName !== undefined && { phoneticLastName: r.phoneticLastName })
    };
  }

  // Only add significant other if any of its fields exist
  if (r.significantOther?.firstName || r.significantOther?.lastName) {
    recipient.significantOther = {
      firstName: r.significantOther?.firstName || "",
      lastName: r.significantOther?.lastName || ""
    };
  }

  // Only add salutation if any of its fields exist
  if (r.salutation?.letterSalutation || r.salutation?.mailingSalutation || r.salutation?.company || r.salutation?.jobTitle) {
    recipient.salutation = {
      ...(r.salutation?.company !== undefined && { company: r.salutation.company }),
      ...(r.salutation?.jobTitle !== undefined && { jobTitle: r.salutation.jobTitle }),
      ...(r.salutation?.letterSalutation !== undefined && { letterSalutation: r.salutation.letterSalutation }),
      ...(r.salutation?.mailingSalutation !== undefined && { mailingSalutation: r.salutation.mailingSalutation })
    };
  }

  // Only add collection fields if they have values
  if (emailAddresses.length > 0) recipient.emailAddresses = emailAddresses;
  if (phoneNumbers.length > 0) recipient.phoneNumbers = phoneNumbers;
  if (userEnteredMailingAddresses.length > 0) recipient.userEnteredMailingAddresses = userEnteredMailingAddresses;
  if (r.notes?.length > 0) recipient.notes = r.notes;
  if (r.dates?.length > 0) recipient.significantDates = r.dates;
  if (r.social?.length > 0) recipient.socialMediaAccounts = r.social;
  if (r.websites?.length > 0) recipient.websites = r.websites;
  if (r.tags?.length > 0) recipient.tags = r.tags;
  if (r.externalIds?.length > 0) { recipient.externalIds = r.externalIds };

  recipient.source = source;

  return recipient as CyclrRecipient;
}
