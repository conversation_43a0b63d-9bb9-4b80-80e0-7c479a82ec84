// This file contains the contract schema for the response of the recipients API.
// It is used to validate the response structure and types.
export const recipientsResponseContract = {
  type: "object",
  required: ["message", "recipients", "total", "count",],
  properties: {
    message: { type: "string" },
    recipients: {
      type: "array",
      items: {
        type: "object",
        required: [
          "id", 
          "first_name",
          "last_name",
          "recipient_group_id",            
          "mailings_paused",
          "letter_salutation",
          "email",
        ],
        properties: {
          id: { type: "string" },
          first_name: { type: "string" },
          last_name: { type: "string" },
          recipient_group_id: { type: "string" },
          mailing_salutation: { type: "string" },
          letter_salutation: { type: "string" },
          email: { type: "string" },
        }
      }
    },
    total: { type: "number" },
    count: { type: "number" },
  }
};
