// This file contains the contract schema for the response of the recipients by campaign API.
// It is used to validate the response structure and types.
export const recipientsByCampaignResponseContract = {
  type: "object",
  required: ["message", "count", "recipientGroup"],
  properties: {
    message: { type: "string" },
    count: { type: "number" },
    recipientGroup: {
      type: "array",
      items: {
        type: "object",
        required: [
          "id", 
          "recipient_group_id", 
          "mailings_paused", 
          "last_name", 
          "mailing_salutation",
          "letter_salutation"
        ],
        properties: {
          id: { type: "string" },
          first_name: { type: "string" },
          last_name: { type: "string" },
          recipient_group_id: { type: "string" },
          mailings_paused: { type: "boolean" },
          mailing_salutation: { type: "string" },
          letter_salutation: { type: "string" },
          address1: { type: "string" },
          address2: { type: "string" },
          city: { type: "string" },
          state: { type: "string" },
          zip: { type: "string" }
        }
      }
    }
  }
};
