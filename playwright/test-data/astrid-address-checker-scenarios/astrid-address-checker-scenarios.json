[{"scenarioName": "INCOMPLETE_ZIP", "address": {"address1": "5 fieldstone ct", "city": "TELFORD", "state": "PA", "zip": "12"}, "expectedResponseBody": {"status": "OK", "codes": [], "address1": "5 FIELDSTONE CT", "address2": "", "city": "TELFORD", "state": "PA", "zip": "18969"}}, {"scenarioName": "CHANGED_ADDRESS1", "address": {"address1": "5 fieldston ct", "city": "TELFORD", "state": "PA", "zip": "12"}, "expectedResponseBody": {"status": "CHANGED", "codes": [{"title": "Street name modified.", "description": "Input street name was incorrect. Correction applied successfully.", "correction": "1", "error": "0", "display": "We've corrected the street name to match postal records."}], "address1": "5 FIELDSTONE CT", "address2": "", "city": "TELFORD", "state": "PA", "zip": "18969", "zip4": "1351"}}, {"scenarioName": "VALID_ADDRESS_WITH_ADDRESS2", "address": {"address1": "3463 Plumtree Drive", "address2": "APT H", "city": "Ellicott City", "state": "MD", "zip": "21042"}, "expectedResponseBody": {"status": "OK", "codes": [{"title": "All highrise records returned; first result is the CASS-certified address; Zs follow last result.", "description": "Multiple records containing secondary ranges (apartment low – high numbers) returned in the results parameter. End of results is indicated by a string of ten 'Z's (ZZZZZZZZZZ). This error code always appears with error code '00'. See Appendix B for information on highrise addresses.", "correction": "0", "error": "0", "display": "Your address is in a multi-unit building. We've returned the standardized format."}, {"title": "Address does not require apartment/suite; incorrect input.", "description": "This address matched to a default delivery record in a multi-unit building, or a rural/highway contract record with route number in the street name field.", "correction": "0", "error": "0", "display": "We found a match for this address, but the apartment/suite number you provided isn't needed or doesn't apply."}], "address1": "3463 PLUMTREE DR APT H", "address2": "", "city": "ELLICOTT CITY", "state": "MD", "zip": "21042"}}, {"scenarioName": "NON_US_ADDRESS", "address": {"address1": "23 PAWNEE BAY", "city": "Winnipeg", "state": "MB", "zip": "R2J 2C8"}, "expectedResponseBody": {"status": "ERROR", "message": "Address not in the USPS permissable state list", "codes": [{"code": "INVALID_STATE", "message": "Address not in the USPS permissable state list"}]}}, {"scenarioName": "FULL_STATE_NAME", "address": {"address1": "2504 29TH AVENUE WEST", "city": "SEATTLE", "state": "Washington", "zip": "98199"}, "expectedResponseBody": {"status": "OK", "codes": [], "address1": "2504 29TH AVE W", "address2": "", "city": "SEATTLE", "state": "WA", "zip": "98199"}}, {"scenarioName": "WRONG_ZIP", "address": {"address1": "836 Elbow Ln", "city": "Warrington", "state": "pa", "zip": "18975"}, "expectedResponseBody": {"status": "CHANGED", "codes": [{"title": "No match in 5-digit ZIP Code; match found in finance number.", "description": "Input ZIP code was incorrect. Correction applied successfully using city/state information provided. See Appendix B for information on finance numbers.", "correction": "1", "error": "0", "display": "We've corrected your ZIP code based on the city and state you provided."}], "address1": "836 ELBOW LN", "address2": "", "city": "WARRINGTON", "state": "PA", "zip": "18976", "zip4": "2026"}}, {"scenarioName": "NON_DELIVERABLE", "address": {"address1": "279 Holderness RD", "city": "Sandwich", "state": "new hampshire", "zip": "03227"}, "expectedResponseBody": {"status": "ERROR", "codes": [{"title": "Address non-deliverable; no add-on assigned.", "description": "Delivery Point Validation (DPV) check failed.", "correction": "0", "error": "1", "display": "This address appears to be non-deliverable according to postal records. Please verify all details."}]}}, {"scenarioName": "CITY_EXTRA_SPACE", "address": {"address1": "107 joshua drive", "city": "hanover  ", "state": "pennsylvania", "zip": "17331"}, "expectedResponseBody": {"status": "OK", "codes": [], "address1": "107 JOSHUA DR", "address2": "", "city": "HANOVER", "state": "PA", "zip": "17331"}}, {"scenarioName": "MISSING_ADDRESS1", "address": {"address1": "", "city": "LUBBOCK", "state": "TX", "zip": "79423"}, "expectedResponseBody": {"status": "ERROR", "message": "Missing required fields: address1"}}]