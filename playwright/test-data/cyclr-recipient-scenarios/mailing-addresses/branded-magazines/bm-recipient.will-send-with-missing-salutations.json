[{"scenarioName": "SALUTATIONS_HAS_FIRST_LAST", "recipient": {"firstName": "<PERSON>", "lastName": "Bath", "addresses": [{"address1": "3602 110th St", "address2": "", "city": "<PERSON><PERSON><PERSON>", "state": "TX", "postalCode": "79423"}], "tags": ["Magazine", "Postcards"]}, "expectedSalutations": {"letterSalutation": "<PERSON>", "mailingSalutation": "<PERSON>"}, "expectedDeliverability": {"code": "WS", "message": "Will <PERSON>", "status": "Good Exclusive Address"}}, {"scenarioName": "SALUTATIONS_FIRST_NAME_ONLY", "recipient": {"firstName": "<PERSON>", "addresses": [{"address1": "10718 Knoxville Ave", "address2": "", "city": "<PERSON><PERSON><PERSON>", "state": "TX", "postalCode": "79423"}], "tags": ["Magazine", "Postcards"]}, "expectedSalutations": {"letterSalutation": "<PERSON>", "mailingSalutation": "<PERSON>"}, "expectedDeliverability": {"code": "WS", "message": "Will <PERSON>", "status": "Good Exclusive Address"}}, {"scenarioName": "SALUTATIONS_LAST_NAME_ONLY", "recipient": {"lastName": "Bug Zappers, LLC", "salutation": {"company": "Bug Zappers, LLC"}, "addresses": [{"address1": "10724 Kenosha Ave", "address2": "", "city": "<PERSON><PERSON><PERSON>", "state": "TX", "postalCode": "79423"}], "tags": ["Magazine", "Postcards"]}, "expectedSalutations": {"letterSalutation": "Current Resident", "mailingSalutation": "Current Resident"}, "expectedDeliverability": {"code": "WS", "message": "Will <PERSON>", "status": "Good Exclusive Address"}}, {"scenarioName": "SALUTATIONS_MISSING_FIRST_AND_LAST_NAMES", "recipient": {"addresses": [{"address1": "10703 Kenosha Ave", "address2": "", "city": "<PERSON><PERSON><PERSON>", "state": "TX", "postalCode": "79423"}], "tags": ["Magazine", "Postcards"]}, "expectedSalutations": {"letterSalutation": "Current Resident", "mailingSalutation": "Current Resident"}, "expectedDeliverability": {"code": "WS", "message": "Will <PERSON>", "status": "Good Exclusive Address"}}]