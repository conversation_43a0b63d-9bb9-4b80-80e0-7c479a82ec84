[{"scenarioName": "GOOD_ADDRESS_SCENARIO_1_ALL_FIELDS", "recipient": {"firstName": "<PERSON>", "middle": "D", "lastName": "Dannheim", "nickname": "<PERSON>", "prefix": "Mr.", "suffix": "Sr.", "phoneticFirstName": "Loo-kas", "phoneticLastName": "<PERSON><PERSON><PERSON><PERSON>e", "salutation": {"company": "Ridge and Gable Roofing", "jobTitle": "Owner/Operator"}, "significantOther": {"firstName": "<PERSON>", "lastName": "Dannheim"}, "emails": {"Primary": "<EMAIL>"}, "phones": {"Mobile": "+18065354567", "Work": {"number": "+18065354569", "extension": "123", "primary": false}}, "addresses": [{"address1": "4001 140th St", "address2": "", "city": "<PERSON><PERSON><PERSON>", "state": "TX", "postalCode": "79423", "label": "Home"}], "notes": [{"value": "Home Builder"}, {"value": "DBC Custom Homes"}, {"value": "Roofer"}, {"value": "Ridge and Gable Roofing"}], "dates": [{"label": "Birthday", "date": "1981-05-15"}, {"label": "Anniversary", "date": "2003-06-20"}], "social": [{"label": "Facebook", "value": "https://www.facebook.com/lucas.dannheim"}, {"label": "Instagram", "value": "@dbc"}], "websites": [{"label": "DBC Lubbock", "url": "https://www.https://www.dbclubbock.com"}, {"label": "Ridge and Gable Roofing", "url": "https://www.ridgegable.com"}], "tags": ["Magazine", "Postcards"], "externalIds": [{"label": "Follow Up Boss", "externalId": "999999999999"}]}, "expectedSalutations": {"letterSalutation": "Lucas & Katie", "mailingSalutation": "<PERSON> & Katie <PERSON>"}, "expectedDeliverability": {"code": "WS", "message": "Will <PERSON>", "status": "Good Exclusive Address"}}, {"scenarioName": "GOOD_ADDRESS_SCENARIO_2", "recipient": {"firstName": "<PERSON>", "lastName": "C<PERSON><PERSON><PERSON><PERSON>", "salutation": {"letterSalutation": "The Cruickshank Family", "mailingSalutation": "<PERSON>"}, "addresses": [{"address1": "2903 107th St", "address2": "", "city": "<PERSON><PERSON><PERSON>", "state": "TX", "postalCode": "79423", "label": "Home"}], "tags": ["Magazine", "Postcards"], "externalIds": [{"label": "Follow Up Boss", "externalId": "999999999998"}]}, "expectedSalutations": {"letterSalutation": "The Cruickshank Family", "mailingSalutation": "<PERSON>"}, "expectedDeliverability": {"code": "WS", "message": "Will <PERSON>", "status": "Good Exclusive Address"}}, {"scenarioName": "GOOD_ADDRESS_SCENARIO_3", "recipient": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "addresses": [{"address1": "10502 Hudson Ave", "city": "<PERSON><PERSON><PERSON>", "state": "TX", "postalCode": "79423", "label": "Home"}], "tags": ["Magazine", "Postcards"]}, "expectedSalutations": {"letterSalutation": "<PERSON>", "mailingSalutation": "<PERSON>"}, "expectedDeliverability": {"code": "WS", "message": "Will <PERSON>", "status": "Good Exclusive Address"}}, {"scenarioName": "GOOD_ADDRESS_SCENARIO_4", "recipient": {"firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "addresses": [{"address1": "3311 104th St", "address2": "", "city": "<PERSON><PERSON><PERSON>", "state": "TX", "postalCode": "79423", "label": "Home"}], "tags": ["Magazine", "Postcards"]}, "expectedSalutations": {"letterSalutation": "<PERSON><PERSON>", "mailingSalutation": "<PERSON><PERSON>"}, "expectedDeliverability": {"code": "WS", "message": "Will <PERSON>", "status": "Good Exclusive Address"}}, {"scenarioName": "GOOD_ADDRESS_SCENARIO_5_DUPLICATE_OF_SCENARIO_4", "recipient": {"firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "addresses": [{"address1": "3311 104th St", "address2": "", "city": "<PERSON><PERSON><PERSON>", "state": "TX", "postalCode": "79423", "label": "Home"}], "tags": ["Magazine", "Postcards"]}, "expectedSalutations": {"letterSalutation": "<PERSON><PERSON>", "mailingSalutation": "<PERSON><PERSON>"}, "expectedDeliverability": {"code": "WS", "message": "Will <PERSON>", "status": "Good Exclusive Address"}}]