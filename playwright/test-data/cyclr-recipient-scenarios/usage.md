# Usage Guide for Cyclr Recipient Scenarios

## Overview

This directory contains JSON files that define test scenarios for Cyclr recipient data. These scenarios are used in Playwright tests to validate recipient creation, mailing address and email validation, deliverability with exclusivity calculations, group count calculations, and many other behaviors. The main advantage of this setup is that adding or updating test cases only requires modifying or creating new JSON files—no changes to the test code are necessary. See below for instructions on creating a new scenario file making sure to follow the interface patterns at the bottom of this doc.

---

## Directory Structure

The scenarios are organized into subdirectories based on the type of data being tested with group product assignments:

- **Email Addresses**: Scenarios for valid and invalid email addresses.
- **Mailing Addresses**: Scenarios for good, bad, incomplete, and exclusive mailing addresses.
- **Salutations**: Scenarios for recipients with missing or partial salutation data.

---

## Adding a New Scenario File

### Steps to Add a New Scenario Batch

1. **Identify the Type of Scenario**:
   - Determine whether the new scenario involves email addresses, mailing addresses, salutations, or another type of recipient data based on the product.

2. **Navigate to the Appropriate Subdirectory**:
   - For example, if the scenario involves mailing addresses for branded magazines, navigate to:
     ```
     /playwright/test-data/cyclr-recipient-scenarios/mailing-addresses/branded-magazines/
     ```

3. **Create a New JSON File**:
   - Name the file descriptively, e.g., `bm-recipient.new-scenario.json`.

4. **Define the Scenario**:
   - Use the following structure as a template:
     ```json
     {
       "scenarioName": "NEW_SCENARIO_NAME",
       "recipient": {
         "firstName": "John",
         "lastName": "Doe",
         "emails": {
           "Work": "<EMAIL>"
         },
         "phones": {
           "Mobile": "+1234567890"
         },
         "addresses": [
           {
             "address1": "123 Main St",
             "city": "Springfield",
             "state": "IL",
             "postalCode": "62704",
             "label": "Home"
           }
         ],
         "tags": ["Magazine", "Postcards"]
       },
       "expectedSalutations": {
         "mailingSalutation": "John Doe"
       },
       "expectedDeliverability": {
         "code": "OK",
         "message": "Deliverable",
         "status": "Deliverable"
       }
     }
     ```

5. **Save the File**:
   - Ensure the file is saved with a `.json` extension.

---

## Example JSON Structures

### Valid Email Scenario
```json
{
  "scenarioName": "VALID_EMAIL_SCENARIO",
  "recipient": {
    "firstName": "Jane",
    "lastName": "Smith",
    "emails": {
      "Work": "<EMAIL>"
    },
    "tags": ["Digital"]
  },
  "expectedSalutations": {
    "mailingSalutation": "Jane Smith"
  },
  "expectedDeliverability": {
    "code": "OK",
    "emailRisk": "low",
    "emailStatus": "deliverable",
    "reason": []
  }
}
```

### Bad Address Scenario
```json
{
  "scenarioName": "BAD_ADDRESS_SCENARIO",
  "recipient": {
    "firstName": "John",
    "lastName": "Doe",
    "addresses": [],
    "tags": ["Magazine"]
  },
  "expectedSalutations": {
    "mailingSalutation": "John Doe"
  },
  "expectedDeliverability": {
    "code": "IA",
    "message": "Bad Address",
    "status": "Bad Address"
  }
}
```

---

## Integration with Tests

### How Scenarios Are Used

1. **Import the JSON File**:
   - In the test file (e.g., `cyclr.post-recipients.spec.ts`), import the scenario:
     ```typescript
     import scenarios from "@test-data/cyclr-recipient-scenarios/mailing-addresses/branded-magazines/bm-recipient.good-and-exclusive-addresses.json";
     ```

2. **Iterate Through Scenarios**:
   - Use a loop to test each scenario:
     ```typescript
     for (const scenario of scenarios) {
       test(`Post Recipient - ${scenario.scenarioName}`, async ({ request }) => {
         const recipientData = createCyclrMockRecipient(scenario, recipientGroupId, source);
         const response = await request.post(`recipients`, {
           headers: {
             "X-API-Key": apiKey,
             "Content-Type": "application/json",
           },
           data: recipientData,
         });

         expect(response.status()).toBe(201);
         const body = await response.json();
         expect(body).toHaveProperty("status", 201);
         expect(body).toHaveProperty("message", "Recipient created successfully.");
       });
     }
     ```

---

## Interfaces

The TypeScript interfaces are used to define the structure of recipients and scenarios:

### Examples:
- **CyclrRecipient**: Represents the recipient object.
- **CyclrEmailAddress**: Represents email addresses associated with the recipient.
- **CyclrPhoneNumber**: Represents phone numbers associated with the recipient.
- **CyclrMailingAddress**: Represents mailing addresses associated with the recipient.
- **Scenario**: Represents the test scenario, including recipient data and expected outcomes.

### Location of Interfaces
The interfaces are located in the following file:
```
/titan/playwright/models/cyclr-interfaces.ts
```

---

## Notes

- **Scenario Naming**: Use descriptive names for scenarios to make them easy to identify in test results.
- **Validation**: Ensure the JSON structure matches the expected format to avoid test failures.
- **Scalability**: This approach allows for easy expansion of test coverage by simply adding new JSON files.

---
