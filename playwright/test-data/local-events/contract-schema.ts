import { Timestamp } from 'firebase/firestore';

export interface LastSyncMarket {
  id: string;
  location: {
    geopoint: {
      latitude?: number;
      longitude?: number;
    } | null;
  };
  event_count: number;
  metadata: {};
}

export interface LocalEventsMarket {
  id: string;
  uuid: string;
  name: string;
  address: string;
  location: {
    latitude: number;
    longitude: number;
    geopoint: {
      lat?: number;
      lon?: number;
    } | null;
  };
  radius: number;
  timezone: string;
  utc_offset_hours: number;
  utc_dst_offset_hours: number;
  event_count: number;
  isActive: boolean;
  metadata: {
    airtableId: string;
    baseId: string;
    createdTime: string | object;
    lastModified: string | object;
    lastSyncTime: string | object;
  };
}

export interface LocalEventsEvent {
  id: string;
  uuid: string;
  name: string;
  description?: string;
  start_date: Timestamp;
  end_date?: string;
  instance_date?: string;
  event_dates?: any[];
  event_type?: string;
  venue: {
    uuid?: string;
    name?: string;
    address_1?: string;
    address_2?: string;
    city?: string;
    region?: string;
    postal_code?: string;
    country?: string;
    latitude?: number;
    longitude?: number;
    g_identifier?: string;
  };
  urls: {
    source?: string;
    ticket?: string;
    image?: string;
  };
  prices: {
    minimum?: number;
    maximum?: number;
  };
  popularity_score?: number;
  image_count?: number;
  image_alt_text_english?: string;
  flags?: string[];
  _searchTerms?: string[];
  _isActive?: boolean;
  _isHidden?: boolean;
  _isPostponed?: boolean;
  _cancellationStatus?: string;
  cancelled?: boolean;
  sold_out?: boolean;
  not_yet_on_sale?: boolean;
  annual?: boolean;
  travel_worthy?: boolean;
  virtual_rule?: string;
  area_uuid: string;
  areas?: string[];
  recurring_event_uuid?: string;
  umbrella_event_uuid?: string;
  _airtableId?: string;
  _baseId?: string;
  _createdTime?: string;
  _lastModified?: string;
  _lastSyncTime?: string;
}

export interface LiveEvent {
  id: string;
  uuid: string;
  name: string;
  description?: string;
  start_date: {
    _seconds: number;
    _nanoseconds?: number;
  };
  end_date?: Timestamp;
  instance_date?: Timestamp | null;
  event_dates?: string;
  event_type?: string;

  venue_name?: string;
  venue_address_1?: string;
  venue_address_2?: string | null;
  venue_city?: string;
  venue_region?: string;
  venue_postal_code?: string;
  venue_country?: string | null;
  venue_latitude?: number;
  venue_longitude?: number;
  venue_g_identifier?: string | null;
  venue_uuid?: string;

  urls: {
    source?: string;
    ticket?: string | null;
    image?: string;
  };

  prices: {
    minimum?: number | null;
    maximum?: number | null;
  };

  popularity_score?: number;
  image_count?: number;
  image_alt_text_english?: string | null;
  flags?: string[];
  _searchTerms?: string[];
  _cancellationStatus?: string | null;
  cancelled?: boolean | null;
  sold_out?: boolean | null;
  not_yet_on_sale?: boolean | null;
  annual?: boolean;
  travel_worthy?: string | boolean | null;
  virtual_rule?: string;

  area_uuid: string;
  areas?: string[];
  recurring_event_uuid?: string | null;
  umbrella_event_uuid?: string | null;

  _airtableId?: string;
  _baseId?: string;
  _createdTime?: Timestamp;
  _lastModified?: Timestamp;
  _lastSyncTime?: Timestamp;
}

export interface GetAllMarketsResponse {
  success: boolean;
  markets: LocalEventsMarket[];
  count: number;
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters_applied: {
    active_only: boolean;
    min_events: number | null;
    search: string | null;
    sort_by: string;
    order: string;
  };
}

export interface GetMarketStatsResponse {
  success: boolean;
  statistics: {
    total_markets: number;
    active_markets: number;
    inactive_markets: number;
    total_events_across_all_markets: number;
    markets_with_events: number;
    markets_without_events: number;
    average_events_per_market: number;
    markets_by_timezone: Record<string, number>;
    top_markets_by_event_count: Array<{
      name: string;
      uuid: string;
      event_count: number;
    }>;
  };
  generated_at: string;
}

export interface GetEventsByAreaResponse {
  success: boolean;
  area: {
    uuid: string;
    name: string;
    address: string;
    latitude: number;
    longitude: number;
    radius: number;
    timezone: string;
    event_count: number;
    isActive: boolean;
    _airtableId: string;
  };
  events: LocalEventsEvent[];
  count: number;
  total_in_area: number;
  filters_applied: {
    active_only: boolean;
    start_date?: string;
    end_date?: string;
    popularity_min?: number;
    blocked_tags_removed: boolean;
  };
  pagination?: {
    has_more: boolean;
    next_page_token?: string;
    limit: number;
  };
}

export interface GetLiveEventsResponse {
  success: boolean;
  events: LiveEvent[];
  count: number;
  pagination?: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface SearchAreasResponse {
  success: boolean;
  areas: Array<{
    id: string;
    uuid: string;
    name: string;
    address: string;
    latitude: number;
    longitude: number;
    radius: number;
    timezone: string;
    event_count: number;
    distance_km?: number;
    _airtableId: string;
  }>;
  count: number;
  total_found?: number;
  search_type: 'name' | 'location';
  search_term?: string;
  search_center?: {
    latitude: number;
    longitude: number;
  };
  search_radius_km?: number;
}

export interface ErrorResponse {
  success: false;
  error: string;
  message?: string;
}
