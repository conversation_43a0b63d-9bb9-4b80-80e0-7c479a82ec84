import { test as base } from "@playwright/test";
import envs from "@configs/env/config";
import { TestUser } from '@models/interfaces';

import { TitanLoginPage } from "@pages/titan-login.page";
import { TitanAccountsListPage } from "@pages/titan-accounts-list.page";

const testAccountId: string = envs.TITAN_TEST_ACCOUNT_ID || '****************';

export const test = base.extend<{ 
  titanLoginPage: TitanLoginPage,
  titanAccountsListPage: TitanAccountsListPage,
}>({
  titanLoginPage: async ({ page, context }, use) => {
    await use(new TitanLoginPage(page, context));
  },
  titanAccountsListPage: async ({ page, context }, use) => {
    await use(new TitanAccountsListPage(page, context, testAccountId));
  },
});

export { expect } from '@playwright/test';
