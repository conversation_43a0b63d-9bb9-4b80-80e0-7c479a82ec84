// import { test as base } from '@fixtures/base.api';
// import { LocalEventsAPI } from '@apis/local-events.api';
// import config from '@configs/env/config';
// import testData from '@test-data/local-events/test-data.json';

// export const test = base.extend<{
//   localEventsAPI: LocalEventsAPI;
//   testData: typeof testData;
// }>({
//   localEventsAPI: async ({ request }, use) => {
//     const baseURL = config.RM_TITAN_BASE_URI || 'https://us-central1-rm-titan-dev.cloudfunctions.net';
//     await use(new LocalEventsAPI(request, baseURL));
//   },
//   testData: async ({}, use) => {
//     await use(testData);
//   },
// });

// export { expect } from '@playwright/test';
