import { test as base, request, APIRequestContext } from '@playwright/test';
import { faker } from '@faker-js/faker';
import { generateAllAndSaveTokens } from '@utils/helpers/generate-jwt';
import { JwtFixture } from 'apis/generateJwtFixture';
import { RecipientGroup } from 'external-api/recipient-group.api';
import { Recipient } from 'external-api/recipient.api';
import config from "@configs/env/config";
import { LocalEventsAPI } from '@apis/local-events.api';
import localEventsTestData from '@test-data/local-events/test-data.json';


const timestamp = Date.now();
export const test = base.extend<{
  auth: JwtFixture, 
  recipientGroup: RecipientGroup,
  recipient: Recipient,
  titanBaseApi: APIRequestContext,
  localEventsApi: LocalEventsAPI,
  localEventsTestData: typeof localEventsTestData;
}>({
  auth: async ({}, use) => {
    const jwts = await generateAllAndSaveTokens();
    await use(new JwtFixture(jwts));
  },
  recipientGroup: async ({}, use) => {
    await use(new RecipientGroup(faker.animal.dog(), `Playwright Test ${timestamp}`));
  },
  recipient: async ({}, use) => {
    await use(new Recipient(faker.animal.bird(), faker.animal.horse(), ['v98yaoe87aoishaas']));
  },
  titanBaseApi: async ({ }, use) => {
    const context = await request.newContext({
      baseURL: config.RM_TITAN_BASE_URI + '/', 
    });
    await use(context);
    await context.dispose();
  },
  localEventsApi: async ({ request }, use) => {
    const baseURL = config.RM_TITAN_BASE_URI || 'https://us-central1-rm-titan-dev.cloudfunctions.net';
    await use(new LocalEventsAPI(request, baseURL));
  },
  localEventsTestData: async ({ }, use) => {
    await use(localEventsTestData);
  }
});

export { expect } from '@playwright/test';
