import dotenv, { DotenvConfigOutput } from "dotenv";

const env: string = process.env.NODE_ENV || "local"; // 'local', 'dev', 'prod', 'stage'

const result: DotenvConfigOutput = dotenv.config({
  path: `playwright/.env`,
});

if (result.error) {
  throw result.error;
}

const { parsed } = result;

function isEnvDefined(envs: any): envs is { [key: string]: string } {
  return envs !== undefined && envs !== null;
}

if (!isEnvDefined(parsed)) {
  throw new Error("Environment variables not properly loaded");
}

const envs = { ...parsed, ...process.env };

export default envs;
