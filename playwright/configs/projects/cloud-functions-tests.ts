import { PlaywrightTestConfig, Project } from '@playwright/test';
import envs from '../env/config';

export const cloudFunctionsProjects: Project[] = [
  // Address Validation On Create Mailing Address
  // This project is designed to test the address validation functionality
  {
    name: "address-validation",
    testDir: "./playwright/tests/functions/address-validation",
    testMatch: "**/on-create-mailing-address.spec.ts",
    fullyParallel: false,
    use: {
      tag: '@cloud-function',
      baseURL: envs.RM_TITAN_BASE_URI,
    },
    dependencies: ['address-validation-setup'],
  },

  {
    name: "address-validation-setup",
    testDir: "./playwright/tests/functions/address-validation",
    testMatch: "**/setup.spec.ts",
    fullyParallel: false,
    use: {
      tag: '@cloud-function-setup',
      baseURL: envs.RM_TITAN_BASE_URI,
    },
  },
];