import { Project } from '@playwright/test';
import envs from '../env/config';

export const cyclrApiProjects: Project[] = [
  // External Integrations E2E Tests
  {
    name: "cyclr-integrations-setup",
    testDir: "./playwright/tests/api/cyclr-integrations",
    testMatch: "**/cyclr.setup.spec.ts",
    fullyParallel: false,
    use: {
      tag: '@integration',
      baseURL: `${envs.RM_TITAN_EXTERNAL_API_URL}/api/v1`,
    },
  },
  {
    name: "cyclr-integrations-post-recipients",
    testDir: "./playwright/tests/api/cyclr-integrations",
    testMatch: "**/cyclr.post-recipients.spec.ts",
    fullyParallel: false,
    use: {
      tag: '@integration',
      baseURL: `${envs.RM_TITAN_EXTERNAL_API_URL}/api/v1/`,
    },
    dependencies: ["cyclr-integrations-setup"],
  },
];
