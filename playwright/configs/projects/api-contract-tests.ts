import { Project } from '@playwright/test';
import envs from '../env/config';

export const apiContractProjects: Project[] = [
  // External Integrations Contract Tests
  {
    name: "external-api-recipients",
    testDir: "./playwright/tests/api/system-contracts/external",
    testMatch: "**/external-api-recipients.spec.ts",
    fullyParallel: false,
    use: {
      tag: '@contract',
      baseURL: envs.RM_TITAN_EXTERNAL_API_URL,
    },
  },

  // API System Contract Tests
  // RMC Authentication with Short-Lived Tokens Contracts
  {
    name: "short-lived-tokens",
    testDir: "./playwright/tests/api/system-contracts/rmc",
    testMatch: "**/short-lived-tokens.spec.ts",
    fullyParallel: true,
    use: {
      tag: '@contract',
      baseURL: envs.RM_TITAN_BASE_URI,
    },
  },

  // Landing Pages System Contract Tests 
  // Shorten URL Contract
  {
    name: "landing-pages-shorten-url",
    testDir: "./playwright/tests/api/system-contracts/landing-pages",
    testMatch: "**/shortenUrl.spec.ts",
    fullyParallel: false,
    use: {
      tag: '@contract',
      baseURL: envs.RM_TITAN_BASE_URI,
    },
  },

  // Astrid Contract Tests
  // Address Validation Contract
  {
    name: "astrid-address-checker",
    testDir: "./playwright/tests/api/system-contracts/astrid/address-validation",
    testMatch: "**/astrid-address-checker.spec.ts",
    fullyParallel: true,
    use: {
      tag: '@contract',
      baseURL: envs.ASTRID_API_URL,
    },
  },

  // Recipients APIs System Contract Tests
  // Printing System - Recipients By Campaign
  {
    name: "printing-recipients-by-campaign",
    testDir: "./playwright/tests/api/system-contracts/printing",
    testMatch: "**/recipientsByCampaign.spec.ts",
    fullyParallel: true,
    use: { 
      tag: '@contract',
      baseURL: envs.RM_TITAN_BASE_URI 
    }
  },

  // Recipients APIs System Contract Tests
  // RMC System - Recipients
  {
    name: "digital-recipients",
    testDir: "./playwright/tests/api/system-contracts/digital",
    testMatch: "**/recipients.spec.ts",
    fullyParallel: true,
    use: { 
      tag: '@contract',
      baseURL: envs.RM_TITAN_BASE_URI 
    }
  },

  // Local Events System Contract Tests
  {
    name: "local-events",
    testDir: "./playwright/tests/api/system-contracts/local-events",
    testMatch: "**/*",
    fullyParallel: true,
    use: { 
      tag: '@contract',
      baseURL: envs.RM_TITAN_BASE_URI 
    }
  }
];
