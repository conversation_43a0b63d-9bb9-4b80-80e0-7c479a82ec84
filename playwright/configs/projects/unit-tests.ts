import { PlaywrightTestConfig, Project } from '@playwright/test';
import envs from '../env/config';

export const unitTestProjects: Project[] = [
  // Mailing Address Validation
  {
    name: "mailing-address-validation-unit-test",
    testDir: "./playwright/tests/unit/address-validation",
    testMatch: "**/*",
    fullyParallel: true,
    use: {
      tag: '@unit',
      baseURL: envs.RM_TITAN_BASE_URI,
    },
  },
  
  // Importer Unit Tests
  // Validate Specific Mappings
  {
    name: "importer-header-mapping-unit-tests",
    testDir: "./playwright/tests/unit/importer",
    testMatch: "**/validateSpecificMappings.spec.ts",
    fullyParallel: true,
    use: {
      tag: '@unit',
      baseURL: envs.RM_TITAN_BASE_URI,
    },
  },

  // Production Header Mapping Scenarios -- DO NOT USE IN CI/CD PIPELINE
  // This test suite is designed to validate the header mapping scenarios for help
  // with development using real-world CSV file headers used in production. It is 
  // not intended for use in CI/CD pipelines. 
  // It is recommended to run this test suite manually to ensure that the header 
  // fuzzy mapping & thresholds are working as expected.
  {
    name: "importer-production-header-mapping-scenarios",
    testDir: "./playwright/tests/unit/importer",
    testMatch: "**/validateCsvHeaderMappings.spec.ts",
    fullyParallel: true,
    use: {
      tag: '@unit-integration',
      baseURL: envs.RM_TITAN_BASE_URI,
    },
  },

  // Merge Tag Resolution Unit Tests
  {
    name: "merge-tag-resolution-unit-tests",
    testDir: "./playwright/tests/unit/merge-tags",
    testMatch: "**/*.spec.ts",
    fullyParallel: true,
    use: {
      tag: '@unit',
      baseURL: envs.RM_TITAN_BASE_URI,
    },
  },

];
