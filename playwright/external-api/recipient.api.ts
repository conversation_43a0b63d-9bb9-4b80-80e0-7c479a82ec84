import { faker } from '@faker-js/faker';
import * as fs from 'fs';
import * as path from 'path';

export class Recipient {
  readonly firstName: string;
  readonly lastName: string;
  readonly recipientGroupIds: string[];

  constructor(firstName: string, lastName: string, recipientGroupIds: string[]) {
    this.firstName = firstName;
    this.lastName = lastName;
    this.recipientGroupIds = recipientGroupIds;
  }

  async writeRecipientToFile(response: Recipient) {
    try {
      const filePath = path.join(__dirname, '../test-data/external-api/recipient.json');
      await fs.promises.writeFile(filePath, JSON.stringify({ response }));
      console.log(`Recipient saved successfully. See /titan/playwright/test-data/external-api/recipient.json}`);
    } catch (error) {
      console.log('Error writing Recipient to File:', error);
    }
  }

  async generateNewPostBody() {
    const timestamp = Date.now();
    return {
      name: {
        firstName: faker.animal.cat(),
        lastName: `Playwright Test Edit ${timestamp}`
      },
      recipientGroupIds: this.recipientGroupIds
    }
  }
}
