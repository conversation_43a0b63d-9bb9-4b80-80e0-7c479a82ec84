import { faker } from '@faker-js/faker';
import * as fs from 'fs';
import * as path from 'path';

export class RecipientGroup {
  readonly name: string;
  readonly description: string;

  constructor(name: string, description: string) {
    this.name = name;
    this.description = description;
  }

  async writeGroupToFile(response: RecipientGroup) {
    try {
      const filePath = path.join(__dirname, '../test-data/external-api/recipient-group.json');
      await fs.promises.writeFile(filePath, JSON.stringify({ response }));
      console.log(`Recipient Group saved successfully. See /titan/playwright/test-data/external-api/recipient-group.json}`);
    } catch (error) {
      console.log('Error writing Recipient Group to File:', error);
    }
  }

  async generateNewPostBody() {
    const timestamp = Date.now();
    return {
      name: faker.animal.cat(),
      description: `Playwright Test Edit ${timestamp}`,
    }
  }
}
