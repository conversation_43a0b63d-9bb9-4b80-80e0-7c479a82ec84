function Avatar({
  user,
}: {
  user: { photoURL: string; status: string; name: string };
}) {
  return (
    <img
      src={user?.photoURL ?? "/images/default_user.png"}
      className={`rounded-full h-12 w-12 border-solid border-4 my-auto ${
        user?.status !== "active" ? " border-green-400" : "border-darker-D1"
      }`}
      alt={`${user?.name?.toUpperCase()}`}
    />
  );
}

export default Avatar;
