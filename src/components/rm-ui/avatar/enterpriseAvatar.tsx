function EnterpriseAvatar({
  photoUrl,
  name,
  size = "12"
}: {
  photoUrl: string; 
  name: string;
  size?: string;
}) {
  return (
    <div>
      <img
        src={photoUrl ?? "/images/pushpin.jpeg"}
        className={`rounded-full h-${size} w-${size} max-h-${size} max-w-${size} border-solid border-4 my-auto mx-auto`}
        alt={`${name?.toUpperCase() || "Unknown Name"  }`}
      />
    </div>
  );
}

export default EnterpriseAvatar;
