import React, { useEffect, useState } from "react";
import { fetchDisplayFromSetting } from "../../../domain/settings/data/settings";

const DisplaySetting = ({
  value
}: {
  value: string;
}) => {
  const [display, setDisplay] = useState("");

  useEffect(() => {
    async function fetchDisplay(value: string) {
      const display = await fetchDisplayFromSetting(value);
      setDisplay(display);
    }
    fetchDisplay(value);
  }, [value]);

  return (
    <>
      {display}
    </>
  );
};

export default DisplaySetting;
