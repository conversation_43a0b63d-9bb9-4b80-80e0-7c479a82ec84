import { ReactFitty } from "react-fitty";

interface ButtonProps extends React.ComponentProps<"button"> {
  text: string;
  minSize?: number;
  maxSize?: number;
  buttonColor?: string; // Use colors from theme.colors
}

function BasicButton(props: ButtonProps) {
  const {
    text,
    minSize = 8,
    maxSize = 16,
    buttonColor = "bg-darker",
    ...buttonProps
  } = props;

  return (
    <div className="inline-block max-w-xs mr-2 ">
      <button
        className={`${buttonColor} inline-block   min-h-10 py-2 px-4 w-full text-center text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
        {...buttonProps}
      >
        <ReactFitty minSize={minSize} maxSize={maxSize}>
          {text}
        </ReactFitty>
      </button>
    </div>
  );
}

export default BasicButton;
