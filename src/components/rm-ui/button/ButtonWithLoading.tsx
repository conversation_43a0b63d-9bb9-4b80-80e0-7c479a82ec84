interface ButtonProps extends React.ComponentProps<"button"> {
  pending?: boolean;
  pendingtext?: string;
  submittingLabel?: string;
  loaderClasses?: string;
}
function ButtonWithLoading(props: ButtonProps) {
  // Omit 'pending' from the button props
  const { pending, submittingLabel = "Submitting...", loaderClasses, className,disabled ,...buttonProps } = props;
  return (
    <>
      <div className="flex justify-center items-center mb-2 text-sm">
        <button
          className={`button min-h-10 w-full justify-center mx-auto ${className}`}
          {...buttonProps}
          disabled={pending || disabled}
        >
          {pending ? (
            <span className="flex items-center justify-center text-center">
              <svg
                className={`animate-spin h-7 w-7 text-white mr-2 ${loaderClasses}`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              {buttonProps?.pendingtext || submittingLabel}
            </span>
          ) : (
            buttonProps.value
          )}
        </button>
      </div>
    </>
  );
}

export default ButtonWithLoading;

