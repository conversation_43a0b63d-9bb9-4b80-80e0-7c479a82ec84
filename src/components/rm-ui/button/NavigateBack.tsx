import { useNavigate } from "react-router-dom";

function NavigateBack({
  className,
  to,
}: {
  className?: string;
  to?: string;
}) {
  const navigate = useNavigate();
  return (
    <div className="mb-1">
      <button
        type="button"
        onClick={() => (to ? navigate(to) : navigate(-1))}
        className={`text-base leading-none text-cyan-500 hover:text-slate-600 ${className}`}
      >
        <span className="icon ion-chevron-left text-xs" />
        Back
      </button>
    </div>
  );
}

export default NavigateBack;
