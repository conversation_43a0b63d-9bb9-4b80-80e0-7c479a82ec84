import { useState } from "react";

interface CopyToClipboardButtonProps extends React.ComponentProps<"button"> {
  text?: string;
}

function CopyTextToClipboardButton(props: CopyToClipboardButtonProps) {
  const [isCopied, setIsCopied] = useState(false);

  function delay(time: number, val: boolean) {
    return new Promise((resolve) => setTimeout(resolve, time, val));
  }
  // omit pending from the button props;
  const { text = "", ...buttonProps } = props;
  return (
    <>
      <button
        {...buttonProps}
        type="button"
        onClick={() => {
          navigator.clipboard.writeText(text);
          setIsCopied(true);
          delay(3000, false).then(() => setIsCopied(false));
        }}
      >
        {isCopied ? (
          <>
            <span className="text-gray-500 hover:text-gray-700 icon icomoon icon-copy w-8 h-8"></span>{" "}
            <span className="font-bold ml-2">Text copied!</span>
          </>
        ) : (
          <span className="text-gray-500 hover:text-gray-700 icon icomoon icon-copy w-8 h-8"></span>
        )}
      </button>
    </>
  );
}

export default CopyTextToClipboardButton;
