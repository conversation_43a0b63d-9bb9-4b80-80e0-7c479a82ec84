import { Link } from "react-router-dom";
import SubMenuIcon from "../icons/SubMenuIcon";

const IconButton = ({
  icon = null,
  iconName,
  text = null,
  pending,
  onClick,
  style = "",
  link = "#",
}) => {
  return (
    <>
      {link !== "#" ? (
        <Link
          to={link}
          className={`inline-flex items-center justify-center ${style}`}
        >
          {icon ? icon : <SubMenuIcon iconName={iconName} alt="icon" />}
        </Link>
      ) : (
        <button
          disabled={pending}
          className={`inline-flex items-center justify-center ${style}`}
          onClick={onClick} // Handle click events
        >
          {icon ? icon : <SubMenuIcon iconName={iconName} alt="icon" />}
          {text && <span className="ml-2">{text}</span>}
        </button>
      )}
    </>
  );
};
export default IconButton;
