import { useState, useEffect } from "react";
import IconMaterial from "../icons/IconMaterial";
const BackToTopButton = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);

    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <>
      {isVisible && (
        <button
          onClick={scrollToTop}
          className="tw-button-icon fixed bottom-[8.5rem] lg:bottom-24 right-3 lg:right-5 w-16 h-16 text-color-white bg-slate-600 hover:bg-slate-700"
        >
          <span className="sr-only">
            Back to top
          </span>
          <IconMaterial name="vertical_align_top" />
        </button>
      )}
    </>
  );
};

export default BackToTopButton;
