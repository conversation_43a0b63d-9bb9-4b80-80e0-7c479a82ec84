import { useCallback, useState } from "react";

import {
  useDropzone,
  DropzoneOptions,
  FileRejection,
  DropEvent,
} from "react-dropzone";
import SuccessCustomPages from "../success/SuccessCustomPages";

function UploadFileCustomPages({ setFile }: { setFile: Function }) {
  const [eventState, setEventState] = useState();
  const [fileName, setFileName] = useState();

  const dragDropEvents = {
    onDragEnter: "onDragEnter",
    onDragLeave: "onDragLeave",
    onDragOver: "onDragOver",
    onDrop: "onDrop",
    onDropAccepted: "onDropAccepted",
  };

  const dropEventStyles = () => {
    if (eventState === dragDropEvents.onDragEnter) return `bg-darker-L2`;
    if (eventState === "onDragLeave") return;
    if (eventState === "onDragOver") return `bg-darker-L2`;
    if (eventState === "onDrop") return;
  };

  const isDropAccepted = eventState === dragDropEvents.onDropAccepted;

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length < 1) return;
    setEventState(dragDropEvents.onDrop);

    const file = acceptedFiles[0];
    const reader = new FileReader();

    reader.onload = () => {
      const base64Data = reader.result?.toString().split(",")[1]; 
      if (!base64Data) return;

      setFile({
        base64: base64Data,
        name: file.name,
        type: file.type,
      });
      setFileName(file.name);
    };

    reader.readAsDataURL(file); 
  }, [setFile]);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: { "application/pdf": [".pdf"] },
    multiple: false,
    maxFiles: 1,
    onDragEnter: () => setEventState(dragDropEvents.onDragEnter),
    onDragLeave: () => setEventState(dragDropEvents.onDragLeave),
    onDragOver: () => setEventState(dragDropEvents.onDragOver),
    onDropAccepted: () => setEventState(dragDropEvents.onDropAccepted),
  });

  return (
    <section>
      <div {...getRootProps()}>
        <input {...getInputProps()} type="file" />
        <div className={`mb-0 md:!w-[500px] p-5 shadow-md border !border-black !border-dashed rounded flex items-center justify-center mb-5 ${dropEventStyles()}`}>
          {isDropAccepted ? (
            <SuccessCustomPages marginBottomClass="mb-0" message={`File uploaded!`} />
          ) : (
            <p className="text-center mb-0 font-bold text-lg border border-black !w-[250px] rounded">
              <span className="icon ion-upload icon-color4">Browse to Upload</span>
            </p>
          )}
        </div>
      </div>
    </section>
  );
}


export default UploadFileCustomPages;
