import { useCallback, useState } from "react";

import {
  useDropzone,
  DropzoneOptions,
  FileRejection,
  DropEvent,
} from "react-dropzone";
import SuccessCircleCheck from "../success/SuccessCircleCheck";

function DragAndDropVariantB({ setFile }: { setFile: Function }) {
  const [formAlert, setFormAlert] = useState(null);

  // onDragEnter, onDragLeave, onDragOver, onDrop
  const [eventState, setEventState] = useState();
  const [fileName, setFileName] = useState();

  const dragDropEvents = {
    onDragEnter: "onDragEnter",
    onDragLeave: "onDragLeave",
    onDragOver: "onDragOver",
    onDrop: "onDrop",
    onDropAccepted: "onDropAccepted",
  };

  const dropEventStyles = () => {
    if (eventState === dragDropEvents.onDragEnter) return `bg-darker-L2`;
    if (eventState === "onDragLeave") return;
    if (eventState === "onDragOver") return `bg-darker-L2`;
    if (eventState === "onDrop") return;
  };

  const isDropAccepted = eventState === dragDropEvents.onDropAccepted;

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length < 1) {
      return;
    }
    setEventState(dragDropEvents.onDrop);
    acceptedFiles.forEach((file) => {
      const reader = new FileReader();
      setFile(file);
      const { size } = file;
      // add a files size limit
      // filename used in the ready message only
      setFileName(file.name);

      reader.onabort = () => console.log("file reading was aborted");
      reader.onerror = () => console.log("file reading has failed");
      reader.onload = () => {
        // Do whatever you want with the file contents
        const binaryStr = reader.result;
      };
      reader.readAsArrayBuffer(file);
    });
  }, []);
  const { acceptedFiles, fileRejections, getRootProps, getInputProps } =
    useDropzone({
      onDrop,
      accept: {
        "application/pdf": [".pdf"],
        "application/msword": [".doc"],
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
          [".docx"],
      },
      multiple: false,
      maxFiles: 1,
      onDragEnter: () => {
        setEventState(dragDropEvents.onDragEnter);
      },
      onDragLeave: () => {
        setEventState(dragDropEvents.onDragLeave);
      },
      onDragOver: () => {
        setEventState(dragDropEvents.onDragOver);
      },
      onDropAccepted: () => {
        setEventState(dragDropEvents.onDropAccepted);
      },
    });

  return (
    <>
      <section>
        <div {...getRootProps()}>
          <input {...getInputProps()} type="file" />
          <div
            className={`p-1 cursor-pointer shadow-md bg-background border divide-x-4 border-primary border-dashed rounded transition-colors ${dropEventStyles()}`}
          >
            {isDropAccepted ? (
              <SuccessCircleCheck message={`Ready to upload ${fileName}!`} />
            ) : (
              <>
                {" "}
                {/* <FontAwesomeIcon
                  className="p-5 rounded text-primary text-base"
                  icon={faUpload}
                  size="xl"
                /> */}
                <p className="text-darker text-base">
                  Drag & Drop or Browse to Upload
                </p>
              </>
            )}
          </div>
        </div>
      </section>
    </>
  );
}

export default DragAndDropVariantB;
