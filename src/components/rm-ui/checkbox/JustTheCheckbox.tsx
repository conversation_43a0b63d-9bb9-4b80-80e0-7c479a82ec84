function JustTheCheckbox({
  id,
  state,
  title,
  handleChange,
  ...props
}: {
  id: string;
  state: boolean;
  title: string;
  handleChange: Function;
}) {
  return (
    <div className="text-center">
      <label htmlFor={id} className="block font-medium text-gray-700">
        {title || "Checkbox"}
      </label>
      <input
        className="h-5 w-5 focus:ring-0 rounded"
        id={id}
        checked={state}
        name="checkbox"
        type="checkbox"
        onChange={(e) => handleChange(e.target.checked)}
        {...props}
      />
    </div>
  );
}
export default JustTheCheckbox;
