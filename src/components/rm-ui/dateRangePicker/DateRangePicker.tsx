import React from 'react';

const DateRangePicker = ({ startDate, endDate, onStartDateChange, onEndDateChange }: { startDate: string; endDate: string; onStartDateChange: (...args: any) => void; onEndDateChange: (...args: any) => void; }) => {
    return (
        <div>
            <label>
                Start Date:
                <input
                    type="date"
                    value={startDate}
                    onChange={(e) => onStartDateChange(e.target.value)}
                />
            </label>

            <label style={{ marginLeft: '1rem' }}>
                End Date:
                <input
                    type="date"
                    value={endDate}
                    onChange={(e) => onEndDateChange(e.target.value)}
                />
            </label>
        </div>
    );
};

export default DateRangePicker;
