import React, { useState } from "react";
import Datepicker from "react-tailwindcss-datepicker";
import './datepicker-tailwind.css';
const NEXT_MONTH = new Date();
NEXT_MONTH.setMonth(NEXT_MONTH.getMonth() + 1);
type DateType = null | Date;

const BasicRangeShortcuts = ({ sendData, value, customInputClass="",customClass }: {
    sendData(...args: any): void; value: {
        startDate: DateType;
        endDate: DateType;
    }; 
    customInputClass?: string;
    customClass?: string;
}) => {

    const handleValueChange = (newValue: any) => {
        sendData(newValue);
    }

    return (
        <div className={`datePicker-container w-[18rem] ${customClass}`}>
            <Datepicker
                inputClassName={`w-full text-base ${customInputClass}` }
                separator="to"
                value={value}
                onChange={handleValueChange}
                showShortcuts={true}
                showFooter={true}
                primaryColor={"blue"}
                readOnly={false}
            />
        </div>
    );
};

export default BasicRangeShortcuts;