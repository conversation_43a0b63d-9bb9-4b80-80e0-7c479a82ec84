const ProgressCircle = ({
  prog = 0,
  r = 100,
  strokeWidth = 8,
  containerClassName = "",
}: {
  prog: number;
  r?: number;
  strokeWidth?: number;
  containerClassName?: string;
}) => {
  const c = 2 * Math.PI * r;
  const offset = c - (prog / 100) * c;

  // Calculate the total width and height considering stroke width
  const totalSize = 2 * (r + strokeWidth);

  return (
    <div className={containerClassName}>
      <div className="spinner-circle">
        <svg className="margin: auto; background: none; display: block; shape-rendering: auto;" 
          height={totalSize}
          width={totalSize}
          viewBox={`0 0 ${totalSize} ${totalSize}`}
          preserveAspectRatio="xMidYMid"
        >
          <g transform="translate(50 50)"><g>
          <animateTransform 
            attributeName="transform" 
            type="rotate" 
            values="0;45" 
            keyTimes="0;1" 
            dur="0.4s" 
            repeatCount="indefinite"
          >
          </animateTransform>
          <path d="M27.874719729532707 -8 L40.87471972953271 -8 L40.87471972953271 8 L27.874719729532707 8 A29 29 0 0 1 25.367257593919405 14.053549094934645 L25.367257593919405 14.053549094934645 L34.559645749344526 23.245937250359763 L23.245937250359766 34.55964574934452 L14.053549094934647 25.3672575939194 A29 29 0 0 1 8 27.874719729532707 L8 27.874719729532707 L8 40.87471972953271 L-7.999999999999996 40.87471972953271 L-7.9999999999999964 27.874719729532707 A29 29 0 0 1 -14.053549094934636 25.36725759391941 L-14.053549094934636 25.36725759391941 L-23.245937250359752 34.559645749344526 L-34.559645749344526 23.24593725035976 L-25.367257593919405 14.053549094934642 A29 29 0 0 1 -27.874719729532707 8.000000000000007 L-27.874719729532707 8.000000000000007 L-40.87471972953271 8.000000000000009 L-40.87471972953271 -8 L-27.874719729532707 -8.000000000000002 A29 29 0 0 1 -25.36725759391941 -14.053549094934635 L-25.36725759391941 -14.053549094934635 L-34.559645749344526 -23.245937250359752 L-23.245937250359763 -34.559645749344526 L-14.053549094934644 -25.367257593919405 A29 29 0 0 1 -8.000000000000009 -27.874719729532707 L-8.000000000000009 -27.874719729532707 L-8.00000000000001 -40.87471972953271 L7.999999999999996 -40.87471972953271 L7.999999999999998 -27.874719729532707 A29 29 0 0 1 14.053549094934633 -25.36725759391941 L14.053549094934633 -25.36725759391941 L23.24593725035975 -34.559645749344526 L34.55964574934452 -23.245937250359766 L25.3672575939194 -14.053549094934645 A29 29 0 0 1 27.874719729532707 -8.00000000000001 M0 -18A18 18 0 1 0 0 18 A18 18 0 1 0 0 -18" fill="#a69159"></path></g></g>
        </svg>
      </div>
    </div>
  );
};

export default ProgressCircle;
