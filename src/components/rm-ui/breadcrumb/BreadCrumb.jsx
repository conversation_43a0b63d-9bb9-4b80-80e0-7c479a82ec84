function BreadCrumb({ crumbs }) {
  return (
    <div className="flex items-center">
      <div className="flex items-center">
        {crumbs.map((crumb, index) => {
          return (
            <div
              key={index}
              className={`flex items-center gap-2 ${index === crumbs.length - 1 ? "" : "text-[#51606ds]"
                }`}
              {...crumb}
            >
              <span className="text-sm text-[#51606d] dark:text-white">{crumb.label}</span>
              {index !== crumbs.length - 1 && (
                <span className="text-[#51606d] mx-2  dark:text-white"> &gt; </span>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default BreadCrumb;