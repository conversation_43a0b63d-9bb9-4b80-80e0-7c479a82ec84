import { QueryKey, useQuery } from "@tanstack/react-query";
import { Timestamp } from "firebase/firestore";
import { fetchDocument } from "../../../utils/fetchDocument";
import {
  doc,
  collection,
  query,
  where,
  orderBy,
  limit,
  addDoc,
} from "firebase/firestore";
import { db, createQuery } from "../../../../providers/database";

export function usePrompt<T>(id: string) {
  return useQuery<T, Error, QueryKey>({
    queryKey: ["prompt", id],
    queryFn: () => fetchDocument<T>("generate", id),
    enabled: !!id,
    refetchOnWindowFocus: true,
    refetchInterval: 5000,
  });
}

export function useAllQuestions<T>(id: string) {
  return useQuery({
    queryKey: ["generate", { id }],
    queryFn: createQuery<T>(() =>
      query(
        collection(db, "generate"),
        where("who", "==", id),
        orderBy("createTime", "desc")
      )
    ),
    enabled: !!id,
  });
}

export async function createNewPrompt(data: object) {
  const res = await addDoc(collection(db, "generate"), data);
  return res.id;
}
