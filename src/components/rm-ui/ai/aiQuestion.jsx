import React, { useState, useEffect } from "react";
import { createNewPrompt, usePrompt, useAllQuestions } from "./data/ai";
import Markdown from "react-markdown";
import remarkGfm from "remark-gfm";
import PageLoader from "../pageLoader/PageLoader";
import { useAuth } from "../../../domain/auth/providers/auth";
import Accordion from "../accordian/accordian";
import Heading from "../headings/heading";
import { useGlobalState } from "../../../providers/globalState/GlobalStateProvider";

const AIQuestion = () => {
  const { globalState } = useGlobalState();
  const auth = useAuth();

  const [question, setQuestion] = useState("");
  const [docId, setDocId] = useState(null);
  const [collection, setCollection] = useState("generate");
  const { data: prompt } = usePrompt(docId);
  const { data: questions } = useAllQuestions(auth.user?.uid);

  const handleQuestionSubmit = async () => {
    if (question !== "") {
      try {
        const prompt = {
          prompt: question,
          who: auth?.user?.uid,
        };
        const docId = await createNewPrompt(prompt);
        setDocId(docId);
      } catch (e) {
        console.error("Error adding document: ", e);
      }
    }
  };

  return (
    <div name="ai-chat" id="ai-chat">
      <Heading title={"How can I help?"} />

      {docId && prompt && prompt.response && (
        <div className="box p-6">
          <Markdown children={prompt.response} remarkPlugins={[remarkGfm]} />
        </div>
      )}

      <div className="form form-style-2">
        <div className="mb-6">
          <textarea
            placeholder="Ask a question..."
            onBlur={(e) => setQuestion(e.target.value)}
          />
        </div>

        <button className="button button-cyan" onClick={handleQuestionSubmit}>
          Submit
        </button>
      </div>

      {docId && prompt && !prompt?.response && !prompt?.status?.error && (
        <div className="margin-bottom">
          <PageLoader loader={"infinity"} />
        </div>
      )}
      {prompt && prompt?.status?.error && (
        <div className="margin-bottom">
          <p>Error: {prompt.status.error}</p>
        </div>
      )}
      <h2>History</h2>
      {questions && (
        <Accordion items={questions} title={"prompt"} content={"response"} />
      )}
    </div>
  );
};

export default AIQuestion;
