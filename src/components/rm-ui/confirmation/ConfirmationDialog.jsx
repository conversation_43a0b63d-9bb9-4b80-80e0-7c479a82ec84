import DialogWrapper from "../dialog/Dialog";
import BasicButton from "../button/BaseButton";

function ConfirmationDialog({
  title,
  onConfirm,
  onCancel,
  confirmText = "Confirm",
  cancelText = "Cancel",
  children,
  icon = "ion-alert-circled",
  iconColorClass = "button-slate",
  actionButtonColorClass = "button-slate",
}) {
  return (
    <DialogWrapper onClose={onCancel}>
      <>
        <div className={`badge badge-xlarge badge-zoom border-none badge-center button-hollow ${iconColorClass}`}>
          <span className={`icon ${icon}`} />
        </div>

        {title && (
          <h2 className="text-2xl font-light p-0 pb-6 text-balance">
            {title}
          </h2>
        )}

        <div className="p-6 space-y-12">
          {children && (
            <div className="space-y-6">
              {children}
            </div>
          )}

          <div className="space-y-6">
            <button
              className={`button button-large w-full m-0 ${actionButtonColorClass}`}
              onClick={onConfirm}
            >
              {confirmText}
            </button>

            <button
              className="button button-large w-full m-0 button-slate button-hollow"
              onClick={onCancel}
            >
              {cancelText}
            </button>
          </div>
        </div>
      </>
    </DialogWrapper>
  );
}

export default ConfirmationDialog;
