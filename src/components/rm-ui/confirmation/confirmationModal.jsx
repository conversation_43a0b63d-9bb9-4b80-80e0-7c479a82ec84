import React ,{useState} from "react";
import { useEffect } from "react";

function ConfirmationModal({ isOpen, message, onConfirm, onCancel, confirmButtonText = "Confirm", cancelButtonText = "Cancel" }) {
  const [isOpenModal, setIsOpenModal] = useState(isOpen||false);
  useEffect(()=>{
    setIsOpenModal(isOpen)
  },[isOpen])
  if (!isOpenModal) return null;
  return (
    <div className="fixed inset-0 flex items-center justify-center rounded-xl z-10">
      <div className="bg-white p-4 rounded-lg shadow-lg">
        <div className="mb-4 dark:text-black">{message}</div>
        <div className="flex justify-end gap-2">
          <button
          type="button"
            className="px-4 py-2 bg-green-400 text-white rounded"
            onClick={onConfirm}
          >
            {confirmButtonText}
          </button>
          <button
            type="button"
            className="px-4 py-2 bg-red-500 text-white rounded"
            onClick={onCancel}
          >
            {cancelButtonText}
          </button>
        </div>
      </div>
    </div>
  );
}

export default ConfirmationModal;
