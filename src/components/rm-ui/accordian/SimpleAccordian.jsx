import React, { useState } from "react";

const SimpleAccordion = ({ title, children }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div>
      <div
        className="flex items-center justify-between gap-6 p-6 text-slate-600 rounded cursor-pointer transition bg-gray-100 hover:bg-gray-150"
        onClick={toggleAccordion}
      >
        <div className="text-lg font-bold">
          {title}
        </div>

        <div className="text-lg">
          <span className={isOpen ? "ion-chevron-up" : "ion-chevron-down"} />
          <span className="sr-only">{isOpen ? "Collapse" : "Expand"}</span>
        </div>
      </div>

      {isOpen && (
        <div className="p-6">
          {children}
        </div>
      )}
    </div>
  );
};

export default SimpleAccordion;