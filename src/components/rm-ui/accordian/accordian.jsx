import React, { useState } from "react";

// Accordion item component
const AccordionItem = ({ title, content, isOpen, onClick }) => {
  return (
    <div className="">
      <button
        onClick={onClick}
        className="flex items-center justify-between gap-6 mb-3 p-6 text-slate-600 w-full rounded transition bg-color-white hover:bg-gray-50"
      >
        <div className="text-base font-bold">
          {title}
        </div>

        <div className="text-lg">
          <span className={isOpen ? "ion-chevron-up" : "ion-chevron-down"} />
          <span className="sr-only">{isOpen ? "Collapse" : "Expand"}</span>
        </div>
      </button>

      {isOpen && (
        <div className="px-3 pb-6">
          {content}
        </div>
      )}
    </div>
  );
};

// Main Accordion component
const Accordion = ({ items, title, content }) => {
  const [openIndex, setOpenIndex] = useState(-1);

  const handleToggle = (index) => {
    // Toggle open/close by setting or unsetting the open index
    setOpenIndex((currentIndex) => (currentIndex === index ? -1 : index));
  };

  return (
    <div>
      {items.map((item, index) => (
        <AccordionItem
          key={index}
          title={item[title]}
          content={item[content]}
          isOpen={openIndex === index}
          onClick={() => handleToggle(index)}
        />
      ))}
    </div>
  );
};

export default Accordion;
