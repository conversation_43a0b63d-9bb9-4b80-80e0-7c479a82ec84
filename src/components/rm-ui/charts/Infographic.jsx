import React from 'react';

const Infographic = ({ title, count, icon }) => {
  return (
    <div className="flex items-center p-4 bg-black shadow-md rounded-lg">
      <div className="flex items-center justify-center w-16 h-16 text-white rounded-full">
        <img src={`/images/icons/lightMode/${icon}`} alt={title} className="w-16 h-16" />
      </div>
      <div className="ml-4 text-center">
        <div className="text-lg font-semibold text-white">{title}</div>
        <div className="text-2xl font-bold text-white">{count}</div>
      </div>
    </div>
  );
};

export default Infographic;
