import { useAuth } from "../../../domain/auth/providers/auth";
import { useGlobalState } from "../../../providers/globalState/GlobalStateProvider";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  SearchBox,
  Hits,
  InstantSearch,
  Configure,
  useSearchBox,
} from "react-instantsearch";

import AssignAccountLineComponent from "./AssignAccountLineComponent";

function AssignAccount({ data, collection }) {
  const { query } = useSearchBox();

  return (
    <div>
      <label>Assign Account</label>
      <div>
        <Configure hitsPerPage={10} />

        <div className="form flex space-x-2 items-center">
          <SearchBox
            placeholder="Search..."
            translations={{ submitTitle: "Search" }}
            classNames={{
              root: "form",
              form: "input-icon-container input-icon-container-left input-icon-container-right",
              input: "",
              reset: "icon input-icon-right",
              resetIcon: "w-3 h-3",
              loadingIndicator: "icon input-icon-right",
              loadingIcon: "w-4 h-4",
              submit: "icon input-icon-left",
              submitIcon: "w-4 h-4",
            }}
          />
        </div>
        <div>
          {query ? (
            <Hits
              hitComponent={({ hit }) => (
                <AssignAccountLineComponent
                  hit={hit}
                  collection={collection}
                  recId={data?.id}
                />
              )}
              classNames={{
                root: "absolute",
                list: "ml-0 space-y-1",
                item: "m-0 list-none",
              }}
            />
          ) : null}
        </div>
      </div>
    </div>
  );
}

export default AssignAccount;
