import { db as titanDb } from "../../../providers/database";
import { doc, updateDoc, arrayUnion, arrayRemove } from "firebase/firestore";
import { useQueryClient } from "@tanstack/react-query";

function AssignAccountLineComponent({
  hit,
  handleSelect,
  collection,
  recId,
  name,
}) {
  const queryClient = useQueryClient();

  const handleChangeAccount = async () => {
    await updateDoc(doc(titanDb, collection, recId), {
      accounts: arrayUnion({ id: hit.objectID, name: hit.name }),
    });

    // 👇 Invalidate the appropriate query based on collection type
    if (collection === "TemplatesSites") {
      queryClient.invalidateQueries(["templateSite", recId]);
    } else {
      queryClient.invalidateQueries(["template", recId]);
    }
  };

  return (
    <a
      href="#"
      className="tw-button-list rounded bg-gray-100 hover:bg-gray-150"
      onClick={handleChangeAccount}
    >
      {hit.name} ({hit.objectID})
    </a>
  );
}

export default AssignAccountLineComponent;
