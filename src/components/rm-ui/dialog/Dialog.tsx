import React from "react";
import { Transition, Dialog } from "@headlessui/react";
import { useAuth } from "../../../domain/auth/providers/auth";
import { XMarkIcon } from "@heroicons/react/24/solid";
function DialogWrapper({
  children,
  title,
  size = "w-full max-w-screen-sm",
  onClose,
  titleColor = "",
  bgColor = "bg-color-white",
  padding = "p-6 pt-16 sm:p-16",
}: {
  children: JSX.Element;
  title: string;
  size?: string;
  titleColor?: string;
  bgColor?: string;
  padding?: string;
  onClose: () => void;
}) {
  const auth = useAuth();
  const user = auth?.user;
  return (
    <>
      <Transition appear={true} show={true}>
        <Dialog
          onClose={onClose}
          className="overflow-y-auto fixed inset-0 z-10 "
        >
          <div className="sm:p-2 min-h-screen text-center flex items-center justify-center">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <Dialog.Overlay className="fixed inset-0 bg-color-black opacity-65" />
            </Transition.Child>
            <span
              className="inline-block h-screen align-middle"
              aria-hidden="true"
            >
              &#8203;
            </span>
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div
                className={`${size} inline-block overflow-hidden ${padding} text-center align-middle ${bgColor} rounded shadow-xl transition-all transform`}
              >
                {title && (
                  <Dialog.Title
                    className={`text-2xl font-light pt-0 pb-6 px-6 text-balance ${titleColor}`}
                  >
                    {title}
                  </Dialog.Title>
                )}

                <div>{children}</div>

                <button
                  type="button"
                  onClick={onClose}
                  className="tw-button-icon absolute top-3 right-3"
                >
                  <span className="ion-close" />
                  <span className="sr-only">Close</span>
                </button>
              </div>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition>
    </>
  );
}

export default DialogWrapper;
