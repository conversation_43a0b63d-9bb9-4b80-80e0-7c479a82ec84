import { isString } from "lodash";
import Tooltip from "../tooltip/tooltip";
function RoundChip({
  label,
  colorClass,
  minSize = 8,
  tooltipText,
  maxSize = 16,
}: {
  label: string;
  colorClass?: string;
  minSize?: number;
  tooltipText?: string;
  maxSize?: number;
}) {
  let colorString = "bg-slate-600";
  if (isString(colorClass)) {
    colorString = colorClass;
  }
  return (
    <>
      {tooltipText ? (
        <Tooltip
          text={tooltipText}
          classParent="!ml-0 flex"
        >
          <span
            className={`tw-label p-1 px-2 text-xs font-bold rounded-full ${colorString}`}
          >
            {label}
          </span>
        </Tooltip>
      ) : (
        <span
          className={`tw-label p-1 text-xs font-bold rounded-full ${colorString}`}
        >
          {label}
        </span>
      )}
    </>
  );
}

export default RoundChip;
