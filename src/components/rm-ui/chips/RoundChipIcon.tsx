import { isString } from "lodash";

interface RoundChipIconProps {
  iconClass: React.ReactNode;
  colorClass?: string;
  bgColor?: string;
}

const RoundChipIcon: React.FC<RoundChipIconProps> = ({
  iconClass,
  colorClass,
  bgColor = "bg-color1",
}) => {
  let colorString = "bg-grayDark";
  if (isString(colorClass)) {
    colorString = colorClass;
  }
  return (
    <span className={`badge badge-small badge-zoom border-none m-0 ${bgColor}`}>
      <span className={`${iconClass}`}></span>
    </span>
  );
};

export default RoundChipIcon;
