import { doc, updateDoc } from "firebase/firestore";
import { db } from "../../../providers/database";
import { useQueryClient } from "@tanstack/react-query";

const OvalChip = ({
  items = [],
  docId,
  collection = "", // 🏷️ Now customizable
  field = "", // 🏷️ Now customizable
  getLabel = (item) => item.name,
  getKey = (item) => item.id,
  showRemove = true, // ✅ Controls whether to show the "X"
  justify = "",
  color = "bg-blue-100 text-blue-800",
}) => {
  const queryClient = useQueryClient();

  const handleRemove = async (itemToRemove) => {
    try {
      const filteredItems = items.filter(
        (item) => getKey(item) !== getKey(itemToRemove)
      );
      await updateDoc(doc(db, collection, docId), {
        [field]: filteredItems,
      });
      queryClient.invalidateQueries();

      // console.log("✅ Item removed:", getLabel(itemToRemove));
    } catch (err) {
      // console.error("❌ Failed to remove item:", err);
    }
  };

  if (!items || items.length === 0) return null;

  return (
    <div
      className={[
        "pl-2 pr-2 max-w-[225px] flex flex-wrap gap-2 mb-4",
        justify || "justify-start", // fallback to 'justify-start' if nothing passed
      ].join(" ")}
    >
      {items.map((item) => (
        <span
          key={getKey(item)}
          className={`flex items-center ${color} text-xs font-medium py-0.5 px-2 rounded-full`}
        >
          {typeof item === "string" ? item : getLabel(item)}
          {showRemove && (
            <button
              onClick={() => handleRemove(item)}
              className="ml-2 text-blue-600 hover:text-red-600 font-bold mb-0.5"
              title="Remove"
            >
              x
            </button>
          )}
        </span>
      ))}
    </div>
  );
};

export default OvalChip;
