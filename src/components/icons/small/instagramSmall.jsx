import React, { useState } from "react";
import SubHeading from "../../rm-ui/headings/subheading";
import ButtonLoading from "../../rm-ui/button/ButtonLoading";
import SelectGeneric from "../../rm-ui/input-fields/select-generic";

const InstagramSmall = ({
  instagram,
  isActive,
  deleteInstagram,
  deletingInstagram,
  changeDefaultPage,
  changingDefaultPage,
  instagramAccounts,
  accountSelected,
  setAccountSelected,
  isConnected = false
}) => {
  const [isValid, setIsValid] = useState(null);

  const img = new Image();
  img.onload = () => setIsValid(true);
  img.onerror = () => setIsValid(false);
  img.src = instagram?.profile_picture_url;

  return (
    <div className="w-full flex flex-col items-center p-4 px-6">
      <SubHeading title={"Instagram"} mini />
      {isConnected ? (
        <>
          <div className="w-full flex flex-col items-center justify-center mt-2 gap-3">
            <div className="w-full flex justify-center items-center">
              <img
                className="w-24 h-24 rounded-full object-cover border-2 border-facebook"
                src={
                  isValid
                    ? instagram?.profile_picture_url
                    : "/images/default-image.png"
                }
                alt="FaceBook Page Avatar"
              />
            </div>
            {instagram && instagram?.username ? <>
              <div className="w-full flex flex-col justify-center items-center gap-2 text-text-color">
                <p className="m-0">
                  <strong className="text-lg font-bold">
                    {instagram?.username}
                  </strong>
                </p>
                <p className="m-0">
                  <i className="text-base">{instagram?.connectedPage?.name}</i>
                </p>
              </div>
              <div className="w-full flex justify-center text-text-color">
                <ButtonLoading
                  value={"Delete Default Instagram Account"}
                  loadingText={" Deleting..."}
                  className={"bg-red text-white rounded-full p-2 px-4 py-2 text-base m-0"}
                  type="submit"
                  onClick={deleteInstagram}
                  isLoading={deletingInstagram}
                />
              </div>
            </> : (
              <p className="text-base mt-4">
                No Connected Instagram Account
              </p>
            )}
            <div className="flex flex-col justify-center items-center gap-2 text-text-color">
              <div>
                <SelectGeneric
                  options={instagramAccounts}
                  onChange={(value) => setAccountSelected(value)}
                  value={accountSelected}
                />
              </div>
              <div>
                <ButtonLoading
                  disabled={accountSelected.value === instagram?.id}
                  value={"Change"}
                  loadingText={" Changing..."}
                  className={
                    "bg-background text-white rounded-full p-2 px-4 py-2 text-base m-0"
                  }
                  type="submit"
                  onClick={changeDefaultPage}
                  isLoading={changingDefaultPage}
                />
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="text-base mt-4">
          No connected account at facebook
        </div>
      )}
    </div>
  );
};

export default InstagramSmall;
