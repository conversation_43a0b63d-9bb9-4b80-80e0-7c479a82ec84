import React from "react";
import SubHeading from "../../rm-ui/headings/subheading";
import ButtonLoading from "../../rm-ui/button/ButtonLoading";

const FacebookSmall = ({
  facebook,
  disconnectFacebook,
  disconnectingFacebook,
}) => {
  return (
    <div className="w-full flex flex-col items-center p-4 px-6">
      <SubHeading title={"Facebook User Account"} mini />
      {facebook && facebook?.isActive ? (
        <>
          <div className="w-full flex flex-col items-center justify-center mt-2 gap-3">
            <div className="w-full flex justify-center items-center">
              <img
                className="w-24 h-24 rounded-full object-cover border-2 border-facebook"
                src={
                  facebook?.isActive
                    ? facebook?.picture?.data?.url
                    : "/images/default-image.png"
                }
                alt="FaceBook Page Avatar"
              />
            </div>
            <div className="w-full flex flex-col justify-center items-center gap-2 text-text-color">
              <p className="m-0">
                <strong className="text-lg font-bold">{facebook?.name}</strong>
              </p>
              <p className="m-0">
                <i className="text-base">{facebook?.email}</i>
              </p>
            </div>
            <div className="w-full flex justify-center items-center">
              <ButtonLoading
                value={"Diconnect Facebook User Account"}
                loadingText={" Disconnecting..."}
                className={"bg-red text-white rounded-full p-2 px-4 py-2 text-base m-0"}
                type="submit"
                onClick={disconnectFacebook}
                isLoading={disconnectingFacebook}
              />
            </div>

          </div>
        </>
      ) : (
        <div className="text-base mt-4">
          No Connected Facebook User Account
        </div>
      )}
    </div>
  );
};

export default FacebookSmall;
