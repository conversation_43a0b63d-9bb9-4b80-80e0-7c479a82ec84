import React, { useState } from "react";
import SubHeading from "../../rm-ui/headings/subheading";
import ButtonLoading from "../../rm-ui/button/ButtonLoading";
import SelectGeneric from "../../rm-ui/input-fields/select-generic";

const MainPageSmall = ({
  page,
  isActive,
  deleteFacebookPageDefault,
  deletingFacebookPageDefault,
  changeDefaultPage,
  changingDefaultPage,
  pages,
  pageSelected,
  setPageSelected,
  isConnected = false
}) => {
  const [isValid, setIsValid] = useState(null);
  const img = new Image();
  img.onload = () => setIsValid(true);
  img.onerror = () => setIsValid(false);
  img.src = page?.picture;
  return (
    <div className="w-full flex flex-col items-center p-4 px-6">
      <SubHeading title={"Facebook Page"} mini />
      {isConnected ? (
        <>
          <div className="w-full flex flex-col items-center justify-center mt-2 gap-3">
            <div className="w-full flex justify-center items-center">
              <img
                className="w-24 h-24 rounded-full object-cover border-2 border-facebook"
                src={isValid ? page?.picture : "/images/default-image.png"}
                alt="FaceBook Page Avatar"
              />
            </div>
            {page && page?.name ? (
              <>
                <div className="w-full flex flex-col justify-center items-center gap-2 text-text-color">
                  <p className="m-0">
                    <strong className="text-lg font-bold">{page?.name}</strong>
                  </p>
                  <p className="m-0">
                    <i className="text-base">{page?.category}</i>
                  </p>
                  <p className="text-base m-0 h-16 text-center">{page?.about}</p>
                  {page?.fan_count && <p className=" text-base  m-0">Fans: {page?.fan_count}</p>}
                </div>
                <div className="w-full flex justify-center text-text-color">
                  <ButtonLoading
                    value={"Delete Default Facebook Page"}
                    loadingText={" Deleting..."}
                    className={"bg-red text-white rounded-full p-2 px-4 py-2 text-base m-0"}
                    type="submit"
                    onClick={deleteFacebookPageDefault}
                    isLoading={deletingFacebookPageDefault}
                  />
                </div>
              </>
            ) :
              (
                <div className="text-base mt-4">
                  No Connected Facebook Page
                </div>
              )}

            <div className="w-full flex flex-col justify-center items-center text-text-color gap-2">
              <div>
                <SelectGeneric
                  options={pages}
                  onChange={(value) => setPageSelected(value)}
                  value={pageSelected}
                />
              </div>
              <div>
                <ButtonLoading
                  disabled={pageSelected.value === page?.id}
                  value={"Change"}
                  loadingText={" Changing..."}
                  className={
                    "bg-background text-white rounded-full p-2 px-4 py-2 text-base m-0"
                  }
                  type="submit"
                  onClick={changeDefaultPage}
                  isLoading={changingDefaultPage}
                />
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="w-full text-base text-center text-text-color mt-4">
          No connected account at facebook
        </div>
      )}
    </div>
  );
};

export default MainPageSmall;
