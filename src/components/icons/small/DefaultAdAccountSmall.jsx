import { useState } from "react";
import SubHeading from "../../rm-ui/headings/subheading";
import ButtonLoading from "../../rm-ui/button/ButtonLoading";
import SelectGeneric from "../../rm-ui/input-fields/select-generic";
import { useEffect } from "react";
import { useAuthorizedAccountId } from "../../../hooks/useAuthorizedAccountId";
import { callMercury } from "../../../ccf/cloudFunctions";
import Swal from "sweetalert2";

const DefaultAdAccountSmall = ({ isConnected = false }) => {
  const { accountId } = useAuthorizedAccountId();

  const [adAccounts, setAdAccounts] = useState([]);
  const [defaultAdAccount, setDefaultAdAccount] = useState(null);
  const [deletingAdAccount, setDeletingAdAccount] = useState(false);
  const [changingDefaultAdAccount, setChangingDefaultAdAccount] =
    useState(false);
  const [deleting, setDeleting] = useState(false);
  const [triggerChangeCount, setTriggerChangeCount] = useState(0);
  const [selectedAdAccount, setSelectedAdAccount] = useState(null);

  useEffect(() => {
    const getAdAccounts = async () => {
      const payload = {
        accountId: accountId,
      };
      const res = await callMercury({
        ...payload,
        path: "getAdAccounts",
        method: "GET",
      });
      console.log("getAdAccounts", res);
      if (res.data.status === "success") {
        setAdAccounts(
          res.data.data.map((option) => {
            return {
              value: option.id,
              label: option.name,
            };
          })
        );
      }
    };
    if (accountId) getAdAccounts();
  }, [accountId]);

  useEffect(() => {
    const getDefaultAdAccount = async () => {
      const payload = {
        accountId,
      };
      const res = await callMercury({
        ...payload,
        method: "GET",
        path: "getDefaultAdAccount",
      });
      console.log("getDefaultAdAccount", res);
      if (res.data.status === "success") {
        if (res.data.data) {
          setDefaultAdAccount({
            label: res.data.data.name,
            value: res.data.data.id,
          });
          setSelectedAdAccount({
            label: res.data.data.name,
            value: res.data.data.id,
          });
        }
      }
    };
    if (accountId) getDefaultAdAccount();
  }, [accountId, triggerChangeCount]);

  const deleteAdAccountDefaultSwal = () => {
    Swal.fire({
      title: "Are you sure?",
      text: "You will delete this Default Ad Account.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (result.isConfirmed) {
        handleDelete();
      }
    });
  };

  const handleDelete = async () => {
    setDeleting(true);
    const data = {
      accountId: accountId,
    };
    const result = await callMercury({
      ...data,
      path: "deleteDefaultAdAccount",
      method: "DELETE",
    });
    setDeleting(false);
    setDefaultAdAccount(null);
    setSelectedAdAccount(null);
    if (result.data.status !== "success") {
      Swal.fire({
        title: "Fail",
        text: "There was an error deleting this default Facebook Ads Page",
        icon: "error",
      });
      return;
    }
    if (result.data.status === "success") {
      Swal.fire({
        title: "Facebook Default Ads Page Deleted",
        text: "You have deleted this default Facebook Ads Page",
        icon: "success",
      });
      return;
    }
  };

  const openChangeModal = ({
    message,
    changeDefaultCallback,
    resetDefaultCallback,
  }) => {
    Swal.fire({
      title: "Are you sure?",
      text: message,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, change it!",
    }).then((result) => {
      if (result.isConfirmed) {
        changeDefaultCallback();
      } else if (result.isDismissed) {
        resetDefaultCallback();
      }
    });
  };

  const showSuccessMessage = (
    message = "Default Ad Account Changed Successfully."
  ) => {
    Swal.fire({
      title: "Success",
      text: message,
      icon: "success",
    });
  };

  const changeDefaultAdAccountSwal = async () => {
    setChangingDefaultAdAccount(true);
    const data = {
      accountId: accountId,
      adAccountId: defaultAdAccount.value,
    };
    const result = await callMercury({
      ...data,
      path: "setDefaultAdAccount",
      method: "POST",
    });
    if (result.data.status === "success") {
      //   setAdPage(result.data.pageAds);
      setSelectedAdAccount({ ...defaultAdAccount });
      showSuccessMessage();
    } else {
      Swal.fire({
        title: "Fail",
        text: result.data.message,
        icon: "error",
      });
    }
    setChangingDefaultAdAccount(false);
  };

  const resetDefaultAdAccount = () => {
    triggerChangeCount((prev) => prev + 1);
  };

  return (
    <div className="w-full flex flex-col items-center p-4 px-6">
      <SubHeading title={"Default Ad Account"} mini />
      {isConnected ? (
        <div className="w-full flex flex-col items-center justify-center mt-2  gap-3">
          {selectedAdAccount ? (
            <>
              <div className="w-full flex flex-col justify-center items-center gap-2 text-text-color">
                <p className="m-0">
                  <strong className="text-lg font-bold">
                    {selectedAdAccount ? selectedAdAccount.label : " "}
                  </strong>
                </p>
                <p className="m-0">
                  <i className="text-base">
                    {selectedAdAccount ? selectedAdAccount.value : " "}
                  </i>
                </p>
              </div>
              <div className="w-full flex justify-center text-text-color">
                <ButtonLoading
                  value={"Delete Default Ad Account"}
                  loadingText={" Deleting..."}
                  className="!bg-red text-white rounded-full p-2 px-4 py-2 text-base m-0"
                  type="submit"
                  onClick={deleteAdAccountDefaultSwal}
                  isLoading={deleting}
                />
              </div>
            </>
          ) : (
            <div className="text-base mt-4">No ad account</div>
          )}
          <div className="w-full flex flex-col justify-center items-center text-center text-text-color gap-2">
            <div>
              <SelectGeneric
                options={adAccounts}
                onChange={(value) => {
                  console.log(adAccounts);
                  console.log(value);
                  setDefaultAdAccount(value);
                }}
                value={defaultAdAccount}
              />
            </div>
            <div>
              <ButtonLoading
                value={"Change"}
                loadingText={" Changing..."}
                className={
                  "bg-background text-white rounded-full p-2 px-4 py-2 text-base m-0"
                }
                type="submit"
                onClick={() => {
                  openChangeModal({
                    message: "You will change this default Ad account.",
                    changeDefaultCallback: changeDefaultAdAccountSwal,
                    resetDefaultCallback: resetDefaultAdAccount,
                  });
                }}
                isLoading={changingDefaultAdAccount}
              />
            </div>
          </div>
        </div>
      ) : (
        <div className="text-base mt-4">No connected account at facebook</div>
      )}
    </div>
  );
};

export default DefaultAdAccountSmall;
