///////MAY NOT NEED IT ANYMORE BUT MAKING SURE FIRST

// import { useState, useRef, useEffect } from "react";
// import {
//   useAllTemplates,
//   useLandingPageTemplateTypes,
// } from "../../domain/templates/data/landingTemplates";
// import { useLocation, useNavigate } from "react-router-dom";

// function ButtonDropdownCombo({ onItemSelect }) {
//   const navigate = useNavigate();
//   const { data: pageTypes = [] } = useLandingPageTemplateTypes();
//   const dropdownItems = pageTypes;
//   const [selectedTemplateId, setSelectedTemplateId] = useState(null);
//   const { data: allTemplates = [] } = useAllTemplates();
//   const location = useLocation();
//   const pathSegments = location.pathname.split("/"); // splits the URL path
//   console.log("🔥 pathSegments:", pathSegments);
//   const category = pathSegments[pathSegments.length - 1]; // gets the last segment
//   const webTemplates = allTemplates.filter(
//     (template) =>
//       template.primaryType === "web" &&
//       (!category || template.category === category)
//   );
//   const [isOpen, setIsOpen] = useState(false);
//   const [selectedItem, setSelectedItem] = useState(null);
//   const [highlightedIndex, setHighlightedIndex] = useState(-1);
//   const listRef = useRef(null);

//   const toggleDropdown = () => {
//     setIsOpen((prev) => !prev);
//   };

//   const handleItemSelect = (item, index) => {
//     setSelectedItem(item);
//     setIsOpen(false);
//     setHighlightedIndex(index);

//     // ✅ Navigate to the same path, but with new category ID
//     const newCategory = item.id; // ← this is your doc ID like 'leadMagnet'
//     const pathSegments = location.pathname.split("/");

//     // Replace the last segment with the new doc ID
//     pathSegments[pathSegments.length - 1] = newCategory;

//     // Rebuild the new path and navigate to it
//     const newPath = pathSegments.join("/");
//     navigate(newPath);

//     if (location.pathname === newPath) {
//       // We're re-selecting the same category — manually trigger preview update
//       const firstTemplate = webTemplates[0];
//       if (firstTemplate) {
//         setSelectedTemplateId(firstTemplate.id);
//         if (onItemSelect) {
//           onItemSelect(firstTemplate);
//           console.log(
//             "🔁 Re-selected same category, showing first template again:",
//             firstTemplate
//           );
//         }
//       }
//     } else {
//       // Navigate to new category like usual
//       navigate(newPath);
//       if (onItemSelect) {
//         onItemSelect(item);
//         console.log("🔁 Navigated to:", newPath);
//       }
//     }
//   };

//   const handleKeyDown = (event) => {
//     if (event.key === "ArrowDown") {
//       event.preventDefault();
//       if (!isOpen) {
//         setIsOpen(true);
//         setHighlightedIndex(0);
//       } else {
//         setHighlightedIndex((prev) =>
//           prev + 1 < dropdownItems.length ? prev + 1 : prev
//         );
//       }
//     } else if (event.key === "ArrowUp") {
//       event.preventDefault();
//       if (isOpen) {
//         setHighlightedIndex((prev) => (prev - 1 >= 0 ? prev - 1 : 0));
//       }
//     } else if (event.key === "Enter" || event.key === " ") {
//       event.preventDefault();
//       if (!isOpen) {
//         setIsOpen(true);
//         setHighlightedIndex(0);
//       } else if (
//         highlightedIndex >= 0 &&
//         highlightedIndex < dropdownItems.length
//       ) {
//         const item = dropdownItems[highlightedIndex];
//         handleItemSelect(item, highlightedIndex);
//       }
//     } else if (event.key === "Escape") {
//       event.preventDefault();
//       setIsOpen(false);
//     }
//   };

//   // When dropdown opens, focus the list container for better keyboard navigation.
//   useEffect(() => {
//     if (isOpen && listRef.current) {
//       listRef.current.focus();
//     }
//   }, [isOpen]);

//   useEffect(() => {
//     setSelectedTemplateId(null);
//   }, [category]);

//   useEffect(() => {
//     if (webTemplates.length > 0 && !selectedTemplateId) {
//       const firstTemplate = webTemplates[0];
//       setSelectedTemplateId(firstTemplate.id);
//       if (onItemSelect) {
//         onItemSelect(firstTemplate);
//         console.log(
//           "🔁 Switched category, showing first template:",
//           firstTemplate
//         );
//       }
//     }
//   }, [category, webTemplates, selectedTemplateId, onItemSelect]);

//   useEffect(() => {
//     if (dropdownItems.length > 0 && !selectedItem) {
//       const match = dropdownItems.find(
//         (type) => type.id.toLowerCase() === category.toLowerCase()
//       );

//       if (match) {
//         setSelectedItem(match);
//       }
//     }
//   }, [dropdownItems, selectedItem, category]);

//   return (
//     <>
//       <div
//         className="relative inline-block w-full"
//         role="combobox"
//         aria-haspopup="listbox"
//         aria-expanded={isOpen}
//         aria-controls="dropdown-list "
//       >
//         {/* Dropdown Toggle Button */}
//         <button
//           type="button"
//           onClick={toggleDropdown}
//           onKeyDown={handleKeyDown}
//           className="mx-auto w-[250px] w-full max-w-xlg bg-gray-300 text-gray-600 px-10 py-5 font-bold flex items-center justify-between border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
//         >
//           <span>{selectedItem?.name}</span>
//           <span
//             className={`text-xl ${isOpen ? "ion-chevron-up" : "ion-chevron-down"}`}
//           ></span>
//         </button>

//         {/* Custom Dropdown List */}
//         {isOpen && (
//           <ul
//             ref={listRef}
//             id="dropdown-list"
//             role="listbox"
//             tabIndex="-1"
//             onKeyDown={handleKeyDown}
//             className="absolute left-0 mt-1 w-full bg-white border border-gray-200 rounded shadow-lg z-10"
//           >
//             {dropdownItems.map((item, index) => (
//               <li
//                 key={item.id}
//                 role="option"
//                 aria-selected={selectedItem && selectedItem.id === item.id}
//                 onClick={() => handleItemSelect(item, index)}
//                 className={`px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center ${
//                   highlightedIndex === index ? "bg-gray-100" : ""
//                 }`}
//               >
//                 <span>{item.name}</span>
//                 {selectedItem && selectedItem.id === item.id && (
//                   <span className="icon icon-color3 ion-checkmark ml-auto"></span>
//                 )}
//               </li>
//             ))}
//           </ul>
//         )}
//       </div>
//       <div className="gallery gallery-medium-col-2 gallery-large-col-1 gallery-xlarge-col-2 gallery-style-2 gallery-caption-style-2 text-center clearfix">
//         {webTemplates.map((template) => (
//           <div
//             key={template.id}
//             className="align-center gallery-item padding padding-top-small padding-bottom-small"
//           >
//             <div className="padding-small text-large text-light">
//               <div className="mx-auto mt-10 max-w-[400px]">
//                 <div
//                   key={template.id}
//                   onClick={() => {
//                     setSelectedTemplateId(template.id);
//                     if (onItemSelect) {
//                       onItemSelect(template); // ✅ This passes the whole object to CustomizeTemplate
//                       console.log("🔥 Clicked gallery template:", template); // Add this for debugging
//                     }
//                   }}
//                   className={`transition-all duration-100 transform hover:scale-105 hover:shadow-xl overflow-hidden
//                             ${selectedTemplateId === template.id ? "border-8 border-green-500" : "border-0"}`}
//                 >
//                   <img src={template.previewImage} className="w-full h-auto" />
//                 </div>

//                 <h3>{template.name}</h3>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>
//     </>
//   );
// }

// export default ButtonDropdownCombo;
