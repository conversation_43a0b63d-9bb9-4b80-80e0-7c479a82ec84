import {
  useQuery,
  hashKey,
  QueryClient,
  QueryClientProvider as QueryClientProviderBase,
} from "@tanstack/react-query";
import {
  getFirestore,
  connectFirestoreEmulator,
  onSnapshot,
  doc,
  collection,
  query,
  where,
  orderBy,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  addDoc,
  deleteDoc,
  limit,
  serverTimestamp,
  Timestamp,
} from "firebase/firestore";
// db can't self reference itself, therefore the as titanDb
// passed in for default value of db, this allows us to pick and choose what db we want to send the data to
// titan/mercury/apollo etc etc
import {
  db as titanDb,
  client,
  createQuery,
  format,
  mercuryDB,
} from "../../providers/database";

export async function updateAutoSaveSubcollection(
  db: any = titanDb,
  collection: string,
  collectionId: string,
  subcollection: string,
  subcollectionId: string,
  data: object
) {
  try {
    const docRef = doc(
      db,
      collection,
      collectionId,
      subcollection,
      subcollectionId
    );
    await updateDoc(docRef, data);
  } catch (error) {
    console.error("Error updating subcollection document", error);
    throw error;
  }
}

export async function updateAutoSave(
  db: any = titanDb,
  collection: string,
  id: string,
  data: object
) {
  try {
    let mercuryData = false;
    const removeMercuryFromKeys = (
      data: Record<string, any>
    ): Record<string, any> => {
      return Object.keys(data).reduce(
        (acc: Record<string, any>, key: string) => {
          if (key.includes("mercury.")) {
            mercuryData = true;
          }
          const newKey = key.replace("mercury.", "");
          acc[newKey] = data[key];
          return acc;
        },
        {}
      );
    };

    const updatedData = removeMercuryFromKeys(data);

    if (!mercuryData) {
      // ensure updatedAt is updated for change
      let collectionPieces = collection.split("/");
      if (collectionPieces?.length && collectionPieces?.[0] === 'Account' && !Number.isNaN(collectionPieces[1])) {
        if (collectionPieces[3] && collectionPieces[3] !== '') {
          let rootCollection = 'Account/' + collectionPieces[1] + '/' + collectionPieces[2] + '/';
          await updateDoc(doc(db, rootCollection, collectionPieces[3]), { "updatedAt": Timestamp.now() });
        } else {
          Object.assign(data, { updatedAt: Timestamp.now() });
        }
      }
      // perform the actual update
      await updateDoc(doc(db, collection, id), data);
    } else {
      await updateDoc(doc(mercuryDB, collection, id), updatedData);
    }
  } catch (error) {
    console.error("Error updating document:", error);
    throw error; // Re-throw or handle as needed
  }
}

export const setAutoSave = (
  db: any = titanDb,
  collection: string,
  id: string,
  data: object
) => {
  return setDoc(doc(db, collection, id), data);
};

export async function updateStatusHistory(
  db: any = titanDb,
  parent: string,
  id: string,
  data: object
) {
  await addDoc(collection(db, `${parent}/${id}/StatusHistory`), data);
}

// This function logs the activity and writes it to the "Activity" collection
export const logActivity = async (
  db: any = titanDb,
  collectionName: string,
  id: string,
  activityField: string,
  beforeField: string,
  afterField: string,
  who: string,
  activityLabel: string
) => {
  const params = {
    collection: collectionName,
    id,
    activityField,
    beforeField: beforeField || "New",
    afterField,
    created_at: Timestamp.now().toDate(),
    who,
    activityLabel,
  };
  await addDoc(collection(db, "Activity"), params);
};

export const analyticsLog = async (
  db: any = titanDb,
  type: string,
  typeId: string,
  userId: string,
  route: string
) => {
  const params = {
    type: type,
    typeId: typeId,
    userId: userId,
    route: route,
    createdAt: Timestamp.now(),
  };
  await addDoc(collection(db, "Views"), params);
};

export function useUtilitySearch<T>(
  db: any = titanDb,
  collectionName: string,
  name: string,
  field: string,
  status: string,
  id: string
) {
  return useQuery({
    queryKey: [name, query],
    queryFn: createQuery<T>(() =>
      query(
        collection(db, collectionName),
        where(field, "==", id),
        where("status", "==", status)
      )
    ),
    enabled: !!query,
  });
}
