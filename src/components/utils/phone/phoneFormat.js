// Comprehensive mapping of country codes to flag emojis
const countryFlags = {
    '1': '🇺🇸', // United States, Canada, and other NANP countries
    '91': '🇮🇳', // India
    '44': '🇬🇧', // United Kingdom
    '61': '🇦🇺', // Australia
    '81': '🇯🇵', // Japan
    '86': '🇨🇳', // China
    '33': '🇫🇷', // France
    '49': '🇩🇪', // Germany
    '39': '🇮🇹', // Italy
    '7': '🇷🇺', // Russia
    '34': '🇪🇸', // Spain
    '55': '🇧🇷', // Brazil
    '27': '🇿🇦', // South Africa
    '82': '🇰🇷', // South Korea
    '90': '🇹🇷', // Turkey
    '48': '🇵🇱', // Poland
    '351': '🇵🇹', // Portugal
    '358': '🇫🇮', // Finland
    '46': '🇸🇪', // Sweden
    '47': '🇳🇴', // Norway
    '32': '🇧🇪', // Belgium
    '31': '🇳🇱', // Netherlands
    '30': '🇬🇷', // Greece
    '36': '🇭🇺', // Hungary
    '380': '🇺🇦', // Ukraine
    '40': '🇷🇴', // Romania
    '421': '🇸🇰', // Slovakia
    '420': '🇨🇿', // Czech Republic
    '372': '🇪🇪', // Estonia
    '370': '🇱🇹', // Lithuania
    '371': '🇱🇻', // Latvia
    '41': '🇨🇭', // Switzerland
    '356': '🇲🇹', // Malta
    '352': '🇱🇺', // Luxembourg
    '353': '🇮🇪', // Ireland
    '357': '🇨🇾', // Cyprus
    '51': '🇵🇪', // Peru
    '52': '🇲🇽', // Mexico
    '56': '🇨🇱', // Chile
    '54': '🇦🇷', // Argentina
    '53': '🇨🇺', // Cuba
    '63': '🇵🇭', // Philippines
    '64': '🇳🇿', // New Zealand
    '66': '🇹🇭', // Thailand
    '92': '🇵🇰', // Pakistan
    '94': '🇱🇰', // Sri Lanka
    '95': '🇲🇲', // Myanmar
    '98': '🇮🇷', // Iran
    '62': '🇮🇩', // Indonesia
    '60': '🇲🇾', // Malaysia
    '65': '🇸🇬', // Singapore
    '93': '🇦🇫', // Afghanistan
    '964': '🇮🇶', // Iraq
    '965': '🇰🇼', // Kuwait
    '971': '🇦🇪', // United Arab Emirates
    '972': '🇮🇱', // Israel
    '973': '🇧🇭', // Bahrain
    '974': '🇶🇦', // Qatar
    '975': '🇧🇹', // Bhutan
    '976': '🇲🇳', // Mongolia
    '977': '🇳🇵', // Nepal
    '961': '🇱🇧', // Lebanon
    '962': '🇯🇴', // Jordan
    '963': '🇸🇾', // Syria
    '966': '🇸🇦', // Saudi Arabia
    '967': '🇾🇪', // Yemen
    '968': '🇴🇲', // Oman
    '970': '🇵🇸', // Palestine
    '880': '🇧🇩', // Bangladesh
    '960': '🇲🇻', // Maldives
    // Add more country codes and their respective flags here
};

export const formatPhoneNumber = (phoneNumber) => {
    if (!phoneNumber || phoneNumber.length < 3) {
        return phoneNumber; // Return as is if the format is unexpected
    }
    const originalPhoneNumber = phoneNumber;
    phoneNumber = phoneNumber.replace(/\D/g, ''); // Remove all non-digit characters

    // Check if the number already has a country code (starts with '+')
    let hasCountryCode = phoneNumber.startsWith('+');

    // If no country code, add '+1' (assuming US/Canada default)
    if (!hasCountryCode) {
        // Check if it already starts with '1'
        if (phoneNumber.startsWith('1') && phoneNumber.length === 11) {
            phoneNumber = '+' + phoneNumber;
        } else if (phoneNumber.length === 10) {
            phoneNumber = '+1' + phoneNumber;
        }
    }


    // Remove the '+' sign for processing
    let processedNumber = phoneNumber.startsWith('+') ? phoneNumber.slice(1) : phoneNumber;

    // Determine the length of the country code
    let countryCodeLength = 1;
    if (processedNumber.startsWith('1') && processedNumber.length === 11) {
        countryCodeLength = 1; // Special case for the US, Canada, and other NANP countries
    } else if (processedNumber.length === 12) {
        countryCodeLength = 2; // Typical case for many international numbers
    } else if (processedNumber.length > 12) {
        countryCodeLength = 3; // For country codes with more than 2 digits
    }

    if (originalPhoneNumber !== phoneNumber) {
        // If the phone number was modified, log the original and modified numbers
        console.log(`Original: ${originalPhoneNumber}, Modified: ${phoneNumber}`);
    }

    const countryCode = processedNumber.slice(0, countryCodeLength);
    const remainingNumber = processedNumber.slice(countryCodeLength);

    const areaCode = remainingNumber.slice(0, 3);
    const centralOfficeCode = remainingNumber.slice(3, 6);
    const lineNumber = remainingNumber.slice(6);

    // Get the flag emoji for the country code
    const flag = countryFlags[countryCode] || '';

    // Return the formatted phone number with the flag and the "+" sign before the country code
    return `${flag} +${countryCode} (${areaCode}) ${centralOfficeCode}-${lineNumber}`;
};
export const formatPhoneNumberWithoutCode = (phoneNumber) => {
    if (phoneNumber) {
        const cleaned = phoneNumber?.replace(/\D/g, "")?.slice(-10);
        return cleaned?.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
    }
    else {
        return null;
    }
}