// fetchDocument.ts
import { doc, getDoc } from "firebase/firestore";
import { db } from "../../providers/database";

export async function fetchDocument<T>(
  collectionName: string,
  docId: string
): Promise<T> {
  const docRef = doc(db, collectionName, docId);
  const docSnapshot = await getDoc(docRef);
  if (docSnapshot.exists()) {
    return docSnapshot.data() as T;
  } else {
    throw new Error("Document not found");
  }
}
