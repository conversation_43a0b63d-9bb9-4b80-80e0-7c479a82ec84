import {
  useQuery,
  hashKey,
  QueryClient,
  QueryClientProvider as QueryClientProviderBase,
} from "@tanstack/react-query";
import {
  getFirestore,
  connectFirestoreEmulator,
  onSnapshot,
  doc,
  collection,
  query,
  where,
  orderBy,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  addDoc,
  deleteDoc,
  limit,
  serverTimestamp,
  Timestamp,
} from "firebase/firestore";
import { db, client, createQuery, format } from "../../providers/database";

import { collectionNames } from "../../data/constants/collectionNames";

const lookupRef = collection(db, collectionNames.lookup);

export function useLookup<T>(group: string) {
  return useQuery({
    queryKey: [`${group}Lookup`, { group }],
    queryFn: createQuery<T>(() =>
      query(lookupRef, orderBy("sortOrder", "asc"), where("group", "==", group))
    ),
    enabled: !!group,
  });
}

export function useLookupItem(group: string, code: string) {
  return useQuery({
    queryKey: [`${group}Lookup`, { group, code }],
    queryFn: createQuery(() =>
      query(
        lookupRef,
        orderBy("sortOrder", "asc"),
        where("group", "==", [group]),
        where("code", "==", [code])
      )
    ),
    enabled: !!group && !!code,
  });
}

export function useLookupItemByArray<T>(group: string, code: Array<string>) {
  return useQuery({
    queryKey: [`${group}Lookup`, { group, code }],
    queryFn: createQuery<T>(() =>
      query(
        lookupRef,
        orderBy("sortOrder", "asc"),
        where("group", "==", group),
        where("code", "in", code)
      )
    ),
    enabled: !!group && !!code,
  });
}

export function useLookupCode<T>(code: string) {
  return useQuery({
    queryKey: [`${code}Lookup`, { code }],
    queryFn: createQuery<T>(() =>
      query(lookupRef, orderBy("sortOrder", "asc"), where("code", "==", code))
    ),
    enabled: !!code,
  });
}

export function useFindLookup<T>(group: string, code: string) {
  return useQuery({
    queryKey: [`${code}Lookup`, { code }],
    queryFn: createQuery<T>(() =>
      query(
        lookupRef,
        orderBy("sortOrder", "asc"),
        where("group", "==", group),
        where("code", "==", code)
      )
    ),
    enabled: !!code,
  });
}
