import { titanApp, titanDb } from "../../providers/firebase";
import {
  useQuery,
  hashKey,
  QueryClient,
  QueryClientProvider as QueryClientProviderBase,
} from "@tanstack/react-query";
import {
  getFirestore,
  connectFirestoreEmulator,
  onSnapshot,
  doc,
  collection,
  query,
  where,
  orderBy,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  addDoc,
  deleteDoc,
  limit,
  serverTimestamp,
  Timestamp,
} from "firebase/firestore";
import { createQuery, format } from "../../providers/database";

import {
  collectionNames,
  subCollectionNames,
} from "../../data/constants/collectionNames";
import { User } from "../../domain/user/types/User";
import { z } from "zod";

export const db = titanDb;

export function useUser<T>(uid: any) {
  return useQuery({
    queryKey: ["user", { uid }],
    queryFn: createQuery<T>(() => doc(db, collectionNames.users, uid)),
    enabled: !!uid,
  });
}

// Fetch user data once (non-hook)
// Useful if you need to fetch data from outside of a component
export function getUser(uid: string) {
  return getDoc(doc(db, collectionNames.users, uid)).then(format);
}

export function useUserStatus<T>(id: string) {
  return useQuery({
    queryKey: ["userStatus", { id }],
    queryFn: createQuery<T>(() => doc(db, "UserStatus", id)),
    enabled: !!id,
  });
}

// Create a new user
export function createUser(uid: string, data: User) {
  return setDoc(doc(db, collectionNames.users, uid), data, { merge: true });
}

// Update an existing user
export function updateUser(uid: string, data: User) {
  return updateDoc(doc(db, collectionNames.users, uid), data);
}

// Update existing user claims on User collection
export function updateUserOnboardedStatus(uid: string, isOnboarded: boolean) {
  return updateDoc(doc(db, collectionNames.users, uid), {
    "claims.onboarded": isOnboarded,
  });
}

export function disableUser(uid: string) {
  return updateDoc(doc(db, "users", uid), { active: false });
}

export function useNotesUser<T>(uid: string) {
  return useQuery({
    queryKey: ["notesUser", { uid }],
    queryFn: createQuery<T>(() => doc(db, collectionNames.users, uid)),
    enabled: !!uid,
  });
}

export function useUserByEmail(email: string, trigger = true) {
  const emailSchema = z.object({
    email: z.string().email(),
  });
  const result = emailSchema.safeParse(email);

  return useQuery({
    queryKey: ["userByEmail", { email }],
    queryFn: createQuery(() => {
      return query(
        collection(db, collectionNames.users),
        where("email", "==", email),
        limit(1)
      );
    }),
    enabled: !!email && !!result && trigger,
  });
}

export function useUsersByRole(role: string) {
  return useQuery({
    queryKey: ["allUsersByRole", { role }],
    queryFn: createQuery(() => {
      return query(
        collection(db, collectionNames.users),
        where("claims.role", "==", role),
        where("claims.active", "==", true)
      );
    }),
    enabled: !!role,
  });
}
export function useUsersByStatus<T>(status: string) {
  return useQuery({
    queryKey: ["allUsersByStatus", { status }],
    queryFn: createQuery<T>(() => {
      return query(
        collection(db, collectionNames.users),
        where("status", "==", status)
      );
    }),
    enabled: !!status,
  });
}
export function useUsersByStatusWithExcludedRole(status: string, role: string) {
  return useQuery({
    queryKey: ["allUsersByStatus", { status }],
    queryFn: createQuery(() => {
      return query(
        collection(db, collectionNames.users),
        where("status", "==", status),
        where("claims.role", "!=", role)
      );
    }),
    enabled: !!status,
  });
}

export function useAllUsers() {
  return useQuery({
    queryKey: ["allUsers"],
    queryFn: createQuery(() => {
      return query(collection(db, collectionNames.users));
    }),
    enabled: true,
  });
}
