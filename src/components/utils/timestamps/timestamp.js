import { format } from 'date-fns';

// use on algolia hits
export function AlgoliaDisplayDate(timestamp) {
  let formattedDate = "";

  if (timestamp !== null && timestamp !== undefined) {
    const date = new Date(timestamp);
    formattedDate = formatDate(date);
  }

  return formattedDate;
}

// use for firebase timestamps fetched with react query
export function DisplayDate(timestamp) {
  let formattedDate = "";
  if (timestamp !== null && timestamp !== undefined && typeof timestamp.toDate === 'function') {
    const date = timestamp.toDate();
    formattedDate = formatDate(date);
  }

  return formattedDate;
}

export function DisplayFullDate(timestamp) {
  let formattedDate = "";
  if (timestamp !== null && timestamp !== undefined) {
    if (typeof timestamp.toDate === 'function') {
      // Firestore Timestamp object
      const date = timestamp.toDate();
      formattedDate = formatFullDate(date);
    } else if (typeof timestamp === 'number') {
      // Unix timestamp
      const date = new Date(timestamp * 1000); // Convert Unix timestamp to milliseconds
      formattedDate = formatFullDate(date);
    } else if (typeof timestamp === 'string') {
      // ISO 8601 date string
      const date = new Date(timestamp);
      formattedDate = formatFullDate(date);
    }
  }

  return formattedDate;
}

function formatDate(date) {
  return format(date, 'MM/dd/yyyy');
}

function formatFullDate(date) {
  return format(date, "MMMM dd, yyyy 'at' hh:mm a");
}

export function DisplayTime(timestamp) {
  let formattedTime = "";
  if (timestamp !== null && timestamp !== undefined && typeof timestamp.toDate === 'function') {
    const date = timestamp.toDate();
    formattedTime = format(date, 'hh:mm a');
  }
  return formattedTime;
}

export function DisplayTimestamp(timestamp) {
  let formattedDate = "";
  if (timestamp !== null && timestamp !== undefined) {
    if (typeof timestamp.toDate === 'function') {
      const date = timestamp.toDate();
      formattedDate = `${format(date, 'P')} @ ${format(date, 'pp')}`;
    } else if (typeof timestamp === 'string') {
      const date = new Date(Date.parse(timestamp));
      formattedDate = format(date, 'MMMM d, yyyy');
    }
  }
  return formattedDate;
}

export function DiffTimestampNow(timestamp) {
  const now = Date.now();
  const date = timestamp.toDate();
  return getDateDiffInDays(date, now);
}

export function getDateDiffInDays(date1, date2) {
  const diffInMilliseconds = date2 - date1;
  const diffInDays = diffInMilliseconds / 86400000;

  return diffInDays;
}

export function DisplayDateOther(timestamp) {
  const date = new Date(timestamp);
  return format(date, 'P');
}

export function DisplayISO(timestamp) {
  if (timestamp === undefined) {
    return "N/A";
  }
  const date = new Date(timestamp);
  return format(date, 'MM/dd/yyyy');
}
