// initialized firebase app
import { titanApp, mercuryApp } from "../providers/firebase";
import { getFunctions, httpsCallable } from "firebase/functions";
import cloudFunctionNames from "./dictionary/cloudFunctionNames";

const functions = getFunctions(titanApp);
const mercuryFunctions = getFunctions(mercuryApp);

export const adminCreateUser = httpsCallable(
  functions,
  cloudFunctionNames.adminCreateUser
);

export const addressValidation = httpsCallable(
  functions,
  cloudFunctionNames.addressValidation
);

export const callMercury = httpsCallable(
  functions,
  cloudFunctionNames.callMercury
);

export const callGaia = httpsCallable(functions, cloudFunctionNames.callGaia);

export const resetPassword = httpsCallable(
  functions,
  cloudFunctionNames.resetPassword
);

export const updateResetPassword = httpsCallable(
  functions,
  cloudFunctionNames.updateResetPassword
);

export const fetchCredentials = httpsCallable(
  functions,
  cloudFunctionNames.fetchCredentials
);

export const sendSMSMessage = httpsCallable(
  functions,
  cloudFunctionNames.sendSMSMessage
);

export const addToTopic = httpsCallable(
  functions,
  cloudFunctionNames.addToTopic
);

export const addToTopicMercury = httpsCallable(
  functions,
  cloudFunctionNames.addToTopicMercury
);

export const getEnterpriseStats = httpsCallable(
  functions,
  cloudFunctionNames.getEnterpriseStats
);

export const massAssignToEnterprise = httpsCallable(
  functions,
  cloudFunctionNames.massAssignToEnterprise
);

export const askChatGPT = httpsCallable(
  functions,
  cloudFunctionNames.askChatGPT
);

export const generateSignedUrl = httpsCallable(
  functions,
  cloudFunctionNames.generateSignedUrl
);

export const extendRecipientProperty = httpsCallable(
  functions,
  cloudFunctionNames.extendRecipientProperty
);

export const recipientSearch = httpsCallable(
  functions,
  cloudFunctionNames.recipientSearch
);

export const articleContent = httpsCallable(
  functions,
  cloudFunctionNames.articleContent
);
export const updateContent = httpsCallable(
  functions,
  cloudFunctionNames.updateContent
);

export const contentOutput = httpsCallable(
  functions,
  cloudFunctionNames.contentOutput
);

export const callAstrid = httpsCallable(
  functions,
  cloudFunctionNames.callAstrid
);

export const getSuggestions = httpsCallable(
  functions,
  cloudFunctionNames.getSuggestions
);

// API functions
export const api = httpsCallable(functions, cloudFunctionNames.api);
export const apiKeyGenerate = httpsCallable(
  functions,
  cloudFunctionNames.apiKeyGenerate
);

export const cyclrApi = httpsCallable(
  functions, 
  cloudFunctionNames.cyclrApi
);

export const integrationsUtils = httpsCallable(
  functions, 
  cloudFunctionNames.integrationsUtils
);

export const accountBillingOverview = httpsCallable(
  functions,
  cloudFunctionNames.accountBillingOverview
);

export const dataSourceRequest = httpsCallable(
  functions,
  cloudFunctionNames.dataSourceRequest
);

export const executeScheduledEndpoint = httpsCallable(
  functions,
  cloudFunctionNames.executeScheduledEndpoint
);
export const createWorkflowFromTemplate = httpsCallable(
  functions,
  cloudFunctionNames.createWorkflowFromTemplate
);

export const releaseExclusivityNow = httpsCallable(
  functions,
  cloudFunctionNames.releaseExclusivityNow
);

export const reconcileMailingAddressClaims = httpsCallable(
  functions,
  cloudFunctionNames.reconcileMailingAddressClaims
);

export const getRecipientGroupCounts = httpsCallable(
  functions,
  cloudFunctionNames.getRecipientGroupCounts
);

export const getAccountMagazineRecipients = httpsCallable(
  functions,
  cloudFunctionNames.getAccountMagazineRecipients
);

export const uploadOptionFiles = httpsCallable(
  functions,
  cloudFunctionNames.uploadOptionFiles
);
