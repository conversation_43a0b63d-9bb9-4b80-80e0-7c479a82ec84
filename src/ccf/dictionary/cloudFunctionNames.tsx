//  this might be best used as an npm package to be shared between cloud functions and client apps.
type CloudFunctionNames = {
  adminCreateUser: string;
  addressValidation: string;
  callMercury: string;
  callGaia: string;
  resetPassword: string;
  sendSMSMessage: string;
  updateResetPassword: string;
  addToTopic: string;
  addToTopicMercury: string;
  getEnterpriseStats: string;
  massAssignToEnterprise: string;
  askChatGPT: string;
  extendRecipientProperty: string;
  recipientSearch: string;
  articleContent: string;
  updateContent: string;
  contentOutput: string;
  callAstrid: string;
  getSuggestions: string;
  generateSignedUrl: string;
  fetchCredentials: string;
  trackEvent: string;
  cyclrApi: string;
  integrationsUtils: string;
  api: string;
  apiKeyGenerate: string;
  accountBillingOverview: string;
  dataSourceRequest: string;
  executeScheduledEndpoint: string;
  createWorkflowFromTemplate: string;
  releaseExclusivityNow: string;
  reconcileMailingAddressClaims: string;
  getRecipientGroupCounts: string;
  getAccountMagazineRecipients: string;
  uploadOptionFiles: string;
};

const cloudFunctionNames: CloudFunctionNames = {
  adminCreateUser: "adminCreateUser",
  addressValidation: "addressValidation-addressValidation",
  callMercury: "callMercury",
  callGaia: "callGaia",
  resetPassword: "resetPassword",
  sendSMSMessage: "sendSMSMessage",
  updateResetPassword: "updateResetPassword",
  addToTopic: "addToTopic",
  addToTopicMercury: "addToTopic",
  getEnterpriseStats: "getEnterpriseStats",
  massAssignToEnterprise: "massAssignToEnterprise",
  askChatGPT: "askChatGPT",
  extendRecipientProperty: "extendRecipientProperty",
  recipientSearch: "recipientSearch",
  articleContent: "articleContent",
  updateContent: "updateContent",
  contentOutput: "contentOutput",
  callAstrid: "callAstrid",
  getSuggestions: "getSuggestions",
  generateSignedUrl: "generateSignedUrl",
  fetchCredentials: "fetchCredentials",
  trackEvent: "trackEvent",
  cyclrApi: "cyclrApi",
  integrationsUtils: "integrationsUtils",
  api: "api",
  apiKeyGenerate: "apiKeyGenerate",
  accountBillingOverview: "accountBillingOverview",
  dataSourceRequest: "dataSourceRequest",
  executeScheduledEndpoint: "executeScheduledEndpoint",
  createWorkflowFromTemplate: "createWorkflowFromTemplate",
  releaseExclusivityNow: "releaseExclusivityNow",
  reconcileMailingAddressClaims: "reconcileMailingAddressClaims",
  getRecipientGroupCounts: "getRecipientGroupCounts",
  getAccountMagazineRecipients: "getAccountMagazineRecipients",
  uploadOptionFiles: "uploadOptionFiles",
};

export default cloudFunctionNames;
