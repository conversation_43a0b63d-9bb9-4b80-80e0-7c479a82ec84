# RM Titan Blue/Green Deployment Setup

This directory contains the Docker and containerization configurations for RM Titan's Blue/Green deployment strategy.

## Overview

The Blue/Green deployment strategy allows for zero-downtime deployments by maintaining two identical production environments (Blue and Green). At any time, one environment serves live traffic while the other is idle or being updated.

## Files

- `nginx.conf` - Main Nginx configuration for serving the React application
- `nginx-lb.conf` - Load balancer configuration for Blue/Green traffic routing
- `nginx-lb-active.conf` - Generated configuration for active traffic routing

## Architecture

```
Internet → Load Balancer → Blue Environment (Active)
                      → Green Environment (Standby)
                      → Canary Environment (Testing)
```

## Deployment Workflow

### 1. Standard Deployment
```bash
# Deploy to development
pnpm run deploy:blue-green:dev

# Deploy to production
pnpm run deploy:blue-green:prod
```

### 2. Interactive Deployment
```bash
./deployBlueGreen.mjs dev
```

### 3. Canary Deployment
```bash
pnpm run deploy:canary
```

## Port Configuration

- **Load Balancer**: 80 (HTTP), 443 (HTTPS)
- **Blue Environment**: 8081
- **Green Environment**: 8082
- **Canary Environment**: 8083
- **Development**: 3000

## Health Checks

All services include health check endpoints at `/health` that return:
- Status: 200 OK
- Body: "healthy"

## Environment Variables

- `DEPLOYMENT_COLOR`: blue/green/canary
- `DEPLOYMENT_TIMESTAMP`: ISO timestamp of deployment
- `NODE_ENV`: production/development
- `BLUE_GREEN_ENABLED`: true/false

## Docker Commands

```bash
# Build all services
pnpm run docker:build

# Start all services
pnpm run docker:up

# Stop all services
pnpm run docker:down

# View logs
pnpm run docker:logs

# Check status
pnpm run docker:status
```

## Traffic Switching

Traffic switching is handled by updating the Nginx load balancer configuration:

1. Deploy new version to inactive environment
2. Perform health checks
3. Update load balancer configuration
4. Reload Nginx without dropping connections

## Rollback

In case of issues, rollback by switching traffic back to the previous environment:

```bash
./deployBlueGreen.mjs dev rollback
```

## Monitoring

- Container health is monitored via Docker healthchecks
- Application health is verified via `/health` endpoints
- Deployment tracking is integrated with existing RM Titan tracking system

## Integration with Existing Systems

- Uses existing `deploy-tracker.js` for deployment logging
- Integrates with Firebase functions deployment
- Follows RM Titan naming conventions and patterns
- Compatible with existing environment configuration scripts

## Security

- Containers run as non-root user (`rmuser`)
- Security headers configured in Nginx
- Rate limiting enabled for API endpoints
- SSL/TLS support for production environments

## Troubleshooting

### Container Won't Start
```bash
docker logs rendering-service-blue
docker logs rendering-service-green
```

### Health Check Failures
```bash
curl http://localhost:8081/health
curl http://localhost:8082/health
```

### Traffic Not Switching
```bash
docker-compose exec load-balancer nginx -t
docker-compose logs load-balancer
```

## Development

For local development, use the development target:

```bash
docker-compose up rm-titan-dev
```

This provides hot-reloading and development-specific configurations.
