events {
    worker_connections 1024;
    use epoll;
}

http {
    upstream blue {
        server rendering-service-blue:8080;
    }

    upstream green {
        server rendering-service-green:8080;
    }

    server {
        listen 80;
        listen [::]:80;

        location / {
            proxy_pass http://blue;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Deployment-Color blue;
        }
    }

    server {
        listen 443 ssl;
        server_name localhost;

        ssl_certificate /etc/nginx/ssl/server.crt;
        ssl_certificate_key /etc/nginx/ssl/server.key;

        location / {
            proxy_pass http://green;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Deployment-Color green;
        }
    }

    server {
        listen 8083;
        listen [::]:8083;

        location / {
            proxy_pass http://canary;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Deployment-Color canary;
        }
    }
}
