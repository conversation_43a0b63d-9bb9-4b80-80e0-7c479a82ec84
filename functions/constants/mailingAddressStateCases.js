exports.mailingAddressStateCases = {
  SET_INCOMPLETE_ADDRESS: "SET_INCOMPLETE_ADDRESS",
  CHECK_SOURCE_MAILING_ADDRESS: "CHECK_SOURCE_MAILING_ADDRESS",
  CHECK_SOURCE_MAILING_ADDRESS_ERROR: "CHECK_SOURCE_MAILING_ADDRESS_ERROR",
  SOURCE_CREATION_SUCCESS: "SOURCE_CREATION_SUCCESS",
  SOURCE_CREATION_ERROR: "SOURCE_CREATION_ERROR",
  VALIDATE_ADDRESS_COMPLETE: "VALIDATE_ADDRESS_COMPLETE",
  VALIDATE_ADDRESS_ERROR: "VALIDATE_ADDRESS_ERROR",
  CHECK_MAGAZINE_PRODUCT_ASSIGNMENT: "CHECK_MAGAZINE_PRODUCT_ASSIGNMENT",
  CHECK_MAGAZINE_PRODUCT_ASSIGNMENT_ERROR:
    "CHECK_MAGAZINE_PRODUCT_ASSIGNMENT_ERROR",
  PROCESS_EXCLUSIVITY_SUCCESS: "PROCESS_EXCLUSIVITY_SUCCESS",
  PROCESS_EXCLUSIVITY_ERROR: "PROCESS_EXCLUSIVITY_ERROR",
  DONE: "DONE",
  REPROCESS: "REPROCESS",
};
