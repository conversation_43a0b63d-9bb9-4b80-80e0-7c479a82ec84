exports.collectionNames = {
  account: "Account",
  ads: "Ads",
  users: "Users",
  contacts: "Recipients",
  controlPanel: "ControlPanel",
  enterprise: "Enterprise",
  emailAddresses: "EmailAddresses",
  logs: {
    mailingAddressValidationFailures: "MailingAddressValidationFailures",
    emailAddressValidationFailures: "EmailAddressValidationFailures",
    recipientGroupMigrationLogs: "RecipientGroupMigrationLogs",
    mailingAddressFailures: "MailingAddressFailures",
  },
  mailingAddresses: "MailingAddresses",
  products: "Products",
  releaseExclusivity: "ReleaseExclusivity",
  settings: "Settings",
  apiKeys: "ApiKeys",
  exclusivityActions: "ExclusivityActions"
};

exports.subCollectionNames = {
  account: {
    accountPlans: "AccountPlans",
    files: "Files",
    groups: "Groups",
    integrations: "Integrations",
  },
  contacts: {
    company: "Company",
    contactGroups: "ContactGroups",
    recipientSources: "RecipientSources",
    emailAddresses: "EmailAddresses",
    followUpStatus: "FollowUpStatus", // Fixed the casing of "FollowUpStatus"
    mailingAddresses: "MailingAddresses",
    notes: "Notes",
    phoneNumbers: "PhoneNumbers",
    realEstate: "RealEstate",
    recipients: "Recipients",
    significantDates: "SignificantDates",
    socialMediaAccounts: "SocialMediaAccounts",
    sources: "Sources",
    waitingList: "WaitingList",
    websites: "Websites",
    externalIds: "ExternalIds",
    userEnteredMailingAddress: "UserEnteredMailingAddress",
    recipientGroups: "RecipientGroups",
    customFields: "CustomFields",
  },
  controlPanel: {
    logs: "Logs",
  },
  mailingAddresses: {
    avmHistory: "AVMHistory",
    waitingList: "WaitingList",
    exclusiveHistory: "ExclusiveHistory",
  },
  enterprise: {
    approvals: "Approvals",
  },
  audit: {
    history: "History",
  },
};
