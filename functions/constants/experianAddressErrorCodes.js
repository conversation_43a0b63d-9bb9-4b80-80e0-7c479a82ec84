const experianAddressErrorCodes = {
  "00": {
    title: "Address is Default Highrise or Rural Route.",
    description:
      "This address matched to a default delivery record in a multi-unit building, or a rural/highway contract record with route number in the street name field. See Appendix B for information on highrise addresses.",
    correction: "0",
    error: "1",
  },
  "01": {
    title: "No match in 5-digit ZIP Code; match found in finance number.",
    description:
      "Input ZIP code was incorrect. Correction applied successfully using city/state information provided. See Appendix B for information on finance numbers.",
    correction: "1",
    error: "0",
  },
  "02": {
    title: "ZIP Code add-on not found; replaced with correct add-on.",
    description:
      "Input +4 code was incorrect. Correction applied successfully. See Appendix B for information on ZIP-Addon codes.",
    correction: "1",
    error: "0",
  },
  "03": {
    title: "The Zip Code is not correct.",
    description:
      "Zip Code Incorrect. This may be a building with a unique Zip Code, such as a government building.",
    correction: "0",
    error: "1",
  },
  "04": {
    title: "City name corrected.",
    description:
      "Input city name was incorrect. Correction applied successfully.",
    correction: "1",
    error: "0",
  },
  "05": {
    title: "PO Box, Rural Route or Highway Contract address standardized.",
    description:
      "Input address was a box, rural route or highway contract address in a non-standard form (e.g., P.O.Box, or POBOX). Standardization performed successfully.",
    correction: "1",
    error: "0",
  },
  "06": {
    title:
      "Street number not precise match in street range; e.g. alphanumeric 10A within numeric range 1-99.",
    description:
      "Input address contained extra characters in the street number. These characters were retained in the output.",
    correction: "0",
    error: "1",
  },
  "07": {
    title: "Address non-deliverable; no add-on assigned.",
    description: "Delivery Point Validation (DPV) check failed.",
    correction: "0",
    error: "1",
  },
  "08": {
    title:
      "Secondary number is not precise match in secondary range; e.g. alphanumeric 10A within numeric range 1-99.",
    description:
      "Input address contained extra characters in the secondary number. These characters were retained in the output.",
    correction: "0",
    error: "1",
  },
  "09": {
    title: "Address is Delivery Point Alternate.",
    description:
      "See Appendix B for information on Delivery Point Alternate records.",
    correction: "0",
    error: "1",
  },
  10: {
    title: "City is part of multiple counties.",
    description:
      "More than one county name is listed for the address city name. Preferred county name is returned.",
    correction: "0",
    error: "0",
  },
  11: {
    title:
      "No match; failed CASS multi-component rule; number of results is absolute value of return code.",
    description:
      "Several problems were found in the input address. Unable to match using CASS logic. Number of near matches returned is equal to the absolute value of the return code.",
    correction: "0",
    error: "1",
  },
  12: {
    title:
      "All highrise records returned; first result is the CASS-certified address; Zs follow last result.",
    description:
      "Multiple records containing secondary ranges (apartment low – high numbers) returned in the results parameter. End of results is indicated by a string of ten “Z”s (ZZZZZZZZZZ). This error code always appears with error code “00”. See Appendix B for information on highrise addresses.",
    correction: "0",
    error: "1",
  },
  13: {
    title: "We cannot send to a Military address.",
    description: "",
    correction: "0",
    error: "1",
  },
  14: {
    title: "Street address with appended apartment number.",
    description:
      "Input address contained secondary information that could not be resolved through the CASS process. Secondary number was retained in the output.",
    correction: "0",
    error: "1",
  },
  15: {
    title:
      "No match; near matches placed in results field; Zs follow last result.",
    description:
      "Multiple records containing possible match candidates returned in the results parameter. End of results is indicated by a string of ten “Z”s (ZZZZZZZZZZ). This error code always appears with error code “99”.",
    correction: "0",
    error: "1",
  },
  16: {
    title: "Preferred city name used.",
    description:
      "Input city name was changed to a preferred city name for this address. See Appendix B for information on preferred city names.",
    correction: "1",
    error: "0",
  },
  18: {
    title: "DPV False Positive.",
    description:
      "DPV process stopped, ZIP+4 codes will no longer be assigned until DPV has been reenabled. Each subsequent call will return error code '21'.",
    correction: "0",
    error: "0",
  },
  19: {
    title: "No match; address found in Early Warning System database.",
    description:
      "See Appendix B for information on the Early Warning System (EWS) database.",
    correction: "0",
    error: "1",
  },
  20: {
    title: "Street name modified.",
    description:
      "Input street name was incorrect. Correction applied successfully.",
    correction: "1",
    error: "1",
  },
  21: {
    title:
      "DPV processing already stopped; please contact IST to restart the module.",
    description: "See error code '18'.",
    correction: "0",
    error: "0",
  },
  22: {
    title:
      "LACS Link processing already stopped; please contact IST to restart the module.",
    description: "See error code '24'.",
    correction: "0",
    error: "0",
  },
  23: {
    title:
      "No match; no correlation between city and unique ZIP Code; 5-digit ZIP Code deleted.",
    description:
      "Input record contained a unique ZIP code, input city did not match the ZIP.",
    correction: "0",
    error: "0",
  },
  24: {
    title: "LACS Link False Positive.",
    description:
      "LACSlink process stopped, ZIP+4 codes will no longer be assigned until LACSlink has been reenabled.",
    correction: "0",
    error: "0",
  },
  30: {
    title: "Foreign address.",
    description: "",
    correction: "0",
    error: "0",
  },
  31: {
    title: "Geocoder files missing or corrupt.",
    description: "",
    correction: "0",
    error: "0",
  },
  40: {
    title: "Multiple matches; incorrect post-directional.",
    description:
      "Input address contained an incorrect post-directional abbreviation. Unable to correct automatically.",
    correction: "0",
    error: "0",
  },
  41: {
    title: "Multiple matches; incorrect pre-directional.",
    description:
      "Input address contained an incorrect pre-directional abbreviation. Unable to correct automatically.",
    correction: "0",
    error: "0",
  },
  42: {
    title: "Multiple matches; incorrect street suffix.",
    description:
      "Input address contained an incorrect street suffix. Unable to correct automatically.",
    correction: "0",
    error: "0",
  },
  43: {
    title: "ZIP Code is PO Box or Rural Route only.",
    description:
      "ZIP code contains no street records. Match is made to a PO Box, route or general delivery record.",
    correction: "0",
    error: "0",
  },
  44: {
    title: "Apparent extraneous information removed.",
    description:
      "Some input information was considered unnecessary and was removed.",
    correction: "0",
    error: "0",
  },
  45: {
    title: "Street suffix modified.",
    description:
      "Input address contained an incorrect street suffix. Correction applied successfully.",
    correction: "1",
    error: "0",
  },
  46: {
    title: "Street directional modified.",
    description:
      "Input address contained an incorrect pre- or post-directional abbreviation. Correction applied successfully.",
    correction: "1",
    error: "0",
  },
  47: {
    title: "Address requires apartment/suite number; none input.",
    description: "Missing secondary number.",
    correction: "0",
    error: "0",
  },
  48: {
    title:
      "Input address contained no pre-directional abbreviation. Unable to correct automatically.",
    description:
      "Input address contained no pre-directional abbreviation. Unable to correct automatically.",
    correction: "0",
    error: "0",
  },
  49: {
    title: "Multiple matches; would resolve with post-directional.",
    description:
      "Input address contained no post-directional abbreviation. Unable to correct automatically.",
    correction: "0",
    error: "0",
  },
  50: {
    title: "Multiple matches; would resolve with street suffix.",
    description:
      "Input address contained no street suffix. Unable to correct automatically.",
    correction: "0",
    error: "0",
  },
  51: {
    title: "Address does not require apartment/suite; none input.",
    description:
      "This address matched to a default delivery record in a multi-unit building, or a rural/highway contract record with route number in the street name field.",
    correction: "0",
    error: "0",
  },
  52: {
    title: "Address does not require apartment/suite; incorrect input.",
    description:
      "This address matched to a default delivery record in a multi-unit building, or a rural/highway contract record with route number in the street name field.",
    correction: "0",
    error: "0",
  },
  57: {
    title: "Address not standardized; not enough information provided.",
    description: "",
    correction: "0",
    error: "0",
  },
  58: {
    title: "Address not standardized; invalid ZIP Code.",
    description: "Input ZIP code was incorrect. Unable to standardize address.",
    correction: "0",
    error: "0",
  },
  59: {
    title: "Address not standardized; belongs to US territory.",
    description:
      "Unable to standardize address. State abbreviation indicates that the address belongs to one of the territories.",
    correction: "0",
    error: "0",
  },
  60: {
    title: "Expired verification database; DPV/LACS LINK processing disabled.",
    description: "Postal data files expired. Address validation halted.",
    correction: "0",
    error: "0",
  },
  61: {
    title: "House number not on street.",
    description: "Input street number was incorrect.",
    correction: "0",
    error: "0",
  },
  64: {
    title: "Data mismatch.",
    description: "Postal data files do not match CorrectAddress library.",
    correction: "0",
    error: "0",
  },
  65: {
    title: "Unable to open data file(s).",
    description: "Postal data files are missing or corrupt.",
    correction: "0",
    error: "0",
  },
  66: {
    title: "Out of memory.",
    description: "",
    correction: "0",
    error: "1",
  },
  67: {
    title: "Trial expired.",
    description: "",
    correction: "0",
    error: "0",
  },
  68: {
    title: "Invalid or missing license key.",
    description: "",
    correction: "0",
    error: "0",
  },
  80: {
    title: "RDI error: unable to open data files.",
    description:
      "Residential Delivery Indicator lookup files are missing or corrupt.",
    correction: "0",
    error: "0",
  },
  81: {
    title: "RDI error: out of memory.",
    description: "",
    correction: "0",
    error: "0",
  },
  99: {
    title: "Could not find anything closely matching that address.",
    description: "",
    correction: "0",
    error: "1",
  },
};

module.exports.experianAddressErrorCodes = experianAddressErrorCodes;
