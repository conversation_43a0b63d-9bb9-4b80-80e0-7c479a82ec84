exports.PRODUCTS = [
  {
    plan_id: "1",
    assignable: true,
    bgButtonColor: "bg-color2",
    category: "direct-mail",
    createdAt: "2019-03-29T05:29:42.000Z",
    description: "",
    icon: "icon ion-ios-book",
    isExclusive: true,
    method: "address",
    name: "American Lifestyle Magazine",
    planId: "1",
    type: "magazine",
    slug: "",
  },
  {
    plan_id: "2",
    assignable: true,
    bgButtonColor: "bg-color3",
    category: "email-marketing",
    createdAt: "2019-03-29T05:29:42.000Z",
    description: "",
    icon: "icon icomoon icon-product-digital",
    isExclusive: false,
    method: "email",
    name: "Digital Edition",
    planId: "2",
    type: "email",
    slug: "digital-edition",
  },
  {
    plan_id: "3",
    assignable: true,
    bgButtonColor: "bg-color2",
    category: "direct-mail",
    createdAt: "2019-03-29T05:29:42.000Z",
    description: "",
    icon: "icon ion-ios-book",
    isExclusive: true,
    method: "address",
    name: "Start Healthy Magazine",
    planId: "3",
    type: "magazine",
    slug: "",
  },
  {
    plan_id: "4",
    assignable: false,
    bgButtonColor: "",
    category: "direct-mail",
    createdAt: "2019-03-29T05:29:42.000Z",
    description: "",
    icon: "icon-product-postcards",
    isExclusive: false,
    method: "address",
    name: "Postcards",
    planId: "4",
    type: "postcard",
    slug: "postcards",
  },
  {
    plan_id: "5",
    assignable: true,
    bgButtonColor: "bg-color6",
    category: "email-marketing",
    createdAt: "2019-10-15T00:04:50.000Z",
    description: "",
    icon: "icon icomoon icon-product-local",
    isExclusive: false,
    method: "email",
    name: "Local Events",
    planId: "5",
    type: "email",
    slug: "local-events",
  },
  {
    plan_id: "6",
    assignable: true,
    bgButtonColor: "bg-color1",
    category: "social-media",
    createdAt: "2019-12-04T22:52:19.000Z",
    description: "",
    icon: "icon icomoon icon-product-social",
    isExclusive: false,
    method: "email",
    name: "Branded Posts",
    planId: "6",
    type: "email",
    slug: "branded-posts",
  },
  {
    plan_id: "7",
    assignable: true,
    bgButtonColor: "bg-color2",
    category: "direct-mail",
    createdAt: "2021-04-14T12:13:08.000Z",
    description: "",
    icon: "icon ion-ios-book",
    isExclusive: true,
    method: "address",
    name: "Good to Be Home Magazine",
    planId: "7",
    type: "magazine",
    slug: "",
  },
  {
    plan_id: "8",
    assignable: true,
    bgButtonColor: "",
    category: "direct-mail",
    createdAt: "2021-06-17T16:52:08.000Z",
    description: "",
    icon: "icon icomoon icon-product-postcards js-no-icon",
    isExclusive: false,
    method: "address",
    name: "Postcard Campaign",
    planId: "8",
    type: "postcard",
    slug: "",
  },
  {
    plan_id: "9",
    assignable: false,
    bgButtonColor: "",
    category: "lead-generation",
    createdAt: "2021-11-17T19:37:33.000Z",
    description: "",
    icon: "",
    isExclusive: true,
    method: "email",
    name: "Landing Pages",
    planId: "9",
    type: "web",
    slug: "landing-pages",
  },
  {
    plan_id: "10",
    assignable: false,
    bgButtonColor: "",
    category: "lead-generation",
    createdAt: "2022-09-23T14:50:04.000Z",
    description: "",
    icon: "bu",
    isExclusive: false,
    method: "",
    name: "Facebook Ads Services",
    planId: "10",
    type: "web",
    slug: "facebook-ad-services",
  },
  {
    plan_id: "11",
    assignable: true,
    bgButtonColor: "",
    category: "direct-mail",
    createdAt: "2022-03-07T14:18:42.000Z",
    description: "",
    icon: "icon ion-ios-book",
    isExclusive: true,
    method: "address",
    name: "Business in Action Magazine",
    planId: "11",
    type: "magazine",
    slug: "",
  },
  {
    plan_id: "12",
    assignable: true,
    bgButtonColor: "bg-color2",
    category: "direct-mail",
    createdAt: "2022-04-26T14:44:59.000Z",
    description: "",
    icon: "icon ion-ios-book",
    isExclusive: true,
    method: "address",
    name: "Branded Magazines",
    planId: "12",
    type: "magazine",
    slug: "branded-magazines",
  },
  {
    plan_id: "13",
    assignable: false,
    bgButtonColor: "",
    category: "",
    createdAt: "2022-08-31T09:55:17.000Z",
    description: "",
    icon: "",
    isExclusive: false,
    method: "",
    name: "Social Media Automation",
    planId: "13",
    type: "web",
    slug: "",
  },
  {
    plan_id: "14",
    assignable: true,
    bgButtonColor: "",
    category: "",
    createdAt: "2025-06-20T06:55:17.000Z",
    description: "",
    icon: "",
    isExclusive: false,
    method: "email",
    name: "Email Campaigns",
    planId: "14",
    type: "email",
    slug: "email-campaigns",
  },
];
