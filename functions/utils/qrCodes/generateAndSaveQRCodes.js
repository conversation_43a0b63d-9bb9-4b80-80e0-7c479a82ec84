const QRCode = require("qrcode");
const { getStorage } = require("firebase-admin/storage");
const fs = require("fs");
const PDFDocument = require("pdfkit");
const SVGtoPDF = require('svg-to-pdfkit');
const { SecretManagerServiceClient } = require('@google-cloud/secret-manager');

// Initialize Secret Manager client
const secretManagerClient = new SecretManagerServiceClient();

/**
 * Access a secret from GCP Secret Manager
 * @param {string} secretName - Name of the secret to access
 * @returns {Promise<string>} - The secret value
 */
async function getSecret(secretName) {
  try {
    // Build the resource name of the secret version
    const projectId = process.env.GCP_PROJECT_ID || process.env.GOOGLE_CLOUD_PROJECT;
    const name = `projects/${projectId}/secrets/${secretName}/versions/latest`;
    
    // Access the secret version
    const [version] = await secretManagerClient.accessSecretVersion({ name });
    
    // Extract the payload as a string
    const payload = version.payload.data.toString('utf8');
    return payload;
  } catch (error) {
    console.error(`Error accessing secret ${secretName}:`, error);
    return null;
  }
}

/**
 * Generates and saves QR codes in PNG, SVG, and PDF formats
 * 
 * @param {Object} options - Configuration options
 * @param {string} options.url - The URL to encode in the QR code
 * @param {string} options.accountOwner - Account ID/owner for folder organization
 * @param {string} options.shortId - Unique identifier for the QR code
 * @param {string} options.bucketName - Name of the storage bucket to save files in (optional, will use CDN_BUCKET secret if not provided)
 * @param {string} options.cdnDomain - Base CDN domain for accessing the saved files (optional, will use CDN_DOMAIN secret if not provided)
 * @param {Object} options.existingRecord - Optional existing record with qrCodes to add CDN URLs to
 * @returns {Object} Object containing storage paths, signed URLs, and CDN URLs
 */
async function generateAndSaveQRCodes({ url, accountOwner, shortId, bucketName, cdnDomain, existingRecord }) {
  // Get the bucket name from Secret Manager if not provided
  if (!bucketName) {
    try {
      const secretBucket = await getSecret('CDN_BUCKET');
      bucketName = secretBucket || "rm-titan-prod-cdn";
    } catch (error) {
      console.error('Error retrieving bucket name from Secret Manager:', error);
      bucketName = process.env.CDN_BUCKET
    }
  }
  
  // Get the CDN domain from Secret Manager if not provided
  if (!cdnDomain) {
    try {
      const secretDomain = await getSecret('CDN_DOMAIN');
      cdnDomain = secretDomain || process.env.CDN_DOMAIN || 'https://codes.remindermdia.com'; // Fallback
    } catch (error) {
      console.error('Error retrieving CDN domain from Secret Manager:', error);
      cdnDomain = process.env.CDN_DOMAIN || 'https://codes.remindermedia.com'; // Fallback
    }
  }
  try {
    // Check if this is an existing record that just needs CDN codes
    if (existingRecord && existingRecord.qrCodes && !existingRecord.cdnQrCodes) {
      console.log("Found existing record without CDN QR codes - generating them");
      
      // If we have an existing record with qrCodes but no cdnQrCodes, create them
      const storagePaths = existingRecord.storagePaths || {};
      
      // If there are no storage paths, we need to infer them from the signed URLs
      if (!storagePaths.png && existingRecord.qrCodes.png) {
        // Try to extract paths from existing signed URLs
        console.log("No storage paths found, inferring from signed URLs");
        
        const inferredPaths = {
          png: null,
          svg: null, 
          pdf: null
        };
        
        // Extract bucket name and path from signed URL if available
        if (existingRecord.qrCodes.png) {
          const match = existingRecord.qrCodes.png.match(/\/([^/]+)\/o\/([^?]+)/);
          if (match && match.length >= 3) {
            inferredPaths.png = decodeURIComponent(match[2]);
            // Update bucketName if it wasn't provided
            if (!bucketName) {
              bucketName = match[1];
            }
          }
        }
        
        if (existingRecord.qrCodes.svg) {
          const match = existingRecord.qrCodes.svg.match(/\/([^/]+)\/o\/([^?]+)/);
          if (match && match.length >= 3) {
            inferredPaths.svg = decodeURIComponent(match[2]);
          }
        }
        
        if (existingRecord.qrCodes.pdf) {
          const match = existingRecord.qrCodes.pdf.match(/\/([^/]+)\/o\/([^?]+)/);
          if (match && match.length >= 3) {
            inferredPaths.pdf = decodeURIComponent(match[2]);
          }
        }
        
        // Use inferred paths
        if (inferredPaths.png && inferredPaths.svg && inferredPaths.pdf) {
          console.log("Using inferred paths from signed URLs:", inferredPaths);
          const cdnUrls = {
            png: `${cdnDomain}/${inferredPaths.png}`,
            svg: `${cdnDomain}/${inferredPaths.svg}`,
            pdf: `${cdnDomain}/${inferredPaths.pdf}`,
          };
          
          return {
            storagePaths: inferredPaths,
            signedUrls: existingRecord.qrCodes,
            cdnUrls: cdnUrls
          };
        }
      } else if (storagePaths.png && storagePaths.svg && storagePaths.pdf) {
        console.log("Creating CDN URLs from existing storage paths");
        const cdnUrls = {
          png: `${cdnDomain}/${storagePaths.png}`,
          svg: `${cdnDomain}/${storagePaths.svg}`,
          pdf: `${cdnDomain}/${storagePaths.pdf}`,
        };
        
        return {
          storagePaths: storagePaths,
          signedUrls: existingRecord.qrCodes,
          cdnUrls: cdnUrls
        };
      }
      
      console.log("Could not create CDN URLs from existing record, generating new QR codes");
    }
  
    // Generate new QR codes if needed
    const qrCodeBufferPNG = await QRCode.toBuffer(url, {
      type: "png",
      errorCorrectionLevel: "H",
      width: 300,
      margin: 2,
      color: {
        dark: "#000000",
        light: "#ffffff"
      },
      scale: 10,
      rendererOpts: {
        quality: 0.8
      }
    });
    
    const qrCodeBufferSVG = await QRCode.toString(url, {
      type: "svg",
      errorCorrectionLevel: "H",
      width: 300,
      margin: 2,
      color: {
        dark: "#000000",
        light: "#ffffff"
      }
    });

    // Get storage reference with specific bucket
    const storage = getStorage();
    const bucket = storage.bucket(bucketName);
    
    const joinName = `${accountOwner}_${shortId}`;
    const saveName = encodeUrlSafe(joinName);
    
    // Define storage paths
    const paths = {
      png: `qr-codes/${accountOwner}/${saveName}.png`,
      svg: `qr-codes/${accountOwner}/${saveName}.svg`,
      pdf: `qr-codes/${accountOwner}/${saveName}.pdf`,
    };

    // Save PNG
    let file = bucket.file(paths.png);
    await file.save(qrCodeBufferPNG, { contentType: "image/png" });
    
    // Save SVG
    file = bucket.file(paths.svg);
    await file.save(Buffer.from(qrCodeBufferSVG), {
      contentType: "image/svg+xml",
    });
    
    // Generate a PDF with the QR code embedded
    const pdfDir = `/tmp`;
    const pdfFileName = `${shortId}.pdf`;
    const pdfPath = `${pdfDir}/${pdfFileName}`;

    console.log(`Attempting to create PDF at: ${pdfPath}`);

    // Verify the directory exists and is writable
    try {
      if (!fs.existsSync(pdfDir)) {
        fs.mkdirSync(pdfDir, { recursive: true });
        console.log(`Created directory: ${pdfDir}`);
      }
      
      // Test write access
      fs.accessSync(pdfDir, fs.constants.W_OK);
      console.log(`Directory ${pdfDir} is writable`);
    } catch (error) {
      console.error(`Error with directory ${pdfDir}:`, error);
      // Create an alternative path if /tmp isn't working
      const altPdfPath = `./${pdfFileName}`;
      console.log(`Attempting to use alternative path: ${altPdfPath}`);
    }

    try {
      // Create PDF document
      const pdfDoc = new PDFDocument({ size: [300, 300], margin: 0 });
      console.log("PDF document created");
      
      // Create write stream with error handling
      const pdfStream = fs.createWriteStream(pdfPath);
      console.log("Write stream created");
      
      // Add error handler for the stream
      pdfStream.on("error", (err) => {
        console.error(`Error with write stream:`, err);
      });
      
      // Pipe document to stream
      pdfDoc.pipe(pdfStream);
      console.log("PDF document piped to stream");
      
      // Add SVG to PDF
      console.log("SVG content length:", qrCodeBufferSVG.length);
      SVGtoPDF(pdfDoc, qrCodeBufferSVG, 0, 0, {
        width: 300,
        height: 300,
        preserveAspectRatio: "xMidYMid meet" // Preserve aspect ratio
      });
      console.log("SVG added to PDF");
      
      // End the document
      pdfDoc.end();
      console.log("PDF document ended");
      
      // Wait for the stream to finish
      await new Promise((resolve, reject) => {
        pdfStream.on("finish", () => {
          console.log("PDF write stream finished");
          resolve();
        });
        pdfStream.on("error", (err) => {
          console.error("PDF write stream error:", err);
          reject(err);
        });
      });
      
      // Verify the file was created
      if (fs.existsSync(pdfPath)) {
        console.log(`PDF file successfully created at ${pdfPath}`);
        const stats = fs.statSync(pdfPath);
        console.log(`PDF file size: ${stats.size} bytes`);
      } else {
        console.error(`PDF file was not created at ${pdfPath}`);
      }
    } catch (error) {
      console.error("Detailed error in PDF generation:", error);
      throw error;
    }
    
    // Upload the PDF to Cloud Storage
    file = bucket.file(paths.pdf);
    await file.save(fs.readFileSync(pdfPath), {
      contentType: "application/pdf",
    });

    // Generate signed URLs for backward compatibility
    const [pngSignedUrl] = await bucket.file(paths.png).getSignedUrl({
      action: "read",
      expires: "03-01-2500",
    });
    
    const [svgSignedUrl] = await bucket.file(paths.svg).getSignedUrl({
      action: "read",
      expires: "03-01-2500",
    });
    
    const [pdfSignedUrl] = await bucket.file(paths.pdf).getSignedUrl({
      action: "read",
      expires: "03-01-2500",
    });

    // Create CDN URLs (direct CDN access without signed URLs)
    const cdnUrls = {
      png: `${cdnDomain}/${paths.png}`,
      svg: `${cdnDomain}/${paths.svg}`,
      pdf: `${cdnDomain}/${paths.pdf}`,
    };

    return {
      storagePaths: paths,
      signedUrls: {
        png: pngSignedUrl,
        svg: svgSignedUrl,
        pdf: pdfSignedUrl,
      },
      cdnUrls: cdnUrls,
      tempPdfPath: pdfPath // Return this for local file cleanup if needed
    };
  } catch (error) {
    console.error("Error generating QR codes:", error);
    throw error;
  }
}

/**
 * Helper function to encode a string to be URL safe
 * @param {string} str - String to encode
 * @returns {string} URL-safe encoded string
 */
function encodeUrlSafe(str) {
  // Replace & with the specified replacement character
  const encodedStr = str.replace(/&/g, "");

  // Base64 encode the modified string
  const base64 = Buffer.from(encodeURIComponent(encodedStr)).toString('base64');

  // Replace characters that are not URL-safe
  return base64
    .replace(/\+/g, "") // Replace + with -
    .replace(/\//g, "") // Replace / with _
    .replace(/=+$/, ""); // Remove trailing = padding
}

/**
 * Add CDN URLs to an existing record that only has signed URLs
 * 
 * @param {Object} record - The existing record with qrCodes but no cdnQrCodes
 * @returns {Promise<Object>} - Updated record with CDN URLs added
 */
async function addCdnUrlsToExistingRecord(record) {
  if (!record || !record.qrCodes) {
    throw new Error("Invalid record: missing qrCodes");
  }
  
  // If record already has CDN URLs, return it as is
  if (record.cdnQrCodes && 
      record.cdnQrCodes.png && 
      record.cdnQrCodes.svg && 
      record.cdnQrCodes.pdf) {
    return record;
  }
  
  console.log("Adding CDN URLs to existing record");
  
  // Get the CDN domain from Secret Manager
  let cdnDomain;
  try {
    cdnDomain = await getSecret('CDN_DOMAIN');
    if (!cdnDomain) {
      cdnDomain = process.env.CDN_DOMAIN || 'https://cdn.codesources.net'; // Fallback
    }
  } catch (error) {
    console.error('Error retrieving CDN domain from Secret Manager:', error);
    cdnDomain = process.env.CDN_DOMAIN || 'https://cdn.codesources.net'; // Fallback
  }
  
  // Get the bucket name if available
  let bucketName = record.bucketName;
  if (!bucketName) {
    try {
      bucketName = await getSecret('CDN_BUCKET');
      if (!bucketName) {
        bucketName = process.env.CDN_BUCKET || 'rm-dev-cdn'; // Fallback
      }
    } catch (error) {
      console.error('Error retrieving bucket name from Secret Manager:', error);
      bucketName = process.env.CDN_BUCKET || 'rm-dev-cdn'; // Fallback
    }
  }
  
  const shortId = record.shortId;
  const accountOwner = record.accountId;
  const shortUrl = record.shortUrl;
  
  // Generate and add CDN URLs to the record
  const qrCodeData = await generateAndSaveQRCodes({
    url: shortUrl,
    accountOwner,
    shortId,
    bucketName,
    cdnDomain,
    existingRecord: record
  });
  
  // Update the record with CDN URLs and storage paths if not already present
  const updatedRecord = {
    ...record,
    cdnQrCodes: qrCodeData.cdnUrls
  };
  
  // Add storage paths if not already present
  if (!record.storagePaths && qrCodeData.storagePaths) {
    updatedRecord.storagePaths = qrCodeData.storagePaths;
  }
  
  // Add bucket name if not already present
  if (!record.bucketName && bucketName) {
    updatedRecord.bucketName = bucketName;
  }
  
  return updatedRecord;
}

module.exports = {
  generateAndSaveQRCodes,
  getSecret,  // Also export the getSecret function for convenience
  addCdnUrlsToExistingRecord  // Add the new function to exports
};