exports.cleanDataForFirestore = function cleanDataForFirestore(jsonString) {
  let jsonObject = JSON.parse(jsonString);

  function sanitizeKey(key) {
    // Replace accented characters with their basic counterparts
    let result = key.normalize("NFD").replace(/[\u0300-\u036f]/g, "");

    // Trim leading and trailing spaces
    result = result.trim();

    // Replace spaces with underscores and sanitize other characters
    result = result.replace(/[.#$/\[\] ]/g, "_");

    return result;
  }

  function iterateObject(obj) {
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        const sanitizedKey = sanitizeKey(key);

        // If the key was changed, update it
        if (sanitizedKey !== key) {
          obj[sanitizedKey] = obj[key];
          delete obj[key];
        }

        // If the value is an object, recursively iterate through it
        if (
          typeof obj[sanitizedKey] === "object" &&
          obj[sanitizedKey] !== null
        ) {
          iterateObject(obj[sanitizedKey]);
        }
      }
    }
  }

  iterateObject(jsonObject);

  return JSON.stringify(jsonObject, null, 2); // Use null, 2 for pretty-printing
};
