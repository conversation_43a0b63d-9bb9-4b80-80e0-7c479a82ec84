const dictionary = require("../constants/collectionNames");
const { FieldValue, getDoc } = require("firebase-admin/firestore");
const { isNil } = require("lodash");

const { collectionNames, subCollectionNames } = dictionary;

exports.enterpriseBreadcrumbs = async (db, id) => {
    const breadcrumbs = [];
    const restrictions = [];
    const mlRestrictions = [];
    const damIds = [];
    let parent = id;
    
    do {
        const parentRef = db.collection(collectionNames.enterprise).doc(parent);
        const parentDoc = await parentRef.get();
        if(parentDoc.exists) {
            const parentData = parentDoc.data();
            damIds.push(parentData?.damId);
            breadcrumbs.push(parentData);
            if(parentData?.restrictions && parentData?.restrictions.length > 0) {
                for(const restriction of parentData?.restrictions) {
                    restrictions.push(restriction);
                }
            }
            if(parentData?.mlRestrictions && parentData?.mlRestrictions.length > 0) {
                for(const restriction of parentData?.mlRestrictions) {
                    mlRestrictions.push(restriction);
                }
            }
            parent = parentData?.parent;
        } else {
            parent = null;
        }
    } while (!isNil(parent) && parent!== 0);

    return { breadcrumbs, restrictions, mlRestrictions, damIds };
};
