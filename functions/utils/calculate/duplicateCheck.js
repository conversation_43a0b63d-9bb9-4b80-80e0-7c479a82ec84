const admin = require('firebase-admin');

exports.startDuplicateCheck = async function startDuplicateCheck({
    accountId
}){
    const db = admin.firestore();
    const docId = `${accountId}-DuplicateCheck`;
    const deliverabilityRef = db.collection('metaQueue').doc(docId);
    
    // Check if a document already exists with pending status
    const doc = await deliverabilityRef.get();
    if (doc.exists && doc.data().status === 'pending') {
        console.log(`Skipping creation: Document ${docId} already exists with pending status`);
        return doc.id;
    }

    // Create a timestamp 30 minutes in the future
    const now = admin.firestore.Timestamp.now();
    const futureTimestamp = new admin.firestore.Timestamp(
        now.seconds + (60 * 60),
        now.nanoseconds
    );

    const expirationTimestamp = new admin.firestore.Timestamp(
        now.seconds + (60 * 60 * 24 * 2),
        now.nanoseconds
    );
    
    const data = {
        accountId,
        createdAt: futureTimestamp,
        status: 'pending',
        target: accountId,
        type: 'DuplicateCheck',
        expiration: expirationTimestamp
    }

    await deliverabilityRef.set(data);
    return docId;
}

exports.updateDuplicateCheck = async function updateDuplicateCheck({
    id
}){
    const db = admin.firestore();
    const deliverabilityRef = db.collection('metaQueue').doc(id);

    const data = {
        startedAt: admin.firestore.Timestamp.now(),
    }

    await deliverabilityRef.update(data);
}

exports.completeDuplicateCheck = async function completeDuplicateCheck({
    id,
}){
    const db = admin.firestore();
    const deliverabilityRef = db.collection('metaQueue').doc(id);

    const data = {
        status: 'complete',
        finishedAt: admin.firestore.Timestamp.now(),
    }

    await deliverabilityRef.update(data);
}

exports.failDuplicateCheck = async function failDuplicateCheck({
    id,
    message,
}){
    const db = admin.firestore();
    const deliverabilityRef = db.collection('metaQueue').doc(id);

    const data = {
        status: 'failed',
        message,
        failedAt: admin.firestore.Timestamp.now(),
    }

    await deliverabilityRef.update(data);
}