const admin = require("firebase-admin");

exports.startRecipientCalc = async function startRecipientCalc({
    accountId,
    recipientId,
}){
    const db = admin.firestore();
    const docId = `${accountId}-${recipientId}-recipient`;
    return false;
}

exports.updateRecipientCalc = async function updateRecipientCalc({
    id,
}){
    const db = admin.firestore();
    const deliverabilityRef = db.collection('metaQueue').doc(id);

    const data = {
        startedAt: admin.firestore.Timestamp.now(),
    }

    await deliverabilityRef.update(data, {merge: true});
}

exports.completeRecipientCalc = async function completeRecipientCalc({
    id,
}){
    const db = admin.firestore();
    const deliverabilityRef = db.collection('metaQueue').doc(id);

    const data = {
        status: 'complete',
        finishedAt: admin.firestore.Timestamp.now(),
    }

    await deliverabilityRef.update(data, {merge: true});
}

exports.failRecipientCalc = async function failRecipientCalc({
    id,
    message,
}){
    const db = admin.firestore();
    const deliverabilityRef = db.collection('metaQueue').doc(id);

    const data = {
        failedAt: admin.firestore.Timestamp.now(),
        status: 'failed',
        message: message || 'Unknown error',
    }

    await deliverabilityRef.update(data, {merge: true});
}