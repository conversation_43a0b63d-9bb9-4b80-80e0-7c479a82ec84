const jwt = require("jsonwebtoken");
const fs = require("fs");
const { defineString } = require("firebase-functions/params");

/**
 * Generates a signed JSON Web Token using a Google API Service Account
 * @param {string} saEmail Service account email
 * @param {string} audience Audience claim
 * @param {number} expiryLength Token expiry length in seconds
 * @param {string} privateKey Private key
 * @returns {string} Signed JWT
 */

exports.generateJwt = function generateJwt(
  saEmail,
  audience,
  expiryLength,
  privateKey
) {
  // Current time and expiration time calculation
  const issuedAt = Math.floor(Date.now() / 1000);
  const expiresAt = issuedAt + expiryLength;

  // JWT payload configuration
  const payload = {
    iat: issuedAt,
    exp: expiresAt,
    iss: saEmail,
    aud: audience,
    sub: saEmail,
    email: saEmail,
  };

  // Read the service account key file
  //   const keyFileContents = fs.readFileSync(saKeyfile, "utf8");
  //   const keyObj = JSON.parse(keyFileContents);
  //   const privateKey = keyObj.private_key;

  // Sign the JWT with RSA SHA-256 algorithm
  const signedJwt = jwt.sign(payload, privateKey, {
    algorithm: "RS256",
  });

  return signedJwt;
};
