const { Timestamp } = require("firebase-admin/firestore");
const chrono = require("chrono-node");
const { parse } = require("date-fns");
const { isValid } = require("date-fns");

/**
 * Parses a date string and converts it into a Firestore Timestamp.
 *
 * @param {string} dateString - The user-entered date string.
 * @returns {admin.firestore.Timestamp | undefined} - The Firestore Timestamp object or undefined if the date is in the future.
 * @throws Will throw an error if the date string cannot be parsed.
 */
exports.parseDateToFirestoreTimestamp = function parseDateToFirestoreTimestamp(
  dateString
) {
  const parsingOptions = {
    forwardDate: false,
  };

  // chrono-node first
  let parsedDate = chrono.parseDate(dateString, new Date(), parsingOptions);

  // If chrono fails, try to parse using date-fns with common formats
  if (!parsedDate) {
    const dateFormats = [
      "dd-MMM-yy",
      "dd/MM/yy",
      "MM/dd/yy",
      "yyyy-MM-dd",
      "dd MMM yy",
      "d MMM yy",
      "MMM d yy",
      "MMM d, yy",
      "d-MMM-yy",
    ];

    for (const format of dateFormats) {
      parsedDate = parse(dateString, format, new Date());
      if (isValid(parsedDate)) {
        break;
      } else {
        parsedDate = null;
      }
    }
  }

  // Check if parsing was successful
  if (!parsedDate) {
    return undefined;
  }

  // Check if the parsed date is in the future
  const now = new Date();
  if (parsedDate > now) {
    return undefined;
  }

  // Convert the Date object to a Firestore Timestamp
  const timestamp = Timestamp.fromDate(parsedDate);
  return timestamp;
};
