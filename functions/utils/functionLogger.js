const admin = require("firebase-admin");
const { Timestamp } = require("firebase-admin/firestore");
exports.logToFirestore = async ({ functionName, message, type, data = {} }) => {
  try {
    const functionLogRef = admin.firestore().collection("FunctionLogs");
    const newFunctionLog = await functionLogRef.add({
      functionName,
      message,
      type,
      data,
      timestamp: Timestamp.now(),
    });
  } catch (logError) {
    console.error("Error writing to log collection:", logError);
  }
};
