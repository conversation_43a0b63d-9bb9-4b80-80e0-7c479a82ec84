const { isEmpty } = require("lodash");
const { Timestamp } = require("firebase-admin/firestore");
const { convertFirestoreTimestampToDateTime } = require("./convertFirestoreTimestampToDateTime");

// Use for importing firestore timestamps from strings
// use the Account's timezone to determine de-localization necessities
exports.delocalizeDate = function (dateString, timezone) {
  if (isEmpty(dateString)) return null;

  const longOffsetFormatter = new Intl.DateTimeFormat("en-US", {timeZone: timezone, timeZoneName: "longOffset"});
  const longOffsetString = longOffsetFormatter.format(new Date(dateString)); // 'xx/xx/xxxx, GMT-05:00'
  const gmtOffset = longOffsetString.split('GMT')[1]; // will give us offset like '-05:00' or '-04:00' DST

  let ourDate = new Date(dateString);
  // if the GMT offset is negative, we need to add one day to the date 
  // for firestore UTC / React localization to display the date correctly
  if (ourDate && gmtOffset?.startsWith('-')) {
    ourDate.setDate(ourDate.getDate() + 1);
  }

  return ourDate?.toISOString() ?? null;
}

// Use for exporting firestore timestamps
// use the Account's timezone to determine localization necessities
exports.localizeDate = function (timestamp, timezone) {
  if (isEmpty(timestamp)) return null;

  // convert the timestamp to a date
  const ourDate = convertFirestoreTimestampToDateTime(timestamp);

  // localize the date to the user's timezone
  const longOffsetFormatter = new Intl.DateTimeFormat("en-US", {timeZone: timezone, timeZoneName: "longOffset"});
  const longOffsetString = longOffsetFormatter.format(ourDate); // 'xx/xx/xxxx, GMT-05:00'
  const gmtOffset = longOffsetString.split('GMT')[1]; // will give us offset like '-05:00' or '-04:00' DST

  // if the GMT offset is negative, we need to subtract one day to the date because 
  // of firestore UTC / React localization requirement for negative timezone offsets
  if (ourDate && gmtOffset?.startsWith('-')) {
    ourDate.setDate(ourDate.getDate() - 1);
  }

  return ourDate?.toISOString() ?? null;
}
