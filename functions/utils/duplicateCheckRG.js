const dictionary = require("../constants/collectionNames");
const { collectionNames, subCollectionNames } = dictionary;

module.exports.checkForDuplicatesAndMark = async function checkForDuplicatesAndMark(db, accountId, recipientGroupId) {
  try {
    const collectionRef = db
      .collection(collectionNames.account)
      .doc(accountId)
      .collection(subCollectionNames.contacts.recipients)
      .where('recipientGroupIds', 'array-contains', recipientGroupId);

    // Use pagination to handle large collections
    const pageSize = 500; // Max batch size
    let lastDocument = null;
    let hasMore = true;

    const emailValueMap = {};
    const mailingValueMap = {};
    const docDuplicatesMap = {}; // Map of docId to { docRef, duplicate: { email: true/false, mailingAddress: true/false } }

    while (hasMore) {
      let query = collectionRef.orderBy('__name__').limit(pageSize);

      if (lastDocument) {
        query = query.startAfter(lastDocument);
      }

      const snapshot = await query.get();

      if (snapshot.empty) {
        hasMore = false;
        break;
      }

      snapshot.forEach((doc) => {
        const data = doc.data();
        const docRef = doc.ref;
        const docId = doc.id;

        // Normalize email address
        const emailAddress = data?.searchTags?.primaryEmail ? data.searchTags.primaryEmail.toLowerCase().trim() : "";

        // Normalize mailing address
        const mailingAddress = data?.searchTags?.formattedMailingAddress ? data.searchTags.formattedMailingAddress.toLowerCase().trim() : "";

        // Initialize duplicate info for the document if not already initialized
        if (!docDuplicatesMap[docId]) {
          docDuplicatesMap[docId] = { docRef, duplicate: {} };
        }
        const docDuplicate = docDuplicatesMap[docId].duplicate;

        // Check for email duplicates
        if (emailAddress) {
          if (emailValueMap[emailAddress]) {
            // Duplicate email found

            // Mark current doc
            docDuplicate.email = true;

            // Mark original doc
            const originalDocRef = emailValueMap[emailAddress];
            const originalDocId = originalDocRef.id;
            if (!docDuplicatesMap[originalDocId]) {
              docDuplicatesMap[originalDocId] = { docRef: originalDocRef, duplicate: { email: true } };
            } else {
              docDuplicatesMap[originalDocId].duplicate.email = true;
            }
          } else {
            emailValueMap[emailAddress] = docRef;
          }
        }

        // Check for mailingAddress duplicates
        if (mailingAddress) {
          if (mailingValueMap[mailingAddress]) {
            // Duplicate mailing address found

            // Mark current doc
            docDuplicate.mailingAddress = true;

            // Mark original doc
            const originalDocRef = mailingValueMap[mailingAddress];
            const originalDocId = originalDocRef.id;
            if (!docDuplicatesMap[originalDocId]) {
              docDuplicatesMap[originalDocId] = { docRef: originalDocRef, duplicate: { mailingAddress: true } };
            } else {
              docDuplicatesMap[originalDocId].duplicate.mailingAddress = true;
            }
          } else {
            mailingValueMap[mailingAddress] = docRef;
          }
        }
      });

      lastDocument = snapshot.docs[snapshot.docs.length - 1];
      hasMore = snapshot.size === pageSize;
    }

    // Prepare batch updates
    const batchUpdates = [];
    let batch = db.batch();
    let operationCount = 0;

    // Update documents with duplicate information
    for (const docId in docDuplicatesMap) {
      const { docRef, duplicate } = docDuplicatesMap[docId];
      batch.update(docRef, { duplicate });
      operationCount++;

      if (operationCount >= 500) {
        // Commit the batch and reset
        batchUpdates.push(
          batch.commit().catch((err) => {
            console.error('Batch commit error:', err);
            throw err;
          })
        );
        batch = db.batch();
        operationCount = 0;
      }
    }

    // Commit any remaining operations
    if (operationCount > 0) {
      batchUpdates.push(
        batch.commit().catch((err) => {
          console.error('Batch commit error:', err);
          throw err;
        })
      );
    }

    // Wait for all batch commits to complete
    await Promise.all(batchUpdates).then(() => {
    }).catch((err) => {
      console.error('Error committing batches:', err);
      throw err;
    });
  } catch (error) {
    console.error('Error checking for duplicates and updating documents:', error);
    throw error;
  }
};
