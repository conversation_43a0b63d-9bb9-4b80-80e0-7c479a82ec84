const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { logger } = require("firebase-functions");
const { getFirestore } = require("firebase-admin/firestore");
const { format } = require("date-fns");

exports.getControlPanel = async function getControlPanel() {
  try {
    const controlPanel = {};
    const db = getFirestore();
    const controlPanelRef = db.collection(collectionNames.controlPanel);
    const controlPanelSnapshot = await controlPanelRef.get();

    if (controlPanelSnapshot.empty) return {};

    controlPanelSnapshot.docs.forEach((controlRecord) => {
      const control = controlRecord.data();
      const controlId = controlRecord.id;
      const { isActive, name } = control ?? {};
      controlPanel[controlId] = { isActive, name };
      // googleAddressValidation: {isActive, name}
      // mailgunValidation: {isActive, name}
    });
    return controlPanel;
  } catch (error) {
    logger.info("Problem in control panel.", error);
    return {};
  }
};

exports.logControlPanel = async function logControlPanel({
  controlId,
  isActive,
}) {
  try {
    const db = getFirestore();
    const now = new Date();
    const year = format(now, "yyyy");
    const month = format(now, "MM");
    const day = format(now, "dd");
    const yyyyMMdd = format(now, "yyyyMMdd");

    const controlPanelLogRef = db
      .collection(collectionNames.controlPanel)
      .doc(controlId)
      .collection(subCollectionNames.controlPanel.logs)
      .doc(yyyyMMdd);

    const controlPanelLogSnapshot = await controlPanelLogRef.get();

    if (controlPanelLogSnapshot.exists) {
      const controlPanelLogData = controlPanelLogSnapshot.data();
      let { count, day, executed, month, year } = controlPanelLogData ?? {};
      await controlPanelLogRef.update({
        count: ++count,
        executed: isActive ? ++executed : executed,
      });
    } else {
      await controlPanelLogRef.set({
        count: 1,
        executed: isActive ? 1 : 0,
        day: parseInt(day),
        month: parseInt(month),
        year: parseInt(year),
      });
    }
  } catch (error) {
    logger.error("Problem logging control panel: ", error);
  }
};
