const shortid = require("shortid");
const { FieldValue } = require("firebase-admin/firestore");
const { astridConnection } = require("../astrid/astridConnection");
const { SecretManagerServiceClient } = require("@google-cloud/secret-manager");
const { generateAndSaveQRCodes } = require("./qrCodes/generateAndSaveQRCodes"); // Import the new function

// Initialize Secret Manager client
const secretManagerClient = new SecretManagerServiceClient();

/**
 * Access a secret from GCP Secret Manager
 * @param {string} secretName - Name of the secret to access
 * @returns {Promise<string>} - The secret value
 */
async function getSecret(secretName) {
  try {
    // Build the resource name of the secret version
    const projectId =
      process.env.GCP_PROJECT_ID || process.env.GOOGLE_CLOUD_PROJECT;
    const name = `projects/${projectId}/secrets/${secretName}/versions/latest`;

    // Access the secret version
    const [version] = await secretManagerClient.accessSecretVersion({ name });

    // Extract the payload as a string
    const payload = version.payload.data.toString("utf8");
    return payload;
  } catch (error) {
    console.error(`Error accessing secret ${secretName}:`, error);
    return null;
  }
}

/**
 * Extract URL parameter value after 'p='
 * @param {string} text - URL or text to parse
 * @returns {string} - Value after 'p=' or original text if not found
 */
function extractAfterPEquals(text) {
  // Find the index of 'p='
  const index = text.indexOf("p=");

  // If 'p=' is found, return everything after it
  if (index !== -1) {
    return text.slice(index + 2);
  }

  // If 'p=' is not found, return the original text
  return text;
}

/**
 * Shorten a URL and generate a QR code for it in PNG, SVG, and PDF formats.
 */
async function shortenUrl(data, db) {
  const { longUrl, lpId, domain, accountId, productId, linkData = {} } = data;
  const {
    source = null,
    medium = null,
    campaign = null,
    content = null,
    page_uuid = null,
    recipient_uuid = null,
    mailing_id = null,
  } = linkData;

  let lpDataSet = {};
  let finalLongUrl = longUrl;
  const recipientId = recipient_uuid;

  if (lpId) {
    const astridRequest = {
      payload: JSON.stringify({ lpId }),
      url: "lp",
      method: "POST",
    };

    try {
      const lpData = await astridConnection(astridRequest);
      lpDataSet = {
        accountId: lpData?.data?.accountId,
        longUrl: lpData?.data?.longUrl,
        domain: lpData?.data?.domain,
        name: lpData?.data?.name,
      };
      if (
        !linkData ||
        !linkData?.page_uuid ||
        linkData?.page_uuid !== lpId ||
        lpData?.page_uuid == null
      ) {
        linkData.page_uuid = lpId;
      }
      // TODO: double check once we figure out why RMC is not sending in a lpId
      linkData.page_uuid = extractAfterPEquals(lpData?.data?.longUrl);
    } catch (error) {
      console.error("Error calling Astrid helper:", error);
    }
  }

  let accountOwner = lpDataSet.accountId || accountId;
  if (typeof accountOwner === "string") {
    accountOwner = accountOwner.trim();
  } else if (accountOwner !== undefined && accountOwner !== null) {
    accountOwner = accountOwner.toString();
  }

  // Get the domain from Secret Manager or use fallbacks
  let finalDomain;
  try {
    const secretDomain = await getSecret("SHORT_CODE_DOMAIN");
    finalDomain = secretDomain || lpDataSet.domain || domain;
  } catch (error) {
    console.error("Error retrieving domain from Secret Manager:", error);
    finalDomain = process.env.SHORT_CODE_DOMAIN || lpDataSet.domain || domain;
  }

  // Get the CDN domain from Secret Manager
  let cdnDomain;
  try {
    cdnDomain = await getSecret("CDN_DOMAIN");
    if (!cdnDomain) {
      cdnDomain = process.env.CDN_DOMAIN || "https://codes.remindermedia.com"; // Fallback
    }
  } catch (error) {
    console.error("Error retrieving CDN domain from Secret Manager:", error);
    cdnDomain = process.env.CDN_DOMAIN || "https://cdn.yourdomain.com"; // Fallback
  }

  const baseUrl = longUrl || lpDataSet?.longUrl;

  if (!baseUrl) {
    return {
      exists: false,
      data: { message: "No Landing Page or Base URL found" },
    };
  }

  const separator = baseUrl.includes("?") ? "&" : "?";
  const encode = (value) => encodeURIComponent(value);

  finalLongUrl = `${baseUrl}${source ? `${separator}utm_source=${encode(source)}` : ""}${
    medium ? `&utm_medium=${encode(medium)}` : ""
  }${campaign ? `&utm_campaign=${encode(campaign)}` : ""}${content ? `&utm_content=${encode(content)}` : ""}${
    page_uuid ? `&page_uuid=${encode(lpId || page_uuid)}` : ""
  }${recipient_uuid ? `&recipient_uuid=${encode(recipient_uuid)}` : ""}${mailing_id ? `&mailing_id=${encode(mailing_id)}` : ""}`;

  // TODO: double check once we figure out why RMC is not sending in a lpId
  linkData.page_uuid = extractAfterPEquals(finalLongUrl);

  const query = db
    .collection("codes")
    .where("longUrl", "==", finalLongUrl)
    .where("linkData", "==", linkData || null)
    .where("productId", "==", productId);

  const existingRecordSnapshot = await query.limit(1).get();

  if (!existingRecordSnapshot.empty) {
    const existingRecord = existingRecordSnapshot.docs[0].data();
    return { exists: true, ...existingRecord };
  }

  shortid.characters(
    "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_&"
  );
  let shortId;
  do {
    shortId = shortid.generate();
  } while ((await db.collection("codes").doc(shortId).get()).exists);

  const shortUrl = `${finalDomain}/${shortId}`;
  const bucketName = "rm-titan-prod-cdn"; // Use the specific bucket name

  // Generate and save QR codes using our new function
  const qrCodeData = await generateAndSaveQRCodes({
    url: shortUrl,
    accountOwner,
    shortId,
    bucketName,
    cdnDomain,
  });

  const newRecord = {
    shortId,
    longUrl: finalLongUrl,
    shortUrl,
    qrCodes: qrCodeData.signedUrls, // Signed URLs for backward compatibility
    cdnQrCodes: qrCodeData.cdnQrCodes || [], // Add new CDN URLs field
    storagePaths: qrCodeData.storagePaths || [], // Store raw paths for future reference
    bucketName, // Store bucket name for reference
    qrCodeGenerated: FieldValue.serverTimestamp(),
    productId,
    accountId: accountOwner,
    domain: finalDomain,
    clicks: 0,
    createdAt: FieldValue.serverTimestamp(),
    linkData: linkData,
  };

  if (recipientId) newRecord.recipientId = recipientId;

  await db.collection("codes").doc(shortId).set(newRecord);

  return { exists: false, ...newRecord };
}

exports.shortenUrl = shortenUrl;
