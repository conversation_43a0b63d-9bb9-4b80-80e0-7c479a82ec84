const { logger } = require("firebase-functions");
const { record } = require("zod");
module.exports.collectValidRecipientsByGroup =
  async function collectValidRecipientsByGroup({
    db,
    groups,
    accountId,
    method = "address",
    productId,
  }) {
    try {
      const MAX_ARRAY_CONTAINS_ANY = 30;
      const recipientsCollection = db.collection(
        `Account/${accountId.toString()}/Recipients`
      );

      // break up the groups in case they're over 30 in length
      const groupChunks = [];
      for (let i = 0; i < groups.length; i += MAX_ARRAY_CONTAINS_ANY) {
        groupChunks.push(groups.slice(i, i + MAX_ARRAY_CONTAINS_ANY));
      }

      const docMap = new Map();
      await Promise.all(
        groupChunks.map(async (chunk) => {
          try {
            const snap = await recipientsCollection
              .where("isActive", "==", true)
              .where("recipientGroupIds", "array-contains-any", chunk)
              .get();
            snap.docs.forEach((doc) => docMap.set(doc.id, doc));
          } catch (err) {
            console.error(
              `Error querying recipientGroupIds chunk [${chunk.join(",")}]:`,
              err
            );
          }
        })
      );

      const total = docMap.size; // total distinct recipients matched
      let validRecipients = [];
      let count = 0;

      //  exit if none found
      if (total === 0) {
        return validRecipients;
      }

      await Promise.all(
        Array.from(docMap.values()).map(async (doc) => {
          const recipientData = doc.data();
          if (await isValid(recipientData, method, productId, db)) {
            count++;
            let record = await formatRecord(doc.id, recipientData);

            if (method === "email") {
              record = await formatRecordForEmail(recipientData, record, db);
              if (record?.email) {
                validRecipients.push(record);
              }
            } else if (method === "address") {
              record = await formatRecordForAddress(
                doc.id,
                recipientData,
                record,
                db
              );
              if (
                record?.address1 &&
                record?.city &&
                record?.state &&
                record?.zip &&
                (record?.first_name || record?.last_name)
              ) {
                validRecipients.push(record);
              }
            }
          }
        })
      );

      logger.info(`TOTAL RECIPIENTS: ${total} | VALID RECIPIENTS: ${count}`);

      // Dedupe by email and return for email method
      if (method === "email") {
        const uniqueEmails = [];
        const seen = new Set();
        for (const rec of validRecipients) {
          if (!seen.has(rec.email)) {
            seen.add(rec.email);
            uniqueEmails.push(rec);
          }
        }
        return { uniqueEmails, total, count }; 
      }

      //  return address results
      return validRecipients;
    } catch (error) {
      console.error("Error in collectValidRecipientsByGroup", error);
    }
  };

// Change other helper functions to remove 'async' from non-exported functions
async function isValid(data, method, productId, db) {
  if (data?.mailingsPaused && data?.mailingsPaused === true) {
    logger.info("mailingsPaused", data?.mailingsPaused, "return false", data);
    return false;
  }

  if (
    data?.overrideDeliverability &&
    data?.overrideDeliverability?.[method] === true
  ) {
    logger.info(
      "overrideDeliverability",
      data?.overrideDeliverability?.[method],
      "return true",
      data
    );
    return true;
  }

  if (
    productId === "8" &&
    (data?.addressDeliverability?.isValidMailingAddress === true ||
      data?.addressDeliverability?.code === "GA" ||
      data?.addressDeliverability?.code === "WS" ||
      data?.addressDeliverability?.code === "WL")
  ) {
    return true;
  }

  if (method === "address") {
    if (
      data?.addressDeliverability &&
      (data?.addressDeliverability?.code === "WS" ||
        data?.addressDeliverability?.emailStatus === "deliverable")
    ) {
      return true;
    } else {
      logger.info("addressDeliverability", data, "return false", data);
    }
  }

  if (method === "email") {
    if (
      data?.emailDeliverability &&
      (data?.emailDeliverability?.code === "WE" ||
        data?.emailDeliverability?.emailStatus === "deliverable")
    ) {
      return true;
    }
  }

  return false;
}

async function formatRecord(id, data) {
  const recordForReturn = {
    id: id,
    first_name: data?.name?.firstName || "",
    last_name: data?.name?.lastName || "",
    recipient_group_id: data?.recipientGroupIds[0],
    mailings_paused: data?.mailingsPaused || false,
    mailing_salutation: data?.salutation?.mailingSalutation || "Current Resident",
    letter_salutation: data?.salutation?.letterSalutation || "Current Resident",
  };
  if(recordForReturn?.first_name === "" && recordForReturn?.last_name === "") {
    recordForReturn.first_name = "Current";
    recordForReturn.last_name = "Resident";
  }
  return recordForReturn;
}

async function formatRecordForEmail(data, returnData) {
  returnData = {
    ...returnData,
    email: data?.searchTags?.primaryEmail,
  };

  return returnData;
}

// async function isWaitingList(data, db) {
//   const accountId = data?.accountId.toString();
//   const addressId = data?.mailingAddresses?.[0]?.id;
//   if(addressId === undefined) {
//     return false;
//   }
//   const mailingAddressRef = db
//     .collection(`MailingAddresses`)
//     .doc(addressId);
//   const mailingAddressLookup = await mailingAddressRef.get();
//   if(mailingAddressLookup.empty) {
//     return false;
//   }
//   const waitingListLookupRef = mailingAddressRef.collection("WaitingList");
//   const waitingListLookup = await mailingAddressRef.get();
//   if(waitingListLookup.empty) {
//     return false;
//   }
//   const waitingListData = waitingListLookup.data();
//   const exclusive = waitingListData?.exclusive?.[0] || false;
//   if(!exclusive || exclusive === "000") {
//     let exclusiveData = [];
//     exclusiveData.push(accountId);
//     await mailingAddressRef.update({
//       exclusive: exclusiveData,
//     });
//     await waitingListLookupRef.doc(accountId).delete();
//     return false;
//   } else if(exclusive === accountId) {
//     return false;
//   } else {
//     return true;
//   }

// }

async function formatRecordForAddress(id, data, returnData, db) {
  returnData = {
    ...returnData,
    address1: "",
    address2: "",
    city: "",
    state: "",
    zip: "",
  };

  if (data?.mailingAddresses?.[0]?.id) {
    const addressRef = db
      .collection("MailingAddresses")
      .doc(data?.mailingAddresses?.[0]?.id);
    const addressLookup = await addressRef.get();
    const addressData = addressLookup.data();
    if (!addressLookup.exists) {
      return returnData;
    }

    returnData.address1 = addressData.address1
      ? addressData?.address1.trim()
      : "";
    returnData.address2 = addressData?.address2
      ? addressData?.address2.trim()
      : "";
    returnData.city = addressData?.city ? addressData?.city.trim() : "";
    returnData.state = addressData?.state ? addressData?.state.trim() : "";
    returnData.zip = addressData?.postalCode
      ? addressData?.postalCode.trim()
      : "";
  }
  if (data?.overrideDeliverability?.address === true) {
    const addressRef = db
      .collection("Account")
      .doc(data?.accountId.toString())
      .collection("Recipients")
      .doc(id)
      .collection("UserEnteredMailingAddress")
      .doc("0");
    const addressLookup = await addressRef.get();
    if (!addressLookup.exists) {
      return returnData;
    }
    const addressData = addressLookup.data();
    returnData.address1 = addressData.address1
      ? addressData?.address1.trim()
      : "";
    returnData.address2 = addressData?.address2
      ? addressData?.address2.trim()
      : "";
    returnData.city = addressData?.city ? addressData?.city.trim() : "";
    returnData.state = addressData?.state ? addressData?.state.trim() : "";
    returnData.zip = addressData?.postalCode
      ? addressData?.postalCode.trim()
      : "";
  }
  return returnData;
}
