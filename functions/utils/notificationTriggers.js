const admin = require("firebase-admin");
const { log } = require("firebase-functions/logger");
exports.createNotification = async (data) => {
  try {
    const accountDocRef = admin
      .firestore()
      .collection("Account")
      .doc(data.accountId);
    const notificationRef = accountDocRef.collection("Notifications");
    await notificationRef.add({
      ...data,
      createdTime: admin.firestore.FieldValue.serverTimestamp(),
    });
  } catch (error) {
    log("Error adding notification to Firestore:", error);
    log("data for notification:", data);
  }
};
