const { PubSub } = require("@google-cloud/pubsub");
const pubSubClient = new PubSub();
const { logger } = require("firebase-functions/v2");
const { defineString } = require("firebase-functions/params");

const projectIdEnv = defineString("TITAN_PROJECT_ID");

exports.addToTopic = async (request) => {
  const { projectId = projectIdEnv.value(), topic, message } = request ?? {};

  if (!projectId || !topic || !message) {
    return { error: "Missing projectId, topic, or message." };
  }

  try {
    const fullTopicPath = `projects/${projectId}/topics/${topic}`;
    const messageString =
      typeof message === "string" ? message : JSON.stringify(message);
    const dataBuffer = Buffer.from(messageString);
    const messageId = await pubSubClient
      .topic(fullTopicPath)
      .publishMessage({ data: dataBuffer });
    return { status: 200, message: `Message published with ID: ${messageId}` };
  } catch (error) {
    logger.error("Error publishing message:", error.message, error);
    return { status: 400, error: `Error publishing message: ${error.message}` };
  }
};
