const { PubSub } = require("@google-cloud/pubsub");
const pubSubClient = new PubSub({
  batching: {
    maxMessages: 1, // send each message immediately
    maxMilliseconds: 50, // if more messages arrive, send whatever you have after 50 ms max.
  },
});
const { logger } = require("firebase-functions/v2");
const { defineString } = require("firebase-functions/params");

const projectIdEnv = defineString("TITAN_PROJECT_ID");

/**
 * Duplicates the work of addToTopic, however this version is configured to fix timeouts
 * observed in the handleMAilingProductAddedToGroup function. Ultimately we'll update the originl
 * addTopic when we can regression test the functions currently using the original addToTopic.js
 */

exports.addToTopicConfigured = async (request) => {
  const { projectId = projectIdEnv.value(), topic, message } = request ?? {};

  if (!projectId || !topic || !message) {
    return { error: "Missing projectId, topic, or message." };
  }

  try {
    const fullTopicPath = `projects/${projectId}/topics/${topic}`;
    const messageString =
      typeof message === "string" ? message : JSON.stringify(message);
    const dataBuffer = Buffer.from(messageString);
    const messageId = await pubSubClient
      .topic(fullTopicPath)
      .publishMessage({ data: dataBuffer });
    return { status: 200, message: `Message published with ID: ${messageId}` };
  } catch (error) {
    logger.error("Error publishing message:", error.message, error);
    throw new Error(`Pub/Sub publish failed: ${err.message}`);
  }
};
