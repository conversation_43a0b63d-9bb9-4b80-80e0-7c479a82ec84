const admin = require("firebase-admin");
const functions = require("firebase-functions");

exports.dateStringToTimestamp = (dateString) => {
  // Check that dateString is a non-empty string
  if (typeof dateString !== "string" || !dateString.trim()) {
    return null;
  }

  // Convert the string to a Date object
  const date = new Date(dateString);

  // Validate that the Date object is valid
  if (isNaN(date.getTime())) {
    return null;
  }

  // Try converting to a Firestore Timestamp
  try {
    return admin.firestore.Timestamp.fromDate(date);
  } catch (error) {
    functions.logger.error("dateStringToTimestamp error: ", error);
    return null;
  }
};

exports.expirationToDate = (expiration) => {
  try {
    // Get the current timestamp in milliseconds
    const currentTimestamp = Date.now();

    // Calculate the expiration timestamp by adding the expiration time (in milliseconds)
    const expirationTimestamp = currentTimestamp + expiration * 1000;

    // Convert the expiration timestamp to a Date object
    const expirationDate = new Date(expirationTimestamp);

    // Return a Firestore Timestamp from the expiration Date
    return admin.firestore.Timestamp.fromDate(expirationDate);
  } catch (error) {
    functions.logger.info("expirationToDate error: ", error);
    return null; // Ensure the function returns null in case of an error
  }
};
