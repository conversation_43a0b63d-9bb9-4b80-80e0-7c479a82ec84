const { get, request } = require("axios");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { Timestamp } = require("firebase-admin/firestore");
const { add } = require("lodash");

async function propertyServiceByAddress(db, addressId) {
  let propertyInfo = null;
  let propertyDetails = null;

  const addressRef = db
    .collection(collectionNames.mailingAddresses)
    .doc(addressId);
  const addressLookup = await addressRef.get();
  const addressData = addressLookup.data();

  try {
    if (addressData && addressData?.address1) {
      const street = addressData?.address1 || "";
      const street2 = addressData?.address2 || "";
      const city = addressData?.city || "";
      const state = addressData?.state || "";
      const zip = addressData?.postalCode || addressData?.postalCode || "";

      // Fetch property info from SmartZip and Zillow APIs
      propertyInfo = await getpropertyInfo(street, street2, city, state, zip);
      // propertyDetails = await getZillow(street, street2, city, state, zip);
    } else {
      console.warn("Warning: No address data found for ID: ", addressId);
    }
  } catch (error) {
    console.warn(
      "Warning: Error fetching property or zestimate data: ",
      error.message
    );
  }
  const data = await buildResults({ addressData, propertyInfo, addressRef });
  // Update the Firestore document with the fetched information
  try {
    await addressRef.update(data);
  } catch (error) {
    console.error("Error updating lead:", error.message);
  }

  return {
    status: 200,
    message: `Address Data extended for ID: ${addressId}`,
    avm_score: data.avm_score,
  };
}

async function buildResults({
  address,
  propertyInfo,
  propertyDetails,
  addressRef,
}) {
  let result = {};
  const avmHistoryRef = addressRef.collection(
    subCollectionNames.mailingAddresses.avmHistory
  );
  const images = [];

  // AVM History Handling
  if (propertyInfo && address?.avm) {
    const avmHistoryRecord = {
      avm: address.avm,
      timestamp: Timestamp.now(),
      avm_date: address.avm_date || null,
      avm_score: address.avm_score || null,
      preMover: address.preMover || null,
    };
    await avmHistoryRef.add(avmHistoryRecord);
  }

  // Property Info Handling
  if (propertyInfo) {
    result.attributes = propertyInfo?.property?.property_attributes
      ? propertyInfo?.property?.property_attributes
      : null;
    result.avm = propertyInfo?.analytics?.avm || null;
    result.avm_date = propertyInfo?.analytics?.avm_date || null;
    result.avm_score = propertyInfo?.analytics?.avm_score || null;
    result.preMover = propertyInfo?.analytics?.premover_score || null;
    result.residency_length = propertyInfo?.analytics?.residency_length || null;
    result.smarty_id = propertyInfo?.id || null;
    result.lastList = propertyInfo?.property?.last_listing
      ? propertyInfo?.property?.last_listing
      : null;
    result.lastTransaction = propertyInfo?.property?.last_transaction
      ? propertyInfo?.property?.last_transaction
      : null;
    result.financials = propertyInfo?.analytics?.financial_indicators
      ? propertyInfo?.analytics?.financial_indicators
      : null;

    if (propertyInfo?.streetview_picture_url) {
      images.push({
        url: propertyInfo?.streetview_picture_url,
        type: "streetview",
        width: 800,
        caption: "Streetview",
      });
    }
  }

  // Property Details Handling
  if (propertyDetails) {
    result.comingSoon = !!propertyDetails?.comingSoonOnMarketDate;
    result.county = propertyDetails?.county || null;
    result.homeStatus = propertyDetails?.homeStatus || null;
    result.homeType = propertyDetails?.homeType || null;
    result.lastSoldPrice = propertyDetails?.lastSoldPrice || null;
    result.zillowLink = propertyDetails?.link || null;

    if (propertyDetails?.originalPhotos?.length > 0) {
      for (const photo of propertyDetails.originalPhotos) {
        const jpegPhoto = photo?.mixedSources?.jpeg?.[0];
        if (jpegPhoto?.url) {
          images.push({
            url: jpegPhoto.url,
            width: jpegPhoto.width || null,
            caption: photo?.caption || "",
            type: "photo",
          });
        }
      }
    }

    result.photoCount = propertyDetails?.photoCount || null;
    result.priceHistory = propertyDetails?.priceHistory || null;
    result.yearBuilt = propertyDetails?.yearBuilt || null;
    result.zestimate = propertyDetails?.zestimate || null;
    result.zpid = propertyDetails?.zpid || null;
  }

  result.images = images;
  result.runTime = Timestamp.now();

  return result;
}

async function processfield(field) {
  if (field) {
    const newValue = [];
    for (const [key, value] of Object.entries(field)) {
      if (value) {
        newValue.push({ [key]: value });
      }
    }
    return newValue;
  } else {
    return null;
  }
}

// Fetch property information from SmartZip API
const getpropertyInfo = async (street, street2, city, state, zip) => {
  const url = process.env.SMARTZIP_URL;
  const key = process.env.SMARTZIP_KEY;

  try {
    const response = await get(`${url}/properties/details.json`, {
      params: {
        api_key: key,
        street_address: street,
        unit_number: street2,
        city: city,
        state: state,
        zip: zip.split("-")[0],
      },
    });
    return response.data; // Return the response data directly
  } catch (error) {
    console.error(
      "Error fetching property info from SmartZip: ",
      error.message
    );
    return { result: "error", error: error.message };
  }
};

// Fetch Zestimate data from Zillow API
const getZestimate = async (street, street2, city, state, zip) => {
  const apiKey = process.env.RAPIDAPI_KEY;
  const address = `${street} ${street2 || ""}, ${city}, ${state} ${zip}`;

  const options = {
    method: "GET",
    url: "https://zillow-zestimate.p.rapidapi.com/zestimate",
    params: {
      address: address,
      includeRentZestimate: "true",
      includeZpid: "true",
    },
    headers: {
      "x-rapidapi-key": apiKey,
      "x-rapidapi-host": "zillow-zestimate.p.rapidapi.com",
    },
  };

  try {
    const response = await request(options);
    return response.data;
  } catch (error) {
    console.error("Error fetching Zestimate data: ", error.message);
    return { result: "error", error: error.message };
  }
};

// Fetch detailed property info from Zillow API
const getZillow = async (street, street2, city, state, zip) => {
  const apiKey = process.env.RAPIDAPI_KEY;
  const address = `${street} ${street2 || ""}, ${city}, ${state} ${zip}`;

  const options = {
    method: "GET",
    url: "https://zillow-working-api.p.rapidapi.com/pro/byaddress",
    params: {
      propertyaddress: address,
    },
    headers: {
      "x-rapidapi-key": apiKey,
      "x-rapidapi-host": "zillow-working-api.p.rapidapi.com",
    },
  };

  try {
    const response = await request(options);
    const propertyDetails = response.data.propertyDetails;
    propertyDetails.link = response.data.zillowURL || "";
    return propertyDetails;
  } catch (error) {
    console.error("Error fetching Zillow property details: ", error.message);
    return { result: "error", error: error.message };
  }
};

module.exports = { propertyServiceByAddress };
