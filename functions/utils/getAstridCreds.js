const { defineSecret, defineString } = require("firebase-functions/params");
const https = require("https");

// Retrieve the token and base URL from Secrets Manager
const token = defineSecret("ASTRID_KEY");
const baseUrl = defineString("ASTRID_URL");
const email = defineSecret("ASTRID_EMAIL");
const password = defineSecret("ASTRID_PASSWORD");

// In-memory cache for token
let cachedToken = null;
let tokenExpiry = null;

exports.getAstridCreds = async function () {
    console.log('[getAstridCreds] Starting credential retrieval process');
    const astridUrl = baseUrl.value();
    console.log(`[getAstridCreds] Base URL: ${astridUrl}`);
    
    // Check if we have a cached token that's still valid
    if (cachedToken && tokenExpiry && new Date() < tokenExpiry) {
        const remainingHours = Math.floor((tokenExpiry - new Date()) / 3600000);
        const remainingMinutes = Math.floor(((tokenExpiry - new Date()) % 3600000) / 60000);
        console.log(`[getAstridCreds] Using cached token (expires in ${remainingHours}h ${remainingMinutes}m)`);
        return {
            token: cachedToken,
            baseUrl: astridUrl,
        };
    }

    // Get the stored token
    console.log('[getAstridCreds] No valid cached token, retrieving stored token');
    let currentToken = processValue(token.value());
    console.log(`[getAstridCreds] Stored token retrieved: ${currentToken ? 'Token found' : 'No token found'}`);
    
    // Validate the token
    console.log('[getAstridCreds] Validating stored token...');
    const isValid = await validateToken(currentToken, astridUrl);
    
    if (isValid) {
        // Token is valid, cache it
        console.log('[getAstridCreds] Stored token is valid, caching for 24 hours');
        cachedToken = currentToken;
        // Set expiry to 24 hours from now
        tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000);
        console.log(`[getAstridCreds] Token cached until: ${tokenExpiry.toISOString()}`);
        
        return {
            token: currentToken,
            baseUrl: astridUrl,
        };
    }
    
    // Token is invalid, get a new one
    console.log('[getAstridCreds] Token is invalid or expired, attempting to login for new token');
    const newToken = await loginAndGetToken(astridUrl);
    
    if (newToken) {
        // Cache the new token
        console.log('[getAstridCreds] Successfully obtained new token, caching for 24 hours');
        cachedToken = newToken;
        tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000);
        console.log(`[getAstridCreds] New token cached until: ${tokenExpiry.toISOString()}`);
        
        return {
            token: newToken,
            baseUrl: astridUrl,
        };
    }
    
    console.error('[getAstridCreds] Failed to obtain valid credentials after all attempts');
    throw new Error('Failed to obtain valid Astrid credentials');
}

/**
 * Makes an HTTPS request using Node's built-in https module
 * 
 * @param {string} url - The full URL to request
 * @param {Object} options - Request options
 * @returns {Promise<Object>} - Response data
 */
function makeHttpsRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        console.log(`[makeHttpsRequest] ${options.method || 'GET'} ${url}`);
        
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || 443,
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: options.headers || {},
            timeout: options.timeout || 10000
        };

        const req = https.request(requestOptions, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                console.log(`[makeHttpsRequest] Response status: ${res.statusCode}`);
                try {
                    const jsonData = data ? JSON.parse(data) : null;
                    resolve({
                        status: res.statusCode,
                        data: jsonData,
                        headers: res.headers
                    });
                } catch (e) {
                    console.log('[makeHttpsRequest] Response is not JSON, returning as text');
                    resolve({
                        status: res.statusCode,
                        data: data,
                        headers: res.headers
                    });
                }
            });
        });

        req.on('error', (error) => {
            console.error(`[makeHttpsRequest] Request error: ${error.message}`);
            reject(error);
        });

        req.on('timeout', () => {
            console.error(`[makeHttpsRequest] Request timeout after ${options.timeout}ms`);
            req.destroy();
            reject(new Error('Request timeout'));
        });

        if (options.body) {
            console.log(`[makeHttpsRequest] Sending body: ${options.body.substring(0, 100)}...`);
            req.write(options.body);
        }

        req.end();
    });
}

/**
 * Validates if the token is still valid by making a test API call
 * 
 * @param {string} token - The token to validate
 * @param {string} baseUrl - The base URL for the API
 * @returns {Promise<boolean>} - True if token is valid, false otherwise
 */
async function validateToken(token, baseUrl) {
    if (!token) {
        console.log('[validateToken] No token provided for validation');
        return false;
    }
    
    try {
        console.log('[validateToken] Starting token validation');
        const validationUrl = `${baseUrl}/checker`;
        
        // Make a lightweight API call to check token validity
        const response = await makeHttpsRequest(validationUrl, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            timeout: 5000 // 5 second timeout
        });
        
        if (response.status === 200) {
            console.log('[validateToken] Token is valid (200 OK)');
            return true;
        } else if (response.status === 401 || response.status === 403) {
            console.log(`[validateToken] Token validation failed: Unauthorized (${response.status})`);
            return false;
        } else {
            console.log(`[validateToken] Token validation returned unexpected status: ${response.status}`);
            if (response.data) {
                console.log(`[validateToken] Response data: ${JSON.stringify(response.data)}`);
            }
            return false;
        }
    } catch (error) {
        console.error(`[validateToken] Error validating token: ${error.message}`);
        console.log('[validateToken] Assuming token might still be valid due to network error');
        // Network errors or timeouts - assume token might still be valid
        return true;
    }
}

/**
 * Logs in to Astrid and retrieves a new authentication token
 * 
 * @param {string} baseUrl - The base URL for the API
 * @returns {Promise<string|null>} - The new token or null if login failed
 */
async function loginAndGetToken(baseUrl) {
    try {
        console.log('[loginAndGetToken] Starting login process');
        const emailValue = email.value();
        const passwordValue = password.value();

        if (!emailValue || !passwordValue) {
            console.error('[loginAndGetToken] Email or password not configured in secrets');
            return null;
        }

        console.log(`[loginAndGetToken] Attempting to login with email: ${emailValue}`);
        const loginUrl = `${baseUrl}/auth/login`;
        
        const body = JSON.stringify({
            email: emailValue,
            password: passwordValue
        });
        
        const response = await makeHttpsRequest(loginUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(body)
            },
            body: body,
            timeout: 10000 // 10 second timeout
        });
        
        console.log(`[loginAndGetToken] Login response status: ${response.status}`);
        
        // Adjust these based on your API's response structure
        let newToken = null;
        
        if (response.data && response.data.token) {
            newToken = response.data.token;
            console.log('[loginAndGetToken] Found token in response.data.token');
        } else if (response.data && response.data.access_token) {
            newToken = response.data.access_token;
            console.log('[loginAndGetToken] Found token in response.data.access_token');
        } else if (response.data && response.data.data && response.data.data.token) {
            newToken = response.data.data.token;
            console.log('[loginAndGetToken] Found token in response.data.data.token');
        }
        
        if (newToken) {
            console.log('[loginAndGetToken] Successfully obtained new token');
            return newToken;
        } else {
            console.error('[loginAndGetToken] Unexpected login response structure:', JSON.stringify(response.data));
            return null;
        }
    } catch (error) {
        console.error('[loginAndGetToken] Login failed:', error.message);
        if (error.stack) {
            console.error('[loginAndGetToken] Stack trace:', error.stack);
        }
        return null;
    }
}

/**
 * Processes a value that might be a JSON string or a regular string
 * Returns the parsed value or first element if array, otherwise the original value
 * 
 * @param {string|any} value - The value to process
 * @returns {any} - The processed value
 */
function processValue(value) {
    // If value is null or undefined, return it as is
    if (value === null || value === undefined) {
        return value;
    }

    // If value is already not a string, return it or its first element
    if (typeof value !== 'string') {
        return Array.isArray(value) && value.length > 0 ? value[0] : value;
    }

    // Try to parse the string as JSON
    try {
        const parsedValue = JSON.parse(value);
        
        // If parsed value is an array with elements, return the first element
        if (Array.isArray(parsedValue) && parsedValue.length > 0) {
            return parsedValue[0];
        }
        
        // Return the parsed value (could be object, number, boolean, etc.)
        return parsedValue;
    } catch (e) {
        // If parsing fails, it's a regular string, return as is
        return value;
    }
}