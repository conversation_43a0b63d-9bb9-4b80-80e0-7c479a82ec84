const admin = require("firebase-admin");

exports.saveApiLog = async (status, req) => {
  try {
    const apiLog = {
      status,
      accountId: req.accountId,
      method: req.method,
      ipAddress: req.ip,
      fullUrl: `${req.protocol}://${req.hostname}${req.originalUrl}`,
      params: req.params,
      body: req.body,
    };

    const apiLogRef = admin.firestore().collection("ApiLogs");
    const newApiLog = await apiLogRef.add({
      ...apiLog,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error("Error saving log entry:", error);
  }
};
