const { Timestamp } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const { collectionNames } = require("../constants/collectionNames");
const { getFirestore } = require("firebase-admin/firestore");

exports.logMailingAddressFailure = async function logMailingAddressFailure({
  path,
  type,
  message,
  error,
}) {
  const db = getFirestore();
  const mailingAddressFailuresRef = db.collection(
    collectionNames.logs.mailingAddressFailures
  );

  try {
    await mailingAddressFailuresRef.add({
      type,
      path,
      createdAt: Timestamp.now(),
      message,
      error,
    });
  } catch (error) {
    logger.error(
      "Problem writing to mailingAddressFailures",
      "path: ",
      mailingAddressFailuresRef?.path,
      "error: ",
      error
    );
  }
};
