const crypto = require("crypto");
const { isString, isNil } = require("lodash");
const { logger } = require("firebase-functions");

const testString = "4023 75TH PL LUBBOCK TX 79423";
const testString2 = "4023_ 75TH PL LUBBOCK TX - 79423 !@#$%^&*()_+{}|:;<>,.?/";
const testString3 = "131 4th street lubbock tx 79423";
const testString4 = "13 14th street, Lubbock, tx 79423";

/**
 * Preprocesses an address object into a standardized string format.
 *
 * @param {Object} address - The address object to preprocess.
 * @param {string} address.address1 - The first line of the address.
 * @param {string} [address.address2] - The second line of the address.
 * @param {string} address.city - The city of the address.
 * @param {string} address.state - The state of the address.
 * @param {string} address.zip - The zip code of the address.
 *
 * @returns {string} - The preprocessed address string.
 * The preprocessed string is formed by:
 * - Trimming and converting all fields to lowercase.
 * - Removing non-alphanumeric characters except whitespaces.
 * - Replacing whitespaces with asterisks.
 */
function preProcessAddressString(address) {
  const { address1, address2, city, state, postalCode } = address;
  const cleanedPostalCode = postalCode?.replace(/-0000$/, "");
  const trimmedAndLowercase = [
    address1,
    address2,
    city,
    state,
    cleanedPostalCode,
  ]
    .map((str) => {
      if (isNil(str) || !isString(str)) return "";
      return str.toLowerCase().trim();
    })
    .join("");

  // Remove non-alphanumeric characters except whitespaces
  const strippedString = trimmedAndLowercase.replace(/[^0-9a-z ]/g, "");
  // replace whitespaces with asterisks
  const processedString = strippedString.replace(/ /g, "*");
  return processedString;
}

function preProcessEmailString(email) {
  return email?.toLowerCase();
}

function hashString(string, preProcessFn = () => {}) {
  const strippedString = preProcessFn(string);
  const hashedString = crypto
    .createHash("sha256")
    .update(strippedString)
    .digest("hex");
  return hashedString;
}

exports.hashString = hashString;
exports.preProcessAddressString = preProcessAddressString;
exports.preProcessEmailString = preProcessEmailString;
