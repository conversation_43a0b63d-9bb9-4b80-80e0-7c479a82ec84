const { Timestamp } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");

exports.convertFirestoreTimestampToDateTime = (firestoreTimestamp) => {
  try {
    const timestamp = new Timestamp(firestoreTimestamp.seconds, firestoreTimestamp.nanoseconds);

    return timestamp.toDate();
  } catch (e) {
    logger.error("Error converting firestore timestamp", firestoreTimestamp, e);
    return;
  }
};
