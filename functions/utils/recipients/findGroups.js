const { logger } = require("firebase-functions");

exports.findGroups = async function findGroups(
  masterGroups,
  productId,
  campaignId = null
) {
  const foundGroups = [];

  if (productId !== "12" && productId !== "8") {
    console.log("removing campaign ID from the search");
    campaignId = null;
  }

  for (const group of masterGroups) {
    console.log("Checking group: ", group.name || group.id);
    const { assignments } = group;

    // Skip if this product ID doesn't exist in assignments
    if (!assignments || !assignments[productId]) continue;

    const planAssignments = assignments[productId];

    // Check if assignments exist for this product
    if (planAssignments) {
      // Case 1: If planAssignments is a direct campaign object (like in "12")
      if (planAssignments.campaign) {
        console.log(
          "Direct campaign assignment found: ",
          planAssignments.campaign
        );

        if (campaignId === null || planAssignments.campaign === campaignId) {
          foundGroups.push(group.id);
        }
      }
      // Case 2: If planAssignments is an object with numeric keys (like in "8")
      else {
        const assignmentEntries = Object.values(planAssignments);
        console.log("Found", assignmentEntries.length, "campaign assignments");

        if (campaignId === null) {
          foundGroups.push(group.id);
        } else {
          // Search through each campaign assignment
          for (const assignment of assignmentEntries) {
            console.log("Checking assignment: ", assignment.campaign);

            if (assignment.campaign === campaignId) {
              foundGroups.push(group.id);
              break; // Found a match, no need to check other assignments
            }
          }
        }
      }
    }
  }

  return foundGroups;
};
