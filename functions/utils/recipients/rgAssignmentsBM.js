const { logger } = require("firebase-functions");
const { isNil } = require("lodash");
const { astridConnection } = require("../../astrid/astridConnection");

exports.rgAssignmentsBM = async function rgAssignmentsBM({ body, db }) {
  const { action, accountId, productId, recipientGroupIds } = body;

  // Early return if not product 12
  if (productId !== "12") {
    console.log("Function optimized for product 12 only.");
    return {
      status: 200,
      message: "Function optimized for product 12 only.",
    };
  }

  let campaignId = body.campaignId;
  let groupsPresent = recipientGroupIds && recipientGroupIds.length > 0;
  let masterGroups = [];
  let foundGroups = [];

  try {
    // Fetch all active recipient groups
    let baseGroups = await db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .where("isActive", "==", true)
      .get();

    baseGroups = baseGroups.docs.map((doc) => ({ ...doc.data(), id: doc.id }));

    // Filter groups if specific recipient groups were provided
    if (groupsPresent) {
      masterGroups = baseGroups.filter((group) =>
        recipientGroupIds.includes(group.id)
      );
    } else {
      masterGroups = baseGroups;
    }

    // For delete action, find groups with the specified campaign
    if (action === "delete") {
      foundGroups = await findGroups(masterGroups, productId, campaignId);

      if (foundGroups.length === 0) {
        console.log("No recipient groups found for deletion");
        return {
          status: 200,
          message: "No recipient groups to update",
        };
      }
    }

    // For replace action, if masterGroups is empty but groupsPresent is true,
    // it means we need to remove from all current groups and not add to any
    if (action === "replace" && masterGroups.length === 0 && groupsPresent) {
      console.log(
        "Replace action with empty recipientGroupIds - removing from all groups with this product"
      );

      // Find all groups that have this product/campaign
      const currentGroups = await findGroups(baseGroups, productId, campaignId);

      if (currentGroups.length === 0) {
        console.log("No groups have this product/campaign combination");
        return {
          status: 200,
          message: "No recipient groups to update",
        };
      }

      // Remove from all current groups
      await Promise.all(
        currentGroups.map((group) =>
          removeAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            campaignId,
            group,
          })
        )
      );

      return {
        status: 200,
        message: `Product ${productId} removed from all recipient groups`,
      };
    }

    // Return early if no groups to process for add action
    if (masterGroups.length === 0 && action === "add") {
      console.log("No recipient groups found for add action");
      return {
        status: 200,
        message: "No recipient groups to update",
      };
    }

    // Handle different actions
    if (action === "delete") {
      await Promise.all(
        foundGroups.map((group) => {
          return removeAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            campaignId,
            group,
          });
        })
      );
      return {
        status: 200,
        message: "Campaign removed from recipient groups",
      };
    }

    if (action === "update") {
      return {
        status: 200,
        message: "Campaign updated in recipient groups",
      };
    }

    if (action === "replace") {
      console.log(
        `Processing replace action for magazine product (${productId}), campaign: ${campaignId}`
      );

      // Check if the recipient group IDs list is empty but explicitly provided
      const emptyList =
        Array.isArray(recipientGroupIds) && recipientGroupIds.length === 0;

      // First, find ALL groups that currently have this product/campaign
      console.log(
        `Finding all groups that currently have magazine product ${productId}, campaign ${campaignId}`
      );
      const allCurrentGroups = await findGroups(
        baseGroups,
        productId,
        campaignId
      );
      console.log(
        `Found ${allCurrentGroups.length} groups currently with this magazine product/campaign`
      );

      // If empty list was explicitly provided, remove product from all groups
      if (emptyList) {
        console.log(
          `Empty recipient group list provided - removing magazine from all ${allCurrentGroups.length} groups`
        );

        if (allCurrentGroups.length === 0) {
          return {
            status: 200,
            message: `No groups have magazine (${productId}) with campaign ${campaignId} - nothing to update`,
          };
        }

        // Remove from all current groups
        await Promise.all(
          allCurrentGroups.map((group) =>
            removeAssignment({
              db,
              accountId,
              recipientGroupId: group.id,
              productId,
              campaignId,
              group,
            })
          )
        );

        return {
          status: 200,
          message: `Magazine (${productId}) with campaign ${campaignId} removed from all recipient groups`,
        };
      }

      // For non-empty list, identify which groups need product removed and which need it added

      // Get the master groups (those that should have the product)
      const masterGroups = baseGroups.filter((group) =>
        recipientGroupIds.includes(group.id)
      );
      console.log(
        `Found ${masterGroups.length} specified recipient groups out of ${recipientGroupIds.length} requested`
      );

      // Identify groups that have the product but aren't in the specified list
      const masterGroupIds = masterGroups.map((group) => group.id);
      const groupsToRemoveFrom = allCurrentGroups.filter(
        (group) => !masterGroupIds.includes(group.id)
      );

      console.log(
        `Need to remove magazine from ${groupsToRemoveFrom.length} groups not in the specified list`
      );
      console.log(
        `Need to add/update magazine in ${masterGroups.length} specified groups`
      );

      // Start with removal operations
      if (groupsToRemoveFrom.length > 0) {
        await Promise.all(
          groupsToRemoveFrom.map((group) =>
            removeAssignment({
              db,
              accountId,
              recipientGroupId: group.id,
              productId,
              campaignId,
              group,
            })
          )
        );
      }

      // Then perform add/update operations
      if (masterGroups.length > 0) {
        await Promise.all(
          masterGroups.map((group) =>
            replaceAssignment({
              db,
              accountId,
              recipientGroupId: group.id,
              productId,
              campaignId,
              group,
            })
          )
        );
      }

      return {
        status: 200,
        message: `Magazine (${productId}) with campaign ${campaignId} updated in recipient groups`,
      };
    }

    if (action === "add") {
      await Promise.all(
        masterGroups.map((group) =>
          replaceAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            campaignId,
            group,
          })
        )
      );

      return {
        status: 200,
        message: "Campaign added to recipient groups",
      };
    }

    // If we get here, invalid action was provided
    return {
      status: 400,
      message: "Invalid action provided",
    };
  } catch (error) {
    logger.error(
      "Failed to update recipients",
      "accountId: ",
      accountId,
      "error: ",
      error
    );

    return {
      status: 500,
      message: `Campaigns failed to ${action} recipient groups: ${error.message}`,
    };
  }
};

async function removeAssignment({
  db,
  accountId,
  recipientGroupId,
  productId,
  campaignId,
  group,
}) {
  try {
    const recipientGroupCollectionRef = db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .doc(group.id);

    // For product 12, we know the structure is assignments.12.campaign
    let newAssignments = { ...group.assignments };

    // Remove product 12 assignment entirely
    if (newAssignments["12"]) {
      // Delete the entire product entry if campaignId matches
      if (newAssignments["12"].campaign === campaignId) {
        console.log(
          `Removing magazine campaign ${campaignId} from group ${recipientGroupId}`
        );
        delete newAssignments["12"];
      }
    }

    // Filter out product 12 from productPlans if it was removed from assignments
    let productPlans = [...(group.productPlans || [])];
    if (!newAssignments["12"]) {
      productPlans = productPlans.filter(
        (plan) => plan !== "12" && plan !== 12
      );
      console.log(
        `Removing product 12 from productPlans. Before: ${group.productPlans}, After: ${productPlans}`
      );
    }

    await recipientGroupCollectionRef.update({
      assignments: newAssignments,
      productPlans: productPlans,
    });

    console.log(
      `Successfully removed magazine assignment from group: ${recipientGroupId}`
    );
  } catch (error) {
    console.error(
      "Problem removing assignment",
      productId,
      "from recipient group: ",
      recipientGroupId,
      "error: ",
      error
    );
  }
}

async function replaceAssignment({
  db,
  accountId,
  recipientGroupId,
  productId,
  campaignId,
  group,
}) {
  try {
    const recipientGroupRef = db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .doc(recipientGroupId);

    // Create payload for the Astrid API call
    const payload = { accountId };

    // Define the magazine-specific request to Astrid
    const astridMagazineRequest = {
      url: `print/campaigns`,
      method: "POST",
      payload,
    };

    let newAssignment;

    try {
      // Make the API call specifically for magazine campaigns
      const response = await astridConnection(astridMagazineRequest);

      // Error handling for missing response data
      if (!response?.data) {
        console.error("No Response from Astrid for Magazine request");
        return; // Exit early if no response data
      }

      console.log("Magazine response data received");

      // Find the specific campaign record from the response
      const record = findRecordByCampaignId(response.data, campaignId);

      if (!record) {
        console.error("Campaign not found in magazine response:", campaignId);
        return; // Exit if campaign not found
      }

      // Create the new assignment object
      newAssignment = { ...record };
    } catch (error) {
      console.error("Astrid API error Magazine:", error);
      return; // Exit early if API call fails
    }

    console.log("New magazine assignment:", newAssignment.campaign);

    // Update the assignments object with the new magazine assignment
    let assignmentsUpdated = { ...group.assignments };

    // For product 12, we directly assign the new assignment to the product key
    assignmentsUpdated["12"] = newAssignment;

    // Ensure product is in the productPlans array
    let productPlans = [...(group.productPlans || [])];

    // Remove any existing instances (both string and number versions)
    productPlans = productPlans.filter((plan) => plan !== "12" && plan !== 12);

    // Then add the string version to ensure consistency
    productPlans.push("12");

    // Log for verification
    console.log(
      `Adding product 12 to productPlans. Before: ${group.productPlans}, After: ${productPlans}`
    );

    // Validate data before Firestore update
    if (!assignmentsUpdated || !productPlans) {
      console.error("Invalid data for Firestore update");
      return;
    }

    // Update the database with new assignments and productPlans
    await recipientGroupRef.update({
      assignments: assignmentsUpdated,
      productPlans: productPlans,
    });

    console.log(
      "Successfully updated magazine assignment for product 12 in group:",
      recipientGroupId
    );
  } catch (error) {
    console.error(
      "Problem adding product 12 to recipient group:",
      recipientGroupId,
      "error:",
      error
    );
  }
}

// Changed to non-async since it doesn't use await
function findRecordByCampaignId(records, campaignId) {
  if (!records || !Array.isArray(records) || records.length === 0) {
    return null;
  }

  return records.find((record) => record.campaign === campaignId) || null;
}

async function findGroups(masterGroups, productId, campaignId) {
  const foundGroups = [];

  for (const group of masterGroups) {
    console.log("Checking group:", group.name || group.id);
    const { assignments } = group;

    // Skip if assignments don't exist or don't contain product "12"
    if (!assignments || !assignments["12"]) continue;

    const planAssignments = assignments["12"];

    // For product "12", we know planAssignments has a direct campaign object
    if (planAssignments.campaign) {
      console.log(
        "Direct campaign assignment found:",
        planAssignments.campaign
      );

      if (planAssignments.campaign === campaignId) {
        foundGroups.push(group);
      }
    }
  }

  return foundGroups;
}
