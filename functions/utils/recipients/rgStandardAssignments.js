const { logger } = require("firebase-functions");
const { isNil } = require("lodash");
const { astridConnection } = require("../../astrid/astridConnection");
// Define the special product categories we've already handled separately
const MAGAZINE_PRODUCT = "12";
const POSTCARD_PRODUCT = "8";
const LEGACY_PRODUCTS = ["1", "3", "7", "11"];
const SPECIAL_PRODUCTS = [
  MAGAZINE_PRODUCT,
  POSTCARD_PRODUCT,
  ...LEGACY_PRODUCTS,
];

exports.rgStandardAssignments = async function rgStandardAssignments({
  body,
  db,
}) {
  const { action, accountId, productId, recipientGroupIds } = body;

  // Early return if this is a special product that should be handled by other functions
  if (SPECIAL_PRODUCTS.includes(productId)) {
    console.log(
      `Product ${productId} should be handled by its specialized function.`
    );
    return {
      status: 200,
      message: `Product ${productId} should be handled by its specialized function.`,
    };
  }

  // For standard products, we don't use campaignId
  const campaignId = null;
  let groupsPresent = recipientGroupIds && recipientGroupIds.length > 0;
  let masterGroups = [];
  let foundGroups = [];

  try {
    // Fetch all active recipient groups
    let baseGroups = await db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .where("isActive", "==", true)
      .get();

    baseGroups = baseGroups.docs.map((doc) => ({ ...doc.data(), id: doc.id }));

    // Filter groups if specific recipient groups were provided
    if (groupsPresent) {
      masterGroups = baseGroups.filter((group) =>
        recipientGroupIds.includes(group.id)
      );
    } else {
      masterGroups = baseGroups;
    }

    // For delete action, find groups with the specified product
    if (action === "delete") {
      foundGroups = await findStandardGroups(masterGroups, productId);

      if (foundGroups.length === 0) {
        console.log(
          `No recipient groups found with product ${productId} for deletion`
        );
        return {
          status: 200,
          message: "No recipient groups to update",
        };
      }
    }

    // For replace action, if masterGroups is empty but groupsPresent is true,
    // it means we need to remove from all current groups and not add to any
    if (action === "replace" && masterGroups.length === 0 && groupsPresent) {
      console.log(
        "Replace action with empty recipientGroupIds - removing from all groups with this product"
      );

      // Find all groups that have this product
      const currentGroups = await findStandardGroups(baseGroups, productId);

      if (currentGroups.length === 0) {
        console.log("No groups have this product");
        return {
          status: 200,
          message: "No recipient groups to update",
        };
      }

      // Remove from all current groups
      await Promise.all(
        currentGroups.map((group) =>
          removeStandardAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            group,
          })
        )
      );

      return {
        status: 200,
        message: `Product ${productId} removed from all recipient groups`,
      };
    }

    // Handle different actions
    if (action === "delete") {
      await Promise.all(
        foundGroups.map((group) => {
          return removeStandardAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            group,
          });
        })
      );
      return {
        status: 200,
        message: `Product ${productId} removed from recipient groups`,
      };
    }

    if (action === "update") {
      return {
        status: 200,
        message: `Product ${productId} updated in recipient groups`,
      };
    }

    if (action === "replace") {
      // Find current groups that have this product assigned
      const currentGroups = await findStandardGroups(baseGroups, productId);

      // Remove from current groups
      await Promise.all(
        currentGroups.map((group) =>
          removeStandardAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            group,
          })
        )
      );

      // Then add to specified groups
      await Promise.all(
        masterGroups.map((group) =>
          replaceStandardAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            group,
          })
        )
      );

      return {
        status: 200,
        message: `Product ${productId} replaced in recipient groups`,
      };
    }

    if (action === "add") {
      await Promise.all(
        masterGroups.map((group) =>
          replaceStandardAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            group,
          })
        )
      );

      return {
        status: 200,
        message: `Product ${productId} added to recipient groups`,
      };
    }

    // If we get here, invalid action was provided
    return {
      status: 400,
      message: "Invalid action provided",
    };
  } catch (error) {
    logger.error(
      "Failed to update recipients",
      "accountId: ",
      accountId,
      "error: ",
      error
    );

    return {
      status: 500,
      message: `Failed to ${action} product ${productId} in recipient groups: ${error.message}`,
    };
  }
};

/**
 * Remove a standard product assignment from a recipient group
 */
async function removeStandardAssignment({
  db,
  accountId,
  recipientGroupId,
  productId,
  group,
}) {
  try {
    const recipientGroupCollectionRef = db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .doc(group.id);

    // For standard products, we simply remove the product entry
    let newAssignments = { ...group.assignments };

    // Remove the product assignment entirely
    if (newAssignments[productId]) {
      console.log(
        `Removing product ${productId} from group ${recipientGroupId}`
      );
      delete newAssignments[productId];
    }

    // Filter out product from productPlans
    let productPlans = [...(group.productPlans || [])];
    productPlans = productPlans.filter(
      (plan) => plan !== productId && plan !== productId.toString()
    );

    // Log before update for verification
    console.log(
      `Removing product ${productId} from productPlans. Before: ${group.productPlans}, After: ${productPlans}`
    );

    await recipientGroupCollectionRef.update({
      assignments: newAssignments,
      productPlans: productPlans,
    });

    console.log(
      `Successfully removed product ${productId} from group ${recipientGroupId}`
    );
  } catch (error) {
    console.error(
      "Problem removing product",
      productId,
      "from recipient group: ",
      recipientGroupId,
      "error: ",
      error
    );
  }
}

/**
 * Add or replace a standard product assignment in a recipient group
 */
async function replaceStandardAssignment({
  db,
  accountId,
  recipientGroupId,
  productId,
  group,
}) {
  try {
    const recipientGroupRef = db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .doc(recipientGroupId);

    // Create payload for the Astrid API call
    const payload = { accountId };

    // Define the non-print request to Astrid for standard products
    const astridRequest = {
      url: `products/requirements`,
      method: "POST",
      payload,
    };

    let newAssignment;

    try {
      // Make the API call for standard products
      const response = await astridConnection(astridRequest);

      // Error handling for missing response data
      if (!response?.data) {
        console.error(
          `No Response from Astrid for product ${productId} request`
        );
        return; // Exit early if no response data
      }

      console.log(`Product ${productId} response data received`);

      // For standard products, we look for the product in the response data
      if (response.data && response.data[productId]) {
        // Get the product-specific data
        newAssignment = { ...response.data[productId] };

        // Ensure we have at least the basic required fields
        if (!newAssignment.enabled) {
          newAssignment.enabled = true;
        }
      } else {
        console.error(`Product ${productId} not found in Astrid response`);
        // Create a default assignment with minimum required fields
        newAssignment = {
          enabled: true,
          productTypeId: productId,
          // For products like "6" we typically include these fields
          bpId: null,
          rules: null,
        };
      }
    } catch (error) {
      console.error(`Astrid API error for product ${productId}:`, error);

      // Create a fallback assignment even if API fails
      newAssignment = {
        enabled: true,
        productTypeId: productId,
        // Default values that might be needed for these products
        bpId: null,
        rules: null,
      };
    }

    console.log(`New assignment for product ${productId}:`, newAssignment);

    // Update the assignments object with the new assignment
    let assignmentsUpdated = { ...group.assignments };

    // For standard products, we directly assign to the product key
    assignmentsUpdated[productId] = newAssignment;

    // Ensure product is in the productPlans array
    let productPlans = [...(group.productPlans || [])];
    const productIdStr = productId.toString();

    // Remove any existing instances (both string and number versions)
    productPlans = productPlans.filter(
      (plan) => plan !== productId && plan !== productIdStr
    );

    // Then add the string version to ensure consistency
    productPlans.push(productIdStr);

    // Log for verification
    console.log(
      `Adding product ${productId} to productPlans. Before: ${group.productPlans}, After: ${productPlans}`
    );

    // Validate data before update
    if (!assignmentsUpdated || !productPlans) {
      console.error("Invalid data for Firestore update");
      return;
    }

    // Update the database with new assignments and productPlans
    await recipientGroupRef.update({
      assignments: assignmentsUpdated,
      productPlans: productPlans,
    });

    console.log(
      `Successfully updated product ${productId} in group ${recipientGroupId}`
    );
  } catch (error) {
    console.error(
      `Problem adding product ${productId} to recipient group:`,
      recipientGroupId,
      "error:",
      error
    );
  }
}

/**
 * Find groups that have a specific standard product assigned
 */
async function findStandardGroups(masterGroups, productId) {
  const foundGroups = [];

  for (const group of masterGroups) {
    console.log("Checking group:", group.name || group.id);
    const { assignments } = group;

    // Skip if assignments don't exist or don't contain the product
    if (!assignments || !assignments[productId]) continue;

    // For standard products, we just check if the product entry exists
    console.log(`Found product ${productId} in group ${group.id}`);
    foundGroups.push(group);
  }

  return foundGroups;
}
