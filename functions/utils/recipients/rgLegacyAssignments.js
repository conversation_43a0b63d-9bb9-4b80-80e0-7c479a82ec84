const { logger } = require("firebase-functions");
const { isNil } = require("lodash");
const { astridConnection } = require("../../astrid/astridConnection");

// Legacy product IDs that share the same structure and behavior
const LEGACY_PRODUCT_IDS = ["1", "3", "7", "11"];

exports.rgLegacyAssignments = async function rgLegacyAssignments({ body, db }) {
  const { action, accountId, productId, recipientGroupIds } = body;

  // Early return if not a legacy product
  if (!LEGACY_PRODUCT_IDS.includes(productId)) {
    console.log("Function optimized for legacy products (1, 3, 7, 11) only.");
    return {
      status: 200,
      message: "Function optimized for legacy products (1, 3, 7, 11) only.",
    };
  }

  // For legacy products, campaign is always "legacy"
  const campaignId = "legacy";
  let groupsPresent = recipientGroupIds && recipientGroupIds.length > 0;
  let masterGroups = [];
  let foundGroups = [];

  try {
    // Fetch all active recipient groups
    let baseGroups = await db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .where("isActive", "==", true)
      .get();

    baseGroups = baseGroups.docs.map((doc) => ({ ...doc.data(), id: doc.id }));

    // Filter groups if specific recipient groups were provided
    if (groupsPresent) {
      masterGroups = baseGroups.filter((group) =>
        recipientGroupIds.includes(group.id)
      );
    } else {
      masterGroups = baseGroups;
    }

    // For delete action, find groups with the specified legacy product
    if (action === "delete") {
      foundGroups = await findLegacyGroups(baseGroups, productId);

      if (foundGroups.length === 0) {
        console.log(
          `No recipient groups found with product ${productId} for deletion`
        );
        return {
          status: 200,
          message: "No recipient groups to update",
        };
      }
    }

    // For replace action, if masterGroups is empty but groupsPresent is true,
    // it means we need to remove from all current groups and not add to any
    if (action === "replace" && masterGroups.length === 0 && groupsPresent) {
      console.log(
        "Replace action with empty recipientGroupIds - removing from all groups with this product"
      );

      // Find all groups that have this product
      const currentGroups = await findLegacyGroups(baseGroups, productId);

      if (currentGroups.length === 0) {
        console.log("No groups have this product");
        return {
          status: 200,
          message: "No recipient groups to update",
        };
      }

      // Remove from all current groups
      await Promise.all(
        currentGroups.map((group) =>
          removeLegacyAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            group,
          })
        )
      );

      return {
        status: 200,
        message: `Product ${productId} removed from all recipient groups`,
      };
    }

    // Return early if no groups to process for add action
    if (masterGroups.length === 0 && action === "add") {
      console.log("No recipient groups found for", action);
      return {
        status: 200,
        message: "No recipient groups to update",
      };
    }

    // Handle different actions
    if (action === "delete") {
      await Promise.all(
        foundGroups.map((group) => {
          return removeLegacyAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            group,
          });
        })
      );
      return {
        status: 200,
        message: `Product ${productId} removed from recipient groups`,
      };
    }

    if (action === "update") {
      return {
        status: 200,
        message: `Product ${productId} updated in recipient groups`,
      };
    }

    if (action === "replace") {
      console.log(
        `Processing replace action for legacy product (${productId})`
      );

      // Check if the recipient group IDs list is empty but explicitly provided
      const emptyList =
        Array.isArray(recipientGroupIds) && recipientGroupIds.length === 0;

      // First, find ALL groups that currently have this product
      console.log(
        `Finding all groups that currently have legacy product ${productId}`
      );
      const allCurrentGroups = await findLegacyGroups(baseGroups, productId);
      console.log(
        `Found ${allCurrentGroups.length} groups currently with this legacy product`
      );

      // If empty list was explicitly provided, remove product from all groups
      if (emptyList) {
        console.log(
          `Empty recipient group list provided - removing legacy product from all ${allCurrentGroups.length} groups`
        );

        if (allCurrentGroups.length === 0) {
          return {
            status: 200,
            message: `No groups have legacy product (${productId}) - nothing to update`,
          };
        }

        // Remove from all current groups
        await Promise.all(
          allCurrentGroups.map((group) =>
            removeLegacyAssignment({
              db,
              accountId,
              recipientGroupId: group.id,
              productId,
              group,
            })
          )
        );

        return {
          status: 200,
          message: `Legacy product (${productId}) removed from all recipient groups`,
        };
      }

      // For non-empty list, identify which groups need product removed and which need it added

      // Get the master groups (those that should have the product)
      const masterGroups = baseGroups.filter((group) =>
        recipientGroupIds.includes(group.id)
      );
      console.log(
        `Found ${masterGroups.length} specified recipient groups out of ${recipientGroupIds.length} requested`
      );

      // Identify groups that have the product but aren't in the specified list
      const masterGroupIds = masterGroups.map((group) => group.id);
      const groupsToRemoveFrom = allCurrentGroups.filter(
        (group) => !masterGroupIds.includes(group.id)
      );

      console.log(
        `Need to remove legacy product from ${groupsToRemoveFrom.length} groups not in the specified list`
      );
      console.log(
        `Need to add/update legacy product in ${masterGroups.length} specified groups`
      );

      // Start with removal operations
      if (groupsToRemoveFrom.length > 0) {
        await Promise.all(
          groupsToRemoveFrom.map((group) =>
            removeLegacyAssignment({
              db,
              accountId,
              recipientGroupId: group.id,
              productId,
              group,
            })
          )
        );
      }

      // Then perform add/update operations
      if (masterGroups.length > 0) {
        await Promise.all(
          masterGroups.map((group) =>
            replaceLegacyAssignment({
              db,
              accountId,
              recipientGroupId: group.id,
              productId,
              group,
            })
          )
        );
      }

      return {
        status: 200,
        message: `Legacy product (${productId}) updated in recipient groups`,
      };
    }

    if (action === "add") {
      await Promise.all(
        masterGroups.map((group) =>
          replaceLegacyAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            group,
          })
        )
      );

      return {
        status: 200,
        message: `Product ${productId} added to recipient groups`,
      };
    }

    // If we get here, invalid action was provided
    return {
      status: 400,
      message: "Invalid action provided",
    };
  } catch (error) {
    logger.error(
      "Failed to update recipients",
      "accountId: ",
      accountId,
      "error: ",
      error
    );

    return {
      status: 500,
      message: `Failed to ${action} product ${productId} in recipient groups: ${error.message}`,
    };
  }
};

/**
 * Remove a legacy product assignment from a recipient group
 */
async function removeLegacyAssignment({
  db,
  accountId,
  recipientGroupId,
  productId,
  group,
}) {
  try {
    const recipientGroupCollectionRef = db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .doc(group.id);

    // For legacy products, we simply remove the product entry
    let newAssignments = { ...group.assignments };

    // Remove the legacy product assignment entirely
    if (newAssignments[productId]) {
      console.log(
        `Removing product ${productId} from group ${recipientGroupId}`
      );
      delete newAssignments[productId];
    }

    // Filter out product from productPlans
    let productPlans = [...(group.productPlans || [])];
    productPlans = productPlans.filter(
      (plan) => plan !== productId && plan !== productId.toString()
    );

    // Log before update for verification
    console.log(
      `Removing product ${productId} from productPlans. Before: ${group.productPlans}, After: ${productPlans}`
    );

    await recipientGroupCollectionRef.update({
      assignments: newAssignments,
      productPlans: productPlans,
    });

    console.log(
      `Successfully removed legacy product ${productId} from group ${recipientGroupId}`
    );
  } catch (error) {
    console.error(
      "Problem removing legacy product",
      productId,
      "from recipient group: ",
      recipientGroupId,
      "error: ",
      error
    );
  }
}

/**
 * Add or replace a legacy product assignment in a recipient group
 */
async function replaceLegacyAssignment({
  db,
  accountId,
  recipientGroupId,
  productId,
  group,
}) {
  try {
    const recipientGroupRef = db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .doc(recipientGroupId);

    // Create payload for the Astrid API call
    const payload = { accountId };

    // Define the legacy-specific request to Astrid
    const astridLegacyRequest = {
      url: `products/legacy`,
      method: "POST",
      payload,
    };

    let newAssignment;

    try {
      // Make the API call for legacy products
      const response = await astridConnection(astridLegacyRequest);

      // Error handling for missing response data
      if (!response?.data) {
        console.error("No Response from Astrid for Legacy product request");
        return; // Exit early if no response data
      }

      console.log("Legacy product response data received");

      // For legacy products, we take the first item in the response
      if (response.data && response.data.length > 0) {
        // Get specific product data if available, otherwise use the first item
        const productData =
          response.data.find((item) => item.productTypeId === productId) ||
          response.data[0];

        // Create a standard legacy assignment structure
        newAssignment = {
          campaign: "legacy", // Always "legacy" for these products
          name: productData.name || getLegacyProductName(productId),
          productType:
            productData.productType || getLegacyProductName(productId),
          productTypeId: productId,
        };
      } else {
        // Create a default assignment if no data returned
        newAssignment = {
          campaign: "legacy",
          name: getLegacyProductName(productId),
          productType: getLegacyProductName(productId),
          productTypeId: productId,
        };
      }
    } catch (error) {
      console.error(`Astrid API error for legacy product ${productId}:`, error);

      // Create a fallback assignment even if API fails
      newAssignment = {
        campaign: "legacy",
        name: getLegacyProductName(productId),
        productType: getLegacyProductName(productId),
        productTypeId: productId,
      };
    }

    console.log(
      `New legacy assignment for product ${productId}:`,
      newAssignment
    );

    // Update the assignments object with the new legacy assignment
    let assignmentsUpdated = { ...group.assignments };

    // For legacy products, we directly assign to the product key
    assignmentsUpdated[productId] = newAssignment;

    // Ensure product is in the productPlans array
    let productPlans = [...(group.productPlans || [])];
    const productIdStr = productId.toString();

    // Remove any existing instances (both string and number versions)
    productPlans = productPlans.filter(
      (plan) => plan !== productId && plan !== productIdStr
    );

    // Then add the string version to ensure consistency
    productPlans.push(productIdStr);

    // Log for verification
    console.log(
      `Adding product ${productId} to productPlans. Before: ${group.productPlans}, After: ${productPlans}`
    );

    // Update the database with new assignments and productPlans
    await recipientGroupRef.update({
      assignments: assignmentsUpdated,
      productPlans: productPlans,
    });

    console.log(
      `Successfully updated legacy product ${productId} in group ${recipientGroupId}`
    );
  } catch (error) {
    console.error(
      `Problem adding legacy product ${productId} to recipient group:`,
      recipientGroupId,
      "error:",
      error
    );
  }
}

/**
 * Find groups that have a specific legacy product assigned
 */
async function findLegacyGroups(masterGroups, productId) {
  const foundGroups = [];

  for (const group of masterGroups) {
    console.log("Checking group:", group.name || group.id);
    const { assignments } = group;

    // Skip if assignments don't exist or don't contain the legacy product
    if (!assignments || !assignments[productId]) continue;

    // For legacy products, we just check if the product entry exists
    // We don't need to check campaign ID since it's always "legacy"
    console.log(`Found legacy product ${productId} in group ${group.id}`);
    foundGroups.push(group);
  }

  return foundGroups;
}

/**
 * Get a human-readable name for legacy products
 */
function getLegacyProductName(productId) {
  const productNames = {
    1: "American Lifestyle",
    3: "Start Healthy",
    7: "Good to Be Home",
    11: "Home Warranty",
  };

  return productNames[productId] || `Legacy Product ${productId}`;
}
