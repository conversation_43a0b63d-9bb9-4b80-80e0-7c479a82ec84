const {PRODUCTS} = require("../../constants/products")

exports.recipientInProduct = async function recipientInProduct({ recipientId, accountId, method, db }) {
  const planIds = getPlanIdsByMethod(PRODUCTS, method);

  const recipientRef = db.collection("Account").doc(accountId).collection("Recipients").doc(recipientId);
  const recipientSnap = await recipientRef.get();
  if(!recipientSnap.exists){
    return false
  }

  const recipient = recipientSnap.data();

  const recipientGroupId = recipient?.recipientGroupIds[0];
  
  if(recipientGroupId){
    const recipientGroupRef = db.collection("Account").doc(accountId).collection("RecipientGroups").doc(recipientGroupId);
    const recipientGroupSnap = await recipientGroupRef.get();
    if(recipientGroupSnap.exists){
      const recipientGroup = recipientGroupSnap.data();
      let hasMatchingPlan = false;
      if(planIds.length > 0){  
        for(const plan of planIds){
          const hasPlan = recipientGroup?.productPlans?.includes(plan);
          if(hasPlan){
            return true;
          }
        };
      }
      return hasMatchingPlan;
    }else{
      return false
    }
  }else{
    return false
  }
}

function getPlanIdsByMethod(products, method) {
  if (!products || !Array.isArray(products)) {
    return [];
  }
  
  // Filter products by the specified method and extract planIds
  return products
    .filter(product => product.method === method)
    .map(product => product.planId);
}