module.exports.collectValidRecipientsByGroupLegacy =
  async function collectValidRecipientsByGroupLegacy({
    db,
    groups,
    accountId,
    method = "address",
    productId,
  }) {
    let validRecipients = [];

    const recipientsRef = await db
      .collection(`Account/${accountId.toString()}/Recipients`)
      .where("isActive", "==", true)
      .where("recipientGroupIds", "array-contains-any", groups);

    const recipientsSnapshot = await recipientsRef.get();

    
    if (recipientsSnapshot.empty) {
      return validRecipients;
    }

    await Promise.all(
      recipientsSnapshot.docs.map(async (doc) => {
        const recipientData = doc.data();
        if (await isValid(recipientData, method, productId, db)) {
          let recordForReturn = await formatRecord(doc.id, recipientData);
          recordForReturn = await formatRecordForAddress(
            doc.id,
            recipientData,
            recordForReturn,
            db
          );
          if (recordForReturn) {
            validRecipients.push(recordForReturn);
          }
        }
      })
    );
    return validRecipients;
  };

// Change other helper functions to remove 'async' from non-exported functions
async function isValid(data, method, productId, db) {
  if (data?.mailingsPaused && data?.mailingsPaused === true) {
    return false;
  }

  if (data?.overrideDeliverability?.[method] && data?.overrideDeliverability?.[method] === true) {
    return true;
  }

  if(productId === 8 && data?.addressDeliverability?.isValidMailingAddress === true) {
    return true;
  }

  const isWait = await isWaitingList(data, db);
  if (method === "address") {
    if (
      data?.addressDeliverability &&
      data?.addressDeliverability?.code === "WS"
    ) {
      return true;
    } else if(!isWait && data?.addressDeliverability?.isValidMailingAddress) {
      return true;
    }
  }

  if (method === "email") {
    if (
      data?.emailDeliverability &&
      data?.emailDeliverability?.code === "WE"
    ) {
      return true;
    }
  }

  return false;
}

async function formatRecord(id, data) {
  const recordForReturn = {
    id: id,
    first_name: data?.name?.firstName,
    last_name: data?.name?.lastName,
    recipient_group_id: data?.recipientGroupIds[0],
    mailings_paused: data?.mailingsPaused,
    mailing_salutation: data?.salutation?.mailingSalutation,
    letter_salutation: data?.salutation?.letterSalutation,
  };
  return recordForReturn;
}

async function formatRecordForEmail(data, returnData) {
  returnData = {
    ...returnData,
    email: data?.searchTags?.primaryEmail,
  };

  return returnData;
}

async function isWaitingList(data, db) {
  const accountId = data?.accountId.toString();
  const addressId = data?.mailingAddresses?.[0]?.id;
  if(addressId === undefined) {
    return false;
  }
  const waitingListRef = db
    .collection(`MailingAddresses`)
    .doc(addressId);
  const waitingListLookup = await waitingListRef.get();
  const waitingListData = waitingListLookup.data();
  const exclusive = waitingListData?.exclusive?.[0] || false;
  if(!exclusive || exclusive === "000") {
    return false;
  } else if(exclusive === accountId) {
    return false;
  } else {
    return true;
  }

}

async function formatRecordForAddress(id, data, returnData, db) {
  if (data?.mailingAddresses?.[0]?.id) {
    const addressRef = db
      .collection("MailingAddresses")
      .doc(data?.mailingAddresses?.[0]?.id);
    const addressLookup = await addressRef.get();
    const addressData = addressLookup.data();
    returnData = {
      ...returnData,
      address1: addressData?.address1,
      address2: addressData?.address2,
      city: addressData?.city,
      state: addressData?.state,
      zip: addressData?.postalCode,
    } 
  }else if(data?.overrideDeliverability?.address === true) {
    const addressRef = db
      .collection("Account")
      .doc(data?.accountId.toString())
      .collection("Recipients")
      .doc(id)
      .collection("UserEnteredMailingAddress")
      .doc('0');
    const addressLookup = await addressRef.get();
    if(!addressLookup.exists) {
      return returnData;
    }
    const addressData = addressLookup.data();
    returnData = {
      ...returnData,
      address1: addressData?.address1,
      address2: addressData?.address2,
      city: addressData?.city,
      state: addressData?.state,
      zip: addressData?.postalCode,
    };
  }
  return returnData;
}
