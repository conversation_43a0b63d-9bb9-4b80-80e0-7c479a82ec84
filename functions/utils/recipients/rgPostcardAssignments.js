const { logger } = require("firebase-functions");
const { isNil } = require("lodash");
const { astridConnection } = require("../../astrid/astridConnection");
exports.rgPostcardAssignments = async function rgPostcardAssignments({
  body,
  db,
}) {
  const { action, accountId, productId, recipientGroupIds } = body;

  // Early return if not product 8
  if (productId !== "8") {
    console.log("Function optimized for product 8 only.");
    return {
      status: 200,
      message: "Function optimized for product 8 only.",
    };
  }

  let campaignId = body.campaignId;
  let groupsPresent = recipientGroupIds && recipientGroupIds.length > 0;
  let masterGroups = [];
  let foundGroups = [];

  try {
    // Fetch all active recipient groups
    let baseGroups = await db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .where("isActive", "==", true)
      .get();

    baseGroups = baseGroups.docs.map((doc) => ({ ...doc.data(), id: doc.id }));

    // Filter groups if specific recipient groups were provided
    if (groupsPresent) {
      masterGroups = baseGroups.filter((group) =>
        recipientGroupIds.includes(group.id)
      );
    } else {
      masterGroups = baseGroups;
    }

    // For delete action, find groups with the specified campaign
    if (action === "delete") {
      foundGroups = await findGroups(masterGroups, productId, campaignId);

      if (foundGroups.length === 0) {
        console.log("No recipient groups found for deletion");
        return {
          status: 200,
          message: "No recipient groups to update",
        };
      }
    }

    // For replace action, if masterGroups is empty but groupsPresent is true,
    // it means we need to remove from all current groups and not add to any
    if (action === "replace" && masterGroups.length === 0 && groupsPresent) {
      console.log(
        "Replace action with empty recipientGroupIds - removing from all groups with this product"
      );

      // Find all groups that have this product/campaign
      const currentGroups = await findGroups(baseGroups, productId, campaignId);

      if (currentGroups.length === 0) {
        console.log("No groups have this product/campaign combination");
        return {
          status: 200,
          message: "No recipient groups to update",
        };
      }

      // Remove from all current groups
      await Promise.all(
        currentGroups.map((group) =>
          removeAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            campaignId,
            group,
          })
        )
      );

      return {
        status: 200,
        message: `Product ${productId} removed from all recipient groups`,
      };
    }

    // Return early if no groups to process for add action
    if (masterGroups.length === 0 && action === "add") {
      console.log("No recipient groups found for add action");
      return {
        status: 200,
        message: "No recipient groups to update",
      };
    }

    // Handle different actions
    if (action === "delete") {
      await Promise.all(
        foundGroups.map((group) => {
          return removeAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            campaignId,
            group,
          });
        })
      );
      return {
        status: 200,
        message: "Campaign removed from recipient groups",
      };
    }

    if (action === "update") {
      return {
        status: 200,
        message: "Campaign updated in recipient groups",
      };
    }

    if (action === "replace") {
      console.log(
        `Processing replace action for postcard product (${productId}), campaign: ${campaignId}`
      );

      // Check if the recipient group IDs list is empty but explicitly provided
      const emptyList =
        Array.isArray(recipientGroupIds) && recipientGroupIds.length === 0;

      // First, find ALL groups that currently have this product/campaign
      console.log(
        `Finding all groups that currently have postcard product ${productId}, campaign ${campaignId}`
      );
      const allCurrentGroups = await findGroups(
        baseGroups,
        productId,
        campaignId
      );
      console.log(
        `Found ${allCurrentGroups.length} groups currently with this postcard product/campaign`
      );

      // If empty list was explicitly provided, remove product from all groups
      if (emptyList) {
        console.log(
          `Empty recipient group list provided - removing postcard from all ${allCurrentGroups.length} groups`
        );

        if (allCurrentGroups.length === 0) {
          return {
            status: 200,
            message: `No groups have postcard (${productId}) with campaign ${campaignId} - nothing to update`,
          };
        }

        // Remove from all current groups
        await Promise.all(
          allCurrentGroups.map((group) =>
            removeAssignment({
              db,
              accountId,
              recipientGroupId: group.id,
              productId,
              campaignId,
              group,
            })
          )
        );

        return {
          status: 200,
          message: `Postcard (${productId}) with campaign ${campaignId} removed from all recipient groups`,
        };
      }

      // For non-empty list, identify which groups need product removed and which need it added

      // Get the master groups (those that should have the product)
      const masterGroups = baseGroups.filter((group) =>
        recipientGroupIds.includes(group.id)
      );
      console.log(
        `Found ${masterGroups.length} specified recipient groups out of ${recipientGroupIds.length} requested`
      );

      // Identify groups that have the product but aren't in the specified list
      const masterGroupIds = masterGroups.map((group) => group.id);
      const groupsToRemoveFrom = allCurrentGroups.filter(
        (group) => !masterGroupIds.includes(group.id)
      );

      console.log(
        `Need to remove postcard from ${groupsToRemoveFrom.length} groups not in the specified list`
      );
      console.log(
        `Need to add/update postcard in ${masterGroups.length} specified groups`
      );

      // Start with removal operations
      if (groupsToRemoveFrom.length > 0) {
        await Promise.all(
          groupsToRemoveFrom.map((group) =>
            removeAssignment({
              db,
              accountId,
              recipientGroupId: group.id,
              productId,
              campaignId,
              group,
            })
          )
        );
      }

      // Then perform add/update operations
      if (masterGroups.length > 0) {
        await Promise.all(
          masterGroups.map((group) =>
            replaceAssignment({
              db,
              accountId,
              recipientGroupId: group.id,
              productId,
              campaignId,
              group,
            })
          )
        );
      }

      return {
        status: 200,
        message: `Postcard (${productId}) with campaign ${campaignId} updated in recipient groups`,
      };
    }

    if (action === "add") {
      await Promise.all(
        masterGroups.map((group) =>
          replaceAssignment({
            db,
            accountId,
            recipientGroupId: group.id,
            productId,
            campaignId,
            group,
          })
        )
      );

      return {
        status: 200,
        message: "Campaign added to recipient groups",
      };
    }

    // If we get here, invalid action was provided
    return {
      status: 400,
      message: "Invalid action provided",
    };
  } catch (error) {
    logger.error(
      "Failed to update recipients",
      "accountId: ",
      accountId,
      "error: ",
      error
    );

    return {
      status: 500,
      message: `Campaigns failed to ${action} recipient groups: ${error.message}`,
    };
  }
};

async function removeAssignment({
  db,
  accountId,
  recipientGroupId,
  productId,
  campaignId,
  group,
}) {
  try {
    const recipientGroupCollectionRef = db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .doc(group.id);

    // For product 8, we need to find the specific campaign in the numbered entries
    let newAssignments = { ...group.assignments };
    let hasRemainingCampaigns = false;

    if (newAssignments["8"]) {
      // Convert the campaign ID to a string for comparison
      const campaignIdStr = campaignId.toString();

      // Find and remove the specific campaign assignment
      const entries = Object.entries(newAssignments["8"]);
      const filteredEntries = entries.filter(([key, value]) => {
        // Keep entries where campaign doesn't match our target
        return value.campaign !== campaignIdStr;
      });

      // If we have remaining entries, rebuild the assignments object
      if (filteredEntries.length > 0) {
        hasRemainingCampaigns = true;
        const newEntries = {};

        // Rebuild with new sequential indices
        filteredEntries.forEach(([_, value], index) => {
          newEntries[index.toString()] = value;
        });

        newAssignments["8"] = newEntries;
      } else {
        // If no campaigns remain, delete the entire product entry
        delete newAssignments["8"];
      }
    }

    // Filter out product 8 from productPlans if all campaigns were removed
    let productPlans = [...(group.productPlans || [])];
    if (!hasRemainingCampaigns && !newAssignments["8"]) {
      productPlans = productPlans.filter((plan) => plan !== "8" && plan !== 8);
      console.log(
        `Removing product 8 from productPlans. Before: ${group.productPlans}, After: ${productPlans}`
      );
    }

    await recipientGroupCollectionRef.update({
      assignments: newAssignments,
      productPlans: productPlans,
    });

    console.log(
      `Successfully removed postcard assignment from group: ${recipientGroupId}`
    );
  } catch (error) {
    console.error(
      "Problem removing assignment",
      productId,
      "from recipient group: ",
      recipientGroupId,
      "error: ",
      error
    );
  }
}

async function replaceAssignment({
  db,
  accountId,
  recipientGroupId,
  productId,
  campaignId,
  group,
}) {
  try {
    const recipientGroupRef = db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .doc(recipientGroupId);

    // Create payload for the Astrid API call
    const payload = { accountId };

    // Define the postcard-specific request to Astrid
    const astridPostcardRequest = {
      url: `print/campaigns/postcard`,
      method: "POST",
      payload,
    };

    let newAssignment;

    try {
      // Make the API call specifically for postcard campaigns
      const response = await astridConnection(astridPostcardRequest);

      // Error handling for missing response data
      if (!response?.data) {
        console.error("No Response from Astrid for Postcard request");
        return; // Exit early if no response data
      }

      console.log("Postcard response data received");

      // Find the specific campaign record from the response
      const record = findRecordByCampaignId(response.data, campaignId);

      if (!record) {
        console.error("Campaign not found in postcard response:", campaignId);
        return; // Exit if campaign not found
      }

      // Create the new assignment object
      newAssignment = { ...record };
    } catch (error) {
      console.error("Astrid API error Postcard:", error);
      return; // Exit early if API call fails
    }

    console.log("New postcard assignment:", newAssignment.campaign);

    // Update the assignments object with the new postcard assignment
    let assignmentsUpdated = { ...group.assignments };

    // For product 8, we need to handle the numbered entries structure
    if (!assignmentsUpdated["8"]) {
      // If no existing campaigns, create a new structure with this as the first entry
      assignmentsUpdated["8"] = {
        0: newAssignment,
      };
    } else {
      // If existing campaigns, check if this campaign already exists
      const entries = Object.entries(assignmentsUpdated["8"]);
      let campaignExists = false;
      let existingKey = null;

      // Find if this campaign already exists and where
      for (const [key, value] of entries) {
        if (value.campaign === campaignId.toString()) {
          campaignExists = true;
          existingKey = key;
          break;
        }
      }

      if (campaignExists && existingKey !== null) {
        // Update the existing campaign entry
        assignmentsUpdated["8"][existingKey] = newAssignment;
      } else {
        // Add as a new entry with the next available index
        const nextIndex = entries.length.toString();
        assignmentsUpdated["8"][nextIndex] = newAssignment;
      }
    }

    // Ensure product is in the productPlans array
    let productPlans = [...(group.productPlans || [])];

    // Remove any existing instances (both string and number versions)
    productPlans = productPlans.filter((plan) => plan !== "8" && plan !== 8);

    // Then add the string version to ensure consistency
    productPlans.push("8");

    // Log for verification
    console.log(
      `Adding product 8 to productPlans. Before: ${group.productPlans}, After: ${productPlans}`
    );

    // Validate data before Firestore update
    if (!assignmentsUpdated || !productPlans) {
      console.error("Invalid data for Firestore update");
      return;
    }

    // Update the database with new assignments and productPlans
    await recipientGroupRef.update({
      assignments: assignmentsUpdated,
      productPlans: productPlans,
    });

    console.log(
      "Successfully updated postcard assignment for product 8 in group:",
      recipientGroupId
    );
  } catch (error) {
    console.error(
      "Problem adding product 8 to recipient group:",
      recipientGroupId,
      "error:",
      error
    );
  }
}

// Find a record by campaign ID
function findRecordByCampaignId(records, campaignId) {
  if (!records || !Array.isArray(records) || records.length === 0) {
    return null;
  }

  // Convert campaign ID to string for consistent comparison
  const campaignIdStr = campaignId.toString();
  return (
    records.find((record) => record.campaign.toString() === campaignIdStr) ||
    null
  );
}

async function findGroups(masterGroups, productId, campaignId) {
  const foundGroups = [];

  // Convert campaign ID to string for consistent comparison
  const campaignIdStr = campaignId.toString();

  for (const group of masterGroups) {
    console.log("Checking group:", group.name || group.id);
    const { assignments } = group;

    // Skip if assignments don't exist or don't contain product "8"
    if (!assignments || !assignments["8"]) continue;

    const planAssignments = assignments["8"];

    // For product "8", we need to check all numbered entries
    let hasCampaign = false;
    for (const key in planAssignments) {
      const assignment = planAssignments[key];
      if (
        assignment.campaign &&
        assignment.campaign.toString() === campaignIdStr
      ) {
        console.log("Found campaign assignment:", assignment.campaign);
        hasCampaign = true;
        break;
      }
    }

    if (hasCampaign) {
      foundGroups.push(group);
    }
  }

  return foundGroups;
}
