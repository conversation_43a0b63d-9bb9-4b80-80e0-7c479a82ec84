//
// Call module with a docRef and a batch object
//
// Ex:
// const recipientRef = db
//    .collection(collectionNames.account)
//    .doc(accountId)
//    .collection(subCollectionNames.contacts.recipients)
//    .doc(recipientId);
// const batch = db.batch();
// await recursiveDocDelete(recipientRef, batch);
// await batch.commit();
//
async function recursiveDocDelete(docRef, batch) {
      // get all subcollections in docRef
      const subcollections = await docRef.listCollections();
      for (const subcollection of subcollections) {
        // get all docs in subcollection
        const subCollectionDocRefs = await docRef.collection(subcollection.id).listDocuments();
        for (const subCollectionDocRef of subCollectionDocRefs) {
          await recursiveDocDelete(subCollectionDocRef, batch);
        }
      }

      batch.delete(docRef);
}

module.exports = { recursiveDocDelete };
