const admin = require("firebase-admin");
const { Timestamp } = require("firebase-admin/firestore");

/**
 * Records changes to a document in a subcollection called "history"
 * @param {Object} params - Function parameters
 * @param {string} params.collection - The collection name
 * @param {string} params.id - The document ID
 * @param {Object|null} params.before - Document data before the change
 * @param {Object|null} params.after - Document data after the change
 * @param {Object} params.context - Function execution context
 * @param {string} params.action - The action performed (CREATE, UPDATE, DELETE)
 * @returns {Promise<DocumentReference>} - Promise resolving to the created history document
 */
exports.collectionHistory = async ({
  collection,
  id,
  before,
  after,
  context,
  action,
}) => {
  try {
    // Get the history subcollection reference
    const collectionHistoryRef = admin
      .firestore()
      .collection(collection)
      .doc(id)
      .collection("History");

    // Extract user information from context
    const userId = context?.auth?.uid || "system";
    const userEmail = context?.auth?.token?.email || "system";

    // Calculate changed fields
    const changedFields = getChangedFields(before, after, action);

    // Create the history entry
    const historyEntry = {
      action,
      timestamp: Timestamp.now(),
      changedFields,
      context: { ...context, userId, userEmail },
    };

    // Add the entry to the history subcollection
    return await collectionHistoryRef.add(historyEntry);
  } catch (logError) {
    console.error("Error writing to history subcollection:", logError);
    throw logError;
  }
};

/**
 * Extracts the changed fields between before and after data
 * @param {Object} before - The data before the change
 * @param {Object} after - The data after the change
 * @param {string} action - The type of change (CREATE, UPDATE, DELETE)
 * @returns {Object} - Object containing the changed fields
 */
function getChangedFields(before, after, action) {
  const changedFields = {};

  // helper to coerce undefined → null
  const normalize = (val) => (val === undefined ? null : val);

  if (action === "CREATE") {
    Object.keys(after || {}).forEach((key) => {
      changedFields[key] = {
        oldValue: null,
        newValue: normalize(after[key]),
      };
    });
  } else if (action === "DELETE") {
    Object.keys(before || {}).forEach((key) => {
      changedFields[key] = {
        oldValue: normalize(before[key]),
        newValue: null,
      };
    });
  } else if (action === "UPDATE") {
    const allKeys = new Set([
      ...Object.keys(before || {}),
      ...Object.keys(after || {}),
    ]);
    allKeys.forEach((key) => {
      const oldValue = normalize(before?.[key]);
      const newValue = normalize(after?.[key]);
      if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        changedFields[key] = { oldValue, newValue };
      }
    });
  }

  return changedFields;
}
