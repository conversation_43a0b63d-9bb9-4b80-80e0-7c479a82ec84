const axios = require("axios");
const { defineString } = require("firebase-functions/params");

const trestleKey = defineString("TRESTLE_KEY");
const trestleUrl = defineString("TRESTLE_URL");
const trestleApiKey = trestleKey.value();
const trestleApiUrl = trestleUrl.value();
const { logToFirestore } = require("../utils/functionLogger");

exports.reversePhoneLookup = async (phone_number) => {
  const url = trestleApiUrl;
  const key = trestleApiKey;
  try {
    const response = await axios.get(`${url}/phone`, {
      params: {
        phone: phone_number,
      },
      headers: {
        "x-api-key": key,
        Accept: "application/json",
      },
    });
    const resp = response.data; // Access the response data directly

    return resp?.owners[0]?.current_addresses[0] || null; // Return the response if needed
  } catch (error) {
    // console.error("Error on reverse phone lookup: ", error.message);
    await logToFirestore({
      functionName: "searchByPhoneWorker",
      type: "error",
      message: `error for ${phone_number}`,
      data: {
        error: error.message,
        errorStack: error.stack,
      },
    });
    return { result: "error", error: error.message };
  }
};
