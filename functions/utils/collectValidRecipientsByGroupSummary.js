const { logger } = require("firebase-functions");
module.exports.collectValidRecipientsByGroupSummary =
  async function collectValidRecipientsByGroupSummary({
    db,
    recipientGroupId,
    accountId,
    method = "address",
    productId,
  }) {
    let validRecipients = [];

    const recipientsRef = db
      .collection(`Account/${accountId.toString()}/Recipients`)
      .where("isActive", "==", true)
      .where("recipientGroupIds", "array-contains", recipientGroupId);

    const recipientsSnapshot = await recipientsRef.get();

    if (recipientsSnapshot.empty) {
      return validRecipients;
    }

    await Promise.all(
      recipientsSnapshot.docs.map(async (doc) => {
        const recipientData = doc.data();
        const valid = await isValid(recipientData, method, productId);

        if (valid) {
          let recordForReturn = await formatRecord(doc.id, recipientData);
          if (method === "email") {
            recordForReturn = await formatRecordForEmail(
              recipientData,
              recordForReturn,
              db
            );
          }
          if (method === "address") {
            recordForReturn = await formatRecordForAddress(
              recipientData,
              recordForReturn,
              db
            );
          }
          if (recordForReturn) {
            validRecipients.push(recordForReturn);
          }
        }
      })
    );

    if (method === "email") {
      const uniqueEmails = [];
      const seenEmails = new Set();
      validRecipients.forEach((record) => {
        if (!seenEmails.has(record.email)) {
          uniqueEmails.push(record);
          seenEmails.add(record.email);
        }
      });
      return uniqueEmails;
    }

    return validRecipients;
  };

// Change other helper functions to remove 'async' from non-exported functions
async function isValid(data, method, productId) {
  if (data?.mailingsPaused) {
    return false;
  }

  if (data?.overrideDeliverability?.[method]) {
    return true;
  }

  if (method === "address") {
    if (productId !== 8) {
      if (
        data?.addressDeliverability &&
        data?.addressDeliverability?.isValidMailingAddress &&
        data?.addressDeliverability?.isExclusive
      ) {
        return true;
      }
    } else {
      if (
        data?.addressDeliverability &&
        data?.addressDeliverability?.isValidMailingAddress
      ) {
        return true;
      }
    }
  }

  if (method === "email") {
    if (
      data?.emailDeliverability &&
      data?.emailDeliverability?.code === "WE"
    ) {
      return true;
    }
  }

  return false;
}

async function formatRecord(id, data) {
  const recordForReturn = {
    id: id,
    first_name: data?.name?.firstName,
    last_name: data?.name?.lastName,
    recipient_group_id: data?.recipientGroupIds[0],
    mailings_paused: data?.mailingsPaused,
    mailing_salutation: data?.salutation?.mailingSalutation,
    letter_salutation: data?.salutation?.letterSalutation,
  };
  return recordForReturn;
}

async function formatRecordForEmail(data, returnData) {
  returnData = {
    ...returnData,
    email: data?.searchTags?.primaryEmail,
  };

  return returnData;
}

async function formatRecordForAddress(data, returnData, db) {
  if (data?.mailingAddresses?.[0]?.id) {
    const addressRef = db
      .collection("MailingAddresses")
      .doc(data?.mailingAddresses?.[0]?.id);
    const addressLookup = await addressRef.get();
    const addressData = addressLookup.data();
    returnData = {
      ...returnData,
      address1: addressData?.address1,
      address2: addressData?.address2,
      city: addressData?.city,
      state: addressData?.state,
      zip: addressData?.postalCode,
      formatted_address: addressData?.formattedAddress,
      formatted_address1: addressData?.formattedAddress1,
      formatted_address2: addressData?.formattedAddress2,
    };

    return returnData;
  }
}
