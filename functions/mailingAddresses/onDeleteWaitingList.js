const { onDocumentDeleted } = require("firebase-functions/v2/firestore");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { logger } = require("firebase-functions");
const { collectionHistory } = require("../utils/collectionHistory");

exports.onDeleteWaitingList = onDocumentDeleted(
  `${collectionNames.mailingAddresses}/{mailingAddressSourceId}/${subCollectionNames.mailingAddresses.waitingList}/{waitingListId}`,
  async (event) => {
    const { mailingAddressSourceId, waitingListId } = event.params;
    const snapshot = event.data;

    const waitingListData = snapshot.data();
    await collectionHistory({
      collection: `${collectionNames.mailingAddresses}/${mailingAddressSourceId}/${subCollectionNames.mailingAddresses.waitingList}`,
      id: waitingListId,
      before: waitingListData,
      after: null,
      action: "DELETE",
      context: {
        notes: `${waitingListId} removed from waiting list`,
      },
    });
  }
);
