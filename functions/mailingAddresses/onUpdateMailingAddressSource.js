const { onDocumentUpdated } = require("firebase-functions/v2/firestore");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");

const {
  addNextInLineExclusivity,
} = require("../recipients/exclusivity/helpers");
const { collectionHistory } = require("../utils/collectionHistory");
const { isEmpty } = require("lodash");

exports.onUpdateMailingAddressSource = onDocumentUpdated(
  `${collectionNames.mailingAddresses}/{mailingAddressSourceId}`,
  async (event) => {
    const { mailingAddressSourceId } = event.params || {};
    const beforeData = event.data.before.data();
    const afterData = event.data.after.data();

    const { exclusive: exclusiveBefore } = beforeData;
    const { exclusive: exclusiveAfter } = afterData;

    const exclusivityRemoved =
      !isEmpty(exclusiveBefore) && isEmpty(exclusiveAfter);
    const mailingAddressAfterRef = event.data.after.ref;

    await collectionHistory({
      collection: `${collectionNames.mailingAddresses}`,
      id: mailingAddressSourceId,
      before: beforeData,
      after: afterData,
      action: "UPDATE",
    });

    // As one account loses exclusivity replace with next in line in waiting list
    if (exclusivityRemoved) {
      await addNextInLineExclusivity(mailingAddressAfterRef);
    }
  }
);
