const { onDocumentCreated } = require("firebase-functions/v2/firestore");

const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");

const { collectionHistory } = require("../utils/collectionHistory");

exports.onCreateWaitingList = onDocumentCreated(
  `${collectionNames.mailingAddresses}/{mailingAddressSourceId}/${subCollectionNames.mailingAddresses.waitingList}/{waitingListId}`,
  async (event) => {
    const { mailingAddressSourceId, waitingListId } = event.params || {};
    const waitingListDocRef = event.data?.ref;
    const waitingListData = event.data.data();

    await collectionHistory({
      collection: `${collectionNames.mailingAddresses}/${mailingAddressSourceId}/${subCollectionNames.mailingAddresses.waitingList}`,
      before: null,
      id: waitingListId,
      after: waitingListData,
      action: "CREATE",
      context: {
        notes: `${waitingListId} added to waiting list`,
      },
    });
  }
);
