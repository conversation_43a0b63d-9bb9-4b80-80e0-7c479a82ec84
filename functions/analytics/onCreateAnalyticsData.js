const { onDocumentCreated } = require("firebase-functions/v2/firestore");
const { Timestamp } = require("firebase-admin/firestore");
const { error } = require("console");

exports.onCreateAnalyticsData = (db) =>
  onDocumentCreated("Analytics/{analyticsId}", async (event) => {
    const analyticsId = event.params.analyticsId;
    const analyticsData = event.data.data();
    const accountId = String(analyticsData.accountId); // Ensure accountId is a string
    const landingPageId = analyticsData.landingPageId
      ? String(analyticsData.landingPageId)
      : null; // Convert if present
    const timestamp = Timestamp.fromDate(new Date());

    if (!accountId) {
      error("Account ID is missing");
      return;
    }

    // Convert both keys and values in analyticsData
    const camelCaseData = convertObjectToCamelCase(analyticsData);

    await db
      .collection("Account")
      .doc(accountId)
      .collection("Analytics")
      .doc(analyticsId)
      .set(
        {
          ...camelCaseData,
          accountId,
          ...(landingPageId ? { landingPageId } : {}),
        },
        { merge: true }
      );
  });

// Function to convert snake_case to camelCase in both keys and values
function convertObjectToCamelCase(obj) {
  if (Array.isArray(obj)) {
    return obj.map((item) => convertObjectToCamelCase(item));
  } else if (obj !== null && typeof obj === "object") {
    return Object.keys(obj).reduce((acc, key) => {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) =>
        letter.toUpperCase()
      );

      let value = obj[key];

      // Convert value if it's a string and contains snake_case
      if (typeof value === "string" && value.includes("_")) {
        value = convertStringToCamelCase(value);
      } else if (typeof value === "object") {
        value = convertObjectToCamelCase(value); // Recursively process objects
      }

      acc[camelKey] = value;
      return acc;
    }, {});
  }
  return obj;
}

// Convert snake_case strings to camelCase
function convertStringToCamelCase(str) {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}
