const { onCall } = require('firebase-functions/v2/https');

exports.massAssignToEnterprise = (db) => onCall(async (data, context) => {
  try {
    const { enterpriseId, accountIds } = data.data;

    // Validate input
    if (!enterpriseId || !Array.isArray(accountIds) || accountIds.length === 0) {
      return { message: 'Invalid input. Provide a valid enterpriseId and a non-empty array of accountIds.' };
    }

    // Fetch enterprise data
    const enterpriseRef = db.collection('Enterprise').doc(enterpriseId);
    const enterpriseSnap = await enterpriseRef.get();
    if (!enterpriseSnap.exists) {
      return { message: 'Enterprise not found' };
    }
    const enterpriseData = enterpriseSnap.data();

    const enterpriseRec = {
      label: enterpriseData.name,
      value: enterpriseId,
    };

    // Update all specified accounts
    const batch = db.batch();
    accountIds.forEach(accountId => {
      const account = accountId.toString();
      const accountRef = db.collection('Account').doc(account);
      batch.update(accountRef, { enterprise: enterpriseRec });
    });

    await batch.commit();

    return { message: 'Successfully updated accounts with enterprise information' };
  } catch (error) {
    console.error('Error assigning enterprise to accounts:', error);
    return { error: 'Error assigning enterprise to accounts', message: error.message };
  }
});
