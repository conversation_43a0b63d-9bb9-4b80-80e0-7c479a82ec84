const functions = require('firebase-functions/v2');

exports.getEnterpriseStats = (db) => functions.https.onCall(async (data, context) => {
  try {
    // Fetch the counts from Firestore using the passed db instance
    const enterpriseSnapshot = await db.collection('Enterprise').get();
    const EnterpriseUsers = await getEnterpriseUsers(db, enterpriseSnapshot);

    // Define the infographic data array
    const infographicArray = [
      { title: 'Enterprise Count', count: enterpriseSnapshot.size, icon: 'enterpriseIcon.png' },
      { title: 'Enrolled Accounts', count: EnterpriseUsers, icon: 'userProfileIcon.png' },
    ];

    // Return the data
    return { data: infographicArray };

  } catch (error) {
    console.error('Error fetching enterprise stats:', error);
    throw new functions.https.HttpsError('internal', 'Unable to fetch enterprise stats');
  }
});

async function getEnterpriseUsers(db, enterpriseSnapshot) {
  let EnterpriseUsers = 0;
  const userCounts = await Promise.all(
    enterpriseSnapshot.docs.map(async (doc) => {
      const users = await db.collection('Account').where('enterprise.value', '==', doc.id).get();
      return users.size;
    })
  );
  EnterpriseUsers = userCounts.reduce((total, count) => total + count, 0);
  return EnterpriseUsers;
}
