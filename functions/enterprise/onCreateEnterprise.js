const { onDocumentCreated, collection, doc, set, where } = require('firebase-functions/v2/firestore');

exports.onCreateEnterprise = (db) => onDocumentCreated('Enterprise/{id}', async (event) => {
  const newValue = event.data;

  async function generateUniqueRandomValue() {
    let isUnique = false;
    let randomValue;

    while (!isUnique) {
      randomValue = generateRandomValue();
      const q = db.collection('Enterprise').where('damId', '==', randomValue);
      const querySnapshot = await q.get();
      isUnique = querySnapshot.empty; 
    }
    return randomValue;
  }

  function generateRandomValue() {
    const min = Math.pow(10, 2);
    const max = Math.pow(10, 7) - 1;
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }
  const uniqueRandomValue = await generateUniqueRandomValue();
  await db.collection('Enterprise').doc(event.params.id).set({ damId: uniqueRandomValue }, { merge: true });
});

