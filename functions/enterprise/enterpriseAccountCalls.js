/* eslint-disable no-unused-vars */
/* eslint-disable no-undef */
const { onRequest } = require("firebase-functions/v2/https");
const functions = require("firebase-functions");
const dictionary = require("../constants/collectionNames");
const { isNil } = require("lodash");
const { enterpriseBreadcrumbs } = require("../utils/enterpriseBreadcrumbs");

const { collectionNames, subCollectionNames } = dictionary;

/**
 * @param {*} db
 * @returns
 */
exports.checkEnterpriseAssociation = (db) =>
  onRequest(async (req, res) => {
    if (req.method !== "GET") {
      res.status(400).send({ message: "Invalid request method" });
      return;
    }

    const { accountId } = req.query;
    const data = {};
    const damIds = [];
    // functions.logger.info("Request for: ", accountId);

    if (isNil(accountId)) {
      res.status(400).send({ message: "Bad request, accountId is required" });
      return;
    }

    try {
      (async () => {
        const accountRef = db
          .collection(collectionNames.account)
          .doc(accountId);
        const accountDoc = await accountRef.get();
        const accountData = accountDoc.data();
        data.name = accountData?.displayName;
        data.account_id = accountId;

        if (accountData?.enterprise?.value) {
          const enterpriseRef = db
            .collection(collectionNames.enterprise)
            .doc(accountData.enterprise.value);
          const enterpriseDoc = await enterpriseRef.get();

          if (enterpriseDoc.exists) {
            let enterpriseData = enterpriseDoc.data();
            const currentRestrictions = enterpriseData?.restrictions || [];
            const currentMlRestrictions = enterpriseData?.mlRestrictions || [];

            damIds.push(enterpriseData?.damId);
            const {
              breadcrumbs,
              restrictions,
              damIds: parentDam,
              mlRestrictions,
            } = await enterpriseBreadcrumbs(db, enterpriseData?.parent);

            if (restrictions.length > 0) {
              const uniqueRestrictions = new Set([
                ...restrictions,
                ...currentRestrictions,
              ]);
              restrictions.length = 0;
              restrictions.push(...uniqueRestrictions);
            } else {
              restrictions.push(...currentRestrictions);
            }

            if (mlRestrictions.length > 0) {
              const uniqueMlRestrictions = new Set([
                ...mlRestrictions,
                ...currentMlRestrictions,
              ]);
              mlRestrictions.length = 0;
              mlRestrictions.push(...uniqueMlRestrictions);
            } else {
              mlRestrictions.push(...currentMlRestrictions);
            }

            data.enterprise_id = accountData.enterprise.value || "";
            data.enterprise_name = accountData.enterprise.label || "";
            data.enterprise = enterpriseData;
            data.breadcrumbs = breadcrumbs || [];
            data.restrictions = restrictions || [];
            data.ml_restrictions = mlRestrictions || [];
            data.dam_owner_ids = [...damIds, ...parentDam];

            // Fetch approvals
            if (enterpriseData.approvalCode) {
              const approvalRef = enterpriseRef.collection(
                subCollectionNames.enterprise.approvals
              );
              const approvalsSnapshot = await approvalRef.get();
              const approvals = approvalsSnapshot.docs.map((doc) => doc.data());
              data.approvals = approvals;
            } else {
              data.approvals = []; // Ensure approvals key is present even if empty
            }
          }
        } else {
          data.enterprise_id = false;
          data.approvals = []; // Ensure approvals key is present even if empty
        }
        res.status(200).send(data);
      })();
    } catch (error) {
      res
        .status(500)
        .send({ type: "Internal Server Error", error: error.message });
    }
  });

exports.enterpriseList = (db) =>
  onRequest(async (req, res) => {
    if (req.method !== "GET") {
      res.status(400).send({ message: "Invalid request method" });
      return;
    }

    try {
      const enterpriseRef = db.collection(collectionNames.enterprise);
      const snapshot = await enterpriseRef.get();

      if (snapshot.empty) {
        res.status(404).send({ message: "No enterprises found." });
        return;
      }

      // Map documents to JSON objects with specific fields
      const enterprises = snapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          id: doc.id,
          name: data.name,
          damId: data.damId,
          shortName: data.shortName,
          parent: data.parent,
          tags: data.tags,
          description: data.description,
          photoUrl: data.photoUrl,
        };
      });

      res.status(200).json(enterprises);
    } catch (error) {
      functions.logger.error("Error fetching enterprises:", error);
      res
        .status(500)
        .send({ type: "Internal Server Error", error: error.message });
    }
  });

exports.removeFromEnterprise = (db) =>
  onRequest(async (req, res) => {
    if (req.method !== "GET") {
      res.status(400).send({ message: "Invalid request method" });
      return;
    }

    const { accountId } = req.query;

    if (!accountId) {
      res.status(400).send({ message: "Missing accountId parameter" });
      return;
    }

    try {
      const accountRef = db.collection(collectionNames.account).doc(accountId);
      const accountDoc = await accountRef.get();

      if (!accountDoc.exists) {
        res.status(404).send({ message: "Account not found" });
        return;
      }

      await accountRef.update({
        enterprise: null,
      });

      res
        .status(200)
        .send({ message: "Account removed from enterprise successfully" });
    } catch (error) {
      functions.logger.error("Error removing enterprise field:", error);
      res
        .status(500)
        .send({ type: "Internal Server Error", error: error.message });
    }
  });

exports.assignToEnterprise = (db) =>
  onRequest(async (req, res) => {
    if (req.method !== "GET") {
      res.status(400).send({ message: "Invalid request method" });
      return;
    }

    const { accountId, enterprise } = req.query;

    if (!accountId || !enterprise) {
      res
        .status(400)
        .send({ message: "Missing accountId/enterprise parameter" });
      return;
    }

    try {
      const accountRef = db.collection(collectionNames.account).doc(accountId);
      const accountDoc = await accountRef.get();

      if (!accountDoc.exists) {
        res.status(404).send({ message: "Account not found" });
        return;
      }

      const enterpriseRef = db
        .collection(collectionNames.enterprise)
        .doc(enterprise);
      const enterpriseDoc = await enterpriseRef.get();

      if (!enterpriseDoc.exists) {
        res.status(404).send({ message: "Enterprise not found" });
        return;
      }

      const enterpriseData = enterpriseDoc.data();
      const data = {
        label: enterpriseData.name,
        value: enterprise,
      };

      await accountRef.update({
        enterprise: data,
      });

      res.status(200).send(data);
    } catch (error) {
      functions.logger.error("Error assigning to enterprise:", error);
      res
        .status(500)
        .send({ type: "Internal Server Error", error: error.message });
    }
  });
