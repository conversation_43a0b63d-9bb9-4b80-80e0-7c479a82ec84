const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const admin = require("firebase-admin");
const { log } = require("firebase-functions/logger");

exports.domainUpdateWorker = (db) =>
  onMessagePublished(
    {
      topic: "domain-changes",
      retryConfig: {
        maxAttempts: 3,
        minBackoffSeconds: 10,
        maxBackoffSeconds: 300,
        maxDoublings: 5,
      },
    },
    async (event) => {
      try {
        const messageBody = event.data.message.data
          ? Buffer.from(event.data.message.data, "base64").toString()
          : null;

        if (messageBody) {
          // Parse messageBody
          let parsedBody;
          try {
            parsedBody = JSON.parse(messageBody);
          } catch (error) {
            log("Error parsing message body:", error);
            parsedBody = {};
          }

          // Save individual fields to CRMLog collection
          await db.collection("CRMLog").add({
            ...parsedBody, // Spread the parsedBody to save all key-value pairs as fields
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
          });
        }
      } catch (error) {
        log("Error processing message:", error);
      }
    }
  );
