const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const admin = require("firebase-admin");
const { log } = require("firebase-functions/logger");
const { createNotification } = require("../utils/notificationTriggers");

exports.addNotificationWorker = onMessagePublished(
  {
    topic: "create-notification",
    retryConfig: {
      maxAttempts: 3,
      minBackoffSeconds: 10,
      maxBackoffSeconds: 300,
      maxDoublings: 5,
    },
  },
  async (event) => {
    try {
      const messageBody = event.data.message.data
        ? Buffer.from(event.data.message.data, "base64").toString()
        : null;

      if (messageBody) {
        const { type, data } = JSON.parse(messageBody);

        // Process the notification based on type
        switch (type) {
          case "newLead":
            try {
              await createNotification(data);
            } catch (error) {
              log("Error adding notification to Firestore:", error);
            }

            break;
          case "newMessage":
            try {
              await createNotification(data);
            } catch (error) {
              log(
                "Error adding notification to Firestore for newMessage:",
                error
              );
            }
            break;
          case "fileReady":
            try {
              await createNotification(data);
            } catch (error) {
              log(
                "Error adding notification to Firestore for fileReady:",
                error
              );
            }
            break;
          case "recipientFileUploaded":
            try {
              await createNotification(data);
            } catch (error) {
              log(
                "Error adding notification to Firestore for fileReady:",
                error
              );
            }
            break;
          case "unformattedRecipientFileUploaded":
            try {
              await createNotification(data);
            } catch (error) {
              log(
                "Error adding notification to Firestore for fileReady:",
                error
              );
            }
            break;

          default:
        }
      }
    } catch (error) {
      log("Error processing message:", error);
    }
  }
);
