const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const { defineString } = require("firebase-functions/params");
const { logger } = require("firebase-functions");
const Papa = require("papaparse");
const { FieldValue } = require("firebase-admin/firestore");
const { addToTopic } = require("../utils/pubsub/addToTopic");
const { logToFirestore } = require("../utils/functionLogger");
const { products } = require("../constants/products");

const projectId = defineString("TITAN_PROJECT_ID");

exports.downloadRecipientsWorker = (admin) =>
  onMessagePublished(
    {
      topic: "download-recipients",
      memory: "1GiB",
      timeoutSeconds: 540,
      retryConfig: {
        maxAttempts: 3,
        minBackoffSeconds: 10,
        maxBackoffSeconds: 300,
        maxDoublings: 5,
      },
    },
    async (event) => {
      const storage = admin.storage();
      const db = admin.firestore();

      try {
        const messageBody = event.data.message.data
          ? Buffer.from(event.data.message.data, "base64").toString()
          : null;

        if (messageBody) {
          let parsedBody;
          try {
            parsedBody = JSON.parse(messageBody);
          } catch (error) {
            logger.error("Error parsing message body:", error, { messageBody });
            return;
          }

          const {
            accountId,
            fileName,
            filteredRecipients,
            fromLTM,
            recipientGroupId,
            columnVisibility,
          } = parsedBody;

          let fileRef;
          try {
            fileRef = db
              .collection("Account")
              .doc(accountId)
              .collection("Files")
              .doc();
          } catch (error) {
            logger.error("Error creating fileRef:", error, { accountId });
            return;
          }

          try {
            const bucket = storage.bucket();
            const currentDate = new Date();
            const options = {
              timeZone: "America/New_York",
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              hour12: true,
            };
            const formattedDate = currentDate.toLocaleString("en-US", options);
            const cleanedDateForFile = formattedDate
              .replace(",", "")
              .replace(/:/g, "-")
              .replace(/\s/g, "_");
            const cleanedDateForDisplay = formattedDate
              .replace(",", "")
              .replace(/\s/g, "_");

            const filePath = `files/${accountId}/recipients/${fileName}.csv`;
            const file = bucket.file(filePath);

            await fileRef.set({
              downloadStarted: FieldValue.serverTimestamp(),
              fileName: `${fileName}_${cleanedDateForDisplay}.csv`,
              recipientGroupId: recipientGroupId || "",
              status: "started",
              type: "export",
            });

            const csvStream = file.createWriteStream({
              metadata: { contentType: "text/csv" },
            });
            csvStream.on("error", (error) => {
              logger.error("Error in CSV stream:", error);
            });

            const batchSize = 30;
            const chunkArray = (array, size) => {
              const result = [];
              for (let i = 0; i < array.length; i += size) {
                result.push(array.slice(i, i + size));
              }
              return result;
            };

            if (!filteredRecipients || filteredRecipients.length === 0) {
              return;
            }

            const recipientBatches = chunkArray(filteredRecipients, batchSize);
            let headersWritten = false; // Flag to ensure headers are written only once

            for (const batch of recipientBatches) {
              let query;
              try {
                query = db
                  .collection("Account")
                  .doc(accountId)
                  .collection("Recipients");

                if (batch.length > 0) {
                  query = query.where(
                    admin.firestore.FieldPath.documentId(),
                    "in",
                    batch
                  );
                }
              } catch (error) {
                logger.error(
                  "Error creating query for Recipients collection:",
                  error,
                  { accountId, batch }
                );
                break;
              }

              let recipientsSnapshot;
              try {
                recipientsSnapshot = await query.get();
              } catch (error) {
                logger.error("Error querying Recipients collection:", error, {
                  accountId,
                  batch,
                });
                break;
              }

              if (recipientsSnapshot.empty) {
                break;
              }

              const currentBatch = recipientsSnapshot.docs.map((doc) => ({
                id: doc.id,
                ...doc.data(),
              }));

              try {
                const transformedRecipientData = await transformRecipientsData({
                  accountId,
                  recipients: currentBatch,
                  db,
                  filteredRecipients,
                  fromLTM,
                  columnVisibility,
                });

                // Only add headers for the first batch
                const csv = headersWritten
                  ? Papa.unparse(transformedRecipientData, { header: false })
                  : Papa.unparse(transformedRecipientData, { header: true });

                headersWritten = true; // Set flag after headers are written once

                csvStream.write(csv + "\n", (error) => {
                  if (error) {
                    logger.error("Error writing to CSV stream:", error);
                  }
                });
              } catch (error) {
                logger.error("Error processing batch:", error);
              }

              if (global.gc) {
                global.gc();
              }
            }

            csvStream.end();

            await db
              .collection("Account")
              .doc(accountId)
              .collection("Files")
              .doc(fileRef.id)
              .update({
                downloadFinished: FieldValue.serverTimestamp(),
                filePath: filePath,
                status: "finished",
              });

            let groupData;
            if (recipientGroupId) {
              try {
                const groupName = await db
                  .collection("Account")
                  .doc(accountId)
                  .collection("RecipientGroups")
                  .doc(recipientGroupId)
                  .get();

                if (!groupName.exists) {
                  throw new Error(
                    `RecipientGroup with ID ${recipientGroupId} does not exist.`
                  );
                }
                groupData = groupName.data();
              } catch (error) {
                logger.error(
                  "Error retrieving RecipientGroup document:",
                  error,
                  { accountId, recipientGroupId }
                );
              }
            }

            const data = {
              accountId,
              url: filePath,
              type: "fileReady",
              isRead: false,
              title: `File ready for download`,
              body: `Your export of ${
                groupData?.name ? groupData?.name : "All Recipients"
              } is ready. Click here to download.`,
            };

            try {
              const queueItem = {
                topic: "create-notification",
                projectId: projectId.value(),
                message: JSON.stringify({ type: "fileReady", data }),
              };
              await addToTopic(queueItem);
            } catch (error) {
              logger.error("Error adding to topic for notification:", error);
            }
          } catch (error) {
            await fileRef.set({
              status: "error",
              error: error.message,
            });
            logger.error("Error during CSV generation:", error);
          }
        }
      } catch (error) {
        logger.error("Error processing message:", error);
      }
    }
  );

// Function to transform recipient data

async function transformRecipientsData({
  accountId,
  recipients,
  db,
  filteredRecipients,
  fromLTM,
  columnVisibility,
}) {
  const transformedRecipientData = [];

  for (const recipient of recipients) {
    try {
      const [
        phoneNumbers,
        mailingAddress,
        emailAddresses,
        userEnteredMailingAddress,
        followUpStatus,
        groupData,
        productPlans,
      ] = await Promise.all([
        getPhoneNumbers(db, accountId, recipient.id),
        getMailingAddress(db, recipient),
        getEmailAddresses(db, accountId, recipient.id),
        getRecipientsUserEnteredMailingAddress(db, accountId, recipient.id),
        getLastFollowUpStatus(db, accountId, recipient.id),
        getRecipientGroupData(db, accountId, recipient.recipientGroupIds[0]),
        getProductPlansForRecipient(
          db,
          accountId,
          recipient.recipientGroupIds[0]
        ),
      ]);

      const record = {};
      // Only add fields if they are visible
      if (columnVisibility) {
        record["First Name"] = recipient?.name?.firstName ?? "";
        record["Last Name"] = recipient?.name?.lastName ?? "";
        record["SO First Name"] = recipient?.significantOther?.firstName ?? "";
        record["SO Last Name"] = recipient?.significantOther?.lastName ?? "";
        if (columnVisibility?.["address"]) {
          record["Address 1"] =
            mailingAddress?.address1 ??
            userEnteredMailingAddress?.address1 ??
            "";
          record["City"] =
            mailingAddress?.city ?? userEnteredMailingAddress?.city ?? "";
          record["State"] =
            mailingAddress?.state ?? userEnteredMailingAddress?.state ?? "";
          record["Zip Code"] =
            mailingAddress?.postalCode ??
            userEnteredMailingAddress?.postalCode ??
            "";
        }
        if (columnVisibility?.["email"]) {
          record["Primary Email"] = emailAddresses[0]?.email ?? "";
        }
        record["Recipient Group"] = groupData?.name ?? "";
        if (columnVisibility?.["score"]) {
          record["Likely to Move Score"] =
            recipient?.avm_score && recipient?.avm_score != -1
              ? recipient?.avm_score
              : "N/A";
        }
        if (columnVisibility?.["score"]) {
          record["Last Status"] = followUpStatus
            ? followUpStatus.display
            : "N/A";
        }
        record["Phone Number"] = phoneNumbers[0]?.phoneNumber ?? "";

        transformedRecipientData.push(record);
      } else {
        const record = {
          "First Name": recipient?.name?.firstName ?? "",
          "Last Name": recipient?.name?.lastName ?? "",
          "SO First Name": recipient?.significantOther?.firstName ?? "",
          "SO Last Name": recipient?.significantOther?.lastName ?? "",
          "Address 1":
            mailingAddress?.address1 ??
            userEnteredMailingAddress?.address1 ??
            "",
          City: mailingAddress?.city ?? userEnteredMailingAddress?.city ?? "",
          State:
            mailingAddress?.state ?? userEnteredMailingAddress?.state ?? "",
          "Zip Code":
            mailingAddress?.postalCode ??
            userEnteredMailingAddress?.postalCode ??
            "",
          "Primary Email": emailAddresses[0]?.email ?? "",
          "Recipient Group": groupData?.name ?? "",
          "Likely to Move Score":
            recipient?.avm_score && recipient?.avm_score != -1
              ? recipient?.avm_score
              : "N/A",
          "Last Status": followUpStatus ? followUpStatus.display : "N/A",
          "Phone Number": phoneNumbers[0]?.phoneNumber ?? "", // Only the first phone number
        };
        transformedRecipientData.push(record);
      }
    } catch (error) {
      logger.error("Error transforming recipient:", recipient.id, error);
    }
  }

  return transformedRecipientData;
}

// Helper function to get mailing address of a recipient
async function getMailingAddress(db, recipient) {
  if (!recipient.mailingAddresses || recipient.mailingAddresses.length === 0) {
    return null;
  }

  const mailingAddressId = recipient.mailingAddresses[0]?.id;
  if (!mailingAddressId) {
    return null;
  }

  try {
    const mailingAddressSnapshot = await db
      .collection("MailingAddresses")
      .doc(mailingAddressId)
      .get();

    return mailingAddressSnapshot.exists ? mailingAddressSnapshot.data() : null;
  } catch (error) {
    logger.error("Error fetching mailing address:", error, {
      recipientId: recipient.id,
      mailingAddressId,
    });
    return null;
  }
}

// Helper function to get phone numbers for a recipient
async function getPhoneNumbers(db, accountId, recipientId) {
  try {
    const phoneNumbersSnapshot = await db
      .collection("Account")
      .doc(accountId)
      .collection("Recipients")
      .doc(recipientId)
      .collection("PhoneNumbers")
      .select("phoneNumber") // Only fetch the phone number field
      .get();

    const phoneNumbers = phoneNumbersSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    return phoneNumbers;
  } catch (error) {
    logger.error("Error fetching phone numbers:", error, {
      accountId,
      recipientId,
    });
    return [];
  }
}

// Helper function to get email addresses for a recipient
async function getEmailAddresses(db, accountId, recipientId) {
  try {
    const emailAddressesSnapshot = await db
      .collection("Account")
      .doc(accountId)
      .collection("Recipients")
      .doc(recipientId)
      .collection("EmailAddresses")
      .get();

    const emailAddresses = emailAddressesSnapshot.docs.map((doc) => doc.data());

    return emailAddresses;
  } catch (error) {
    logger.error("Error fetching email addresses:", error, {
      accountId,
      recipientId,
    });
    return [];
  }
}

async function getRecipientsUserEnteredMailingAddress(
  db,
  accountId,
  recipientId
) {
  try {
    const userEnteredMailingAddressSnapshot = await db
      .collection("Account")
      .doc(accountId)
      .collection("Recipients")
      .doc(recipientId)
      .collection("UserEnteredMailingAddress")
      .doc("0")
      .get();

    // Return the data if document exists, otherwise null or empty object
    return userEnteredMailingAddressSnapshot.exists
      ? userEnteredMailingAddressSnapshot.data()
      : null;
  } catch (error) {
    await logToFirestore({
      functionName: "downloadRecipientsWorker",
      message: "Error fetching user entered mailing address",
      type: "error",
      data: {
        accountId,
        recipientId,
        error,
      },
    });
    return null; // Or return a consistent value with the try block
  }
}
// Helper function to get the last follow-up status for a recipient
async function getLastFollowUpStatus(db, accountId, recipientId) {
  try {
    const followUpStatusSnapshot = await db
      .collection("Account")
      .doc(accountId)
      .collection("Recipients")
      .doc(recipientId)
      .collection("FollowUpStatusHistory")
      .orderBy("updatedAt", "desc")
      .limit(1)
      .get();

    if (followUpStatusSnapshot.empty) {
      return null;
    }

    return followUpStatusSnapshot.docs[0].data();
  } catch (error) {
    logger.error("Error fetching follow-up status history:", error, {
      accountId,
      recipientId,
    });
    return null;
  }
}

// Helper function to get product plans for a recipient group
async function getProductPlansForRecipient(db, accountId, recipientGroupId) {
  if (!recipientGroupId) {
    return [];
  }

  try {
    const recipientGroupSnapshot = await db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .doc(recipientGroupId)
      .get();

    if (!recipientGroupSnapshot.exists) {
      return [];
    }

    const productPlanNumbers = recipientGroupSnapshot.data().productPlans;

    if (!productPlanNumbers || productPlanNumbers.length === 0) {
      return [];
    }

    const productPlans = products.filter((product) =>
      productPlanNumbers.includes(product.plan_id)
    );

    return productPlans;
  } catch (error) {
    logger.error("Error fetching recipient group for product plans:", error, {
      accountId,
      recipientGroupId,
    });
    return [];
  }
}

// Helper function to get recipient group data for a specific recipient group
async function getRecipientGroupData(db, accountId, recipientGroupId) {
  if (!recipientGroupId) {
    return null; // Return null if no recipientGroupId is provided
  }

  try {
    const recipientGroupSnapshot = await db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .doc(recipientGroupId)
      .get();

    if (!recipientGroupSnapshot.exists) {
      return null; // Return null if the recipient group does not exist
    }

    return recipientGroupSnapshot.data(); // Return recipient group data if it exists
  } catch (error) {
    logger.error("Error fetching recipient group data:", error, {
      accountId,
      recipientGroupId,
    });
    return null; // Return null if there’s an error
  }
}
