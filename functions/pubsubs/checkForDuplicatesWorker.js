const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const { log } = require("firebase-functions/logger");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { completeDuplicateCheck, failDuplicateCheck } = require("../utils/calculate/duplicateCheck");

/**
 * Cloud Function that checks for duplicate email addresses and mailing addresses
 * within a specific account's recipients collection, and marks records accordingly.
 * 
 * This function processes recipients in batches for efficient memory usage and
 * publishes results to a new topic for further processing if needed.
 * 
 * @param {FirebaseFirestore.Firestore} db - Firestore database instance
 * @returns {Function} - Firebase Cloud Function
 */
exports.checkForDuplicatesWorker = (db) =>
  onMessagePublished(
    {
      topic: "check-duplicates",
      memory: "256MB",
      timeoutSeconds: 540,
      concurrency: 5,
    },
    async (event) => {
      let accountId = "0";
      try {
        // Decode the message from base64 if it exists
        const messageBody = event.data.message.data
          ? Buffer.from(event.data.message.data, "base64").toString()
          : null;

        if (messageBody) {
          let parsedBody;
          try {
            parsedBody = JSON.parse(messageBody);
          } catch (error) {
            log("Error parsing message body:", error);
            parsedBody = {};
          }
          // Ensure we have an accountId to process
          if (!parsedBody.accountId) {
            log("No accountId provided in message body");
            return;
          }

          accountId = parsedBody.accountId.toString();
          // Process the duplicate check for this account
          await checkForDuplicatesAndMark(db, parsedBody.accountId.toString(), parsedBody);
          completeDuplicateCheck({ id: `${parsedBody.accountId}-DuplicateCheck` });
        }
      } catch (error) {
        log("Error processing duplicate check message:", error);
        failDuplicateCheck({
          id: `${accountId}-DuplicateCheck`,
          message: error.message,
        });
      }
    }
  );

/**
 * Checks for duplicate email addresses and mailing addresses among recipients,
 * and marks them accordingly in the database.
 * 
 * This function supports pagination through the lastDocumentId parameter,
 * allowing for processing large collections efficiently.
 * 
 * @param {FirebaseFirestore.Firestore} db - Firestore database instance
 * @param {string} accountId - The account ID to check recipients for
 * @param {Object} options - Additional options for processing
 * @param {string} [options.lastDocumentId] - ID of the last document processed for pagination
 * @param {number} [options.pageSize=200] - Number of documents to process in each batch
 * @returns {Promise<void>}
 */
async function checkForDuplicatesAndMark(db, accountId, options = {}) {
  try {
    // Get reference to the recipients collection for this account
    const baseRecipients = db
      .collection(collectionNames.account)
      .doc(accountId)
      .collection(subCollectionNames.contacts.recipients);

    // Use default pageSize of 200 if not specified
    const pageSize = options.pageSize || 200;
    
    // If this is the first page (no lastDocumentId), clear all duplicate flags
    if (!options.lastDocumentId) {
      log(`Clearing existing duplicate flags for account ${accountId}`);
      const snapshot = await baseRecipients.get();

      // Use batched writes for efficiency
      const clearBatch = db.batch();
      snapshot.forEach((doc) => {
        clearBatch.update(doc.ref, { duplicate: {} });
      });

      await clearBatch.commit();
      log(`Cleared duplicate flags for ${snapshot.size} recipients`);
    }

    // Query only active recipients for duplicate checking
    const collectionRef = baseRecipients.where("isActive", "==", true);

    // Set up pagination
    let query = collectionRef.orderBy("__name__").limit(pageSize);
    if (options.lastDocumentId) {
      const lastDocRef = baseRecipients.doc(options.lastDocumentId);
      const lastDocSnapshot = await lastDocRef.get();
      if (lastDocSnapshot.exists) {
        query = query.startAfter(lastDocSnapshot);
      }
    }

    // Execute the query
    const snapshot = await query.get();
    
    if (snapshot.empty) {
      return;
    }

    // Maps to track unique email addresses and mailing addresses
    const emailValueMap = {};
    const mailingValueMap = {};
    const docDuplicatesMap = {}; // Stores duplicate info for documents

    // Process each document in the current batch
    snapshot.forEach((doc) => {
      const data = doc.data();
      const docRef = doc.ref;
      const docId = doc.id;

      // Normalize and extract email and mailing address
      // Proper normalization is crucial for accurate duplicate detection
      const emailAddress = data?.searchTags?.primaryEmail?.toLowerCase().trim() || "";
      
      // Format mailing address consistently, removing extra spaces and zip+4 extension
      const mailingAddress = data?.searchTags?.formattedMailingAddress
        ?.toLowerCase()
        .trim()
        .replace(/-0000$/, "") || "";

      // Initialize duplicate information for this document
      if (!docDuplicatesMap[docId]) {
        docDuplicatesMap[docId] = { docRef, duplicate: {} };
      }
      const docDuplicate = docDuplicatesMap[docId].duplicate;

      // Check for email duplicates
      if (emailAddress) {
        if (emailValueMap[emailAddress]) {
          // This is a duplicate email
          docDuplicate.email = true;
          
          // Mark the original document as having a duplicate email too
          const originalDocRef = emailValueMap[emailAddress];
          const originalDocId = originalDocRef.id;
          if (!docDuplicatesMap[originalDocId]) {
            docDuplicatesMap[originalDocId] = {
              docRef: originalDocRef,
              duplicate: { email: true },
            };
          } else {
            docDuplicatesMap[originalDocId].duplicate.email = true;
          }
        } else {
          // This is the first occurrence of this email
          emailValueMap[emailAddress] = docRef;
        }
      }

      // Check for mailing address duplicates
      if (mailingAddress) {
        if (mailingValueMap[mailingAddress]) {
          // This is a duplicate mailing address
          docDuplicate.mailingAddress = true;
          
          // Mark the original document as having a duplicate mailing address too
          const originalDocRef = mailingValueMap[mailingAddress];
          const originalDocId = originalDocRef.id;
          if (!docDuplicatesMap[originalDocId]) {
            docDuplicatesMap[originalDocId] = {
              docRef: originalDocRef,
              duplicate: { mailingAddress: true },
            };
          } else {
            docDuplicatesMap[originalDocId].duplicate.mailingAddress = true;
          }
        } else {
          // This is the first occurrence of this mailing address
          mailingValueMap[mailingAddress] = docRef;
        }
      }
    });

    // Get the last document in this batch for pagination
    const lastDocument = snapshot.docs[snapshot.docs.length - 1];
    const lastDocumentId = lastDocument?.id;

    // Update the documents with duplicate information
    const batchUpdates = [];
    let batch = db.batch();
    let operationCount = 0;
    const batchSize = 500; // Maximum operations per batch in Firestore

    // Apply updates in batches of 500 (Firestore limit)
    for (const docId in docDuplicatesMap) {
      const { docRef, duplicate } = docDuplicatesMap[docId];
      batch.update(docRef, { duplicate });
      operationCount++;

      if (operationCount >= batchSize) {
        batchUpdates.push(batch.commit());
        batch = db.batch();
        operationCount = 0;
      }
    }

    // Commit any remaining operations
    if (operationCount > 0) {
      batchUpdates.push(batch.commit());
    }

    // Wait for all batch updates to complete
    await Promise.all(batchUpdates);
    
    // Log progress
    log(`Processed ${snapshot.size} recipients for duplicates in account ${accountId}`);
    log(`Found ${Object.keys(emailValueMap).length} unique emails and ${Object.keys(mailingValueMap).length} unique addresses`);
    
    // Determine if we need to continue with another batch
    const hasMoreToProcess = snapshot.size === pageSize;
    
    if (hasMoreToProcess && lastDocumentId) {
      // Schedule the next batch by publishing a message back to this topic
      const { addToTopic } = require("../utils/pubsub/addToTopic.js");
      await addToTopic({
        topic: "check-duplicates",
        message: JSON.stringify({
          accountId,
          lastDocumentId,
          pageSize,
          timestamp: new Date().toISOString()
        }),
      });
      log(`Scheduled next batch starting after document ${lastDocumentId}`);
    } else {
      log(`Duplicate check completed for account ${accountId}`);
      
      // Optionally, you can publish a completion message to another topic
      // for any follow-up processing that needs to occur
      const { addToTopic } = require("../utils/pubsub/addToTopic.js");
      await addToTopic({
        topic: "duplicate-check-completed",
        message: JSON.stringify({
          accountId,
          timestamp: new Date().toISOString()
        }),
      });
    }
  } catch (error) {
    log("Error checking for duplicates:", error);
    throw error;
  }
}