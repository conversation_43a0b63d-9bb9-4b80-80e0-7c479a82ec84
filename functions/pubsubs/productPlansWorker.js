const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const { Timestamp } = require("firebase-admin/firestore");
const admin = require("firebase-admin");
const { logger } = require("firebase-functions");
const {
  subCollectionNames,
  collectionNames,
} = require("../constants/collectionNames");
const { createUUID } = require("../utils/createUUID");

exports.productPlansWorker = (db) =>
  onMessagePublished(
    {
      topic: "product-plan-added",
      retryConfig: {
        maxAttempts: 3,
        minBackoffSeconds: 10,
        maxBackoffSeconds: 300,
        maxDoublings: 5,
      },
    },
    async (event) => {
      await new Promise((resolve) => setTimeout(resolve, 30000));
      try {
        const messageBody = event.data.message.data
          ? Buffer.from(event.data.message.data, "base64").toString()
          : null;

        if (messageBody) {
          // Parse messageBody
          let parsedBody;
          try {
            parsedBody = JSON.parse(messageBody);
          } catch (error) {
            logger.log("Error parsing message body:", error);
            parsedBody = {};
            return;
          }

          // Check number of RecipientGroups
          // Add Default Groups if there are none
          const { accountId } = parsedBody;

          const accountRef = db
            .collection(collectionNames.account)
            .doc(accountId);

          await createDefaultRecipientGroups({ accountRef });
        }
      } catch (error) {
        logger.log("Error processing message:", error);
      }
    }
  );

async function createDefaultRecipientGroups({ accountRef }) {
  const recipientGroupCollectionRef = accountRef.collection(
    subCollectionNames.contacts.recipientGroups
  );

  const recipientGroupQuerySnapshot = await recipientGroupCollectionRef
    .count()
    .get();

  const recipientGroupCount = recipientGroupQuerySnapshot.data().count;
  if (recipientGroupCount === 0) {
    await addDefaultRecipientGroups(recipientGroupCollectionRef);
  }
}

async function addDefaultRecipientGroups(recipientGroupCollectionRef) {
  const categoriesMap = new Map([
    {
      name: "Sphere",
      description:
        "Your sphere of influence is all the people you know professionally and personally that present an opportunity for word of mouth marketing, a referral, or direct business. We recommend sending them a Branded Magazine each issue and putting them on an email drip.",
    },
    {
      name: "Prospects",
      description:
        "These are your leads and people that have shown interest in your services. Whether you are actively working these leads, make sure to stay in contact with them regularly with a postcard or email marketing.",
    },
    {
      name: "Current Clients",
      description:
        "This is anyone that you are currently working with. Send them a Branded Magazine to let them know how much you appreciate their business!",
    },
    {
      name: "Past Clients",
      description:
        "Include everyone that you've worked with in the past to remind them of your services and brand. They should be a great resource for repeat business and are most likely to refer you.",
    },
    {
      name: "Geographic Farm",
      description:
        "This list should contain the people in the communities that you are prospecting. This is an ideal list to send a monthly postcard.",
    },
    {
      name: "No Follow-Up Needed",
      description:
        "This group contains people that you do not currently need to follow up with and aren't receiving any marketing from you.",
    },
  ]);

  for (const cats of categoriesMap) {
    try {
      await recipientGroupCollectionRef.doc(createUUID()).set({
        name: cats.name,
        description: cats.description,
        isActive: true,
        createdAt: Timestamp.now(),
      });
    } catch (error) {
      logger.error("Problem adding default recipient groups", error);
    }
  }
}
