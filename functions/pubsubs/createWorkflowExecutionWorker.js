const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const { FieldValue } = require("firebase-admin/firestore");
const admin = require('firebase-admin');

exports.createWorkflowExecutionWorker = (db) => onMessagePublished(
  {
    topic: "workflow-executions",
    retryConfig: {
      maxAttempts: 10, // Maximum number of retry attempts
      minBackoffSeconds: 10, // Minimum time to wait before retrying
      maxBackoffSeconds: 300, // Maximum time to wait before retrying
      maxDoublings: 5, // Maximum number of times to double the backoff time
    },
  },
  async (event) => {
    try {
      console.log("starting");
      const message = event.data.message;
      const messageBody = message.data
        ? Buffer.from(message.data, "base64").toString()
        : null;

      if (messageBody !== null) {
        console.log("New message on workflow-executions topic:", messageBody);

        const messageJson = JSON.parse(messageBody);

        const { accountId, leadId, workflowId } = messageJson;

        const workflow = await prepareWorkflow(db, accountId, leadId, workflowId);

        const execution = await admin.firestore().collection("WorkflowExecutions").add({
          ...workflow,
          createdAt: FieldValue.serverTimestamp()
        });

        console.log(`Workflow Execution ${execution.id} Created successfully.`);
      } else {
        console.log("New message on workflow-executions topic with an empty message");
      }
    } catch (error) {
      console.error("Error processing workflow-executions queue:", error.message);
      throw new Error("Error processing workflow-executions queue: " + error.message);
    }
  }
);

async function prepareWorkflow(db, accountId, leadId, workflowId) {
  const workflowRef = db.collection(`Account`).doc(accountId).collection(`Workflows`).doc(workflowId);
  const workflowDoc = await workflowRef.get();
  const workflow = workflowDoc.data();

  let tempTime = new Date();
  const workflowActions = workflow.actions.map(action => {
    tempTime = new Date(tempTime.getTime() + action.delay * 60 * 1000)
    return {
      ...action,
      status: "PENDING",
      executeOn: tempTime
    }
  });

  delete workflow.id;
  delete workflow.docId;

  return {
    accountId,
    leadId,
    workflowId,
    ...workflow,
    actions: workflowActions,
    nextActionExecuteOn: workflowActions[0].executeOn,
    status: "IN_PROGRESS",
    createdAt: FieldValue.serverTimestamp(),
  }
}