const { onMessagePublished } = require("firebase-functions/v2/pubsub");

const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { reversePhoneLookup } = require("../utils/reversePhoneSearch");
const { logToFirestore } = require("../utils/functionLogger");

exports.searchByPhoneWorker = (db) =>
  onMessagePublished(
    {
      topic: "search-by-phone",
      retryConfig: {
        maxAttempts: 3,
        minBackoffSeconds: 10,
        maxBackoffSeconds: 300,
        maxDoublings: 5,
      },
      concurrency: 20,
    },
    async (event) => {
      try {
        const messageBody = event.data.message.data
          ? Buffer.from(event.data.message.data, "base64").toString()
          : null;

        if (messageBody) {
          let parsedBody;
          try {
            parsedBody = JSON.parse(messageBody);
          } catch (error) {
            await logToFirestore({
              functionName: "searchByPhoneWorker",
              type: "error",
              message: `Error parsing message body for ${parsedBody?.accountId} & ${parsedBody?.recipientGroupId} by ${parsedBody?.userId}`,
              data: { error: error.message, errorStack: error.stack },
            });
            return;
          }

          const { recipientGroupId, accountId, recipientIds, userId } =
            parsedBody;

          await logToFirestore({
            functionName: "searchByPhoneWorker",
            type: "info",
            message: `Invocation for ${accountId} & ${recipientGroupId} with ${recipientIds.length} recipient(s) by ${userId}`,
            data: {},
          });

          // Mark the beginning of processing — you can manage state here if needed.
          const recipientGroupRef = db
            .collection(collectionNames.account)
            .doc(accountId)
            .collection(subCollectionNames.contacts.recipientGroups)
            .doc(recipientGroupId);

          // Process each recipient from this batch
          for (const recipientId of recipientIds) {
            try {
              const recipientRef = db
                .collection(collectionNames.account)
                .doc(accountId)
                .collection(subCollectionNames.contacts.recipients)
                .doc(recipientId);

              // … continue with your existing logic (fetch phoneNumbers, address, etc.)
              const phoneNumbers = await recipientRef
                .collection(subCollectionNames.contacts.phoneNumbers)
                .get();
              const isPhoneFound = phoneNumbers.size > 0;
              const addresses = await recipientRef
                .collection(
                  subCollectionNames.contacts.userEnteredMailingAddress
                )
                .get();
              const isAddressFound = addresses.size > 0;

              if (isPhoneFound && !isAddressFound) {
                const theNumber =
                  phoneNumbers.size > 0 ? phoneNumbers.docs[0].data() : null;
                if (theNumber && theNumber.phoneNumber) {
                  const validatedNumber = validateUSPhoneNumber(
                    theNumber.phoneNumber
                  );

                  if (validatedNumber) {
                    const foundAddress =
                      await reversePhoneLookup(validatedNumber);
                    if (foundAddress && foundAddress.street_line_1) {
                      const postalCode = `${foundAddress.postal_code}`;
                      const newAddress = {
                        address1: foundAddress.street_line_1 || "",
                        address2: foundAddress.street_line_2 || "",
                        city: foundAddress.city || "",
                        state: foundAddress.state_code || "",
                        postalCode: postalCode || "",
                        processingStatus: "REPROCESS",
                      };
                      await recipientRef
                        .collection(
                          subCollectionNames.contacts.userEnteredMailingAddress
                        )
                        .doc("0")
                        .set(newAddress);
                    }
                  }
                }
              }
            } catch (error) {
              await logToFirestore({
                functionName: "searchByPhoneWorker",
                type: "error",
                message: `Error processing ${accountId} & ${recipientGroupId}`,
                data: {
                  error: error.message,
                  errorStack: error.stack,
                },
              });
            }
          }

          // transactionally update the recipient group and decrement the batch jobs remaining
          await db.runTransaction(async (transaction) => {
            const groupDoc = await transaction.get(recipientGroupRef);
            const currentCount = groupDoc.data().batchJobsRemaining || 0;
            const newCount = currentCount - 1;

            const updateData = { batchJobsRemaining: newCount };
            if (newCount === 0) {
              updateData.isSearchingByPhone = false;
            }

            transaction.update(recipientGroupRef, updateData);
            await logToFirestore({
              functionName: "searchByPhoneWorker",
              type: "info",
              message: `Decremented batch jobs remaining for ${accountId} & ${recipientGroupId}`,
              data: {
                accountId,
                recipientGroupId,
                batchJobsRemaining: newCount,
              },
            });
          });
        }
      } catch (error) {
        await logToFirestore({
          functionName: "searchByPhoneWorker",
          type: "error",
          message: `Error processing searchByPhoneWorker `,
          data: { error: error.message, errorStack: error.stack },
        });
      }
    }
  );

function validateUSPhoneNumber(phoneNumber) {
  const cleaned = phoneNumber.replace(/[^\d+]/g, "");
  if (/^\+1[2-9]\d{9}$/.test(cleaned)) {
    return cleaned;
  }
  const numericOnly = cleaned.replace(/\D/g, "");
  if (/^1[2-9]\d{9}$/.test(numericOnly)) {
    return `+${numericOnly}`;
  }
  const isValidUSNumber = /^[2-9]\d{9}$/.test(numericOnly);
  if (isValidUSNumber) {
    return `+1${numericOnly}`;
  }
  return false;
}
