const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const { log } = require("firebase-functions/logger");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const {
  completeRecipientGroupCalc,
  failRecipientGroupCalc,
} = require("../utils/calculate/recipientGroupCalc");
const { has } = require("lodash");

/**
 * Cloud Function that calculates metrics for recipient groups.
 * This function is triggered by Pub/Sub messages on the "calculate-recipient-group" topic.
 * After processing, it triggers a duplicate check for the account.
 *
 * @param {FirebaseFirestore.Firestore} db - Firestore database instance
 * @returns {Function} - Firebase Cloud Function
 */
exports.calculateRGWorker = (db) =>
  onMessagePublished(
    {
      topic: "calculate-recipient-group",
      memory: "256MB",
      timeoutSeconds: 540,
    },
    async (event) => {
      let accountId = "0";
      let recipientGroupId = "0";
      let docId = null;
      let parsedBody = {};

      try {
        // Decode the message from base64 if it exists
        const messageBody = event.data.message.data
          ? Buffer.from(event.data.message.data, "base64").toString()
          : null;

        if (messageBody) {
          try {
            parsedBody = JSON.parse(messageBody);
          } catch (error) {
            log("Error parsing message body:", error);
            parsedBody = {};
          }
          accountId = parsedBody.accountId;
          recipientGroupId = parsedBody.recipientGroupId;
          docId = `${accountId}-${recipientGroupId}-recipientGroup`;
        }
      } catch (error) {
        await failRecipientGroupCalc({
          id: docId,
          message: "Parsed Body is empty",
        });
        log("Error processing message:", error);
      }
      // Process a specific recipient group
      try {
        const runValue = await processGroup(db, parsedBody);
        if (runValue && runValue.error) {
          if (docId) {
            await failRecipientGroupCalc({
              id: docId,
              message: runValue.error,
            });
          }
        }
        await completeRecipientGroupCalc({ id: docId });
      } catch (error) {
        await failRecipientGroupCalc({ id: docId, message: error.message });
        log("Error processing group", error);
      }
    }
  );

/**
 * Processes a recipient group by calculating various metrics and updating the group metadata.
 *
 * @param {FirebaseFirestore.Firestore} db - Firestore database instance
 * @param {Object} parsedBody - The parsed message body containing accountId and recipientGroupId
 * @returns {Promise<void>}
 */
async function processGroup(db, parsedBody) {
  try {
    // Get reference to the recipient group document
    const recipientGroupRef = db
      .collection(collectionNames.account)
      .doc(parsedBody.accountId.toString())
      .collection(subCollectionNames.contacts.recipientGroups)
      .doc(parsedBody.recipientGroupId);

    const recipientGroupSnapshot = await recipientGroupRef.get();
    const recipientGroupData = recipientGroupSnapshot.data();

    // Exit early if the group doesn't exist or has no assignments
    if (!recipientGroupData) {
      return true;
    }
    if (
      !recipientGroupData.assignments ||
      Object.keys(recipientGroupData.assignments).length === 0
    ) {
      return true;
    }

    if (recipientGroupData?.name) {
      // Query all recipients belonging to this group
      const recipientsRef = db
        .collection(collectionNames.account)
        .doc(parsedBody.accountId.toString())
        .collection(subCollectionNames.contacts.recipients)
        .where(
          "recipientGroupIds",
          "array-contains",
          parsedBody.recipientGroupId
        );

      const recipientSnapshot = await recipientsRef.get();
      const activeRecipients = recipientSnapshot.docs.filter(
        (recipient) => recipient.data().isActive
      );

      const hasAddress = activeRecipients.filter(
        (recipient) => recipient.data()?.isActive
      );

      // Filter recipients by various criteria
      const willSendAddress = activeRecipients.filter(
        (recipient) => recipient.data()?.addressDeliverability?.code === "WS"
      );
      const pausedAddress = activeRecipients.filter(
        (recipient) => recipient.data()?.mailingsPaused === true
      );
      const badAddress = activeRecipients.filter(
        (recipient) => recipient.data()?.addressDeliverability?.code === "IA"
      );
      const waitingList = activeRecipients.filter(
        (recipient) => (recipient.data()?.addressDeliverability?.code === "WL" || recipient.data()?.addressDeliverability?.code === "NE")
      );
      const sendAnyway = activeRecipients.filter(
        (recipient) =>
          recipient.data().overrideDeliverability?.address === true
      );
      const validEmail = activeRecipients.filter(
        (recipient) =>
          recipient.data()?.emailDeliverability?.isValidEmailAddress
      );
      const badEmail = activeRecipients.filter(
        (recipient) =>
          !recipient.data()?.emailDeliverability?.isValidEmailAddress
      );
      
      const deliverAnyway = activeRecipients.filter(
        (recipient) =>
          recipient.data().overrideDeliverability?.email === true
      );

      //Not Viable Mailing Address calculated by Total Active Recipients - hasAddress
      const notViableMailingAddress = activeRecipients.length - hasAddress.length;

      // Calculate the total number of recipients that will not receive mail - Waiting List, Bad Address, Paused Address and Not Viable Mailing Address
      const totalNotSending = (waitingList.length + badAddress.length + pausedAddress.length + notViableMailingAddress) - sendAnyway.length;

      //Calculate total sending recipients Will Send Address + Send Anyway
      const totalSending = activeRecipients.length;


      // Calculate metadata for the group
      let data = {
        activeMembers: activeRecipients.length,        
        addressWillSend: totalSending - totalNotSending,
        totalSending: totalSending,
        totalNotSending: totalNotSending,
        notViableMailingAddress: notViableMailingAddress,
        addressPaused: pausedAddress.length,
        addressBad: badAddress.length,
        sendAnyway: sendAnyway.length,
        hasAddress: hasAddress.length,
        waitingList: waitingList.length,
        postcardWillSend: hasAddress.length - (badAddress.length + pausedAddress.length),
        addressWillNotSend: totalNotSending,
        emailValid: validEmail.length + deliverAnyway.length,
        emailBad: badEmail.length,
        deliverAnyway: deliverAnyway.length,
      };
      
      // Update the recipient group with calculated metadata
      await recipientGroupRef.update({ meta: data }, { merge: true });
    }
    return true;
  } catch (error) {
    console.log("Error processing group", error.message);
    return { error: error.message };
  }
}
