const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const admin = require("firebase-admin");
const { log } = require("firebase-functions/logger");

exports.modifyAddressWorker = onMessagePublished(
  {
    topic: "address-validation",
    retryConfig: {
      maxAttempts: 3,
      minBackoffSeconds: 10,
      maxBackoffSeconds: 300,
      maxDoublings: 5,
    },
  },
  async (event) => {
    try {
      const messageBody = event.data.message.data
        ? Buffer.from(event.data.message.data, "base64").toString()
        : null;

      if (messageBody) {
        const { type, data } = JSON.parse(messageBody);

        // Process the notification based on type
        switch (type) {
          case "newLead":
            try {
              await createNotification(data);
            } catch (error) {
              log("Error adding notification to Firestore:", error);
            }

            break;

          default:
        }
      }
    } catch (error) {
      log("Error processing message:", error);
    }
  }
);
