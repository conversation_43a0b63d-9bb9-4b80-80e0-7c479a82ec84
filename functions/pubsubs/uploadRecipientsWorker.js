const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const { defineString, HttpsError } = require("firebase-functions/params");
const { logger } = require("firebase-functions");
const Papa = require("papaparse");
const { FieldValue } = require("firebase-admin/firestore");
const { addToTopic } = require("../utils/pubsub/addToTopic");
const { errorMonitor } = require("events");

exports.downloadRecipientsWorker = (admin) =>
  onMessagePublished(
    {
      topic: "upload-recipients",
      retryConfig: {
        maxAttempts: 3,
        minBackoffSeconds: 10,
        maxBackoffSeconds: 300,
        maxDoublings: 5,
      },
    },
    async (event) => {
      try {
        const messageBody = event.data.message.data
          ? Buffer.from(event.data.message.data, "base64").toString()
          : null;

        if (messageBody) {
          const parsedBody = JSON.parse(messageBody);
          const { accountId, recipientGroupId } = parsedBody;
        }
      } catch (error) {
        logger.error("Problem uploading recipients file.", error);
      }
    }
  );
