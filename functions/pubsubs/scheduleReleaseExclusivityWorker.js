const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const { Timestamp, FieldValue } = require("firebase-admin/firestore");
const admin = require("firebase-admin");
const { logger } = require("firebase-functions");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { isNil, isEmpty } = require("lodash");
const { addReleaseRecord } = require("../recipients/exclusivity/helpers");

exports.scheduleReleaseExclusivityWorker = (db) =>
  onMessagePublished(
    {
      topic: "schedule-release-exclusivity",
      retryConfig: {
        maxAttempts: 3,
        minBackoffSeconds: 10,
        maxBackoffSeconds: 300,
        maxDoublings: 5,
      },
    },
    async (event) => {
      try {
        const messageBody = event.data.message.data
          ? Buffer.from(event.data.message.data, "base64").toString()
          : null;

        if (messageBody) {
          const parsedBody = JSON.parse(messageBody);
          const { accountId, recipientId, action, userId } = parsedBody;

          if (isNil(accountId) || isNil(recipientId)) {
            logger.error(
              "Missing required parameters for release exclusivity worker: ",
              "accountId: ",
              accountId,
              "recipientId: ",
              recipientId
            );
            return;
          }

          const recipientRef = db
            .collection(collectionNames.account)
            .doc(accountId)
            .collection(subCollectionNames.contacts.recipients)
            .doc(recipientId);

          const recipientSnapshot = await recipientRef.get();

          if (!recipientSnapshot.exists) return;

          const { mailingAddresses = [] } = recipientSnapshot.data();
          const sourceMailingAddressIds =
            mailingAddresses?.map((mailingObj) => mailingObj?.id) ?? [];

          for (const sourceMailingAddressId of sourceMailingAddressIds) {
            await addReleaseRecord({
              accountId,
              recipientId,
              sourceMailingAddressId,
              action,
              userId,
            });
          }
        }
      } catch (error) {
        logger.error("Problem in release exclusivity worker", error);
      }
    }
  );
