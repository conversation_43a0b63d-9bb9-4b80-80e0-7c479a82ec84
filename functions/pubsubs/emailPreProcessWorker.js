const {
  onMessagePublished
} = require("firebase-functions/v2/pubsub");
const { PubSub } = require("@google-cloud/pubsub");

const pubsub = new PubSub();

exports.emailPreProcessWorker = (admin, db) =>
  onMessagePublished(
    {
      topic: "email-pre-process-worker",
      memory: "256MiB",
      timeoutSeconds: 300,
    },
    async (message) => {
      const base64Data = message.data?.message?.data;

      if (!base64Data) {
        console.error("No data found in message");
        return;
      }

      let payload;
      try {
        const decodedData = Buffer.from(base64Data, "base64").toString();
        payload = JSON.parse(decodedData);
      } catch (err) {
        console.error("Failed to parse JSON:", err);
        return;
      }

      const { recipientGroupPaths, flag = "PROCESS" } = payload;

      if (!Array.isArray(recipientGroupPaths)) {
        console.error("recipientGroupPaths is not an array");
        return;
      }

      const db = admin.firestore();

      for (const path of recipientGroupPaths) {
        try {
          const docRef = db.doc(path);
          const docSnap = await docRef.get();
          const match = path.match(/^Account\/([^/]+)/);
          const accountId = match ? match[1] : null;

          if (!docSnap.exists) {
            console.warn(`Document does not exist: ${path}`);
            continue;
          }
          if (!accountId) {
            console.warn(`Could not extract accountId from path: ${path}`);
            continue;
          }

          const groupData = docSnap.data();
          const groupId = docRef.id;
          const matchingRecipients = [];

          const recipientsRef = db.collection(
            `Account/${accountId}/Recipients`
          ).where(
            "recipientGroupIds",
            "array-contains",
            docRef.id
          );
          const recipientsSnap = await recipientsRef.get();

          recipientsSnap.forEach((doc) => {
              matchingRecipients.push(doc.id);
          });

          console.log(
            `📦 Group ${path} has ${matchingRecipients.length} matching recipients`
          );          

          const chunkSize = 500;
          for (let i = 0; i < matchingRecipients.length; i += chunkSize) {
            const chunk = matchingRecipients.slice(i, i + chunkSize);

            const messageData = {
              accountId,
              groupPath: path,
              recipientIds: chunk,
              flag
            };

            await pubsub.topic("email-recipient-chunk-worker").publishMessage({
              json: messageData,
            });

            console.log(
              `🚀 Published chunk of ${chunk.length} matchingRecipients to worker`
            );
          }

          console.log(`✅ Finished fan-out for group: ${path}`);
        } catch (err) {
          console.error(`Error processing group path ${path}:`, err);
        }
      }
    }
  );
