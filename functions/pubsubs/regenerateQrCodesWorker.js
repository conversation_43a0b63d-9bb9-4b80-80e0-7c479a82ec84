const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const { getStorage } = require("firebase-admin/storage");
const { Timestamp } = require("firebase-admin/firestore");

exports.regenerateQrCodesWorker = (db) =>
  onMessagePublished(
    {
      topic: "regenerate-qr-codes",
      memory: "256MB",
      timeoutSeconds: 540,
      concurrency: 5,
    },
    async (event) => {
      try {
        const messageData = JSON.parse(
          Buffer.from(event.data.message.data, "base64").toString()
        );
        const code = messageData.code;
        const accountId = code.accountId;
        const shortId = code.shortId;

        const storage = getStorage();
        const bucket = storage.bucket();

        // Generate new signed URLs with 7-day expiration
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 7);

        const fileTypes = ["pdf", "png", "svg"];
        const updatedQrCodes = {};

        for (const type of fileTypes) {
          const filePath = `qr-codes/${accountId}/${shortId}.${type}`;
          const [url] = await bucket.file(filePath).getSignedUrl({
            action: "read",
            expires: expirationDate,
          });
          updatedQrCodes[type] = url;
        }

        await db.collection("codes").doc(shortId).update({
          qrCodes: updatedQrCodes,
          qrCodeGenerated: Timestamp.now(),
        });

        console.log(`Successfully regenerated QR code URLs for ${shortId}`);
        return { success: true };
      } catch (error) {
        console.error("Error regenerating QR code URLs:", error);
        throw error;
      }
    }
  );
