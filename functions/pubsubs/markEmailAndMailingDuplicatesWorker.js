const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const admin = require("firebase-admin");
const { log } = require("firebase-functions/logger");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const {
  Timestamp,
  FieldValue,
  getFirestore,
} = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");

exports.markEmailAndMailingDuplicatesWorker = onMessagePublished(
  {
    topic: "mark-duplicates",
    timeoutSeconds: 540,
    memory: "2GiB",
    retryConfig: {
      maxAttempts: 3,
      minBackoffSeconds: 10,
      maxBackoffSeconds: 300,
      maxDoublings: 5,
    },
  },
  async (event) => {
    try {
      const messageBody = event.data.message.data
        ? Buffer.from(event.data.message.data, "base64").toString()
        : null;
      if (messageBody) {
        // Parse messageBody
        let parsedBody;
        try {
          parsedBody = JSON.parse(messageBody);
        } catch (error) {
          log("Error parsing message body:", error);
          parsedBody = {};
        }

        const { pageSize, lastDocumentId } = parsedBody;
        const accountId = parsedBody.accountId.toString();
        const db = getFirestore();

        const recipientsCollectionRef = db
          .collection(collectionNames.account)
          .doc(accountId)
          .collection(subCollectionNames.contacts.recipients);

        const activeRecipientsCollectionRef = recipientsCollectionRef.where(
          "isActive",
          "==",
          true
        );
        let query = activeRecipientsCollectionRef
          .orderBy("__name__")
          .limit(pageSize);

        if (lastDocumentId) {
          query = query.startAfter(lastDocumentId);
        }

        const snapshot = await query.get();
        const emailValueMap = {};
        const mailingValueMap = {};
        const docDuplicatesMap = {}; // Stores duplicate info for documents

        snapshot.forEach((doc) => {
          const data = doc.data();
          const docRef = doc.ref;
          const docId = doc.id;

          // Normalize email and mailing address
          const emailAddress =
            data?.searchTags?.primaryEmail?.toLowerCase().trim() || "";
          const mailingAddress =
            data?.searchTags?.formattedMailingAddress?.toLowerCase().trim() ||
            "";

          // Initialize duplicate info for the document
          if (!docDuplicatesMap[docId]) {
            docDuplicatesMap[docId] = { docRef, duplicate: {} };
          }
          const docDuplicate = docDuplicatesMap[docId].duplicate;

          // Check for email duplicates
          if (emailAddress) {
            if (emailValueMap[emailAddress]) {
              // Mark current and original documents as duplicates
              docDuplicate.email = true;
              const originalDocRef = emailValueMap[emailAddress];
              const originalDocId = originalDocRef.id;
              if (!docDuplicatesMap[originalDocId]) {
                docDuplicatesMap[originalDocId] = {
                  docRef: originalDocRef,
                  duplicate: { email: true },
                };
              } else {
                docDuplicatesMap[originalDocId].duplicate.email = true;
              }
            } else {
              emailValueMap[emailAddress] = docRef;
            }
          }

          // Check for mailing address duplicates
          if (mailingAddress) {
            if (mailingValueMap[mailingAddress]) {
              // Mark current and original documents as duplicates
              docDuplicate.mailingAddress = true;
              const originalDocRef = mailingValueMap[mailingAddress];
              const originalDocId = originalDocRef.id;
              if (!docDuplicatesMap[originalDocId]) {
                docDuplicatesMap[originalDocId] = {
                  docRef: originalDocRef,
                  duplicate: { mailingAddress: true },
                };
              } else {
                docDuplicatesMap[originalDocId].duplicate.mailingAddress = true;
              }
            } else {
              mailingValueMap[mailingAddress] = docRef;
            }
          }
        });

        // Batch updates for duplicate marking
        const batchUpdates = [];
        let batch = db.batch();
        let operationCount = 0;

        for (const docId in docDuplicatesMap) {
          const { docRef, duplicate } = docDuplicatesMap[docId];
          batch.update(docRef, { duplicate });
          operationCount++;

          if (operationCount >= 500) {
            batchUpdates.push(batch.commit());
            batch = db.batch();
            operationCount = 0;
          }
        }

        if (operationCount > 0) {
          batchUpdates.push(batch.commit());
        }

        await Promise.all(batchUpdates);
      }
    } catch (error) {
      logger.error("Problem in check for duplicates", error);
    }
  }
);
