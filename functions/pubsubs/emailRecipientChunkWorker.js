const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const pLimit = require("p-limit");

exports.emailRecipientChunkWorker = (admin, db) =>
  onMessagePublished(
    {
      topic: "email-recipient-chunk-worker",
      memory: "512MiB",
      timeoutSeconds: 540,
    },
    async (message) => {
      const base64Data = message.data?.message?.data;

      if (!base64Data) {
        console.error("No data found in message");
        return;
      }

      let payload;
      try {
        const decodedData = Buffer.from(base64Data, "base64").toString();
        payload = JSON.parse(decodedData);
      } catch (err) {
        console.error("Failed to parse chunk JSON:", err);
        return;
      }

      const { accountId, groupPath, recipientIds, flag = "PROCESS" } = payload;

      if (!Array.isArray(recipientIds) || !accountId || !groupPath) {
        console.error("Malformed payload");
        return;
      }

      const db = admin.firestore();
      const missingEmailPaths = [];
      let completed = 0;

      const limit = pLimit(10); // throttle concurrency

      const tasks = recipientIds.map((recipientId) =>
        limit(async () => {
          const recipientDocPath = `Account/${accountId}/Recipients/${recipientId}`;
          const recipientDocRef = db.doc(recipientDocPath);
          const emailCollectionRef =
            recipientDocRef.collection("EmailAddresses");

          const emailDocsSnap = await emailCollectionRef.get();

          if (emailDocsSnap.empty) {
            missingEmailPaths.push(recipientDocPath);
            return;
          }

          try {
            await Promise.all(emailDocsSnap.docs.map(async (emailDoc) => {
              await emailDoc.ref.update({
                processingStatus: "DONE",
              });
            }));          
            await Promise.all(emailDocsSnap.docs.map(async (emailDoc) => {
              await emailDoc.ref.update({
                processingStatus: flag,
              });
            }));

          } catch (err) {
            console.error(`Error validating ${recipientId}:`, err);
          } finally {
            completed++;
          }
        })
      );

      await Promise.all(tasks);

      console.log(
        `🎯 Finished chunk for group ${groupPath}, ${completed}/${recipientIds.length} done`
      );

      if (missingEmailPaths.length) {
        console.warn("Missing email addresses for recipients:");
        console.warn(missingEmailPaths.slice(0, 10));
      }
    }
  );
