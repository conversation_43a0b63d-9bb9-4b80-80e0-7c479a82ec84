const { onMessagePublished } = require("firebase-functions/v2/pubsub");
const admin = require("firebase-admin");
const { log } = require("firebase-functions/logger");
const { astridConnection } = require("../astrid/astridConnection");
exports.crmEventWorker = (db) =>
  onMessagePublished(
    {
      topic: "crm-events",
      retryConfig: {
        maxAttempts: 3,
        minBackoffSeconds: 10,
        maxBackoffSeconds: 300,
        maxDoublings: 5,
      },
    },
    async (event) => {
      try {
        const messageBody = event.data.message.data
          ? Buffer.from(event.data.message.data, "base64").toString()
          : null;

        if (messageBody) {
          // Parse messageBody
          let parsedBody;
          try {
            parsedBody = JSON.parse(messageBody);
          } catch (error) {
            log("Error parsing message body:", error);
            parsedBody = {};
          }

          // Save individual fields to CRMLog collection
          const newAlertRecord = await db.collection("CRMLog").add({
            ...parsedBody, // Spread the parsedBody to save all key-value pairs as fields
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            statusMessage: "pending",
          });

          if (parsedBody?.type && parsedBody?.isFake === false) {
            let payload = {
              accountId: parsedBody?.accountId,
              accountName: parsedBody?.adAccountName,
              adName: parsedBody?.adName,
              campaignName: parsedBody?.campaignName,
              adEffectiveStatus: parsedBody?.adEffectiveStatus,
              type: parsedBody?.type,
              message: parsedBody?.message,
              title: parsedBody?.title,
            };

            let url = null;

            if (parsedBody?.type === "AD_CPL_PERFORMING_POORLY") {
              url = "crm/adPerformance";
            }

            if (parsedBody?.type === "AD_STOPPED_UNEXPECTED") {
              url = "crm/adStopped";
            }

            if (parsedBody?.type === "AD_HAS_ISSUES") {
              url = "crm/adHasIssues";
            }

            if (parsedBody?.type === "AD_RUNNING_UNEXPECTED") {
              url = "crm/adRunning";
            }

            if (parsedBody?.type === "AD_ACCOUNT_STATUS_ISSUE") {
              url = "crm/adAccountHasIssues";
            }

            if (parsedBody?.type === "DEFAULT_AD_ACCOUNT_NOT_CONNECTED") {
              url = "crm/noDefaultAdAccount";
            }

            if (parsedBody?.type === "DEFAULT_ADS_PAGE_NOT_CONNECTED") {
              url = "crm/noDefaultAdsPage";
            }

            let statusError = null;

            if (url) {
              const request = {
                payload: payload,
                url: url,
                method: "POST",
              };
              try {
                await astridConnection(request);
              } catch (error) {
                statusError = error.message;
                await newAlertRecord.update({
                  statusMessage: "error",
                  statusTimestamp: admin.firestore.FieldValue.serverTimestamp(),
                  statusError: statusError,
                });
                log("Error calling Astrid:", error);
              }
            }
          }

          // Add success message to the newAlertRecord
          await newAlertRecord.update({
            statusMessage: "success",
            statusTimestamp: admin.firestore.FieldValue.serverTimestamp(),
          });
        }
      } catch (error) {
        log("Error processing message:", error);
      }
    }
  );
