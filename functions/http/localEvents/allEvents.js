// functions/http/localEvents/allEvents.js
const { onRequest } = require('firebase-functions/v2/https');
const admin = require('firebase-admin');
const { logger } = require('firebase-functions/v2');

/**
 * Process event data and filter blocked tags
 */
function processEventData(docId, data, blockedTags) {
  // Filter search terms to remove blocked tags
  let filteredSearchTerms = data._searchTerms || [];
  if (filteredSearchTerms.length > 0) {
    filteredSearchTerms = filteredSearchTerms.filter(term => {
      return !blockedTags.has(term.toLowerCase());
    });
  }

  return {
    id: docId,
    _cancellationStatus: data._cancellationStatus,
    _createdTime: data._createdTime?.__time__ || data._createdTime,
    _dateRange: data._dateRange,
    _lastModified: data._lastModified?.__time__ || data._lastModified,
    _lastSyncTime: data._lastSyncTime?.__time__ || data._lastSyncTime,
    _original_dates: data._original_dates,
    _searchTerms: filteredSearchTerms,
    annual: data.annual,
    area_uuid: data.area_uuid,
    areas: data.areas,
    cancelled: data.cancelled,
    description: data.description,
    end_date: data.end_date?.__time__ || data.end_date,
    end_time: data.end_time,
    event_dates: data.event_dates,
    event_description_native: data.event_description_native,
    event_name_native: data.event_name_native,
    event_type: data.event_type,
    flags: data.flags,
    image_alt_text_english: data.image_alt_text_english,
    image_count: data.image_count,
    image_url: data.image_url,
    instance_date: data.instance_date,
    maximum_price: data.maximum_price,
    minimum_price: data.minimum_price,
    name: data.name,
    not_yet_on_sale: data.not_yet_on_sale,
    popularity_score: data.popularity_score,
    recurring_event_uuid: data.recurring_event_uuid,
    rrule: data.rrule,
    sold_out: data.sold_out,
    source_url: data.source_url,
    start_date: data.start_date?.__time__ || data.start_date,
    start_time: data.start_time,
    ticket_url: data.ticket_url,
    travel_worthy: data.travel_worthy,
    umbrella_event_uuid: data.umbrella_event_uuid,
    uuid: data.uuid,
    venue_address_1: data.venue_address_1,
    venue_address_2: data.venue_address_2,
    venue_city: data.venue_city,
    venue_country: data.venue_country,
    venue_g_identifier: data.venue_g_identifier,
    venue_id: data.venue_id,
    venue_latitude: data.venue_latitude,
    venue_longitude: data.venue_longitude,
    venue_name: data.venue_name,
    venue_postal_code: data.venue_postal_code,
    venue_region: data.venue_region,
    venue_uuid: data.venue_uuid,
    virtual_rule: data.virtual_rule
  };
}

/**
 * API endpoint to get all live events with blocked tag filtering
 * Supports both paginated and non-paginated responses
 * 
 * Usage:
 * GET /getLiveEvents - Get all events
 * GET /getLiveEvents?paginate=true&page=1&limit=50 - Get paginated events
 */
const getLiveEvents = onRequest({
  cors: true,
  maxInstances: 10,
  memory: '2GiB',
  timeoutSeconds: 540 // 9 minutes max
}, async (request, response) => {
  
  // Only allow GET requests
  if (request.method !== 'GET') {
    response.status(405).json({
      success: false,
      error: 'Method not allowed. Use GET.'
    });
    return;
  }

  try {
    // Extract query parameters
    const { 
      paginate = 'false',
      limit = 100,
      page = 1
    } = request.query;

    const isPaginated = paginate === 'true';
    const limitNum = Math.min(parseInt(limit) || 100, 1000); // Max 1000 per request
    const pageNum = parseInt(page) || 1;
    const offset = (pageNum - 1) * limitNum;

    logger.info('Fetching live events', {
      paginate: isPaginated,
      limit: limitNum,
      page: pageNum
    });

    const db = admin.firestore();

    // Get all blocked tags from OGTags collection
    const blockedTagsSnapshot = await db.collection('OGTags')
      .where('blocked', '==', true)
      .get();
    
    const blockedTags = new Set();
    blockedTagsSnapshot.forEach(doc => {
      const tag = doc.data();
      if (tag.name) {
        blockedTags.add(tag.name.toLowerCase());
      }
    });

    logger.info(`Found ${blockedTags.size} blocked tags`);

    let responseData = {
      success: true
    };

    if (isPaginated) {
      // Paginated response
      // Get total count of active events
      const totalSnapshot = await db.collection('LiveEvents')
        .where("isActive", "==", true)
        .count()
        .get();
      
      const totalCount = totalSnapshot.data().count;
      const totalPages = Math.ceil(totalCount / limitNum);

      // Build query - without orderBy since we'll sort in memory
      let query = db.collection('LiveEvents')
        .where("isActive", "==", true)
        .limit(limitNum);

      // For pages after the first, we need to get the start point
      if (pageNum > 1) {
        // We'll need to fetch all previous pages to maintain order
        const allPreviousQuery = db.collection('LiveEvents')
          .where("isActive", "==", true)
          .limit(offset + limitNum);
        
        const allPreviousSnapshot = await allPreviousQuery.get();
        
        // Sort all documents by start date in memory
        const allDocs = [];
        allPreviousSnapshot.forEach(doc => {
          const data = doc.data();
          allDocs.push({ doc, startDate: data.start_date?.__time__ || data.start_date || '' });
        });
        
        allDocs.sort((a, b) => {
          const dateA = new Date(a.startDate || 0);
          const dateB = new Date(b.startDate || 0);
          return dateA - dateB; // Ascending order
        });
        
        // Get events for current page
        const startIndex = offset;
        const endIndex = Math.min(startIndex + limitNum, allDocs.length);
        
        const events = [];
        for (let i = startIndex; i < endIndex; i++) {
          const eventData = allDocs[i].doc.data();
          const processedEvent = processEventData(allDocs[i].doc.id, eventData, blockedTags);
          events.push(processedEvent);
        }
        
        responseData.pagination = {
          page: pageNum,
          limit: limitNum,
          totalPages: totalPages,
          totalCount: totalCount,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        };
        responseData.events = events;
      } else {
        // First page - simpler logic
        const eventsSnapshot = await query.get();
        
        const eventsWithDates = [];
        eventsSnapshot.forEach(doc => {
          const eventData = doc.data();
          eventsWithDates.push({
            id: doc.id,
            data: eventData,
            startDate: eventData.start_date?.__time__ || eventData.start_date || ''
          });
        });
        
        // Sort by start date
        eventsWithDates.sort((a, b) => {
          const dateA = new Date(a.startDate || 0);
          const dateB = new Date(b.startDate || 0);
          return dateA - dateB; // Ascending order
        });
        
        const events = eventsWithDates.map(item => 
          processEventData(item.id, item.data, blockedTags)
        );
        
        responseData.pagination = {
          page: pageNum,
          limit: limitNum,
          totalPages: totalPages,
          totalCount: totalCount,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        };
        responseData.events = events;
      }

    } else {
      // Non-paginated response - get all events
      const eventsSnapshot = await db.collection('LiveEvents')
        .where("isActive", "==", true)
        .get();

      const eventsWithDates = [];
      eventsSnapshot.forEach(doc => {
        const eventData = doc.data();
        eventsWithDates.push({
          id: doc.id,
          data: eventData,
          startDate: eventData.start_date?.__time__ || eventData.start_date || ''
        });
      });
      
      // Sort by start date in memory
      eventsWithDates.sort((a, b) => {
        const dateA = new Date(a.startDate || 0);
        const dateB = new Date(b.startDate || 0);
        return dateA - dateB; // Ascending order
      });

      const events = eventsWithDates.map(item => 
        processEventData(item.id, item.data, blockedTags)
      );

      responseData.count = events.length;
      responseData.events = events;
    }

    // Cache control for better performance
    response.set('Cache-Control', 'public, max-age=300'); // Cache for 5 minutes
    
    response.status(200).json(responseData);

  } catch (error) {
    logger.error('Error fetching events:', error);
    
    response.status(500).json({
      success: false,
      error: 'Failed to fetch events',
      message: error.message
    });
  }
});

exports.getLiveEvents = getLiveEvents;