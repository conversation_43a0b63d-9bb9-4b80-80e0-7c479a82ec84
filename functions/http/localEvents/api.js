// functions/api/getEventsByArea.js
const { onRequest } = require('firebase-functions/v2/https');
const admin = require('firebase-admin');
const { logger } = require('firebase-functions/v2');
const { Timestamp } = require('firebase-admin/firestore');

/**
 * API endpoint to get all events for a specific area
 * 
 * Usage:
 * GET /getEventsByArea?area_uuid=YOUR_AREA_UUID
 * GET /getEventsByArea?area_uuid=YOUR_AREA_UUID&limit=50
 * GET /getEventsByArea?area_uuid=YOUR_AREA_UUID&active_only=true
 * GET /getEventsByArea?area_uuid=YOUR_AREA_UUID&start_date=2025-01-01
 */
const getEventsByArea = onRequest({
  cors: true,
  maxInstances: 10,
  memory: '512MiB',
  timeoutSeconds: 60
}, async (request, response) => {
  
  // Only allow GET requests
  if (request.method !== 'GET') {
    response.status(405).json({
      success: false,
      error: 'Method not allowed. Use GET.'
    });
    return;
  }

  try {
    // Extract query parameters
    const { 
      area_uuid, 
      limit = 100, 
      active_only = 'false',
      start_date = null,
      end_date = null,
      popularity_min = null,
      page_token = null
    } = request.query;

    // Validate area_uuid
    if (!area_uuid) {
      response.status(400).json({
        success: false,
        error: 'Missing required parameter: area_uuid'
      });
      return;
    }

    logger.info(`Fetching events for area: ${area_uuid}`, {
      limit,
      active_only,
      start_date,
      end_date
    });

    const db = admin.firestore();
    
    // First, verify the area exists by area_uuid
    const areaSnapshot = await db.collection('EventMarkets')
      .where('area_uuid', '==', area_uuid)
      .limit(1)
      .get();
    
    if (areaSnapshot.empty) {
      response.status(404).json({
        success: false,
        error: 'Area not found',
        area_uuid
      });
      return;
    }

    const areaDoc = areaSnapshot.docs[0];
    const areaData = areaDoc.data();

    // Get all blocked tags from OGTags collection
    const blockedTagsSnapshot = await db.collection('OGTags')
      .where('blocked', '==', true)
      .get();
    
    const blockedTags = new Set();
    blockedTagsSnapshot.forEach(doc => {
      const tag = doc.data();
      if (tag.name) {
        blockedTags.add(tag.name.toLowerCase());
      }
    });

    logger.info(`Found ${blockedTags.size} blocked tags`);
    
    // Build the query for events
    let query = db.collection('LiveEvents')
      .where('area_uuid', '==', area_uuid);

    // Apply filters
    if (active_only === 'true') {
      query = query.where('isActive', '==', true);
    }

    if (start_date) {
      // Validate and convert start_date to Firestore Timestamp
      const startDateObj = new Date(start_date);
      if (isNaN(startDateObj.getTime())) {
      response.status(400).json({
        success: false,
        error: 'Invalid start_date format. Use ISO date format (YYYY-MM-DD).'
      });
      return;
      }
      const startTimestamp = Timestamp.fromDate(startDateObj);
      query = query.where('start_date', '>=', startTimestamp);
    }

    if (end_date) {
      // Validate and convert end_date to Firestore Timestamp
      const endDateObj = new Date(end_date);
      if (isNaN(endDateObj.getTime())) {
        response.status(400).json({
          success: false,
          error: 'Invalid end_date format. Use ISO date format (YYYY-MM-DD).'
        });
        return;
      }
      const endTimestamp = Timestamp.fromDate(endDateObj);
      query = query.where('end_date', '<=', endTimestamp);
    }

    // Popularity filter
    if (popularity_min) {
      const minPop = parseInt(popularity_min);
      if (!isNaN(minPop)) {
        query = query.where('popularity_score', '>=', minPop);
      }
    }

    // Apply limit first (we'll handle date filtering in memory)
    const limitNum = Math.min(parseInt(limit) || 100, 500);
    
    // Add ordering to ensure consistent pagination
    query = query.orderBy('start_date', 'desc');
    
    query = query.limit(limitNum * 2); // Get extra to account for date filtering

    // Apply pagination start after last document if page_token is provided
    if (page_token) {
      try {
        // Get the document to start after using the page_token (which is a document ID)
        const startAfterDoc = await db.collection('LiveEvents').doc(page_token).get();
        if (startAfterDoc.exists) {
          query = query.startAfter(startAfterDoc);
        }
      } catch (error) {
        logger.warn('Invalid page_token provided:', page_token);
        // Continue without pagination if token is invalid
      }
    }

    // Execute query
    let snapshot = await query.get();
    
    // Format results and apply date filtering in memory
    let events = [];
    let lastDocId = null;
    
    // Parse date filters if provided
    const startDateFilter = start_date ? new Date(start_date) : null;
    const endDateFilter = end_date ? new Date(end_date) : null;
    
    snapshot.forEach(doc => {
      const data = doc.data();
      lastDocId = doc.id;
      
      // Apply date filtering in memory
      let includeEvent = true;
      
      if (startDateFilter && data.start_date) {
        let eventStartDate = null;
        if (typeof data.start_date.toDate === 'function') {
          eventStartDate = data.start_date.toDate();
        } else if (data.start_date.__time__) {
          eventStartDate = new Date(data.start_date.__time__);
        } else {
          eventStartDate = new Date(data.start_date);
        }
        if (!eventStartDate || eventStartDate < startDateFilter) {
          includeEvent = false;
        }
      }
      
      if (endDateFilter && data.end_date) {
        let eventEndDate = null;
        if (typeof data.end_date.toDate === 'function') {
          eventEndDate = data.end_date.toDate();
        } else if (data.end_date.__time__) {
          eventEndDate = new Date(data.end_date.__time__);
        } else {
          eventEndDate = new Date(data.end_date);
        }
        if (!eventEndDate || eventEndDate > endDateFilter) {
          includeEvent = false;
        }
      }
      
      if (includeEvent && events.length < limitNum) {
        // Filter search terms to remove blocked tags
        let filteredSearchTerms = data._searchTerms || [];
        if (filteredSearchTerms.length > 0) {
          filteredSearchTerms = filteredSearchTerms.filter(term => {
            return !blockedTags.has(term.toLowerCase());
          });
        }
        
        // Clean up the response data
        events.push({
          id: doc.id,
          uuid: data.uuid,
          name: data.name,
          description: data.description,
          start_date: data.start_date?.__time__ || data.start_date,
          end_date: data.end_date?.__time__ || data.end_date,
          instance_date: data.instance_date,
          event_dates: data.event_dates,
          event_type: data.event_type,
          venue: {
            uuid: data.venue_uuid,
            name: data.venue_name,
            address_1: data.venue_address_1,
            address_2: data.venue_address_2,
            city: data.venue_city,
            region: data.venue_region,
            postal_code: data.venue_postal_code,
            country: data.venue_country,
            latitude: data.venue_latitude,
            longitude: data.venue_longitude,
            g_identifier: data.venue_g_identifier
          },
          urls: {
            source: data.source_url,
            ticket: data.ticket_url,
            image: data.image_url
          },
          prices: {
            minimum: data.minimum_price,
            maximum: data.maximum_price
          },
          popularity_score: data.popularity_score,
          image_count: data.image_count,
          image_alt_text_english: data.image_alt_text_english,
          flags: data.flags || [],
          _searchTerms: filteredSearchTerms,
          _isActive: data._isActive,
          _isHidden: data._isHidden,
          _isPostponed: data._isPostponed,
          _cancellationStatus: data._cancellationStatus,
          cancelled: data.cancelled,
          sold_out: data.sold_out,
          not_yet_on_sale: data.not_yet_on_sale,
          annual: data.annual,
          travel_worthy: data.travel_worthy,
          virtual_rule: data.virtual_rule,
          area_uuid: data.area_uuid,
          areas: data.areas,
          recurring_event_uuid: data.recurring_event_uuid,
          umbrella_event_uuid: data.umbrella_event_uuid,
          _airtableId: data._airtableId,
          _baseId: data._baseId,
          _createdTime: data._createdTime?.__time__ || data._createdTime,
          _lastModified: data._lastModified?.__time__ || data._lastModified,
          _lastSyncTime: data._lastSyncTime?.__time__ || data._lastSyncTime
        });
      }
    });
    
    // Events are already sorted from the query with orderBy

    // Prepare response
    const responseData = {
      success: true,
      area: {
        uuid: areaData.area_uuid,
        name: areaData.area_name,
        address: areaData.area_address,
        latitude: areaData.area_latitude,
        longitude: areaData.area_longitude,
        radius: areaData.area_radius,
        timezone: areaData.area_timezone,
        event_count: areaData.event_count,
        isActive: areaData.isActive,
        _airtableId: areaData._airtableId
      },
      events: events,
      count: events.length,
      total_in_area: areaData.event_count || 0,
      filters_applied: {
        active_only: active_only === 'true',
        start_date,
        end_date,
        popularity_min: popularity_min ? parseInt(popularity_min) : null,
        blocked_tags_removed: blockedTags.size > 0
      }
    };

    // Add pagination info if there are more results
    if (events.length === limitNum && lastDocId) {
      responseData.pagination = {
        has_more: true,
        next_page_token: lastDocId,
        limit: limitNum
      };
    } else {
      responseData.pagination = {
        has_more: false,
        limit: limitNum
      };
    }

    // Cache control for better performance
    response.set('Cache-Control', 'public, max-age=300'); // Cache for 5 minutes
    
    response.status(200).json(responseData);

  } catch (error) {
    logger.error('Error fetching events by area:', error);
    
    response.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * Additional endpoint to search for areas by name or location
 */
const searchAreas = onRequest({
  cors: true,
  maxInstances: 10,
  memory: '256MiB',
  timeoutSeconds: 30
}, async (request, response) => {
  
  if (request.method !== 'GET') {
    response.status(405).json({
      success: false,
      error: 'Method not allowed. Use GET.'
    });
    return;
  }

  try {
    const { 
      name,
      lat,
      lng,
      radius_km = 50,
      limit = 20
    } = request.query;

    const db = admin.firestore();
    let query = db.collection('EventMarkets')
      .where('isActive', '==', true); // Only return active markets

    // Search by name
    if (name) {
      // Note: This is a simple prefix search. For better search, consider using Algolia
      const searchTerm = name.toLowerCase();
      
      // Get all active markets and filter by name
      const snapshot = await query.get();
      const areas = [];
      
      snapshot.forEach(doc => {
        const data = doc.data();
        if (data.area_name && data.area_name.toLowerCase().includes(searchTerm)) {
          areas.push({
            id: doc.id,
            uuid: data.area_uuid,
            name: data.area_name,
            address: data.area_address,
            latitude: data.area_latitude,
            longitude: data.area_longitude,
            radius: data.area_radius,
            timezone: data.area_timezone,
            event_count: data.event_count,
            _airtableId: data._airtableId
          });
        }
      });

      // Sort by name and apply limit
      areas.sort((a, b) => a.name.localeCompare(b.name));
      const limitedAreas = areas.slice(0, parseInt(limit));

      response.status(200).json({
        success: true,
        areas: limitedAreas,
        count: limitedAreas.length,
        search_type: 'name',
        search_term: name
      });
      return;
    }

    // Search by location (requires both lat and lng)
    if (lat && lng) {
      const latitude = parseFloat(lat);
      const longitude = parseFloat(lng);
      const radiusKm = parseFloat(radius_km);

      if (isNaN(latitude) || isNaN(longitude) || isNaN(radiusKm)) {
        response.status(400).json({
          success: false,
          error: 'Invalid latitude, longitude, or radius values'
        });
        return;
      }

      // Get all active areas and calculate distances
      const snapshot = await query.get();
      const areas = [];

      snapshot.forEach(doc => {
        const data = doc.data();
        if (data.area_latitude && data.area_longitude) {
          // Calculate distance using Haversine formula
          const distance = calculateDistance(
            latitude, 
            longitude, 
            data.area_latitude, 
            data.area_longitude
          );

          if (distance <= radiusKm) {
            areas.push({
              id: doc.id,
              uuid: data.area_uuid,
              name: data.area_name,
              address: data.area_address,
              latitude: data.area_latitude,
              longitude: data.area_longitude,
              radius: data.area_radius,
              timezone: data.area_timezone,
              event_count: data.event_count,
              distance_km: Math.round(distance * 10) / 10,
              _airtableId: data._airtableId
            });
          }
        }
      });

      // Sort by distance
      areas.sort((a, b) => a.distance_km - b.distance_km);

      // Apply limit
      const limitedAreas = areas.slice(0, parseInt(limit));

      response.status(200).json({
        success: true,
        areas: limitedAreas,
        count: limitedAreas.length,
        total_found: areas.length,
        search_type: 'location',
        search_center: { latitude, longitude },
        search_radius_km: radiusKm
      });
      return;
    }

    // No valid search parameters
    response.status(400).json({
      success: false,
      error: 'Provide either "name" or both "lat" and "lng" parameters'
    });

  } catch (error) {
    logger.error('Error searching areas:', error);
    
    response.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * Calculate distance between two points using Haversine formula
 */
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

function toRad(deg) {
  return deg * (Math.PI / 180);
}

// Export both functions
exports.getEventsByArea = getEventsByArea;
exports.searchAreas = searchAreas;