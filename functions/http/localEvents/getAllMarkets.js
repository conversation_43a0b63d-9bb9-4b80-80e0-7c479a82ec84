// functions/http/localEvents/allMarkets.js
const { onRequest } = require('firebase-functions/v2/https');
const admin = require('firebase-admin');
const { logger } = require('firebase-functions/v2');

/**
 * Format market data for response
 */
function formatMarketData(docId, data) {
  return {
    id: docId,
    uuid: data.area_uuid,
    name: data.area_name,
    address: data.area_address,
    location: {
      latitude: data.area_latitude,
      longitude: data.area_longitude,
      geopoint: data._geopoint ? {
        lat: data._geopoint.__lat__,
        lon: data._geopoint.__lon__
      } : null
    },
    radius: data.area_radius,
    timezone: data.area_timezone,
    utc_offset_hours: data.utc_offset_hours,
    utc_dst_offset_hours: data.utc_dst_offset_hours,
    event_count: data.event_count || 0,
    isActive: data.isActive,
    metadata: {
      airtableId: data._airtableId,
      baseId: data._baseId,
      createdTime: data._createdTime?.__time__ || data._createdTime,
      lastModified: data._lastModified?.__time__ || data._lastModified,
      lastSyncTime: data._lastSyncTime?.__time__ || data._lastSyncTime
    }
  };
}

/**
 * API endpoint to get all event markets
 * Supports pagination and filtering
 * 
 * Usage:
 * GET /getAllMarkets - Get all markets
 * GET /getAllMarkets?active_only=true - Get only active markets
 * GET /getAllMarkets?paginate=true&page=1&limit=50 - Get paginated markets
 * GET /getAllMarkets?min_events=10 - Get markets with at least 10 events
 * GET /getAllMarkets?sort_by=name&order=asc - Sort markets by name
 */
const getAllMarkets = onRequest({
  cors: true,
  maxInstances: 10,
  memory: '512MiB',
  timeoutSeconds: 60
}, async (request, response) => {
  
  // Only allow GET requests
  if (request.method !== 'GET') {
    response.status(405).json({
      success: false,
      error: 'Method not allowed. Use GET.'
    });
    return;
  }

  try {
    // Extract query parameters
    const { 
      active_only = 'false',
      paginate = 'false',
      page = 1,
      limit = 100,
      min_events = null,
      sort_by = 'name', // Options: name, event_count, created
      order = 'asc', // Options: asc, desc
      search = null // Search term for market names
    } = request.query;

    const isPaginated = paginate === 'true';
    const limitNum = Math.min(parseInt(limit) || 100, 500); // Max 500 per request
    const pageNum = parseInt(page) || 1;
    const isActiveOnly = active_only === 'true';

    logger.info('Fetching markets', {
      active_only: isActiveOnly,
      paginate: isPaginated,
      limit: limitNum,
      page: pageNum,
      sort_by,
      order
    });

    const db = admin.firestore();
    
    // Build the base query
    let query = db.collection('EventMarkets');

    // Apply active filter
    if (isActiveOnly) {
      query = query.where('isActive', '==', true);
    }

    // Apply minimum events filter
    if (min_events) {
      const minEvents = parseInt(min_events);
      if (!isNaN(minEvents)) {
        query = query.where('event_count', '>=', minEvents);
      }
    }

    // We'll handle sorting in memory to avoid issues with nested fields
    // First, get all matching documents
    const snapshot = await query.get();
    
    // Convert to array for sorting
    const allMarkets = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      allMarkets.push({
        docId: doc.id,
        data: data
      });
    });
    
    // Apply search filter if provided
    let filteredMarkets = allMarkets;
    if (search) {
      const searchLower = search.toLowerCase();
      filteredMarkets = allMarkets.filter(market => 
        market.data.area_name && market.data.area_name.toLowerCase().includes(searchLower)
      );
    }
    
    // Sort in memory based on sort_by parameter
    const orderDirection = order === 'desc' ? 'desc' : 'asc';
    filteredMarkets.sort((a, b) => {
      let aVal, bVal;
      
      switch (sort_by) {
        case 'event_count':
          aVal = a.data.event_count || 0;
          bVal = b.data.event_count || 0;
          break;
        case 'created':
          aVal = a.data._createdTime?.__time__ || a.data._createdTime || '';
          bVal = b.data._createdTime?.__time__ || b.data._createdTime || '';
          break;
        case 'name':
        default:
          aVal = a.data.area_name || '';
          bVal = b.data.area_name || '';
          if (typeof aVal === 'string' && typeof bVal === 'string') {
            return orderDirection === 'asc' 
              ? aVal.localeCompare(bVal)
              : bVal.localeCompare(aVal);
          }
          break;
      }
      
      if (orderDirection === 'asc') {
        return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
      } else {
        return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
      }
    });

    let responseData = {
      success: true
    };

    if (isPaginated) {
      // Calculate pagination
      const totalCount = filteredMarkets.length;
      const totalPages = Math.ceil(totalCount / limitNum);
      
      // Get the markets for the current page
      const startIndex = (pageNum - 1) * limitNum;
      const endIndex = Math.min(startIndex + limitNum, totalCount);
      
      const markets = [];
      for (let i = startIndex; i < endIndex; i++) {
        const market = formatMarketData(filteredMarkets[i].docId, filteredMarkets[i].data);
        markets.push(market);
      }

      responseData.pagination = {
        page: pageNum,
        limit: limitNum,
        totalPages,
        totalCount,
        hasNext: pageNum < totalPages,
        hasPrev: pageNum > 1
      };
      responseData.markets = markets;
      responseData.count = markets.length;

    } else {
      // Non-paginated response - return all filtered markets
      const markets = filteredMarkets.map(item => 
        formatMarketData(item.docId, item.data)
      );

      responseData.markets = markets;
      responseData.count = markets.length;
    }

    // Add metadata to response
    responseData.filters_applied = {
      active_only: isActiveOnly,
      min_events: min_events ? parseInt(min_events) : null,
      search: search || null,
      sort_by,
      order: orderDirection
    };

    // Cache control for better performance
    response.set('Cache-Control', 'public, max-age=300'); // Cache for 5 minutes
    
    response.status(200).json(responseData);

  } catch (error) {
    logger.error('Error fetching markets:', error);
    
    response.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * Get market statistics
 * Returns aggregate statistics about all markets
 */
const getMarketStats = onRequest({
  cors: true,
  maxInstances: 10,
  memory: '256MiB',
  timeoutSeconds: 30
}, async (request, response) => {
  
  if (request.method !== 'GET') {
    response.status(405).json({
      success: false,
      error: 'Method not allowed. Use GET.'
    });
    return;
  }

  try {
    const db = admin.firestore();
    
    // Get all markets
    const marketsSnapshot = await db.collection('EventMarkets').get();
    
    let totalMarkets = 0;
    let activeMarkets = 0;
    let totalEvents = 0;
    let marketsWithEvents = 0;
    let marketsByTimezone = {};
    let topMarketsByEvents = [];

    const allMarkets = [];

    marketsSnapshot.forEach(doc => {
      const data = doc.data();
      totalMarkets++;
      
      if (data.isActive) {
        activeMarkets++;
      }
      
      const eventCount = data.event_count || 0;
      totalEvents += eventCount;
      
      if (eventCount > 0) {
        marketsWithEvents++;
      }
      
      // Count by timezone
      if (data.area_timezone) {
        marketsByTimezone[data.area_timezone] = (marketsByTimezone[data.area_timezone] || 0) + 1;
      }
      
      // Collect for top markets
      allMarkets.push({
        name: data.area_name,
        uuid: data.area_uuid,
        event_count: eventCount
      });
    });

    // Sort and get top 10 markets by event count
    topMarketsByEvents = allMarkets
      .sort((a, b) => b.event_count - a.event_count)
      .slice(0, 10);

    const stats = {
      success: true,
      statistics: {
        total_markets: totalMarkets,
        active_markets: activeMarkets,
        inactive_markets: totalMarkets - activeMarkets,
        total_events_across_all_markets: totalEvents,
        markets_with_events: marketsWithEvents,
        markets_without_events: totalMarkets - marketsWithEvents,
        average_events_per_market: totalMarkets > 0 ? Math.round(totalEvents / totalMarkets) : 0,
        markets_by_timezone: marketsByTimezone,
        top_markets_by_event_count: topMarketsByEvents
      },
      generated_at: new Date().toISOString()
    };

    // Cache control
    response.set('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    
    response.status(200).json(stats);

  } catch (error) {
    logger.error('Error fetching market statistics:', error);
    
    response.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

// Export functions exactly like getEventsByArea
exports.getAllMarkets = getAllMarkets;
exports.getMarketStats = getMarketStats;