const { onRequest } = require("firebase-functions/v2/https");

exports.getAccount = (db) => onRequest(async (req, res) => {
  if (req.method !== 'GET') {
    res.status(405).send("Method Not Allowed");
  }

  const { accountId } = req.query;

  if (!accountId) {
    return res.status(400).send({ status: 'failure', message: 'Account ID is required.' });
  }

  const accountRef = db.collection('Account').doc(accountId);
  const accountDoc = await accountRef.get();
  if (!accountDoc.exists) {
    res.status(404).send({ status: 'failure', message: `Account not found` });
  }

  try {
    const emailAddressesSnapshot = await accountRef.collection('EmailAddresses')
      .where('isPrimary', '==', true)
      .get();
    const primaryEmailDoc = emailAddressesSnapshot.docs[0];

    const phoneNumbersSnapshot = await accountRef.collection('PhoneNumbers')
      .where('isPrimary', '==', true)
      .get();
    const primaryPhoneDoc = phoneNumbersSnapshot.docs[0];

    res.status(200).json({
      status: 'success',
      message: `Account fetched successfully.`,
      data: {
        id: accountId,
        name: accountDoc.data()?.name,
        verifiedTwilioNumber: accountDoc.data()?.verifiedTwilioNumber,
        primaryEmail: primaryEmailDoc?.data()?.email,
        primaryPhoneNumber: primaryPhoneDoc?.data()
      }
    });
  } catch (error) {
    console.error(`Error getting account ${accountId}. ${error.message}`);
    res.status(500).send({ status: 'failure', message: `Error getting account ${accountId}. ${error.message}` });
  }
});
