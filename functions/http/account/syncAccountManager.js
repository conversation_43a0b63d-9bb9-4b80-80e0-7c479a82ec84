const { logToFirestore } = require("../../utils/functionLogger");
const { onRequest } = require("firebase-functions/v2/https");

exports.syncAccountManager = (admin, db) =>
  onRequest(async (request, response) => {
    if (request.method !== "POST") {
      return response.status(405).send("Method Not Allowed");
    }

    const { accountId, email, userId, action } = request.body;

    if (!accountId || !email || !userId || !action) {
      return response.status(400).send("Missing required parameters");
    }

    const lowerCasedEmail = email.toLowerCase();
    let updatedAccounts;
    let logMessage = "";

    try {
      const authSnapshot = await admin.auth().getUser(userId);
      const existingAuthClaims = authSnapshot.customClaims || {};

      if (existingAuthClaims.type === "internal") {
        await logToFirestore({
          functionName: "syncAccountManager",
          type: "info",
          message: "Request received for internal user",
          data: {
            email: email,
            userId: userId,
            action: action,
            lowerCasedEmail: lowerCasedEmail,
          },
        });
        return response.status(400).send("Cannot sync internal user");
      }

      const userDocRef = db.collection("Users").doc(userId);
      const userData = await userDocRef.get();

      if (!userData.exists) {
        await logToFirestore({
          functionName: "syncAccountManager",
          type: "error",
          message: "User not found in Firestore",
          data: {
            email: email,
            userId: userId,
            action: action,
            lowerCasedEmail: lowerCasedEmail,
          },
        });
        return response.status(404).send("User not found");
      }

      const currentFirestoreClaims = userData.data().claims || {};
      const currentAccountsInFirestore = currentFirestoreClaims.accounts || [];

      let actionTaken = false;

      if (action === "add") {
        if (!currentAccountsInFirestore.includes(accountId)) {
          updatedAccounts = [...currentAccountsInFirestore, accountId];
          logMessage = "Account added to user claims and Firestore";
          actionTaken = true;
        } else {
          updatedAccounts = currentAccountsInFirestore;
          logMessage = "Account already linked to user, no action taken";
        }
      } else if (action === "remove") {
        if (currentAccountsInFirestore.includes(accountId)) {
          updatedAccounts = currentAccountsInFirestore.filter(
            (acct) => acct !== accountId
          );
          logMessage = "Account removed from user claims and Firestore";
          actionTaken = true;
        } else {
          updatedAccounts = currentAccountsInFirestore;
          logMessage = "Account not linked to user, no action taken";
        }
      } else {
        await logToFirestore({
          functionName: "syncAccountManager",
          type: "error",
          message: "Invalid action provided",
          data: {
            accountId: accountId,
            email: email,
            userId: userId,
            action: action,
            lowerCasedEmail: lowerCasedEmail,
          },
        });
        return response
          .status(400)
          .send("Invalid action. Must be 'add' or 'remove'.");
      }

      if (actionTaken) {
        await userDocRef.update({
          "claims.accounts": updatedAccounts,
        });

        await admin.auth().setCustomUserClaims(userId, {
          ...existingAuthClaims,
          accounts: updatedAccounts,
        });
      }

      await logToFirestore({
        functionName: "syncAccountManager",
        type: "info",
        message: logMessage,
        data: {
          accountId: accountId,
          email: email,
          userId: userId,
          action: action,
          lowerCasedEmail: lowerCasedEmail,
          updatedAccounts: updatedAccounts,
        },
      });

      return response.status(200).send(logMessage);
    } catch (error) {
      await logToFirestore({
        functionName: "syncAccountManager",
        type: "error",
        message: "Error syncing account manager",
        data: {
          accountId: accountId,
          email: email,
          userId: userId,
          action: action,
          lowerCasedEmail: lowerCasedEmail,
          error: error.message,
          stack: error.stack,
        },
      });

      return response
        .status(500)
        .send("Internal Server Error: " + error.message);
    }
  });
