const { onRequest } = require("firebase-functions/v2/https");
const { FieldValue } = require('firebase-admin/firestore');

exports.updateAccount = (db) => onRequest(async (req, res) => {

  if (req.method !== 'POST') {
    return res.status(405).send({ status: 'failure', message: 'Method Not Allowed.' });
  }

  const { accountId, data } = req.body;

  if (!data) {
    return res.status(400).send({ status: 'failure', message: 'No data provided to be updated.' });
  }

  if (!accountId) {
    return res.status(400).send({ status: 'failure', message: 'Account ID is required.' });
  }

  const accountRef = db.collection('Account').doc(accountId);

  const accountDoc = await accountRef.get();
  if (!accountDoc.exists) {
    res.status(404).send({ status: 'failure', message: `Account not found` });
  }

  try {
    await accountRef.set({
      ...data,
      updatedAt: FieldValue.serverTimestamp()
    }, { merge: true });

    return res.status(200).send({ status: "success", message: "Account updated successfully." });
  } catch (error) {
    console.error('Error: ', error.message);
    return res.status(500).send({ status: 'failure', message: 'Error updating account.' });
  }
});
