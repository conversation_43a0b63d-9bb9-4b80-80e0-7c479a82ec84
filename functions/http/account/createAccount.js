const { logger } = require("firebase-functions");
const {
  Timestamp,
  FieldValue,
  getFirestore,
} = require("firebase-admin/firestore");
const { onRequest } = require("firebase-functions/v2/https");
const { dateStringToTimestamp } = require("../../utils/dateStringToTimestamp");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");

const { setMigrationLog } = require("../../migration/utils/setMigrationLog");
const { isEmpty } = require("lodash");

exports.createAccount = onRequest(async (req, res) => {
  if (req.method !== "POST") {
    res.status(405).send("Method Not Allowed");
  }

  const { account, plans } = req.body;
  await saveAccount({ account, plans });
  res.status(200).send("Account created successfully");
});

async function saveAccount({ account, plans }) {
  try {
    await setAccount(account);
    if (Array.isArray(plans) && !isEmpty(plans)) {
      await setAccountPlan(plans);
    }
  } catch (error) {
    logger.error("handleMissingAccount: ", error);
    throw error;
  }
}

async function setAccount(accountRecord) {
  const {
    id: legacyAccountId,
    name,
    display_name: displayName,
    slug,
    timezone,
    market_uuid: marketUuid,
    created_at: createdAt,
    updated_at: updatedAt,
    email_addresses: emailAddressRecords,
  } = accountRecord ?? {};

  const db = getFirestore();
  const accountId = legacyAccountId?.toString();
  const accountRef = db.collection(collectionNames.account).doc(accountId);
  try {
    await accountRef.set(
      {
        name,
        displayName,
        slug,
        timezone,
        marketUuid,
        createdAt: dateStringToTimestamp(createdAt),
        isActive: true,
        ...{ updatedAt: dateStringToTimestamp(updatedAt) },
        isRmcMigration: true,
      },
      { merge: true }
    );
    await setMigrationLog({
      id: accountId,
      record: accountRecord,
      type: "account",
      isSuccess: true,
    });
  } catch (error) {
    logger.error("Problem writing accountDetails for accountId: ", accountId);
    await setMigrationLog({
      id: accountId,
      record: accountRecord,
      type: "account",
      isSuccess: false,
      message: error.message,
      errorStack: error.stack,
    });
  }
}

async function setAccountPlan(accountPlans) {
  for (const accountPlanRecord of accountPlans) {
    // logger.info("accountPlanRecord: ", accountPlanRecord);
    const {
      id: accountPlanId,
      plan_id: planId,
      account_id: accountId,
      active_at: activeAt,
      created_at: createdAt,
      updated_at: updatedAt,
    } = accountPlanRecord;
    try {
      const db = getFirestore();
      const accountPlanRef = db
        .collection(collectionNames.account)
        .doc(accountId.toString())
        .collection(subCollectionNames.account.accountPlans)
        .doc(planId.toString());

      await accountPlanRef.set({
        planId: planId.toString(),
        accountId: accountId.toString(),
        accountPlanId: accountPlanId.toString(),
        createdAt: dateStringToTimestamp(createdAt),
        activeAt: dateStringToTimestamp(activeAt),
        isActive: true,
        ...{ updatedAt: dateStringToTimestamp(updatedAt) },
      });
      await setMigrationLog({
        id: `${accountId}-${accountPlanId}`,
        record: accountPlans,
        type: "accountPlan",
        isSuccess: true,
      });
    } catch (error) {
      logger.error(error);
      await setMigrationLog({
        id: `${accountId}-${accountPlanId}`,
        record: accountPlans,
        type: "accountPlan",
        isSuccess: false,
        message: error.message,
        errorStack: error.stack,
      });
    }
  }
}
