const { logger } = require("firebase-functions");
const {
  Timestamp,
  FieldValue,
  getFirestore,
} = require("firebase-admin/firestore");
const { onRequest } = require("firebase-functions/v2/https");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const {
  legacyProductIds,
  brandedMagazineProductId,
} = require("../../constants/magazineProductIds");
const {
  getGroupIdsByProductAssociation,
} = require("../../recipients/productHelpers/productHelpers");
const { logToFirestore } = require("../../utils/functionLogger");
const { dateStringToTimestamp } = require("../../utils/dateStringToTimestamp");

// Helper function to convert Firestore Timestamps to ISO strings
function convertTimestampsToISO(obj) {
  const converted = { ...obj };
  for (const key in converted) {
    if (
      converted[key] &&
      converted[key].toDate &&
      typeof converted[key].toDate === "function"
    ) {
      converted[key] = converted[key].toDate().toISOString();
    }
  }
  return converted;
}

exports.accountPlans = (db) =>
  onRequest(async (req, res) => {
    // Fixed method checking logic
    if (req.method !== "POST" && req.method !== "GET") {
      res.status(405).send("Method Not Allowed");
      return;
    }

    let response = {};
    let actions = [];

    const { accountId, removedPlan = null, addedPlan = null } = req.body || {};
    const { account_id } = req.query || {};

    if (!accountId && !account_id) {
      return res.status(400).send("Missing account id");
    }

    // Fixed: Check for POST method AND missing plans
    if (req.method === "POST" && !removedPlan && !addedPlan) {
      return res.status(400).send("No plans to update");
    }

    // Fixed: Check for GET method AND account_id
    if (req.method === "GET" && account_id) {
      try {
        const accountPlans = await getAccountPlans(db, account_id);
        console.log("Account Plans:", accountPlans);

        response.data = accountPlans;
        response.count = accountPlans.length;
        response.accountId = account_id;
        actions.push("getAccountPlans");

        if (!accountPlans || accountPlans.length === 0) {
          response.message = "No plans found for the account";
        }
      } catch (error) {
        logger.error("Error getting account plans:", error);
        return res.status(500).send("Error retrieving account plans");
      }
    }

    if (addedPlan && !removedPlan && req.method === "POST") {
      try {
        await setAccountPlan(db, addedPlan, accountId);
        actions.push(`setAccountPlan ${addedPlan.plan_id || addedPlan}`);
      } catch (error) {
        logger.error("Error adding plan:", error);
        return res.status(500).send("Error adding plan");
      }
    }

    if (removedPlan && !addedPlan && req.method === "POST") {
      try {
        const removed = await removeAccountPlan(db, removedPlan, accountId);
        actions.push(`removeAccountPlan ${removedPlan} - ${removed.status}`);
      } catch (error) {
        logger.error("Error removing plan:", error);
        return res.status(500).send("Error removing plan");
      }
    }

    if (addedPlan && removedPlan && req.method === "POST") {
      // has removed a magazine product?
      // has added a branded magazine product?
      // if so then we want to replace the legacy plan group productPlan with the branded magazine group productPlan
      console.log("adding and removing plan....");
      const shouldReplaceMagazinePlan = isBrandedMagazineReplacement({
        addedPlan,
        removedPlan,
      });
      console.log("shouldReplaceMagazinePlan", shouldReplaceMagazinePlan);
      try {
        if (shouldReplaceMagazinePlan) {
          const groupIdsWithLegacyProduct =
            await getGroupIdsByProductAssociation({
              accountId,
              products: [String(removedPlan?.plan_id)],
            });

          for (const groupId of groupIdsWithLegacyProduct) {
            console.log("addedPlan.plan_id", addedPlan?.plan_id);
            console.log("removedPlan.plan_id", removedPlan?.plan_id);
            const groupRef = db
              .collection(collectionNames.account)
              .doc(accountId)
              .collection(subCollectionNames.contacts.recipientGroups)
              .doc(groupId);
            await setAccountPlan(db, addedPlan, accountId);
            // add branded magazine prouct to group
            await groupRef.update({
              productPlans: FieldValue.arrayUnion(String(addedPlan.plan_id)),
            });
            // remove legacy product from group
            const removed = await removeAccountPlan(db, removedPlan, accountId);
            actions.push(
              `removeAccountPlan ${removedPlan} - ${removed?.status}`
            );
            await groupRef.update({
              productPlans: FieldValue.arrayRemove(String(removedPlan.plan_id)),
            });
          }
        } else {
          await setAccountPlan(db, addedPlan?.plan_id, accountId);
          const removed = await removeAccountPlan(db, removedPlan, accountId);
          actions.push(`removeAccountPlan ${removedPlan} - ${removed.status}`);
        }
      } catch (error) {
        logger.error("Error updating account plans:", error);
        await logToFirestore({
          functionName: "accountPlans",
          type: "error",
          message: error.message,
          data: {
            errorStack: error.stack,
          },
        });
      }
    }

    response.accountId = accountId || account_id;
    response.actions = actions;

    res.status(200).send(response);
  });

async function setAccountPlan(db, plan, accountId) {
  const record = {
    planId: plan?.plan_id,
    accountId,
    isActive: true,
    by: "crm_update",
    ...(plan?.created_at && {
      createdAt: dateStringToTimestamp(plan?.created_at),
    }),
    ...(plan?.active_at && {
      activeAt: dateStringToTimestamp(plan?.active_at),
    }),
    ...(plan?.updated_at && {
      updatedAt: dateStringToTimestamp(plan?.updated_at),
    }),
  };
  try {
    const accountPlanRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.account.accountPlans)
      .doc(plan.plan_id.toString());

    await accountPlanRef.set(record, { merge: true });
  } catch (error) {
    logger.error("Error setting account plan:", error);
    throw error;
  }
}

async function removeAccountPlan(db, plan, accountId) {
  try {
    const planId = plan.toString();
    const accountPlanRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.account.accountPlans)
      .doc(planId);

    // Check if the plan exists before attempting to delete
    const planDoc = await accountPlanRef.get();

    if (!planDoc.exists) {
      logger.info(
        `Plan ${planId} does not exist for account ${accountId}, skipping deletion`
      );
      return {
        status: "not_found",
        message: "Plan does not exist, no action taken",
      };
    }

    // Delete the plan document
    await accountPlanRef.delete();
    logger.info(
      `Successfully removed plan ${planId} from account ${accountId}`
    );

    // Optionally update assignments when removing a plan
    try {
      await updateAssignments(db, accountId, planId);
    } catch (updateError) {
      // Log the error but don't fail the entire operation
      logger.warn(
        `Failed to update assignments after removing plan ${planId}:`,
        updateError
      );
    }

    return { status: "removed", message: "Plan successfully removed" };
  } catch (error) {
    logger.error("Error removing account plan:", error);
    // Instead of throwing, we'll return success with a warning
    logger.warn(
      `Treating plan removal as success despite error for plan ${plan} in account ${accountId}`
    );
    return {
      status: "error_ignored",
      message: "Error occurred but treating as success",
    };
  }
}

async function updateAssignments(db, accountId, planId) {
  try {
    const assignmentsRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.account.assignments);

    const assignmentsSnapshot = await assignmentsRef.get();
    if (assignmentsSnapshot.empty) {
      logger.info(
        `No assignments found for account ${accountId}, skipping update`
      );
      return;
    }

    const batch = db.batch();
    let updateCount = 0;

    assignmentsSnapshot.forEach((doc) => {
      const assignmentData = doc.data();
      if (assignmentData.plan_id === planId) {
        batch.update(doc.ref, { plan_id: null });
        updateCount++;
      }
    });

    if (updateCount > 0) {
      await batch.commit();
      logger.info(
        `Updated ${updateCount} assignments for removed plan ${planId}`
      );
    } else {
      logger.info(`No assignments were using plan ${planId}`);
    }
  } catch (error) {
    logger.error("Error updating assignments:", error);
    // Don't throw here - we want plan removal to succeed even if assignment update fails
    throw error;
  }
}

async function getAccountPlans(db, accountId) {
  try {
    const accountPlansRef = db
      .collection("Account")
      .doc(accountId.toString())
      .collection("AccountPlans");

    const accountPlansSnapshot = await accountPlansRef.get();
    if (accountPlansSnapshot.empty) {
      return [];
    }

    const accountPlans = await Promise.all(
      accountPlansSnapshot.docs.map(async (doc) => {
        let data = doc.data();
        data.id = doc.id;
        data = convertTimestampsToISO(data);
        const assignmentData = await getAssignmentsByPlan(
          db,
          accountId,
          data.planId || doc.id
        );
        data.assignments = assignmentData;
        return data;
      })
    );

    console.log("Account Plans: ", accountPlans);
    return accountPlans;
  } catch (error) {
    logger.error("Error in getAccountPlans:", error);
    throw error;
  }
}

async function getAssignmentsByPlan(db, accountId, planId) {
  try {
    const assignmentsRef = db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .where("productPlans", "array-contains", planId);

    const assignmentsSnapshot = await assignmentsRef.get();
    if (assignmentsSnapshot.empty) {
      return [];
    }
    const assignments = [];
    assignmentsSnapshot.forEach((doc) => {
      let data = doc.data();
      data = convertTimestampsToISO(data);
      const rec = {
        group: data.name,
        meta: data.meta || {},
        id: doc.id,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
      };
      assignments.push(rec);
    });

    return assignments;
  } catch (error) {
    logger.error("Error in getAssignmentsByPlan:", error);
    throw error;
  }
}

async function isBrandedMagazineReplacement({ addedPlan, removedPlan }) {
  const isAddedPlanBrandedMagazineProduct = brandedMagazineProductId.includes(
    addedPlan?.plan_id
  );
  const isRemovedPlanMagazineProduct = legacyProductIds.includes(
    removedPlan?.plan_id
  );

  return isAddedPlanBrandedMagazineProduct && isRemovedPlanMagazineProduct;
}
