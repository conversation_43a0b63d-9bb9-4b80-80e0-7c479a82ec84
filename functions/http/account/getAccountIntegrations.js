const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");

exports.getAccountIntegrations = (db) => onRequest(async (req, res) => {
  if (req.method !== 'GET') {
    res.status(405).send("Method Not Allowed");
  }

  const { accountId } = req.query;

  if (!accountId) {
    return res.status(400).send({ status: 'failure', message: 'Account ID is required.' });
  }

  try {
    const accountRef = db.collection('Account').doc(accountId);
    const accountDoc = await accountRef.get();
    if (!accountDoc.exists) {
      res.status(404).send({ status: 'failure', message: `Account not found` });
    }

    const accountIntegrationsSnapshot = await accountRef.collection('Integrations').get();
    const accountIntegrations = accountIntegrationsSnapshot.docs.map(doc => doc.data());

    res.status(200).json({
      status: 'success',
      message: `Account integrations fetched successfully.`,
      data: {
        id: accountId,
        integrations: accountIntegrations,
      }
    });
  } catch (error) {
    logger.error(`Error getting account ${accountId}. ${error.message}`);
    res.status(500).send({ status: 'failure', message: `Error getting account ${accountId}. ${error.message}` });
  }
});
