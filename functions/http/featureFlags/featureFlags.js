const { onCall, onRequest } = require("firebase-functions/v2/https");
const { Timestamp } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");

exports.featureFlags = (db) =>
  onRequest(async (request, response) => {
    const { flag, accountId } = request.body;
    let status = false;
    try {
      if (!flag || !accountId) {
        return response
          .status(400)
          .send("Invalid input: 'flag' and 'accountId' are required.");
      }

      const flagData = await db.collection("FeatureFlag").doc(flag).get();

      if (!flagData.exists) {
        return response.status(404).send("Flag not found");
      }

      const flagInfo = flagData.data();

      // Check if accountId is in accountIds
      if (
        Array.isArray(flagInfo.accountIds) &&
        flagInfo.accountIds.includes(accountId)
      ) {
        status = true;
        return response.status(200).send({ status: status });
      } else {
        return response.status(403).send({ status: status });
      }
    } catch (error) {
      logger.error(
        `Error fetching feature flag for flag '${flag}' and accountId '${accountId}':`,
        error
      );
      response.status(500).send("Error fetching feature flag");
    }
  });
