const functions = require("firebase-functions/v2");
const { FieldValue } = require("firebase-admin/firestore");
const { shortenUrl } = require("../../utils/shortenUrl");
const { addCdnUrlsToExistingRecord } = require("../../utils/qrCodes/generateAndSaveQRCodes");

/**
 * Cloud Function to shorten a URL.
 */
exports.createShortUrl = (db) =>
  functions.https.onRequest(async (req, res) => {
    try {
      // Extract data from the request body
      const {
        longUrl = null,
        lpId = null,
        domain = "https://rmco.pro",
        accountId = null,
        productId = null,
        linkData = {},
      } = req.body;

      // Check for missing fields - Requires LongUrl and AccountId or lpId
      if (!longUrl) {
        if (!lpId) {
          return res
            .status(400)
            .json({ success: false, error: "Missing field: longUrl/lpId" });
        }
      }
      if (!accountId) {
        if (!lpId) {
          return res
            .status(400)
            .json({ success: false, error: "Missing field: accountId" });
        }
      }

      // Call the shortenUrl function
      const result = await shortenUrl(
        { longUrl, lpId, domain, accountId, productId, linkData },
        db
      );

      return res.status(200).json({ success: true, data: result });
    } catch (error) {
      console.error("Error shortening URL:", error);
      return res.status(500).json({ success: false, error: error.message });
    }
  });

/**
 * Cloud Function to track a click on a shortened URL.
 */
exports.trackClick = (db) =>
  functions.https.onRequest(async (req, res) => {
    const shortId = req.path.slice(1);

    try {
      if(shortId && shortId !== "codeForward"){
        const codeDoc = await db.collection("codes").doc(shortId).get();

        if (!codeDoc.exists) {
          return res.status(404).send("URL not found");
        }

        const longUrl = codeDoc.data().longUrl;

        // Increment the click count
        await db
          .collection("codes")
          .doc(shortId)
          .update({
            clicks: FieldValue.increment(1),
          });

        const trafficRecord = {
          ipAddress: req.ip,
          ips: req.ips,
          userAgent: req.get("User-Agent"),
          timestamp: FieldValue.serverTimestamp(),
        };

        // Save requestor information to the "traffic" subcollection
        await db
          .collection("codes")
          .doc(shortId)
          .collection("traffic")
          .add(trafficRecord);

        const separator = longUrl.includes("?") ? "&" : "?";
        const destination = `${longUrl}${separator}shortCode=${shortId}`;

        // Redirect to the original long URL
        res.redirect(destination);
      } else if(shortId === "codeForward"){
        const { code: newShort } = req.query;
        try{

          const codeDoc = await db.collection("codes").doc(newShort).get();

          if (!codeDoc.exists) {
            return res.status(404).send("URL not found");
          }

          const longUrl = codeDoc.data().longUrl;

          // Increment the click count
          await db
            .collection("codes")
            .doc(newShort)
            .update({
              clicks: FieldValue.increment(1),
            });

          const trafficRecord = {
            ipAddress: req.ip,
            ips: req.ips,
            userAgent: req.get("User-Agent"),
            timestamp: FieldValue.serverTimestamp(),
          };

          // Save requestor information to the "traffic" subcollection
          await db
            .collection("codes")
            .doc(newShort)
            .collection("traffic")
            .add(trafficRecord);

          const separator = longUrl.includes("?") ? "&" : "?";
          const destination = `${longUrl}${separator}shortCode=${newShort}`;


          // Redirect to the original long URL
          res.redirect(destination);
        } catch (error) {
          console.error("Error processing referer:", error);
          return res.status(500).send({ message: error.message, body: req?.body || null, headers: req.headers, query: req.query, params: req.params });
        }
      } else {
        res.status(404).send("URL not found");
      }
    } catch (error) {
      console.error("Error tracking click:", error);
      res.status(500).send({ message: error.message, body: req?.body || null, headers: req.headers, query: req.query, params: req.params });
    }
  });

exports.linkDomains = (db) =>
  functions.https.onRequest(async (req, res) => {
    try {
      const domainsSnapshot = await db
        .collection("Settings")
        .where("group", "==", "links.domains")
        .where("isActive", "==", true)
        .get();
      const domains = domainsSnapshot.docs.map((doc) => doc.data());
      res.status(200).json({ success: true, data: domains });
    } catch (error) {
      console.error("Error Fetching Link Domains:", error.message);
      res.status(400).send("Error Fetching Link Domains:", error.message);
    }
  });

/**
 * Cloud Function to get landing page data including QR codes
 * Updated to create CDN URLs if they're missing
 */
exports.lpData = (db) =>
  functions.https.onRequest(async (req, res) => {
    const { lpId } = req.query;

    // Fail if no lpId is provided
    if (!lpId) {
      return res.status(400).json({
        success: false,
        message: "Error: lpId is required",
      });
    }

    try {
      // Query the 'codes' collection to get the relevant documents
      const codesSnapshot = await db
        .collection("codes")
        .where("linkData.page_uuid", "==", lpId)
        .get();
      if (codesSnapshot.empty) {
        return res.status(404).json({
          success: false,
          message: "No codes found for the provided lpId",
        });
      }

      // Create an array to hold the result
      const codesWithTraffic = [];

      // Iterate through each code document
      for (const codeDoc of codesSnapshot.docs) {
        let codeData = codeDoc.data();
        const codeId = codeDoc.id;

        // Check if CDN URLs are missing and generate them if needed
        if (codeData.qrCodes && (!codeData.cdnQrCodes)) {
          try {
            console.log(`Adding CDN URLs for code ${codeId}`);
            const updatedRecord = await addCdnUrlsToExistingRecord(codeData);
            
            // Update the document in Firestore
            await db.collection("codes").doc(codeId).update({
              cdnQrCodes: updatedRecord.cdnQrCodes,
              storagePaths: updatedRecord.storagePaths || codeData.storagePaths,
              bucketName: updatedRecord.bucketName || codeData.bucketName,
              updatedAt: FieldValue.serverTimestamp()
            });
            
            // Update the code data with the new CDN URLs
            codeData = {
              ...codeData,
              cdnQrCodes: updatedRecord.cdnQrCodes,
              storagePaths: updatedRecord.storagePaths || codeData.storagePaths,
              bucketName: updatedRecord.bucketName || codeData.bucketName
            };
          } catch (error) {
            console.error(`Error adding CDN URLs for code ${codeId}:`, error);
            // Continue with the original data if there's an error
          }
        }

        // Fetch the 'traffic' subcollection for each code
        const trafficSnapshot = await db
          .collection("codes")
          .doc(codeId)
          .collection("traffic")
          .get();
        const trafficData = trafficSnapshot.docs.map((doc) => doc.data());

        // Combine code data with traffic data and ensure CDN URLs are included
        const codeWithTraffic = {
          longUrl: codeData.longUrl,
          shortUrl: codeData.shortUrl,
          clicks: codeData.clicks,
          productId: codeData.productId,
          qrCodes: codeData.qrCodes,
          // Use cdnQrCodes if available, otherwise cdnUrls, or fall back to qrCodes
          cdnQrCodes: codeData.cdnQrCodes || codeData.qrCodes,
          // Use whatever storage paths are available
          storagePaths: codeData.storagePaths || codeData.paths,
          bucketName: codeData.bucketName,
          ...codeData.linkData,
          traffic: trafficData,
        };

        codesWithTraffic.push(codeWithTraffic);
      }

      // Return the result with the codes and their traffic data
      res.status(200).json({
        success: true,
        data: codesWithTraffic,
      });
    } catch (error) {
      console.error("Error Fetching Landing Page Data:", error.message);
      res.status(500).json({
        success: false,
        message: "Error Fetching Landing Page Data",
        error: error.message,
      });
    }
  });

/**
 * Cloud Function to get data for a specific short link
 * Updated to create CDN URLs if they're missing
 */
exports.linkData = (db) =>
  functions.https.onRequest(async (req, res) => {
    const { shortCode = null, shortUrl = null } = req.query;
    try {
      // 1. Validate that either shortCode or shortUrl is provided
      if (!shortCode && !shortUrl) {
        return res.status(400).json({
          success: false,
          message: "Error: Either shortCode or shortUrl is required",
        });
      }

      // 2. Extract shortCode from shortUrl if shortUrl is provided
      let code = shortCode;
      if (shortUrl) {
        try {
          const urlParts = new URL(shortUrl);
          code = urlParts.pathname.split("/").pop(); // Extract last part of the path as shortCode
        } catch (urlError) {
          return res.status(400).json({
            success: false,
            message: "Error: Invalid shortUrl provided",
          });
        }
      }

      // 3. Fetch the document using the shortCode
      const shortLinkSnapshot = await db.collection("codes").doc(code).get();
      if (!shortLinkSnapshot.exists) {
        return res.status(404).json({
          success: false,
          message: `ShortCode ${code} not found`,
        });
      }

      // 4. Get the data and check if CDN URLs need to be added
      let shortLinkData = shortLinkSnapshot.data();
      
      // Check if CDN URLs are missing and generate them if needed
      if (shortLinkData.qrCodes && (!shortLinkData.cdnQrCodes)) {
        try {
          console.log(`Adding CDN URLs for code ${code}`);
          const updatedRecord = await addCdnUrlsToExistingRecord(shortLinkData);
          
          // Update the document in Firestore
          await db.collection("codes").doc(code).update({
            cdnQrCodes: updatedRecord.cdnQrCodes,
            storagePaths: updatedRecord.storagePaths || shortLinkData.storagePaths,
            bucketName: updatedRecord.bucketName || shortLinkData.bucketName,
            updatedAt: FieldValue.serverTimestamp()
          });
          
          // Update the data with the new CDN URLs
          shortLinkData = {
            ...shortLinkData,
            cdnQrCodes: updatedRecord.cdnQrCodes,
            storagePaths: updatedRecord.storagePaths || shortLinkData.storagePaths,
            bucketName: updatedRecord.bucketName || shortLinkData.bucketName
          };
        } catch (error) {
          console.error(`Error adding CDN URLs for code ${code}:`, error);
          // Continue with the original data if there's an error
        }
      }
      
      // Prepare the final data to return
      const data = {
        id: shortLinkSnapshot.id,
        ...shortLinkData,
      };
      
      // Ensure cdnQrCodes is populated
      data.cdnQrCodes = data.cdnQrCodes || data.qrCodes;
      
      // 5. Fetch the 'traffic' subcollection if it exists
      const trafficSnapshot = await db
        .collection("codes")
        .doc(code)
        .collection("traffic")
        .get();

      if (!trafficSnapshot.empty) {
        const trafficData = trafficSnapshot.docs.map((doc) => doc.data());
        data.traffic = trafficData;
      } else {
        data.traffic = [];
      }

      res.status(200).json({ success: true, data });
    } catch (error) {
      console.error("Error retrieving data:", error);
      res.status(500).json({
        success: false,
        message: "Error retrieving data",
        error: error.message,
      });
    }
  });

exports.domainUpdate = (db) =>
  functions.https.onRequest(async (req, res) => {
    const { accountId } = req.query;
    let message = "No codes found";
    try {
      const codesSnapShot = await db
        .collection("codes")
        .where("accountId", "==", accountId)
        .get();
      if (codesSnapShot.size > 0) {
        message = `Updating ${codesSnapShot.size} codes`;
      }
      const data = {
        message,
        count: codesSnapShot.size,
      };

      res.status(200).json({ success: true, data: data });
    } catch (error) {
      console.error("Error Fetching Link Domains:", error.message);
      res.status(400).send("Error Fetching Link Domains:", error.message);
    }
  });

exports.shortURLTraffic = (db) =>
  functions.https.onRequest(async (req, res) => {
    const { lpId } = req.query;
    try {
      const codesSnapshot = await db
        .collection("codes")
        .where("linkData.page_uuid", "==", lpId)
        .get();
      if (codesSnapshot.empty)
        return res
          .status(404)
          .json({ success: false, message: "No codes found" });

      let totalVisits = 0,
        uniqueVisits = new Set(),
        qrScans = 0;

      for (const doc of codesSnapshot.docs) {
        const trafficSnapshot = await doc.ref.collection("traffic").get();
        totalVisits += trafficSnapshot.size;
        uniqueVisits = new Set([
          ...uniqueVisits,
          ...trafficSnapshot.docs.map((d) => d.data().ipAddress),
        ]);
        qrScans += doc.data().clicks || 0;
      }

      const data = {
        totalVisits,
        uniqueVisits: uniqueVisits.size,
        qrScans,
      };

      res.status(200).json({
        success: true,
        data,
        message: "Analytics data fetched successfully",
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: "Error fetching analytics data",
        error: error.message,
      });
    }
  });

exports.lpAnalytics = (db) =>
  functions.https.onRequest(async (req, res) => {
    const { lpId, eventType = "page_visit" } = req.query;

    try {
      // Get analytics data
      let analyticsQuery = db
        .collection("Analytics")
        .where("landingPageId", "==", lpId);

      const leadsSnapshot = await analyticsQuery.where("eventType", "==", "form_completed").get();

      if (eventType) {
        analyticsQuery = analyticsQuery.where("eventType", "==", eventType);
      }

      const analyticsSnapshot = await analyticsQuery.get();

      let totalVisits = 0;
      let uniqueIps = new Set();

      analyticsSnapshot.forEach((doc) => {
        const analyticsData = doc.data();
        totalVisits += 1;
        if (analyticsData.visitorData?.ipAddress) {
          uniqueIps.add(analyticsData.visitorData.ipAddress);
        }
      });

      // Get codes data
      const codesQuery = db
        .collection("codes")
        .where("linkData.page_uuid", "==", lpId);

      const codesSnapshot = await codesQuery.get();
      
      // Create array of promises for getting clicks
      const clickPromises = codesSnapshot.docs.map(doc => getClicks(db, doc.id));
      
      // Wait for all click counts to resolve
      const clickCounts = await Promise.all(clickPromises);

      // Sum up total clicks
      const totalClicks = clickCounts.reduce((sum, count) => sum + count, 0);

      const data = {
        totalVisits,
        uniqueVisits: uniqueIps.size,
        qrScans: totalClicks,
        totalLeads: leadsSnapshot.size,
      };

      res.status(200).json({
        success: true,
        data,
        message: "Analytics data fetched successfully",
      });
    } catch (error) {
      console.error("Error in lpAnalytics:", error);
      res.status(400).json({
        success: false,
        message: "Error fetching analytics data",
        error: error.message,
      });
    }
  });

async function getClicks(db, code) {
  try {
    const trafficSnapshot = await db
      .collection("codes")
      .doc(code)
      .collection("traffic")
      .get();
    
    return trafficSnapshot.size;
  } catch (error) {
    console.error(`Error getting clicks for code ${code}:`, error);
    return 0; // Return 0 if there's an error
  }
}