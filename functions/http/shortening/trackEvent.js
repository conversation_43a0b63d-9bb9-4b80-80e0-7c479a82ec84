const { onCall, onRequest } = require("firebase-functions/v2/https");
const { Timestamp } = require("firebase-admin/firestore");

// Utility function to convert snake_case to camelCase
const toCamelCase = (str) => {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
};

// Recursively transform object keys to camelCase and format timestamps
const transformData = (data) => {
  if (Array.isArray(data)) {
    return data.map(transformData);
  } else if (data && typeof data === "object") {
    return Object.keys(data).reduce((acc, key) => {
      const camelKey = toCamelCase(key);
      let value = data[key];

      // Format ISO timestamps to Firestore Timestamp
      if (
        typeof value === "string" &&
        /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)
      ) {
        value = Timestamp.fromDate(new Date(value));
      } else if (typeof value === "object") {
        value = transformData(value);
      }

      acc[camelKey] = value;
      return acc;
    }, {});
  }
  return data;
};

exports.trackEvent = (db) =>
  onRequest(async (request, response) => {
    const { event } = request.body;

    try {
      const transformedData = transformData(event);
      const analyticsData = {
        ...transformedData,
        createdAt: Timestamp.now(),
      };
      await db.collection("Analytics").add(analyticsData);
    } catch (error) {
      console.error("Error adding analytics data", error);
      response.status(500).send("Error adding analytics data");
    }
    response.status(200).send("Analytics data added successfully");
  });
