const { onRequest } = require("firebase-functions/v2/https");
const { addToTopic } = require("../../utils/pubsub/addToTopic");
const { defineString } = require("firebase-functions/params");

const projectIdEnv = defineString("TITAN_PROJECT_ID");

exports.emailPreProcess = (db) =>
  onRequest(async (req, res) => {
    const { recipientGroupPaths } = req.body;
    console.log("req body", req.body);

    const topic = "email-pre-process-worker";
    const message = JSON.stringify({ recipientGroupPaths });
    console.log("message", message);

    const projectId = projectIdEnv.value();

    const queueItem = {
      topic,
      message,
      projectId,
    };

    console.log("queueItem", queueItem);

    await addToTopic(queueItem);

    res.status(200).send("Email pre-process worker added to queue");
  });
