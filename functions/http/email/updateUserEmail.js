const { logToFirestore } = require("../../utils/functionLogger");
const { onRequest } = require("firebase-functions/v2/https");

const isValidEmail = (email) => {
  const emailRegex =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return emailRegex.test(String(email).toLowerCase());
};

exports.updateUserEmail = (admin, db) =>
  onRequest(async (request, response) => {
    if (request.method !== "POST") {
      await logToFirestore({
        functionName: "updateUserEmail",
        type: "warn",
        message: "Method Not Allowed",
        data: { method: request.method },
      });
      return response.status(405).send("Method Not Allowed");
    }

    const { userId, oldEmail, newEmail } = request.body;

    if (!userId || typeof userId !== "string") {
      await logToFirestore({
        functionName: "updateUserEmail",
        type: "error",
        message: "Invalid or missing userId in request body",
        data: { payload: request.body },
      });
      return response.status(400).send("Invalid or missing userId.");
    }

    if (!oldEmail || typeof oldEmail !== "string" || !isValidEmail(oldEmail)) {
      await logToFirestore({
        functionName: "updateUserEmail",
        type: "error",
        message: "Invalid or missing oldEmail in request body",
        data: { userId, payload: request.body },
      });
      return response.status(400).send("Invalid or missing oldEmail.");
    }

    if (!newEmail || typeof newEmail !== "string" || !isValidEmail(newEmail)) {
      await logToFirestore({
        functionName: "updateUserEmail",
        type: "error",
        message: "Invalid or missing newEmail in request body",
        data: { userId, payload: request.body },
      });
      return response.status(400).send("Invalid or missing newEmail.");
    }

    if (oldEmail.toLowerCase() === newEmail.toLowerCase()) {
      await logToFirestore({
        functionName: "updateUserEmail",
        type: "info",
        message: "New email is the same as old email, no action needed.",
        data: { userId, oldEmail, newEmail },
      });
      return response
        .status(200)
        .send("New email is the same as old email, no action taken.");
    }

    try {
      const authSnapshot = await admin.auth().getUser(userId);

      if (authSnapshot.email.toLowerCase() !== oldEmail.toLowerCase()) {
        await logToFirestore({
          functionName: "updateUserEmail",
          type: "error",
          message:
            "Provided oldEmail does not match user's current email in Auth.",
          data: {
            userId,
            oldEmail,
            newEmail,
            authEmail: authSnapshot.email,
          },
        });
        return response
          .status(400)
          .send(
            "The provided old email does not match the user's current email."
          );
      }

      if (
        authSnapshot.customClaims &&
        authSnapshot.customClaims.type === "internal"
      ) {
        await logToFirestore({
          functionName: "updateUserEmail",
          type: "info",
          message:
            "Attempted to update email for an internal user (not allowed).",
          data: { userId, oldEmail, newEmail },
        });
        return response
          .status(403)
          .send("Cannot update email for internal users.");
      }

      const userDocRef = db.collection("Users").doc(userId);
      const userData = await userDocRef.get();

      if (!userData.exists) {
        await logToFirestore({
          functionName: "updateUserEmail",
          type: "error",
          message: "User document not found in Firestore.",
          data: { userId, oldEmail, newEmail },
        });
        return response.status(404).send("User not found in Firestore.");
      }

      await admin.auth().updateUser(userId, { email: newEmail });

      await userDocRef.update({ email: newEmail });

      await logToFirestore({
        functionName: "updateUserEmail",
        type: "info",
        message: "User email updated successfully.",
        data: { userId, oldEmail, newEmail },
      });

      return response.status(200).send("User email updated successfully.");
    } catch (error) {
      let errorMessage = error.message;
      let statusCode = 500;

      if (error.code) {
        switch (error.code) {
          case "auth/user-not-found":
            statusCode = 404;
            errorMessage = "User not found.";
            break;
          case "auth/email-already-in-use":
            statusCode = 409;
            errorMessage =
              "The new email address is already in use by another account.";
            break;
          case "auth/invalid-email":
            statusCode = 400;
            errorMessage = "The new email address is not valid.";
            break;
          case "auth/invalid-argument":
            statusCode = 400;
            errorMessage = "Invalid argument provided: " + error.message;
            break;
          default:
            statusCode = 500;
            errorMessage =
              "An unexpected authentication error occurred: " + error.message;
            break;
        }
      }

      await logToFirestore({
        functionName: "updateUserEmail",
        type: "error",
        message: "Error updating user email.",
        data: {
          userId: userId,
          oldEmail: oldEmail,
          newEmail: newEmail,
          error: error.message,
          stack: error.stack,
          errorCode: error.code || "N/A",
        },
      });

      return response.status(statusCode).send(errorMessage);
    }
  });
