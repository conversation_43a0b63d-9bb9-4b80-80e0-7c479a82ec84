const { onRequest } = require("firebase-functions/v2/https");
const { Timestamp } = require("firebase-admin/firestore");
const cors = require("cors")({ origin: true });
const { logToFirestore } = require("../utils/functionLogger");
exports.shortLivedTokenForCustomers = (admin) =>
  onRequest(async (request, response) => {
    return cors(request, response, async () => {
      function setTempPassword(accountId) {
        const numberAccountId = parseInt(accountId);

        const tempValue = numberAccountId * numberAccountId + numberAccountId;

        return tempValue.toString() + "rmc";
      }

      if (request.method !== "POST") {
        return response.status(405).send("Method Not Allowed");
      }
      const { accountId, email, name } = request.body;

      let lowerCasedEmail = email.toLowerCase();

      try {
        const userSnapshot = await admin
          .firestore()
          .collection("Users")
          .where("claims.accounts", "array-contains", accountId)
          .get();
        if (userSnapshot.empty) {
          const authUser = await admin.auth().createUser({
            email: lowerCasedEmail,
            password: setTempPassword(accountId),
            emailVerified: true,
            displayName: name,
            name: name,
          });

          await admin.auth().setCustomUserClaims(authUser.uid, {
            type: "customer",
            accounts: [accountId],
            role: "general",
          });

          const userData = {
            email: lowerCasedEmail,
            emailVerified: true,
            createdAt: Timestamp.now(),
            claims: {
              type: "customer",
              accounts: [accountId],
              role: "general",
            },
            displayName: name,
            name: name,
          };
          await admin
            .firestore()
            .collection("Users")
            .doc(authUser.uid)
            .set(userData);

          const token = await admin.auth().createCustomToken(authUser.uid);

          return response.status(200).json({ token });
        } else {
          const userId = userSnapshot.docs[0].id;
          const userData = userSnapshot.docs[0].data();
          const userDataLowerCasedEmail = userData.email.toLowerCase();

          try {
            // Check if auth user exists
            await admin.auth().getUser(userId);
          } catch (error) {
            // Auth user doesn't exist, create it
            await admin.auth().createUser({
              uid: userId,
              email: userDataLowerCasedEmail,
              password: setTempPassword(accountId),
              emailVerified: userData.emailVerified || false,
              displayName: name,
              name: name,
            });

            // Set custom claims
            await admin.auth().setCustomUserClaims(userId, userData.claims);
          }

          const token = await admin.auth().createCustomToken(userId);

          return response.status(200).json({ token });
        }
      } catch (error) {
        await logToFirestore({
          functionName: "shortLivedTokenForCustomers",
          type: "error",
          message: "Error creating token",
          data: {
            accountId: accountId,
            email: email,
            name: name,
            lowerCasedEmail: lowerCasedEmail,
            error: error.message,
            stack: error.stack,
          },
        });
        return response
          .status(400)
          .send("Authentication failed:" + error.message);
      }
    });
  });

exports.shortLivedTokenForAdmins = (admin) =>
  onRequest(async (request, response) => {
    return cors(request, response, async () => {
      if (request.method !== "POST") {
        return response.status(405).send("Method Not Allowed");
      }

      const { email } = request.body;
      let lowerCasedEmail = email.toLowerCase();
      if (lowerCasedEmail.includes(".alt")) {
        await logToFirestore({
          functionName: "shortLivedTokenForAdmins",
          type: "info",
          message: "Request received",
          data: {
            email: email,
            replacedEmail: lowerCasedEmail.replace(".alt", ""),
          },
        });
        // remove .alt from email
        lowerCasedEmail = lowerCasedEmail.replace(".alt", "");
      }

      await logToFirestore({
        functionName: "shortLivedTokenForAdmins",
        type: "info",
        message: "Request received",
        data: { email: email, lowerCasedEmail: lowerCasedEmail },
      });

      try {
        const userSnapshot = await admin
          .firestore()
          .collection("Users")
          .where("email", "==", lowerCasedEmail)
          .get();
        await logToFirestore({
          functionName: "shortLivedTokenForAdmins",
          type: "info",
          message: "User snapshot retrieved",
          data: {
            email: email,
            lowerCasedEmail: lowerCasedEmail,
            userSnapshot: userSnapshot.docs.map((doc) => doc.data()),
          },
        });

        if (userSnapshot.empty) {
          await logToFirestore({
            functionName: "shortLivedTokenForAdmins",
            type: "error",
            message: "User not found",
            data: { email: email, lowerCasedEmail: lowerCasedEmail },
          });
          return response.status(404).send("User not found");
        }

        const adminDocs = userSnapshot.docs.filter((doc) => {
          const data = doc.data();
          return data?.claims?.type === "internal";
        });

        if (adminDocs.length > 1) {
          await logToFirestore({
            functionName: "shortLivedTokenForAdmins",
            type: "error",
            message: "Multiple internal user records found",
            data: {
              email: email,
              lowerCasedEmail: lowerCasedEmail,
              numberOfRecords: adminDocs.length,
            },
          });
          return response
            .status(400)
            .send(
              `Found multiple internal user records for email: ${email}. Please resolve duplicates.`
            );
        }

        let userDocId;
        let userData;

        if (adminDocs.length === 0) {
          await logToFirestore({
            functionName: "shortLivedTokenForAdmins",
            message: "No admin record found",
            type: "error",
            data: { email: email, lowerCasedEmail: lowerCasedEmail },
          });
          return response.status(404).send("User not found");
        } else {
          const existingDoc = adminDocs[0];
          userDocId = existingDoc.id;
          userData = existingDoc.data();
        }

        const token = await admin.auth().createCustomToken(userDocId);
        await logToFirestore({
          functionName: "shortLivedTokenForAdmins",
          message: "Token created",
          type: "info",
          data: {
            email: email,
            lowerCasedEmail: lowerCasedEmail,
            token: token,
          },
        });

        return response.status(200).json({ token });
      } catch (error) {
        await logToFirestore({
          functionName: "shortLivedTokenForAdmins",
          message: "Error creating token",
          type: "error",
          data: {
            email: email,
            lowerCasedEmail: lowerCasedEmail,
            error: error.message,
            stack: error.stack,
          },
        });
        console.error("Error creating token", error);
        return response
          .status(400)
          .send("Authentication failed:" + error.message);
      }
    });
  });
