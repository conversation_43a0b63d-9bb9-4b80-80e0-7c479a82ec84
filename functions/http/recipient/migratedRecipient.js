// https://firebase.google.com/docs/functions/gcp-storage-events?gen=2nd
const { Timestamp, getFirestore } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const { dateStringToTimestamp } = require("../../utils/dateStringToTimestamp");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const {
  isAccountDataMissing,
  handleMissingAccount,
} = require("../../migration/handleMissingAccounts");
const { setMigrationLog } = require("../../migration/utils/setMigrationLog");
const { onRequest } = require("firebase-functions/v2/https");

exports.migratedRecipient = onRequest(async (req, res) => {
  if (req.method !== "POST") {
    res.status(405).send("Method Not Allowed");
  }
  const { recipient } = req.body;
  logger.info("Body: " + req.body);
  await handleRecipient(recipient);
  res.status(200).send("Recipient created successfully");
});
async function handleRecipient(recipient) {
  const db = getFirestore();
  logger.info("Recipient: ", recipient);
  const jsonData = recipient;
  const accountId = jsonData.account_id.toString();
  try {
    // const hasMissingAccountData = await isAccountDataMissing(accountId);
    // if (hasMissingAccountData) {
    //   await handleMissingAccount(accountId);
    // }

    const recipientRef = db
      .collection(collectionNames.account)
      .doc(accountId)
      .collection(collectionNames.contacts)
      .doc(jsonData.uuid);
    let baseRecipient = await setBaseRecipient(jsonData);
    let uuids = [];
    if (jsonData?.cg_uuid) {
      uuids.push(jsonData.cg_uuid);
    }
    baseRecipient.recipientGroupIds = uuids;
    const overrides = await getOverrides(jsonData);
    if (overrides) {
      if (overrides.isDuplicate) {
        baseRecipient.isDuplicate = true;
      }
      if (overrides.isMailingSendAnyway) {
        baseRecipient.overrideDeliverability.address = true;
      }
      if (overrides.isPaused) {
        baseRecipient.mailingsPaused = true;
      }
    }
    await recipientRef.set(baseRecipient, { merge: true });
    if (jsonData.address && jsonData.address.length > 0) {
      const writePromises = jsonData.address.map((address, index) => {
        return (async () => {
          const [isValid, isExclusive, addressRecord] = await Promise.all([
            checkValid(jsonData),
            checkExclusive(jsonData),
            getAddressRecord(address),
          ]);

          addressRecord.migrationMetadata = {
            deliverabilityStatus: jsonData.deliverability_status,
            cds: jsonData.cds,
          };
          addressRecord.isMigratedValid = isValid;
          addressRecord.isMigratedExclusive = isExclusive;

          return recipientRef
            .collection(subCollectionNames.contacts.userEnteredMailingAddress)
            .doc(index.toString())
            .set(
              { ...addressRecord, processingStatus: "preprocess" },
              { merge: true }
            );
        })();
      });

      // Await all Firestore writes to complete.
      await Promise.all(writePromises);
    }

    const writePromises = [];

    // Emails
    if (jsonData.emails && jsonData.emails.length > 0) {
      writePromises.push(setEmails(jsonData, recipientRef));
    }

    // Phone Numbers
    if (jsonData.phoneNumbers && jsonData.phoneNumbers.length > 0) {
      writePromises.push(
        ...jsonData.phoneNumbers.map((phoneNumber) => {
          let priority = phoneNumber.priority === 1;
          if (!priority && jsonData.phoneNumbers.length === 1) {
            priority = true;
          }
          const newRecord = {
            phoneNumber: phoneNumber.number,
            phoneExtension: phoneNumber.extension,
            label: phoneNumber.label,
            isPrimary: priority,
            createdAt: dateStringToTimestamp(phoneNumber.created_at) || "",
            updatedAt: dateStringToTimestamp(phoneNumber.updated_at) || "",
          };
          return recipientRef
            .collection(subCollectionNames.contacts.phoneNumbers)
            .doc(phoneNumber.uuid)
            .set(newRecord, { merge: true });
        })
      );
    }

    // Notes
    if (jsonData.notes && jsonData.notes.length > 0) {
      writePromises.push(
        ...jsonData.notes.map((note) => {
          const newRecord = {
            value: note.note,
            createdAt: dateStringToTimestamp(note.created_at) || "",
            updatedAt: dateStringToTimestamp(note.updated_at) || "",
          };
          return recipientRef
            .collection(subCollectionNames.contacts.notes)
            .doc(note.uuid)
            .set(newRecord, { merge: true });
        })
      );
    }

    // SignificantDates
    if (jsonData.dates && jsonData.dates.length > 0) {
      writePromises.push(
        ...jsonData.dates.map((date) => {
          const newRecord = {
            date: dateStringToTimestamp(date.date),
            label: date.label,
            createdAt: dateStringToTimestamp(date.created_at) || "",
            updatedAt: dateStringToTimestamp(date.updated_at) || "",
          };
          return recipientRef
            .collection(subCollectionNames.contacts.significantDates)
            .doc(date.uuid)
            .set(newRecord, { merge: true });
        })
      );
    }

    // Social Media
    if (jsonData.socialMedia && jsonData.socialMedia.length > 0) {
      writePromises.push(
        ...jsonData.socialMedia.map((sm) => {
          const newRecord = {
            value: sm.value,
            label: sm.label,
            isPrimary: sm.priority === 1,
            createdAt: dateStringToTimestamp(sm.created_at) || "",
            updatedAt: dateStringToTimestamp(sm.updated_at) || "",
          };
          return recipientRef
            .collection(subCollectionNames.contacts.socialMediaAccounts)
            .doc(sm.uuid)
            .set(newRecord, { merge: true });
        })
      );
    }

    // Websites
    if (jsonData.websites && jsonData.websites.length > 0) {
      writePromises.push(
        ...jsonData.websites.map(async (web) => {
          if(!web?.uri && web?.uuid) {
            const newRecord = {
              url: web?.uri ? ensureHttpProtocol(web?.uri) : "",
              label: web?.label,
              createdAt: dateStringToTimestamp(web?.created_at) || "",
              updatedAt: dateStringToTimestamp(web?.updated_at) || "",
            };
          return recipientRef
              .collection(subCollectionNames.contacts.websites)
              .doc(web.uuid)
              .set(newRecord, { merge: true });
          }})        
      );
    }

    // Real Estate
    if (jsonData.purchase_date) {
      const realEstateRecord = {
        date: dateStringToTimestamp(jsonData.purchase_date) || "",
        price: jsonData.purchase_price || "",
      };
      writePromises.push(
        recipientRef
          .collection(subCollectionNames.contacts.realEstate)
          .doc("0")
          .set(realEstateRecord, { merge: true })
      );
    }

    // Await all writes in parallel
    await Promise.all(writePromises);
    await setMigrationLog({
      id: `${accountId}-${jsonData.uuid}`,
      record: jsonData,
      type: "recipient",
      isSuccess: true,
    });
  } catch (error) {
    logger.error(
      "Problem in handleRecipient error: ",
      error.message,
      error.stack
    );
    await setMigrationLog({
      id: `${accountId}-${jsonData.uuid}`,
      record: jsonData,
      type: "recipient",
      isSuccess: false,
      message: error.message,
      errorStack: error.stack,
    });
  }
}

async function ensureHttpProtocol(url) {
  if (!/^https?:\/\//i.test(url)) {
    return `http://${url}`;
  }
  return url;
}

async function getAddressRecord(addressData) {
  let newAddress = {};
  newAddress.isRmcMigration = true;
  newAddress.address1 = addressData.address1 || "";
  newAddress.address2 = addressData.address2 || "";
  newAddress.city = addressData.city || "";
  newAddress.state = addressData.state || "";
  newAddress.postalCode = addressData.zip || "";
  newAddress.label = addressData.label || "";
  newAddress.isMigratedValid = false;
  newAddress.isMigratedExclusive = false;
  return newAddress;
}

async function checkValid(jsonData) {
  let isValid = false;
  if (jsonData?.rmcValidation?.[0].status === "validated") {
    return true;
  }
  let targetType =
    "Infrastructure\\Models\\MailingAddress\\MailingAddressValidation";

  const validationResult = findMostRecentCdRecordByType(
    jsonData?.cds,
    targetType
  );

  if (validationResult) {
    const dataValue = JSON.parse(
      validationResult?.deliverability_status_properties
    );
    if (dataValue?.address_status === "validated") {
      isValid = true;
    }
  }
  return isValid;
}

function findMostRecentCdRecordByType(records, targetType) {
  let mostRecentRecord = null;

  for (const record of records) {
    if (record.deliverability_status_type === targetType) {
      // If we haven't set a record yet, or the current record is more recent
      if (
        !mostRecentRecord ||
        new Date(record.created_at) > new Date(mostRecentRecord.created_at)
      ) {
        mostRecentRecord = record;
      }
    }
  }

  return mostRecentRecord;
}

async function checkExclusive(jsonData) {
  let isExclusive = false;
  if (jsonData?.rmcExclusive?.[0].status === "exclusive") {
    return true;
  }
  let targetType =
    "Infrastructure\\Models\\MailingAddress\\MailingAddressExclusiveAddress";
  const result =
    jsonData?.cds?.find(
      (item) => item.deliverability_status_type === targetType
    ) || null;
  if (result) {
    const dataValue = JSON.parse(result.deliverability_status_properties);
    if (
      dataValue?.address_status === "exclusive" ||
      dataValue?.address_status === "duplicate" ||
      dataValue?.address_status === "overridden"
    ) {
      isExclusive = true;
    }
  }
  return isExclusive;
}

async function getOverrides(addressData) {
  let overrides = {};
  let targetType = "Modules\\Contacts\\Models\\Address";
  const resultDeliverability =
    addressData?.cds?.filter(
      (item) => item.deliverability_status_type === targetType
    ) || null;
  if (resultDeliverability) {
    for (const cds of resultDeliverability) {
      const dataValue = JSON.parse(cds?.deliverability_status_properties);
      if (dataValue?.address_status === "RECIPIENT_STATUS_DA") {
        overrides.isDuplicate = true;
      }
      if (
        dataValue?.address_status === "RECIPIENT_STATUS_AS" ||
        dataValue?.address_status === "overridden"
      ) {
        overrides.isMailingSendAnyway = true;
      }
      if (dataValue?.address_status === "RECIPIENT_STATUS_MAILING_PAUSED") {
        overrides.isPaused = true;
      }
    }
  }
  // handling the following contact data scenario:
  // for when there is no override and no cd data but the deliverability_status sends the product
  // essentially implicit overrides
  const isMigratedValid = await checkValid(addressData);
  const isMigratedExclusive = await checkExclusive(addressData);
  if (
    (isMigratedExclusive === false || isMigratedValid === false) &&
    (addressData.deliverability_status === "will_send_magazine" ||
      addressData.deliverability_status === "will_send_all_products")
  ) {
    overrides.isMailingSendAnyway = true;
  }

  targetType = "App\\Models\\ContactProductSubscription";
  const resultEmail =
    addressData?.cds?.filter(
      (item) => item.deliverability_status_type === targetType
    ) || null;
  if (resultEmail) {
    for (const cds of resultEmail) {
      const dataValue = JSON.parse(cds?.deliverability_status_properties);
      if (!dataValue?.subscription_status?.is_subscribed) {
        overrides.emailIsPaused = true;
      }
    }
  }

  return overrides;
}

async function setEmails(data, recipientRef) {
  let overridePrimary = false;
  if (data?.emails?.length === 1) {
    overridePrimary = true;
  }
  for (const email of data.emails) {
    const newRecord = {
      isRmcMigrated: true,
      isRmcValid: email.is_valid === 1 ? true : false,
      email: email.email_address.toLowerCase(),
      label: email.label,
      isPrimary: email.priority === 1 ? true : overridePrimary ? true : false,
      markedInvalidAt: email.marked_invalid_at
        ? dateStringToTimestamp(email.marked_invalid_at)
        : "",
      hardbounceRetry: email.hard_bounce_retried_at
        ? dateStringToTimestamp(email.hard_bounce_retried_at)
        : "",
      mailingEmailAddressId: email.mailing_email_address_id || "",
      createdAt: dateStringToTimestamp(email.created_at) || "",
      updatedAt: dateStringToTimestamp(email.updated_at) || "",
    };
    let targetType = "Modules\\Mailing\\Models\\MailingRecipient";
    const result =
      data?.cds?.find(
        (item) => item.deliverability_status_type === targetType
      ) || null;
    if (result) {
      const dataValue = JSON.parse(result?.deliverability_status_properties);
      const emailAddressStatus = dataValue?.email_address_status;
      if (emailAddressStatus) {
        if (emailAddressStatus?.hard_bounce) {
          newRecord.isHardBounced = true;
        }
        if (emailAddressStatus?.spam_complaints) {
          newRecord.isSpamComplaint = emailAddressStatus?.spam_complaints;
        }
        if (emailAddressStatus?.unsubscribed) {
          newRecord.isUnsubscribed = emailAddressStatus?.unsubscribed;
        }
      }
    }
    await recipientRef
      .collection(subCollectionNames.contacts.emailAddresses)
      .doc(email.uuid)
      .set(newRecord, { merge: true });
  }
}

async function setBaseRecipient(jsonData) {
  let newRecipient = {};
  newRecipient.accountId = jsonData.account_id;
  newRecipient.migrationDate = Timestamp.now();
  newRecipient.createdAt = dateStringToTimestamp(jsonData.created_at) || "";
  newRecipient.updatedAt = dateStringToTimestamp(jsonData.updated_at) || "";
  newRecipient.isActive = jsonData.deleted_at ? false : true;
  newRecipient.legacyContactGroupId = jsonData.contact_group_id || "";
  newRecipient.legacyRecipientId = jsonData.id || "";
  newRecipient.name = {};
  newRecipient.name.firstName = jsonData?.first_name || "";
  newRecipient.name.lastName = jsonData?.last_name || "";
  newRecipient.name.suffix = jsonData?.name_suffix || "";
  newRecipient.name.nickname = jsonData?.nickname || "";
  newRecipient.name.phoneticFirstName = jsonData.phonetic_first_name || "";
  newRecipient.name.phoneticLastName = jsonData.phonetic_last_name || "";
  newRecipient.name.prefix = jsonData.name_prefix || "";
  newRecipient.significantOther = {};
  newRecipient.significantOther.firstName =
    jsonData?.significant_other_first_name || "";
  newRecipient.significantOther.lastName =
    jsonData?.significant_other_last_name || "";
  newRecipient.significantOther.middleName =
    jsonData?.significant_other_middle_name || "";
  newRecipient.significantOther.suffix =
    jsonData?.significant_other_name_suffix || "";
  newRecipient.significantOther.prefix =
    jsonData?.significant_other_name_prefix || "";
  newRecipient.significantOther.birthday = jsonData?.significant_other_birthday
    ? dateStringToTimestamp(jsonData?.significant_other_birthday)
    : "";
  newRecipient.salutation = {};
  newRecipient.salutation.company = jsonData?.company || "";
  newRecipient.salutation.jobTitle = jsonData?.title || "";
  newRecipient.salutation.mailingSalutation =
    jsonData?.mailing_salutation || "";
  newRecipient.salutation.letterSalutation = jsonData?.letter_salutation || "";
  newRecipient.overrideDeliverability = { address: false, email: false };
  return newRecipient;
}
