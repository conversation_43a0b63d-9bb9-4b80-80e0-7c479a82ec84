const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const { isEmpty, isNil, isString, isArray } = require("lodash");
const {
  collectValidRecipientsByGroup,
} = require("../../utils/collectValidRecipientsByGroup");

exports.recipients = (db) =>
  onRequest(
    {
      memory: "1GiB",
      timeoutSeconds: 540,
    },
    async (req, res) => {
      if (req.method === "GET") {
        await handleGetRequest({ req, res, db });
      } else {
        res.status(405).json({ error: "Method Not Allowed" });
      }
    }
  );

async function handleGetRequest({ req, res, db }) {
  const {
    accountId,
    recipientGroupId = null,
    productId = null,
    method,
  } = req.query;

  if (isEmpty(accountId) || isNil(accountId)) {
    res.status(400).json({
      error: "Bad request: 'accountId' is a required query parameter.",
    });
    return;
  }

  if (isEmpty(productId) && isEmpty(recipientGroupId)) {
    res.status(400).json({
      error:
        "Bad request: 'productId' or 'recipientGroupId' is a required query parameter.",
    });
    return;
  }

  const validMethods = ["address", "email"];
  if (!validMethods.includes(method)) {
    res.status(400).json({
      error: `Bad request: 'method' must be one of ${validMethods.join(", ")}.`,
    });
    return;
  }

  try {
    let recipientResults = {
      validRecipents: [],
      total: 0,
      count: 0,
    };
    let myGroups = [];

    const recipientGroupCollectionRef = db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups");

    let recipientGroupIds = Array.isArray(recipientGroupId)
      ? recipientGroupId
      : recipientGroupId != null
        ? Object.values(recipientGroupId)
        : [];

    // Ensure recipientGroupIds is an array
    if (!isEmpty(recipientGroupIds)) {
      if (!Array.isArray(recipientGroupIds)) {
        // Check if it's a comma-separated string
        if (
          typeof recipientGroupIds === "string" &&
          recipientGroupIds.includes(",")
        ) {
          recipientGroupIds = recipientGroupIds
            .split(",")
            .map((id) => id.trim());
        } else {
          recipientGroupIds = [recipientGroupIds];
        }
      }
    }

    if (!isEmpty(recipientGroupIds)) {
      // Handle the case where 'recipientGroupId' is provided as an array or single value
      for (const groupId of recipientGroupIds) {
        if (!isString(groupId)) {
          console.log("groupId is not a string", groupId);
          continue;
        }

        const recipientGroupDocRef = recipientGroupCollectionRef.doc(groupId);
        const recipientGroupSnapshot = await recipientGroupDocRef.get();

        if (!recipientGroupSnapshot.exists) {
          continue;
        }

        const groupData = recipientGroupSnapshot.data();

        // Check if the product is assigned to this group (if 'productId' is provided)
        if (!isEmpty(productId)) {
          if (
            groupData?.assignments &&
            groupData.assignments.hasOwnProperty(productId.toString())
          ) {
            myGroups.push(groupId);
          } else {
            res.status(404).json({
              error: `Product ${productId} not assigned to recipient group: ${groupId}`,
            });
            return;
          }
        } else {
          // No 'productId' provided, include the group
          myGroups.push(groupId);
        }
      }
    } else if (!isEmpty(productId)) {
      // Handle the case where 'productId' is provided but 'recipientGroupId' is not
      const recipientGroupQuery = recipientGroupCollectionRef.where(
        "isActive",
        "==",
        true
      );
      const recipientGroupSnapshot = await recipientGroupQuery.get();

      if (recipientGroupSnapshot.empty) {
        logger.info(
          `No active recipient groups found for account: ${accountId}`
        );
        res.status(200).json({
          message:
            "Recipients retrieved successfully. No active recipient groups found.",
          recipients: {},
        });
        return;
      }

      recipientGroupSnapshot.forEach((groupDoc) => {
        const groupData = groupDoc.data();
        if (
          groupData?.assignments &&
          groupData.assignments.hasOwnProperty(productId.toString())
        ) {
          myGroups.push(groupDoc.id);
        }
      });

      if (myGroups.length === 0) {
        logger.info(
          `No recipient groups have product ${productId} assigned for account: ${accountId}`
        );
        res.status(200).json({
          message: `Product ${productId} is not assigned to any recipient groups.`,
          recipients: {},
        });
        return;
      }
    }
    console.log("myGroups:", myGroups);
    logger.info(
      `Processing recipient groups: ${myGroups.join(", ")} for account: ${accountId}`
    );

    // Only call collectValidRecipientsByGroup if we have groups to process
    if (myGroups.length > 0) {
      recipientResults = await collectValidRecipientsByGroup({
        db,
        groups: myGroups,
        accountId,
        method,
        productId,
      });
    }
    if (method === "email") {
      const { uniqueEmails, total, count } = recipientResults;
      console.log("recipientResults: ", uniqueEmails);
      console.log("total: ", total);
      console.log("count: ", count);
      res.status(200).json({
        message: "Recipients retrieved successfully.",
        recipients: uniqueEmails,
        total,
        count,
      });
    } else {
      const { validRecipients, total, count } = recipientResults;
      console.log("recipientResults: ", validRecipients);
      console.log("total: ", total);
      console.log("count: ", count);
      res.status(200).json({
        message: "Recipients retrieved successfully.",
        recipients: validRecipients,
        total,
        count,
      });
    }
  } catch (error) {
    logger.error(
      `Failed to retrieve recipients for account ${accountId}:`,
      error
    );
    res.status(500).json({
      error: "Failed to retrieve recipients.",
      message: error.message,
    });
  }
}
