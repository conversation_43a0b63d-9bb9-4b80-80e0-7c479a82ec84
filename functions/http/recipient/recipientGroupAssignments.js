const { onRequest } = require("firebase-functions/v2/https");
const { isNil } = require("lodash");
const { rgAssignmentsBM } = require("../../utils/recipients/rgAssignmentsBM");
const {
  rgLegacyAssignments,
} = require("../../utils/recipients/rgLegacyAssignments");
const {
  rgPostcardAssignments,
} = require("../../utils/recipients/rgPostcardAssignments");
const {
  rgStandardAssignments,
} = require("../../utils/recipients/rgStandardAssignments");

// Define product categories for clear routing
const MAGAZINE_PRODUCT = "12";
const POSTCARD_PRODUCT = "8";
const LEGACY_PRODUCTS = ["1", "3", "7", "11"];

exports.recipientGroupAssignments = (db) =>
  onRequest({ timeoutSeconds: 300 }, async (req, res) => {
    if (req.method === "POST") {
      console.log("POST request received for recipient group assignments");
      await handlePostRequest({ req, res, db });
    } else {
      res.status(405).send("Method not allowed");
    }
  });

async function handlePostRequest({ req, res, db }) {
  const { productId, accountId, campaignId, action } = req.body;
  const body = req.body;

  const requiresCampaign = [MAGAZINE_PRODUCT, POSTCARD_PRODUCT];

  let response = {
    status: 200,
    message: "Recipient Groups Updated.",
  };

  // Validate required parameters
  if (isNil(action)) {
    console.log("Error: action parameter is missing");
    res.status(400).send(`Bad request, action is a required query param.`);
    return;
  }

  if (isNil(accountId)) {
    console.log("Error: accountId parameter is missing");
    res.status(400).send(`Bad request, accountId is a required query param`);
    return;
  }

  if (isNil(productId)) {
    console.log("Error: productId parameter is missing");
    res.status(400).send(`Bad request, productId is a required query param`);
    return;
  }

  if (requiresCampaign.includes(productId) && isNil(campaignId)) {
    console.log(`Error: campaignId is required for product ${productId}`);
    res.status(400).send(`Bad request, campaignId is a required query param`);
    return;
  }

  switch (productId) {
    case MAGAZINE_PRODUCT:
      console.log("🔄 FIRING MAGAZINE HANDLER (Product 12)");
      response = await rgAssignmentsBM({ body, db });
      break;

    case POSTCARD_PRODUCT:
      console.log("🔄 FIRING POSTCARD HANDLER (Product 8)");
      response = await rgPostcardAssignments({ body, db });
      break;

    case "1":
    case "3":
    case "7":
    case "11":
      console.log(`🔄 FIRING LEGACY PRODUCT HANDLER (Product ${productId})`);
      response = await rgLegacyAssignments({ body, db });
      break;

    default:
      console.log(`🔄 FIRING STANDARD PRODUCT HANDLER (Product ${productId})`);
      response = await rgStandardAssignments({ body, db });
      break;
  }
  return res.status(response.status).send(response);
}
