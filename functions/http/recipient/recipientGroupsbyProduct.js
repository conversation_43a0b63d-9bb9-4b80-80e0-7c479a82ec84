/* eslint-disable no-undef */
const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const { Timestamp, FieldValue } = require("firebase-admin/firestore");
const { isNil, isEmpty } = require("lodash");

exports.recipientGroupsByProduct = (db) =>
  onRequest(async (req, res) => {
    if (req.method === "POST") {
      await handlePostRequest({ req, res, db });
      return;
    }
  });

async function handlePostRequest({ req, res, db }) {
  const { accountId, name, description } = req.body;
  if (isEmpty(accountId)) {
    res.status(400).send(`Bad request, accountId is required`);
    return;
  }

  

  /**
   * Add recipientId to Recipient group recipientIds array
   */
  try {
    const newRecipientGroup = {
      name: name,
      description: description,
      assignments: {},
      contactIds: [],
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      isActive: true,
      isPrimary: false,
      meta: {},
      productPlans: [],
    }
    const newGroup = await db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .add(newRecipientGroup);

      res.status(200).json({
        message: "Recipient group created successfully",
        name: name,
        description: description,
        id: newGroup?.id || "",
      });
    
  } catch (error) {
    logger.error(
      "Failed to add new recipientGroup",
      "accountId: ",
      accountId,
      "name: ",
      name,
      "description: ",
      description,
      "error: ",
      error
    );
    res.status(500).json({
      error: "Failed to add recipient to recipientGroup",
      message: error.message,
    });
    return;
  }

  
}