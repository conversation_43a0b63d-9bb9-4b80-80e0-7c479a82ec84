const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const { FieldValue, Timestamp } = require("firebase-admin/firestore");
const { createUUID } = require("../../utils/createUUID");
const { isNil, isEmpty, isArray } = require("lodash");
const { dateStringToTimestamp } = require("../../utils/dateStringToTimestamp");

exports.recipient = (db) =>
  onRequest(async (req, res) => {
    if (req.method === "POST") {
      await handlePostRequest({ req, res, db });
      return;
    }

    if (req.method === "GET") {
      await handleGetRequest({ req, res, db });
    }
  });

async function handlePostRequest({ req, res, db }) {
  const {
    accountId: accountIdInt,
    leadId: leadIdInt,
    firstName = "",
    lastName = "",
    phoneNumber,
    email,
    externalLeadId,
    sourceUrl,
  } = req.body;

  const accountId = accountIdInt.toString();
  const leadId = leadIdInt.toString();

  // logger.info("request body: " + JSON.stringify(req.body));

  if (isNil(leadId)) {
    res.status(400).send(`Bad request, leadId is required`);
    return;
  } else if (isNil(accountId)) {
    res.status(400).send(`Bad request, accountId is required`);
    return;
  } else if (isNil(phoneNumber) && isNil(email)) {
    res.status(400).send(`phoneNumber or email is required`);
    return;
  } else if (isNil(firstName) && isNil(lastName)) {
    res.status(400).send(`firstName or lastName is required`);
    return;
  }

  const name = { firstName, lastName };

  /**
   * Create or update recipient
   */
  const recipientRef = await createOrUpdateRecipient({
    db,
    accountId,
    leadId,
    name,
    phoneNumber,
    email,
    res,
  });

  // logger.info(`Recipient ref: ${JSON.stringify(recipientRef)}`);

  if (isNil(recipientRef)) return;

  try {
    const [
      emailSubCollectionResult,
      phoneSubCollectionResult,
      assignRecipientResult,
    ] = await Promise.all([
      // Add email to recipient email subcollection
      createOrUpdateEmailSubcollection({
        accountId,
        email,
        recipientRef,
      }),

      // Add phoneNumber to recipient phoneNumber subcollection
      createOrUpdatePhoneSubcollection({
        accountId,
        phoneNumber,
        recipientRef,
      }),

      // Assign Recipient to Recipient Group
      assignRecipientToRecipientGroup({
        db,
        accountId,
        recipientRef,
        externalLeadId,
        sourceUrl,
      }),
    ]);

    // logger.info(
    //   "emailSubCollecttionResult: ",
    //   emailSubCollectionResult,
    //   "phoneSubCollectionResult: ",
    //   phoneSubCollectionResult,
    //   "assignRecipientResult: ",
    //   assignRecipientResult
    // );
    res.status(200).json({
      message: `Recipient added successfully. ${emailSubCollectionResult?.message}. ${phoneSubCollectionResult?.message}. ${assignRecipientResult?.message}.`,
      recipientId: recipientRef.id,
    });

    // logger.info(
    //   "Created Recipient",
    //   "email status: ",
    //   emailSubCollectionResult?.message,
    //   "phone status: ",
    //   phoneSubCollectionResult?.message,
    //   "accountId: ",
    //   accountId,
    //   "recipientId: ",
    //   recipientRef?.id,
    //   "leadId: ",
    //   leadId,
    //   "firstName: ",
    //   firstName,
    //   "lastName: ",
    //   lastName,
    //   "phoneNumber: ",
    //   phoneNumber,
    //   "email: ",
    //   email
    // );
  } catch (error) {
    logger.error("Error in handlePostRequest:", error);
    res.status(500).send("Internal Server Error");
  }
}

async function handleGetRequest({ req, res, db }) {
  const { accountId, recipientId } = req.query;

  const recipientCollectionRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients)
    .doc(recipientId);

  try {
    const recipient = await recipientCollectionRef.get();
    if (recipient.exists) {
      const recipientData = recipient.data();

      // Fetch emails
      const emailsSnapshot = await recipientCollectionRef
        .collection(subCollectionNames.contacts.emailAddresses)
        .get();
      const emails = emailsSnapshot.docs.map((doc) => doc.data());

      // Fetch phone numbers
      const phoneNumbersSnapshot = await recipientCollectionRef
        .collection(subCollectionNames.contacts.phoneNumbers)
        .get();
      const phoneNumbers = phoneNumbersSnapshot.docs.map((doc) => doc.data());

      // Attach to recipient
      recipientData.emails = emails;
      recipientData.phoneNumbers = phoneNumbers;

      // Return everything
      res.status(200).json({
        message: "Recipient retrieved successfully.",
        recipient: recipientData,
      });
    } else {
      res.status(404).json({
        message: "Recipient not found.",
      });
    }
  } catch (error) {
    logger.error("Error in handleGetRequest:", error);
    res.status(500).send("Internal Server Error");
  }
}

async function assignRecipientToRecipientGroup({
  db,
  accountId,
  recipientRef,
  externalLeadId,
  sourceUrl,
}) {
  const LEADS_GROUP_NAME = "Facebook Leads";
  const LANDING_PAGES_GROUP_NAME = "Landing Pages";
  const recipientGroupCollectionRef = db
    .collection("Account")
    .doc(accountId)
    .collection("RecipientGroups");

  /**
   * Determine where lead originated
   * if "externalLeadId" is present then Facebook Lead
   * if "sourceUrl" then from landing pages product
   */

  if (externalLeadId) {
    const groupQuerySnapshot = await recipientGroupCollectionRef
      .where("name", "==", LEADS_GROUP_NAME)
      .limit(1)
      .get();

    if (groupQuerySnapshot.empty) {
      /**
       * No Facebook Leads Recipient Group exists
       */
      try {
        const groupRef = await recipientGroupCollectionRef.add({
          name: LEADS_GROUP_NAME,
          description: "Facebook Leads",
        });
        await groupRef.update({
          recipientIds: FieldValue.arrayUnion(recipientRef.id),
        });
        await recipientRef.update({
          recipientGroupIds: FieldValue.arrayUnion(groupRef.id),
        });
        return {
          message:
            "Created Facebook Leads Recipient Group, added recipient to group",
        };
      } catch (error) {
        logger.error(
          "Problem creating Facebook Leads recipient group and adding recipient",
          "accountId: ",
          accountId,
          "recipientId: ",
          recipientRef.id,
          "error: ",
          error
        );
        return {
          message:
            "Problem creating Facebook Leads recipient group and adding recipient",
        };
      }
    } else {
      /**
       * Facebook Leads Recipient Group exists
       */
      try {
        const groupRef = groupQuerySnapshot.docs[0].ref;
        await groupRef.update({
          recipientIds: FieldValue.arrayUnion(recipientRef.id),
        });
        await recipientRef.update({
          recipientGroupIds: FieldValue.arrayUnion(groupRef.id),
        });
        return {
          message: "Added recipient to existing Facebook Leads Recipient group",
        };
      } catch (error) {
        logger.error(
          "Problem adding recipient to existing Facebook Leads Recipient group",
          "accountId: ",
          accountId,
          "recipientId: ",
          recipientRef.id,
          "error: ",
          error
        );
        return {
          message:
            "Problem adding recipient to existing Facebook Leads Recipient group",
        };
      }
    }
  } else if (sourceUrl) {
    const groupQuerySnapshot = await recipientGroupCollectionRef
      .where("name", "==", LANDING_PAGES_GROUP_NAME)
      .limit(1)
      .get();

    if (groupQuerySnapshot.empty) {
      /**
       * No Landing Pages Recipient Group exists
       */
      try {
        const groupRef = await recipientGroupCollectionRef.add({
          name: LANDING_PAGES_GROUP_NAME,
          description: "My landing page leads!",
        });
        await groupRef.update({
          recipientIds: FieldValue.arrayUnion(recipientRef.id),
        });
        await recipientRef.update({
          recipientGroupIds: FieldValue.arrayUnion(groupRef.id),
        });
        return {
          message:
            "Created Landing Pages Recipient Group, added recipient to group",
        };
      } catch (error) {
        logger.error(
          "Problem creating Landing Pages Group and adding recipient",
          "accountId: ",
          accountId,
          "recipientId: ",
          recipientRef.id,
          "error: ",
          error
        );
        return {
          message:
            "Problem creating Landing Pages Group and adding recipient to group.",
        };
      }
    } else {
      /**
       * Landing Pages Recipient Group exists
       */
      try {
        const groupRef = groupQuerySnapshot.docs[0].ref;
        await groupRef.update({
          recipientIds: FieldValue.arrayUnion(recipientRef.id),
        });
        await recipientRef.update({
          recipientGroupIds: FieldValue.arrayUnion(groupRef.id),
        });
        return {
          message: "Added recipient to existing Landing Pages group",
        };
      } catch (error) {
        logger.error(
          "Problem updating Landing Pages Group",
          "accountId: ",
          accountId,
          "recipientId: ",
          recipientRef.id,
          "error: ",
          error
        );
        return {
          message: "Problem adding recipient to existing Landing Pages group",
        };
      }
    }
  } else {
    return { message: "Did not receive externalLeadId or sourceUrl" };
  }
}

async function createOrUpdateEmailSubcollection({
  accountId,
  email,
  recipientRef,
}) {
  try {
    if (email) {
      const emailAddressSubCollectionRef = recipientRef.collection(
        subCollectionNames.contacts.emailAddresses
      );

      const emailQuerySnapshot = await emailAddressSubCollectionRef
        .where("email", "==", email)
        .limit(1)
        .get();

      if (!emailQuerySnapshot.empty) {
        return { message: "Email already exists" };
      } else {
        await emailAddressSubCollectionRef.add({
          email,
          isPrimary: true,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
        });
        return { message: "Email Added" };
      }
    }
  } catch (error) {
    logger.error(
      "Problem writing email to recipient email subcollection",
      "accountId: ",
      accountId,
      "recipientId: ",
      recipientRef.id,
      "error: ",
      error
    );

    return {
      message: "Problem writing email to recipient email subcollection",
    };
  }
}

async function createOrUpdatePhoneSubcollection({
  accountId,
  phoneNumber,
  recipientRef,
}) {
  try {
    if (phoneNumber) {
      const phoneNumberSubCollectionRef = recipientRef.collection(
        subCollectionNames.contacts.phoneNumbers
      );
      const phoneQuerySnapshot = await phoneNumberSubCollectionRef
        .where("phoneNumber", "==", phoneNumber)
        .limit(1)
        .get();

      if (!phoneQuerySnapshot.empty) {
        return { message: "Phone number already exists" };
      } else {
        await phoneNumberSubCollectionRef.add({
          phoneNumber,
          isPrimary: true,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
        });

        return { message: "Phone number added" };
      }
    }
  } catch (error) {
    logger.error(
      "Problem writing phoneNumber to recipient PhoneNumbers subcollection",
      "accountId: ",
      accountId,
      "recipientId: ",
      recipientRef.id,
      "error: ",
      error
    );
    return {
      message:
        "Problem writing phoneNumber to recipient PhoneNumbers subcollection",
    };
  }
}

async function createOrUpdateRecipient({
  accountId,
  leadId,
  name,
  phoneNumber,
  email,
  db,
  res,
}) {
  const recipientsCollectionRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients);

  /**
   * Does a recipient with leadId already exist?
   */
  const recipientQuerySnapshot = await recipientsCollectionRef
    .where("leadId", "==", leadId)
    .limit(1)
    .get();

  const recipientExists = !recipientQuerySnapshot.empty;

  if (recipientExists) {
    try {
      const recipientRef = recipientQuerySnapshot.docs[0].ref;

      await recipientRef.update({
        accountId,
        name,
        isActive: true,
        updatedAt: Timestamp.now(),
      });

      return recipientRef;
    } catch (error) {
      logger.error(
        "Problem updating recipient",
        "accountId: ",
        accountId,
        "leadId: ",
        leadId,
        "name: ",
        name,
        "phoneNumber: ",
        phoneNumber,
        "email: ",
        email,
        "error:",
        error
      );
      res.status(500).json({
        error: "Failed to update recipient in Firestore",
        message: error.message,
      });
      return;
    }
  } else {
    try {
      // recipient does not exist, create new one
      const uuid = createUUID();
      const recipientRef = recipientsCollectionRef.doc(uuid);
      await recipientsCollectionRef.doc(uuid).set({
        accountId,
        name,
        isActive: true,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        leadId,
      });
      return recipientRef;
    } catch (error) {
      logger.error(
        "Problem creating recipient",
        "accountId: ",
        accountId,
        "leadId: ",
        leadId,
        "name: ",
        name,
        "phoneNumber: ",
        phoneNumber,
        "email: ",
        email,
        "error:",
        error
      );
      res.status(500).json({
        error: "Failed to write recipient to Firestore",
        message: error.message,
      });
      return;
    }
  }
}
