/* eslint-disable no-undef */
const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const { Timestamp, FieldValue } = require("firebase-admin/firestore");
const { isNil, isEmpty, create, update } = require("lodash");
const createUUID = require("../../utils/createUUID");
const { findGroups } = require("../../utils/recipients/findGroups");



exports.recipientGroupsForPrint = (db) =>
  onRequest(async (req, res) => {
    if (req.method === "PUT") {
      await handlePatchRequest({ req, res, db });
      return;
    }
    if (req.method === "POST") {
      await handlePostRequest({ req, res, db });
      return;
    }

    if (req.method === "GET") {
      await handleGetRequest({ req, res, db });
    }
  });

async function handleGetRequest({ req, res, db }) {
  const { accountId, groupId="", groupIds=[], productPlanId, campaignId=null } = req.query;
  if (isEmpty(accountId)) {
    res.status(400).send(`Bad request, accountId is a required query param`);
    return;
  }
  try {
    let groupsPresent = false;
    if (groupIds.length > 0) {
      groupsPresent = true;
    }

    let masterGroups = [];

    let baseGroups = await db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .where("isActive", "==", true)
      .get();

    baseGroups = baseGroups.docs.map((doc) => ({ ...doc.data(), id: doc.id }));
    if(groupsPresent) {
      for (const group of baseGroups) {
        const { id } = group;
        if (recipientGroupIds.includes(id)) {
          masterGroups.push(group);
        }
      } 
    } else if(groupId !== "") {
      for (const group of baseGroups) {
        const { id } = group;
        if (id === groupId) {
          masterGroups.push(group);
        }
      } 
    }
    else {
      masterGroups = baseGroups;
    }

    const currentGroups = await findGroups(masterGroups, productPlanId, campaignId);

    res.status(200).json({
      message: "Recipient groups retrieved successfully",
      recipientGroups: currentGroups,
    });
  } catch (error) {
    logger.error(
      "Failed to retrieve recipientGroups",
      "accountId: ",
      accountId,
      "error: ",
      error
    );
    res.status(500).json({
      error: "Failed to retrieve recipientGroups",
      message: error.message,
    });
  }
}

async function handlePatchRequest({ req, res, db }) {
  const { accountId, recipientGroupId, name, description } = req.body;
  if (isEmpty(accountId)) {
    res.status(400).send(`Bad request, accountId is required`);
    return;
  }

  if (isEmpty(recipientGroupId)) {
    res.status(400).send(`Bad request, recipientGroupId is required`);
    return;
  }

  if (isEmpty(name)) {
    res.status(400).send(`Bad request, name is required`);
    return;
  }

  if (isEmpty(description)) {
    res.status(400).send(`Bad request, description is required`);
    return;
  }

  try {
    const recipientGroupRef = db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .doc(recipientGroupId);

    await recipientGroupRef.update({
      recipientIds: FieldValue.arrayUnion(recipientId),
    });
  } catch (error) {
    logger.error(
      "Failed to add recipient to recipientGroup",
      "accountId: ",
      accountId,
      "recipientGroupId: ",
      recipientGroupId,
      "recipientId: ",
      recipientId,
      "error: ",
      error
    );
    res.status(500).json({
      error: "Failed to add recipient to recipientGroup",
      message: error.message,
    });
    return;
  }

  /**
   * Add recipient group reference to recipients
   */
  try {
    const recipientRef = db
      .collection("Account")
      .doc(accountId)
      .collection("Recipients")
      .doc(recipientId);

    await recipientRef.update({
      recipientGroupIds: FieldValue.arrayUnion(recipientGroupId),
    });
  } catch (error) {
    logger.error(
      "Failed to add recipientGroup reference to recipient",
      "accountId: ",
      accountId,
      "recipientGroupId: ",
      recipientGroupId,
      "recipientId: ",
      recipientId,
      "error: ",
      error
    );
    res.status(500).json({
      error: "Failed to add recipientGroup reference to recipient",
      message: error.message,
    });
    return;
  }

  res.status(200).send("recipient added to recipient group successfully");
}

async function handlePostRequest({ req, res, db }) {
  const { accountId, name, description } = req.body;
  if (isEmpty(accountId)) {
    res.status(400).send(`Bad request, accountId is required`);
    return;
  }

  if (isEmpty(name)) {
    res.status(400).send(`Bad request, name is required`);
    return;
  }

  if (isEmpty(description)) {
    res.status(400).send(`Bad request, description is required`);
    return;
  }

  /**
   * Add recipientId to Recipient group recipientIds array
   */
  try {
    const uuid = createUUID();
    const newRecipientGroup = {
      name: name,
      description: description,
      assignments: {},
      contactIds: [],
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      isActive: true,
      isPrimary: false,
      meta: {},
      productPlans: [],
    };
    const newGroup = await db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .doc(uuid)
      .add(newRecipientGroup);

    res.status(200).json({
      message: "Recipient group created successfully",
      name: name,
      description: description,
      id: newGroup?.id || "",
    });
  } catch (error) {
    logger.error(
      "Failed to add new recipientGroup",
      "accountId: ",
      accountId,
      "name: ",
      name,
      "description: ",
      description,
      "error: ",
      error
    );
    res.status(500).json({
      error: "Failed to add recipient to recipientGroup",
      message: error.message,
    });
    return;
  }
}
