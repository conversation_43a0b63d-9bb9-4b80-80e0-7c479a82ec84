// https://firebase.google.com/docs/functions/gcp-storage-events?gen=2nd

const { Timestamp, getFirestore } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const { dateStringToTimestamp } = require("../../utils/dateStringToTimestamp");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const {
  isAccountDataMissing,
  handleMissingAccount,
} = require("../../migration/handleMissingAccounts");
const { setMigrationLog } = require("../../migration/utils/setMigrationLog");
const { onRequest } = require("firebase-functions/v2/https");
const { products } = require("../../constants/products");

exports.migratedRecipientGroup = onRequest(async (req, res) => {
  if (req.method !== "POST") {
    res.status(405).send("Method Not Allowed");
  }
  const { recipientGroup } = req.body;
  logger.info("Body: " + req.body);
  logger.info("recipientGroup: ", recipientGroup);

  await handleRecipientGroup(recipientGroup);
  res.status(200).send("RecipientGroup created successfully");
});
async function handleRecipientGroup(recipientGroup) {
  const db = getFirestore();
  const jsonData = recipientGroup;
  logger.info("accountId: ", jsonData.accountId);
  const {
    accountId,
    name,
    createdAt,
    description,
    isActive,
    isPrimary,
    legacyRecipientGroupId,
    updatedAt,
    uuid,
  } = jsonData;
  try {
    const hasMissingAccountData = await isAccountDataMissing(accountId);
    if (hasMissingAccountData) {
      await handleMissingAccount(accountId);
    }

    /**
     * Reconcile associated product plans with print campaigns
     */

    const reconciledData =
      await reconcileRecipientGroupProductAssociationsWithPrintCampaigns({
        db,
        data: jsonData,
      });

    /**
     * Determine Product Assignments
     */
    const assignments = await createProductPlanAssignments({
      db,
      data: reconciledData,
    });

    /**
     * Write recipient group data to firetore
     */

    const recipientGroupRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.recipientGroups)
      .doc(uuid);

    const result = await recipientGroupRef.set(
      {
        name,
        ...(assignments && { assignments }),
        description,
        productPlans: reconciledData.productPlans.map((plan) =>
          plan.plan_id.toString()
        ),
        isActive,
        isPrimary,
        legacyRecipientGroupId,
        createdAt: dateStringToTimestamp(createdAt),
        ...(updatedAt && { updatedAt: dateStringToTimestamp(updatedAt) }),
      },
      { merge: true }
    );
    await setMigrationLog({
      id: `${accountId}-${jsonData.uuid}`,
      record: jsonData,
      type: "recipientGroup",
      isSuccess: true,
    });
  } catch (error) {
    logger.error("Error processing the file: ", error);
    await setMigrationLog({
      id: `${accountId}-${jsonData.uuid}`,
      record: jsonData,
      type: "recipientGroup",
      isSuccess: false,
      message: error.message,
      errorStack: error.stack,
    });
    throw error;
  }
}

async function createProductPlanAssignments({ db, data }) {
  try {
    const {
      productPlans,
      bmCampaign: bmCampaignArray,
      pcCampaign: pcCampaignArray,
      campaignRecipients: campaignRecipientsArray,
      digitalEditionRecurrances: digitalEditionRecurrancesArray,
      localEventsRecurrances: localEventsRecurrancesArray,
      productSchedules: productSchedulesArray,
    } = data;
    if (!Array.isArray(productPlans)) return {};

    const campaignRecipients = campaignRecipientsArray?.[0];
    const bmCampaign = bmCampaignArray[0];
    const pcCampaign = pcCampaignArray[0];
    const digitalEditionRecurrances = digitalEditionRecurrancesArray?.[0];
    const localEventsRecurrances = localEventsRecurrancesArray?.[0];
    const productSchedules = productSchedulesArray?.[0];

    const allProductPlans = products;

    const assignments = productPlans.reduce(
      (accumulator, plan, index, plansArray) => {
        switch (plan.plan_id) {
          case 2: // Digital Edition
          case "2":
            return {
              ...accumulator,
              [plan.plan_id.toString()]: {
                id: digitalEditionRecurrances?.id?.toString(),
                issueId: digitalEditionRecurrances?.issue_id?.toString(),
              },
            };

          case 5: // Local Events
          case "5":
            return {
              ...accumulator,
              [plan.plan_id.toString()]: {
                id: localEventsRecurrances?.id?.toString() ?? "",
                frequency: localEventsRecurrances?.frequency ?? "",
              },
            };
          case 6: // Branded Posts
          case "6":
            return {
              ...accumulator,
              [plan.plan_id.toString()]: {
                id: productSchedules?.id?.toString() ?? "",
                productScheduleId:
                  productSchedules?.product_schedule_id?.toString() ?? "",
              },
            };
          case 8:
          case "8":
            return {
              ...accumulator,
              [plan.plan_id.toString()]: {
                campaignId: pcCampaign?.campaign_id?.toString(),
                name: pcCampaign?.campaigns?.name,
                product:
                  pcCampaign?.campaigns?.product_types?.description ?? "",
              },
            };
          case 1: // American Lifestyle Magazines
          case "1":
          case 3: // Start Health Magazine
          case "3":
          case 7: // Good To Be Home Magazine
          case "7":
          case 11: // Business In Action Magazine
          case "11":
            return {
              ...accumulator,
              [plan.plan_id.toString()]: {
                campaign: "legacy",
                campaignName: getProductPlanDetails({
                  plans: allProductPlans,
                  planId: plan?.plan_id,
                })?.name, // get the name from products collection
              },
            };
          case 12: // Branded Magazines
          case "12":
            return {
              ...accumulator,
              [plan.plan_id.toString()]: {
                campaign: bmCampaign?.campaign_id?.toString(),
                campaignName: bmCampaign?.campaigns?.name,
                productType: bmCampaign?.campaigns.product_types?.description,
                productTypeId: bmCampaign?.campaigns?.product_types?.id,
              },
            };
          default:
            // console.log("plan id: ", plan?.plan_id, "not accounted for.");
            return accumulator;
        }
      },
      {}
    );

    return assignments;
  } catch (error) {
    logger.error(error);
    throw error;
  }
}

async function reconcileRecipientGroupProductAssociationsWithPrintCampaigns({
  db,
  data,
}) {
  // we're only ever adding or removing product plan associations in this function
  // e.g. recipientGroup.productPlans
  // based on the presence or absence of associated print campaign information
  try {
    // reference to RecipientGroupMigrationLogs
    const recipientGroupMigrationLogsRef = db.collection(
      collectionNames.logs.recipientGroupMigrationLogs
    );

    const {
      accountId,
      accountPlans,
      uuid,
      legacyRecipientGroupId,
      productPlans,
      campaignRecipients,
      digitalEditionRecurrances,
      localEventsRecurrances,
      productSchedules,
      pcCampaign: pcCampaignArray,
      bmCampaign: bmCampaignArray,
    } = data;

    const hasPcCampaign = !!pcCampaignArray[0];
    const hasBmCampaign = !!bmCampaignArray[0];

    const hasMatchingAccountPlan = (planId) => {
      return !!accountPlans.find((plan) => plan.plan_id === planId);
    };
    const hasPcProductAssociation = !!productPlans.find(
      (plan) => plan.plan_id === 8
    );
    const bmProductIds = [1, 3, 7, 11, 12];
    const hasBmProductAssociation = !!productPlans.find((plan) =>
      bmProductIds.includes(plan.plan_id)
    );

    const pcCampaign = pcCampaignArray[0];
    const pcCampaignLegacyProductId =
      pcCampaign?.campaigns?.product_types?.legacy_product_id;

    const bmCampaign = bmCampaignArray[0];
    const bmCampaignLegacyProductId =
      bmCampaign?.campaigns?.product_types?.legacy_product_id;

    // removes recipient group branded magazine and/or postcard campaigns product associations if no corresponding print campaign is present
    // logs any changes to plans

    const removedPlans = [];

    const reconciledProductPlanAssociations = productPlans.filter((plan) => {
      switch (plan.plan_id) {
        case 1: // American Lifesyle Magazine
        case "1":
        case 3: // Start Health Magazine
        case "3":
        case 7: // Good To Be Home Magazine
        case "7":
        case 11: // Business In Action Magazine
        case "11":
          return true; // do not remove any legacy magazine products
        case 12: // Branded Magazines
        case "12":
          if (!hasBmCampaign) removedPlans.push(plan);
          return hasBmCampaign; // if there is an associated bm campaign then keep the plan in productPlans otherwise remove
        case 8:
        case "8":
          if (!hasPcCampaign) removedPlans.push(plan);
          return hasPcCampaign;
        default:
          return true; // leave other plans alone
      }
    });
    for (const plan of removedPlans) {
      try {
        await recipientGroupMigrationLogsRef
          .doc(`added-${uuid}-${plan.plan_id}`)
          .set({
            type: "removed-product-association",
            success: true,
            note: "removed a product plan association because there was no associated print campaign",
            recipientGroupUuid: uuid,
            accountId: accountId.toString(),
            planId: plan.plan_id?.toString(),
            createdAt: Timestamp.now(),
          });
      } catch (error) {
        logger.error("Problem writing migration log: ", error);
      }
    }

    // add Branded Magazine or Postcard Campaigns product associations if print campaign is present but no corresponding product association
    if (hasPcCampaign) {
      if (!hasPcProductAssociation) {
        // Check if plan_id is in account_plans before adding
        if (hasMatchingAccountPlan(8)) {
          const now = new Date().toISOString();
          reconciledProductPlanAssociations.push({
            id: `added-${uuid}-8`,
            account_id: accountId,
            contact_group_id: legacyRecipientGroupId,
            plan_id: 8,
            properties: "[]",
            created_at: now,
            updated_at: now,
            deleted_at: null,
          });
          await recipientGroupMigrationLogsRef.doc(`added-${uuid}-8`).set({
            type: "added-product-association",
            success: true,
            note: "added product association",
            recipientGroupUuid: uuid,
            accountId: accountId.toString(),
            planId: "8",
            createdAt: Timestamp.now(),
          });
        } else {
          await recipientGroupMigrationLogsRef.doc(`added-${uuid}-8`).set({
            type: "added-product-association",
            success: false,
            note: "attempted to add product association but no matching account plan",
            recipientGroupUuid: uuid,
            accountId: accountId.toString(),
            planId: "8",
            createdAt: Timestamp.now(),
          });
        }
      }
    } else if (hasBmCampaign) {
      if (!hasBmProductAssociation) {
        // Check if plan_id is in account_plans before adding
        if (hasMatchingAccountPlan(12)) {
          const now = new Date().toISOString();
          reconciledProductPlanAssociations.push({
            id: `added-${uuid}-${"12"}`,
            account_id: accountId,
            contact_group_id: legacyRecipientGroupId,
            plan_id: 12,
            properties: "[]",
            created_at: now,
            updated_at: now,
            deleted_at: null,
          });
          await recipientGroupMigrationLogsRef
            .doc(`added-${uuid}-${"12"}`)
            .set({
              type: "added-product-association",
              success: true,
              note: "added product association",
              recipientGroupUuid: uuid,
              accountId: accountId.toString(),
              planId: "12",
              createdAt: Timestamp.now(),
            });
        } else {
          await recipientGroupMigrationLogsRef.doc(`added-${uuid}-8`).set({
            type: "added-product-association",
            success: false,
            note: "attempted to add product association but no matching account plan",
            recipientGroupUuid: uuid,
            accountId: accountId.toString(),
            planId: "12",
            productTypeId: bmCampaignLegacyProductId.toString(),
            createdAt: Timestamp.now(),
          });
        }
      }
    }
    data.productPlans = reconciledProductPlanAssociations;
    return data;
  } catch (error) {
    logger.error(error);
    throw error;
  }
}

function getProductPlanDetails({ plans, planId }) {
  if (!Array.isArray(plans)) {
    logger.warn("getProductPlans expects an array of plans");
    return;
  }
  const planIdString = planId.toString();
  return plans.find((plan) => plan.planId === planIdString);
}
