/* eslint-disable no-undef */
const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const { isEmpty } = require("lodash");

exports.recipientGroupCounts = (db) =>
  onRequest(async (req, res) => {
    if (req.method === "GET") {
      await handleGetRequest({ req, res, db });
    }
  });

async function handleGetRequest({ req, res, db }) {
  const { accountId } = req.query;
  if ((isEmpty(accountId))) {
    res
      .status(400)
      .send(
        `Bad request, accountId is required query params. You sent ${req.query}`
      );
    return;
  }
  try {
    let baseGroups = await db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .where("isActive", "==", true)
      .get();

    baseGroups = baseGroups.docs.map((doc) => ({ ...doc.data(), id: doc.id }));
    const listing = [];
    for (const doc of baseGroups) {
      let dataListing = {
        id: doc.id,
        name: doc.name,
        productPlans: doc.productPlans,
       };
      
      let emailRecipients = await db
        .collection("Account")
        .doc(accountId.toString())
        .collection("Recipients")
        .where("recipientGroupIds", "array-contains", doc.id)
        .where("searchTags.primaryEmail", "!=", "")
        .where("isActive", "==", true)
        .count()
        .get();

      let emailOverrideRecipients = await db
        .collection("Account")
        .doc(accountId.toString())
        .collection("Recipients")
        .where("recipientGroupIds", "array-contains", doc.id)
        .where("overrideDeliverability.email", "==", true)
        .where("isActive", "==", true)
        .count()
        .get();

        dataListing.email = emailRecipients.data().count + emailOverrideRecipients.data().count;

        listing.push(dataListing);
    }
    res.status(200).json({
      message: "Recipient groups retrieved successfully",
      recipientGroups: listing,
    });
  } catch (error) {
    logger.error(
      "Failed to retrieve recipientGroups",
      "accountId: ",
      accountId,
      "error: ",
      error
    );
    res.status(500).json({
      error: "Failed to retrieve recipientGroups",
      message: error.message,
    });
  }
}
