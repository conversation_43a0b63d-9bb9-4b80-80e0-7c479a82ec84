const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const { isEmpty, isNil } = require("lodash");
const {
  collectValidRecipientsByGroup,
} = require("../../utils/collectValidRecipientsByGroup");
const { findGroups } = require("../../utils/recipients/findGroups");
const { count } = require("console");

exports.recipientsByCampaign = (db) =>
  onRequest(
    {
      timeoutSeconds: 540,
      memory: "2GiB",
    },
    async (req, res) => {
      if (req.method === "GET") {
        await handleGetRequest({ req, res, db });
      }
    }
  );

async function handleGetRequest({ req, res, db }) {
  const { accountId, campaignId = null, productId = null } = req.query;

  if (isEmpty(accountId)) {
    res.status(400).send(`Bad request, accountId is a required query param`);
    return;
  }

  if (isEmpty(campaignId)) {
    res.status(400).send(`Bad request, campaignId is a required query param`);
    return;
  }
  if (isEmpty(productId)) {
    res.status(400).send(`Bad request, productId is a required query param`);
    return;
  }
  logger.info(
    "accountId",
    accountId,
    "campaignId",
    campaignId,
    "productId",
    productId
  );
  try {
    let recipients = [];
    let myGroups = [];

    let masterGroups = [];
    let foundGroups = [];

    let baseGroups = await db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .where("isActive", "==", true)
      .get();
    console.log("baseGroups", baseGroups.docs, "isEmpty: ", baseGroups.empty);
    masterGroups = baseGroups.docs.map((doc) => ({
      ...doc.data(),
      id: doc.id,
    }));
    logger.info(
      "masterGroups",
      masterGroups,
      "masterGroups.length",
      masterGroups.length
    );

    if (masterGroups.length === 0) {
      res.status(200).json({
        message: "No Recipient groups found",
        recipientGroup: [],
      });
      return;
    }
    let campaignIdNew = campaignId;
    // Clear the campaign id if it is legacy
    if (campaignId === "legacy") {
      campaignIdNew = null;
    }

    foundGroups = await findGroups(masterGroups, productId, campaignIdNew);
    logger.info("foundGroups", foundGroups);
    if (!foundGroups.length > 0) {
      res.status(200).json({
        message: "No Recipient groups after product checking",
        recipientGroup: [],
      });
      return;
    }

    recipients = await collectValidRecipientsByGroup({
      db,
      groups: foundGroups,
      accountId,
      productId,
    });

    res.status(200).json({
      message: "Recipients retrieved successfully",
      count: recipients.length,
      recipientGroup: recipients,
    });
  } catch (error) {
    logger.error(
      "Failed to retrieve recipients",
      "accountId: ",
      accountId,
      "error: ",
      error
    );
    res.status(500).json({
      error: "Failed to retrieve recipients",
      message: error.message,
    });
  }
}
