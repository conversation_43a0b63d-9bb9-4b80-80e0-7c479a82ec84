/* eslint-disable no-undef */
const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const { Timestamp, FieldValue } = require("firebase-admin/firestore");
const { isNil, isEmpty } = require("lodash");
const { createUUID } = require("../../utils/createUUID");
const BATCH_SIZE = 10;

exports.recipientGroups = (db) =>
  onRequest(async (req, res) => {
    if (req.method === "PUT") {
      await handlePatchRequest({ req, res, db });
      return;
    }
    if (req.method === "POST") {
      await handlePostRequest({ req, res, db });
      return;
    }

    if (req.method === "GET") {
      await handleGetRequest({ req, res, db });
    }
  });

async function handleGetRequest({ req, res, db }) {
  const { accountId, groupId, groupIds, productPlanId } = req.query;
  if (isEmpty(accountId)) {
    return res.status(400).send("Bad request: accountId is required");
  }

  const baseRef = db
    .collection("Account")
    .doc(accountId)
    .collection("RecipientGroups")
    .where("isActive", "==", true);

  let ids = [];
  if (groupId) {
    ids = [groupId];
  } else if (groupIds) {
    try {
      const parsed = JSON.parse(groupIds);
      if (!Array.isArray(parsed)) throw new Error("must be a JSON array");
      ids = parsed;
    } catch (err) {
      return res
        .status(400)
        .send(`Bad request: invalid groupIds – ${err.message}`);
    }
  }

  // Chunk into <=10 for 'in' clasue
  const queries = [];
  if (ids.length) {
    for (const chunk of chunkArray(ids, BATCH_SIZE)) {
      let q = baseRef.where("__name__", "in", chunk);
      if (productPlanId) {
        q = q.where("productPlans", "array-contains", productPlanId);
      }
      queries.push(q.get());
    }
  } else {
    let q = baseRef;
    if (productPlanId) {
      q = q.where("productPlans", "array-contains", productPlanId);
    }
    queries.push(q.get());
  }

  try {
    const snaps = await Promise.all(queries);
    const docsMap = new Map();
    snaps.forEach((snap) => snap.forEach((doc) => docsMap.set(doc.id, doc)));

    if (!docsMap.size) {
      return res.status(200).send("No recipient groups found");
    }

    const recipientGroups = Array.from(docsMap.values()).map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    return res.status(200).json({
      message: "Recipient groups retrieved successfully",
      recipientGroups,
    });
  } catch (error) {
    logger.error("Failed to retrieve recipientGroups", { accountId, error });
    return res.status(500).json({
      error: "Failed to retrieve recipientGroups",
      message: error.message,
    });
  }
}

async function handlePatchRequest({ req, res, db }) {
  const { accountId, recipientGroupId, name, description = "" } = req.body;
  if (isEmpty(accountId)) {
    res.status(400).send(`Bad request, accountId is required`);
    return;
  }

  if (isEmpty(recipientGroupId)) {
    res.status(400).send(`Bad request, recipientGroupId is required`);
    return;
  }

  if (isEmpty(name)) {
    res.status(400).send(`Bad request, name is required`);
    return;
  }

  try {
    const recipientGroupRef = db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .doc(recipientGroupId);

    await recipientGroupRef.update({
      recipientIds: FieldValue.arrayUnion(recipientId),
    });
  } catch (error) {
    logger.error(
      "Failed to add recipient to recipientGroup",
      "accountId: ",
      accountId,
      "recipientGroupId: ",
      recipientGroupId,
      "recipientId: ",
      recipientId,
      "error: ",
      error
    );
    res.status(500).json({
      error: "Failed to add recipient to recipientGroup",
      message: error.message,
    });
    return;
  }

  /**
   * Add recipient group reference to recipients
   */
  try {
    const recipientRef = db
      .collection("Account")
      .doc(accountId)
      .collection("Recipients")
      .doc(recipientId);

    await recipientRef.update({
      recipientGroupIds: FieldValue.arrayUnion(recipientGroupId),
    });
  } catch (error) {
    logger.error(
      "Failed to add recipientGroup reference to recipient",
      "accountId: ",
      accountId,
      "recipientGroupId: ",
      recipientGroupId,
      "recipientId: ",
      recipientId,
      "error: ",
      error
    );
    res.status(500).json({
      error: "Failed to add recipientGroup reference to recipient",
      message: error.message,
    });
    return;
  }

  res.status(200).send("recipient added to recipient group successfully");
}

async function handlePostRequest({ req, res, db }) {
  const { accountId, name, description = "" } = req.body;
  if (isEmpty(accountId)) {
    res.status(400).send(`Bad request, accountId is required`);
    return;
  }

  if (isEmpty(name)) {
    res.status(400).send(`Bad request, name is required`);
    return;
  }

  /**
   * Add recipientId to Recipient group recipientIds array
   */
  try {
    const uuid = createUUID();
    const newRecipientGroup = {
      name: name,
      description: description || "",
      assignments: {},
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      isActive: true,
      isPrimary: false,
      meta: {},
      productPlans: [],
    };
    await db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .doc(uuid)
      .set(newRecipientGroup);

    res.status(200).json({
      message: "Recipient group created successfully",
      name: name,
      description: description || "",
      id: uuid || "",
    });
  } catch (error) {
    logger.error(
      "Failed to add new recipientGroup",
      "accountId: ",
      accountId,
      "name: ",
      name,
      "description: ",
      description || "",
      "error: ",
      error
    );
    res.status(500).json({
      error: "Failed to add recipient to recipientGroup",
      message: error.message,
    });
    return;
  }
}

function chunkArray(arr, size) {
  const chunks = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
}
