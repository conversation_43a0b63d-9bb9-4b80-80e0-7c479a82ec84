const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const { isEmpty } = require("lodash");
const {
  collectValidRecipientsByGroupLegacy,
} = require("../../utils/recipients/collectValidRecipientsByGroupLegacy");

exports.recipientsForLegacy = (db) =>
  onRequest({
      timeoutSeconds: 540,
      memory: "1GiB",
      minInstances: 2,
    },
    async (req, res) => {
      if (req.method === "GET") {
        await handleGetRequest({ req, res, db });
      } else {
        res.status(405).send("Method Not Allowed");
      }
    });

async function handleGetRequest({ req, res, db }) {
  const { accountId, method } = req.query;
  logger.info("accountId ", accountId, "method: ", method);
  if (isEmpty(accountId)) {
    res.status(400).json({
      error: "Bad request: 'accountId' is a required query parameter.",
    });
    return;
  }

  const validMethods = ["address", "email"];
  if (!validMethods.includes(method)) {
    res.status(400).json({
      error: `Bad request: 'method' must be one of ${validMethods.join(", ")}.`,
    });
    return;
  }

  const productIds = ["1", "3", "7", "11"];

  try {
    let recipients = [];
    let finalGroups = [];

    const masterGroupsSnapshot = await db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups")
      .get();

    if (masterGroupsSnapshot.empty) {
      res.status(200).json({
        message: "Recipients retrieved successfully.",
        recipients: [],
      });
      return;
    }

    // Convert snapshots to a usable format with data and id
    const masterGroups = masterGroupsSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    console.log("Master Groups:", masterGroups);

    for (const group of masterGroups) {
      const { assignments } = group;
      if (!assignments) continue;
      for (const productId of productIds) {
        if (!assignments[productId]) continue;
        finalGroups.push(group.id);
      }
    }

    logger.info("Final Groups:", finalGroups);

    if (finalGroups.length > 0) {
      recipients = await collectValidRecipientsByGroupLegacy({
        db,
        groups: finalGroups,
        accountId,
        method,
      });
    }

    res.status(200).json({
      message: "Recipients retrieved successfully.",
      recipients: recipients,
    });
  } catch (error) {
    logger.error("Failed to retrieve recipients:", accountId, error);
    res.status(500).json({
      error: "Failed to retrieve recipients.",
      message: error.message,
    });
  }
}

async function findGroups(masterGroups, productId) {
  const foundGroups = [];

  for (const group of masterGroups) {
    logger.debug("Checking group: ", group.name || group.id);
    const { assignments } = group;

    // Skip if assignments don't exist or this product ID doesn't exist in assignments
    if (!assignments || !assignments[productId]) continue;

    // If the product exists in assignments, add the group regardless of campaign
    foundGroups.push(group);
  }

  return foundGroups;
}
