/* eslint-disable no-undef */
const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const { isEmpty, isNil } = require("lodash");
const {
  collectValidRecipientsByGroupSummary,
} = require("../../utils/collectValidRecipientsByGroupSummary");
const { findGroups } = require("../../utils/recipients/findGroups");

exports.recipientGroupSummary = (db) =>
  onRequest(async (req, res) => {
    if (req.method === "GET") {
      await handleGetRequest({ req, res, db });
    }
  });

async function handleGetRequest({ req, res, db }) {
  const { accountId, campaignId, productId } = req.query;
  if ((isEmpty(accountId), isEmpty(campaignId), isEmpty(productId))) {
    res
      .status(400)
      .send(
        `Bad request, accountId, campaignId, and productId are required query params. You sent ${req.query}`
      );
    return;
  }
  try {
    let baseGroups = await db
      .collection("Account")
      .doc(accountId.toString())
      .collection("RecipientGroups")
      .where("isActive", "==", true)
      .get();

    baseGroups = baseGroups.docs.map((doc) => ({ ...doc.data(), id: doc.id }));
    const foundGroups = await findGroups(baseGroups, productId, campaignId);
    console.log(
      "foundGroups Data: ",
      baseGroups,
      productId,
      campaignId,
      foundGroups
    );

    if (foundGroups.length === 0) {
      res.status(200).send("No recipient groups found");
      return;
    }

    const recipientGroupsArray = [];

    for (const doc of foundGroups) {
      let validRecipientCount = 0;
      const groupData = baseGroups.find((group) => group.id === doc);
      validRecipientCount = groupData?.meta?.emailValid || 0;
      

      if (productId === "8") {
        if(!isNil(groupData?.meta?.postcardWillSend)) {
          validRecipientCount = groupData?.meta?.postcardWillSend;
        } else {
          let pcRecipients = await db
            .collection("Account")
            .doc(accountId.toString())
            .collection("Recipients")
            .where("recipientGroupIds", "array-contains", doc)
            .get();

          pcRecipients = pcRecipients.docs.map((doc) => doc.data());
          let validPCRecipients = 0;
          for (const recipient of pcRecipients) {
            const recipientId = recipient.id;
            const recipientMailingData = await db
              .collection("Account")
              .doc(accountId.toString())
              .collection("Recipients")
              .doc(recipientId)
              .collection("UserEnteredMailingAddress")
              .doc(0)
              .get();
            
            const address = recipientMailingData.data();
            if (address?.address1) {
              validPCRecipients++;
            }
          }
          validRecipientCount = validPCRecipients;
        }
      }

      if (productId === "12") {
        validRecipientCount = groupData?.meta?.addressWillSend || 0;
      }

      recipientGroupsArray.push({
        id: doc,
        ...groupData,
        validRecipientCount,
      });
    }

    res.status(200).json({
      message: "Recipient groups retrieved successfully",
      recipientGroups: recipientGroupsArray,
    });
  } catch (error) {
    logger.error(
      "Failed to retrieve recipientGroups",
      "accountId: ",
      accountId,
      "error: ",
      error
    );
    res.status(500).json({
      error: "Failed to retrieve recipientGroups",
      message: error.message,
    });
  }
}
