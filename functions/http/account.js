const { onRequest } = require("firebase-functions/v2/https");
const { defineString } = require("firebase-functions/params");
const functions = require("firebase-functions");
const dictionary = require("../constants/collectionNames");
const { FieldValue } = require("firebase-admin/firestore");
const { dateStringToTimestamp } = require("../utils/dateStringToTimestamp");
const { isNil, isArray, isEmpty } = require("lodash");

const { collectionNames, subCollectionNames } = dictionary;

/**
 * The rmcAccountSync function is an HTTP request function that
 * 1. creates or updates an account in the Titan database.
 * 2. syncs inactive plans, plans that are no longer in the RM account_plans table but are in Titan
 * The function handles POST requests from RMC that originate in the RMC domain/Account/Reactors
 * For example, ProductAddedReactor.php (onAccountEnrolledInFacebookAdsServices)
 *
 *
 *
 * @param {*} db
 * @returns
 */

exports.rmcAccountSync = (db) =>
  onRequest(async (req, res) => {
    if (req.method !== "POST") {
      res.status(400).send("Invalid request method");
      return;
    }

    /**
     * Step 1: Collect the account data from the request body
     */
    const {
      accountId: rmcAccountId,
      accountCreatedAt,
      accountUpdatedAt,
      accountPlans: rmcAccountPlans,
      displayName,
      groupId,
      groupName,
      marketUuid,
      name,
      slug,
      adsPaused,
      timezone: timezoneObject,
    } = req.body;
    // functions.logger.info("RMC Request:", JSON.stringify(req.body));
    if (isNil(rmcAccountId)) {
      res.status(400).send(`Bad request, accountId is required`);
      return;
    }

    const timezone = timezoneObject.timezone;
    // RMC likes to use integers for record ids so remember to convert to string.
    // Firebase prohibits integers for document IDs
    const rmcAccountIdString = rmcAccountId.toString();
    const groupIdString = groupId.toString() ?? "";
    /**
     * Step 2: Create or Overwrite Titan Account document
     * Account/{accountId}
     */
    try {
      const writeResultAccount = await db
        .collection(collectionNames.account)
        .doc(rmcAccountIdString)
        .set(
          {
            createdAt: dateStringToTimestamp(accountCreatedAt),
            updatedAt: dateStringToTimestamp(accountUpdatedAt),
            displayName,
            ...(groupIdString && { groupId: groupIdString }),
            ...(groupName && { groupName }),
            marketUuid,
            name,
            slug,
            timezone,
            ...(typeof adsPaused === "boolean" && { adsPaused }),
            syncedAt: FieldValue.serverTimestamp(),
          },
          { merge: true }
        );
    } catch (error) {
      functions.logger.error(
        error,
        `Error processing account from RMC. rmcAccountId: ${rmcAccountId} accountPlans: ${rmcAccountPlans}, name: ${displayName}`
      );
      res.status(500).send("Internal Server Error");
      return;
    }

    /**
     * Step 3: Create or Overwrite the AccountPlans subcollection
     * Add all tracked plans (products) to the Mercury account
     * AccountPlans are a Subollection of the Account document e.g. Account{accountId}/AccountPlans{planId}
     */
    if (isArray(rmcAccountPlans)) {
      try {
        await addProductPlans(db, rmcAccountIdString, rmcAccountPlans);
      } catch (error) {
        res.status(500).send("Internal Server Error", error);
        return;
      }

      /**
       * Step 4: Sync inactive plans (products) to the Titan account
       * Plans are considered inactive if they are no longer present in RMC account_plans table
       * RMC deletes records from account_plans table when a plan is removed from an account
       **/
      try {
        await syncInactivePlans(db, rmcAccountIdString, rmcAccountPlans);
      } catch (error) {
        res.status(500).send("Internal Server Error", error);
        return;
      }
    }
    // done syncing accounts and plans from RMC
    res.status(200).send("Processed Account from RMC");
    return;
  });

// Helper functions for createOrUpdateAccount
async function addProductPlans(db, rmcAccountId, rmcAccountPlans) {
  // Add all tracked products (plans) to the Titan Account/{accountId}/AccountPlans subcollection
  try {
    const accountRef = db.collection(collectionNames.account).doc(rmcAccountId);
    for (const plan of rmcAccountPlans) {
      const planIdString = plan.plan_id.toString();
      const rmcAccountIdString = plan.account_id.toString();

      const writeResultAccountSubCollection = await accountRef
        .collection(subCollectionNames.account.accountPlans)
        .doc(planIdString)
        .set({
          accountId: plan.account_id,
          accountPlanId: plan.id,
          planId: plan.plan_id,
          activeAt: dateStringToTimestamp(plan.active_at),
          createdAt: dateStringToTimestamp(plan.created_at),
          updatedAt: dateStringToTimestamp(plan.updated_at),
          isActive: true,
        });
    }
  } catch (error) {
    functions.logger.error(
      error,
      `Error processing account plans from RMC. rmcAccountPlans: ${JSON.stringify(
        rmcAccountPlans
      )}`
    );
    throw error;
  }
}

async function syncInactivePlans(db, rmcAccountId, rmcAccountPlans) {
  // Set inactive any plans that are not in the RMC account_plans table but are in the Titan Accounts/{accountId}/AccountPlans subcollection

  const accountRef = db.collection(collectionNames.account).doc(rmcAccountId);

  try {
    // Check for any plans we need to set as inactive
    const accountPlansSnapshot = await accountRef
      .collection(subCollectionNames.account.accountPlans)
      .get();

    if (!accountPlansSnapshot.empty) {
      const accountPlanIds = accountPlansSnapshot.docs.map((doc) => {
        return doc.ref.id;
      });

      const inactivePlans = plansToSetInactive(rmcAccountPlans, accountPlanIds);

      if (inactivePlans.length === 0) {
        return;
      }

      for (const planId of inactivePlans) {
        await accountRef
          .collection(subCollectionNames.account.accountPlans)
          .doc(planId)
          .set(
            {
              isActive: false,
              updatedAt: FieldValue.serverTimestamp(),
            },
            { merge: true }
          );
      }
    }
  } catch (error) {
    functions.logger.error(
      error,
      `Error syncing inactive plans from RMC. rmcAccountId: ${rmcAccountId}`
    );
    throw error;
  }
}

function plansToSetInactive(rmcAccountPlans, accountPlanIds) {
  if (!Array.isArray(accountPlanIds) || !Array.isArray(rmcAccountPlans)) return;

  const plansToSetInactive = accountPlanIds.reduce((accumulator, planId) => {
    const found = rmcAccountPlans.find(
      (rmcPlan) => rmcPlan.plan_id.toString() === planId.toString()
    );
    if (found) return accumulator;
    return [...accumulator, planId];
  }, []);

  return plansToSetInactive;
}
