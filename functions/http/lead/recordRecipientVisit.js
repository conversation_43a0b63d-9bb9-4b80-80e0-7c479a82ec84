// PUT
// POST

const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const { Timestamp } = require("firebase-admin/firestore");
const { isNil } = require("lodash");

exports.recordRecipientVisit = (db) =>
  onRequest(async (req, res) => {
    if (req.method === "POST") {
      await handlePostRequest({ req, res, db });
      return;
    }

    if (req.method === "GET") {
      res.status(200).json({
        message: "Nothing here.",
      });
    }
  });
async function handlePostRequest({ req, res, db }) {
  const {
    accountId: accountIdInt,
    recipientId,
    lpId: lpIdInt,
    shortCodeRef,
  } = req.body;

  if (isNil(accountIdInt) || isNil(recipientId) || isNil(lpIdInt)) {
    res
      .status(400)
      .send(
        `Bad request, accountId, recipientId, and lpId are required. You sent accountId: ${accountIdInt}, recipientId: ${recipientId}, lpId: ${lpIdInt}, shortCodeRef: ${shortCodeRef}`
      );
    return;
  }
  const accountId = accountIdInt.toString();
  const lpId = lpIdInt.toString();
  const nowTimestamp = Timestamp.now();

  const recipientRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients)
    .doc(recipientId);

  const recipientSourcesCollectionRef = recipientRef.collection(
    subCollectionNames.contacts.recipientSources
  );

  try {
    // Use a transaction to ensure both operations succeed or fail together
    await db.runTransaction(async (transaction) => {
      // Get the recipient document
      const recipientDoc = await transaction.get(recipientRef);

      // Update recipient document with lpId
      transaction.update(recipientRef, {
        lpId: lpId,
      });

      // Add to recipient sources
      transaction.set(recipientSourcesCollectionRef.doc(), {
        lpId: lpId,
        shortCodeRef: shortCodeRef,
        createdAt: nowTimestamp,
      });
    });

    res.status(200).json({
      message: `Visit tracked successfully.`,
      accountId: accountId,
      recipientId: recipientId,
      lpId: lpId,
      createdAt: nowTimestamp.toDate(),
    });
  } catch (error) {
    logger.error("Error in handlePostRequest:", error);
    res.status(500).send("Internal Server Error");
  }
}
