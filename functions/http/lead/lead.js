const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const {
  FieldValue,
  Timestamp,
  getFirestore,
} = require("firebase-admin/firestore");
const { createUUID } = require("../../utils/createUUID");
const { isNil, isArray, isString } = require("lodash");
const { processingStatus } = require("../../constants/processingStatus");

exports.lead = (db) =>
  onRequest(async (req, res) => {
    if (req.method === "POST") {
      await handlePostRequest({ req, res, db });
      return;
    }
    if (req.method === "GET") {
      res.status(200).json({
        message: "Nothing here.",
      });
    }
  });

async function handlePostRequest({ req, res, db }) {
  const {
    accountId: accountIdInt,

    notes,
    recipientGroupId,
    lpId = null,
  } = req.body;

  // since we can't control wtf the form sends, we'll normalize it to what we expect
  const firstName =
    req.body.firstName ||
    req.body.first_name ||
    req.body.fname ||
    req.body.given_name ||
    "";

  const lastName =
    req.body.lastName ||
    req.body.last_name ||
    req.body.lname ||
    req.body.surname ||
    "";

  const email =
    req.body.email ||
    req.body.emailAddress ||
    req.body.email_address ||
    req.body.userEmail ||
    null;

  const postalCode =
    req.body.postalCode ||
    req.body.zipCode ||
    req.body.zipcode ||
    req.body.ZIPCODE ||
    req.body.zip_code ||
    req.body.zip ||
    req.body.postcode ||
    req.body.postalcode ||
    null;

  const address1 =
    req.body.address1 ||
    req.body.addr1 ||
    req.body.streetAddress ||
    req.body.addressLine1 ||
    req.body.street ||
    null;

  const address2 =
    req.body.address2 ||
    req.body.addr2 ||
    req.body.suite ||
    req.body.apt ||
    req.body.unit ||
    req.body.addressLine2 ||
    null;

  const phoneNumberInput =
    req.body.phoneNumber ||
    req.body.phone ||
    req.body.tel ||
    req.body.mobile ||
    req.body.cell ||
    null;

  const city = req.body.city || req.body.cityName || req.body.town || null;

  const state =
    req.body.state ||
    req.body.province ||
    req.body.stateCode ||
    req.body.stateName ||
    req.body.region ||
    null;

  // Process and validate phone number
  let phoneNumber = phoneNumberInput
    ? phoneNumberInput.replace(/\s+/g, "")
    : null;
  let phoneNumberValidationMessage = "";

  if (phoneNumber) {
    // Remove country code if exists; then check for a 10-digit number
    const digitsOnly = phoneNumber.replace(/^\+?1/, "");
    if (digitsOnly.length === 10 && /^\d+$/.test(digitsOnly)) {
      // Add country code if not already there
      if (!phoneNumber.startsWith("+")) {
        phoneNumber = "+1" + digitsOnly;
      }
    } else {
      phoneNumberValidationMessage = `Invalid phone number format: ${phoneNumberInput}`;
      phoneNumber = null;
    }
  }

  if (isNil(accountIdInt)) {
    res.status(400).send("Bad request, accountId is required");
    return;
  }
  const accountId = accountIdInt.toString();
  const name = { firstName, lastName };

  // Create recipient document in Firestore
  const recipientRef = await createRecipient({
    db,
    accountId,
    name,
    phoneNumber,
    email,
    lpId,
  });
  if (isNil(recipientRef)) return;

  try {
    const [
      emailSubCollectionResult,
      phoneSubCollectionResult,
      assignRecipientResult,
    ] = await Promise.all([
      createOrUpdateEmailSubcollection({ accountId, email, recipientRef }),
      createOrUpdatePhoneSubcollection({
        accountId,
        phoneNumber,
        recipientRef,
      }),
      // Pass postalCode along to the mailing address function.
      createUserEnteredMailingAddress({
        address1,
        address2,
        city,
        state,
        postalCode,
        recipientRef,
      }),
      assignRecipientToRecipientGroup({
        db,
        accountId,
        recipientRef,
        recipientGroupId,
      }),
    ]);
    await createRecipientNote({ recipientRef, notes });
    res.status(200).json({
      message:
        `Recipient added successfully. ${emailSubCollectionResult?.message}. ` +
        `${phoneNumberValidationMessage || phoneSubCollectionResult?.message}. ` +
        `${assignRecipientResult?.message}.`,
      recipientId: recipientRef.id,
    });
  } catch (error) {
    console.error("Error in handlePostRequest:", error);
    res.status(500).send("Internal Server Error");
  }
}

async function createUserEnteredMailingAddress({
  address1,
  address2,
  city,
  state,
  postalCode,
  recipientRef,
}) {
  // Validate the most necessary fields
  if (isNil(address1) || isNil(city) || isNil(state)) {
    return {
      message:
        `Problem creating mailing address. address1, city and state are required. ` +
        `You sent address1: ${address1}, address2: ${address2}, city: ${city}, state: ${state}, postalCode: ${postalCode}`,
    };
  }
  const userEnteredMailingAddressRef = recipientRef
    .collection(subCollectionNames.contacts.userEnteredMailingAddress)
    .doc("0");
  try {
    await userEnteredMailingAddressRef.set({
      address1,
      ...(address2 && { address2 }),
      ...(city && { city }),
      ...(state && { state }),
      ...(postalCode && { postalCode }),
      processingStatus: processingStatus.PRE_PROCESS,
    });
    return {
      message:
        `User Entered Mailing Address Created with address1: ${address1}, ` +
        `address2: ${address2}, city: ${city}, state: ${state}, postalCode: ${postalCode}`,
    };
  } catch (error) {
    console.error(`Problem creating mailing address: ${error}`);
    return { message: "Problem creating Mailing Address" };
  }
}

async function createOrUpdateEmailSubcollection({
  accountId,
  email,
  recipientRef,
}) {
  try {
    if (email) {
      const emailAddressSubCollectionRef = recipientRef.collection(
        subCollectionNames.contacts.emailAddresses
      );
      const emailQuerySnapshot = await emailAddressSubCollectionRef
        .where("email", "==", email)
        .limit(1)
        .get();
      if (!emailQuerySnapshot.empty) {
        return { message: "Email already exists" };
      } else {
        await emailAddressSubCollectionRef.add({
          email,
          isPrimary: true,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
        });
        return { message: "Email Added" };
      }
    }
  } catch (error) {
    console.error(
      "Problem writing email to recipient email subcollection",
      "accountId: ",
      accountId,
      "recipientId: ",
      recipientRef.id,
      "error: ",
      error
    );
    return {
      message: "Problem writing email to recipient email subcollection",
    };
  }
}

async function createOrUpdatePhoneSubcollection({
  accountId,
  phoneNumber,
  recipientRef,
}) {
  try {
    if (phoneNumber) {
      const phoneNumberSubCollectionRef = recipientRef.collection(
        subCollectionNames.contacts.phoneNumbers
      );
      const phoneQuerySnapshot = await phoneNumberSubCollectionRef
        .where("phoneNumber", "==", phoneNumber)
        .limit(1)
        .get();
      if (!phoneQuerySnapshot.empty) {
        return { message: "Phone number already exists" };
      } else {
        await phoneNumberSubCollectionRef.add({
          phoneNumber,
          isPrimary: true,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
        });
        return { message: "Phone number added" };
      }
    }
  } catch (error) {
    console.error(
      "Problem writing phoneNumber to recipient PhoneNumbers subcollection",
      "accountId: ",
      accountId,
      "recipientId: ",
      recipientRef.id,
      "error: ",
      error
    );
    return {
      message:
        "Problem writing phoneNumber to recipient PhoneNumbers subcollection",
    };
  }
}

async function createRecipient({ accountId, name, db, lpId }) {
  const recipientsCollectionRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients);
  try {
    const uuid = createUUID();
    const recipientRef = recipientsCollectionRef.doc(uuid);
    await recipientRef.set({
      accountId,
      name,
      isActive: true,
      createdAt: Timestamp.now(),
      lpId,
    });
    return recipientRef;
  } catch (error) {
    console.error(
      "Problem creating recipient",
      "accountId: ",
      accountId,
      "name: ",
      name,
      "error:",
      error
    );
    throw new Error("Failed to write recipient to Firestore");
  }
}

async function createRecipientNote({ recipientRef, notes = [] }) {
  try {
    const notesSubCollectionRef = recipientRef.collection(
      subCollectionNames.contacts.notes
    );
    const db = getFirestore();
    const BATCH_LIMIT = 500;
    let batch = db.batch();
    let opCount = 0;
    const batchPromises = [];
    if (isArray(notes)) {
      for (const note of notes) {
        if (isString(note)) {
          const newNoteRef = notesSubCollectionRef.doc();
          batch.set(newNoteRef, { note, createdAt: Timestamp.now() });
          opCount++;
          if (opCount === BATCH_LIMIT) {
            batchPromises.push(batch.commit());
            batch = db.batch();
            opCount = 0;
          }
        }
      }
      if (opCount > 0) {
        batchPromises.push(batch.commit());
      }
      await Promise.all(batchPromises);
    }
  } catch (error) {
    console.error(
      "Problem writing recipientNotes to recipient Notes subcollection",
      "recipient path: ",
      recipientRef?.path,
      "error: ",
      error
    );
    return {
      message: `Problem writing note to recipient Notes at ${recipientRef?.path}`,
    };
  }
  return { message: `Notes updated at ${recipientRef?.path}/Notes` };
}

async function assignRecipientToRecipientGroup({
  db,
  accountId,
  recipientRef,
  recipientGroupId,
}) {
  const recipientGroupRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection("RecipientGroups")
    .doc(recipientGroupId);
  if (recipientGroupId) {
    const recipientGroupSnapshot = await recipientGroupRef.get();
    if (recipientGroupSnapshot.exists) {
      try {
        await recipientRef.update({
          recipientGroupIds: FieldValue.arrayUnion(recipientGroupId),
        });
        return {
          message: `Added recipient ${recipientRef.id} to group ${recipientGroupId}`,
        };
      } catch (error) {
        console.error(
          "Problem adding recipient to recipientGroup.",
          "accountId: ",
          accountId,
          "recipientId: ",
          recipientRef.id,
          "recipientGroupId: ",
          recipientGroupId,
          "error: ",
          error
        );
        return { message: `Error assigning recipient to group: ${error}` };
      }
    } else {
      return {
        message: `Recipient group ${recipientGroupRef.path} does not exist`,
      };
    }
  } else {
    return { message: `No recipientGroupId provided` };
  }
}
