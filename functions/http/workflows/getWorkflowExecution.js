const { onRequest } = require("firebase-functions/v2/https");

exports.getWorkflowExecution = (db) => onRequest(async (req, res) => {
  if (req.method !== 'GET') {
    res.status(405).send("Method Not Allowed");
    return;
  }

  const { accountId, leadId } = req.query;

  if (!accountId) {
    return res.status(400).send({ status: 'failure', message: 'Account ID is required.' });
  }

  if (!leadId) {
    return res.status(400).send({ status: 'failure', message: 'Lead ID is required.' });
  }

  const executionRef = db.collection(`WorkflowExecutions`)
    .where('accountId', '==', accountId)
    .where('leadId', '==', leadId)
    .limit(1);
  const executionDoc = await executionRef.get();
  if (executionDoc.empty) {
    res.status(404).send({ status: 'failure', message: `Workflow Execution not found` });
    return;
  }

  res.status(200).json({
    status: 'success',
    message: `Workflow Execution fetched successfully.`,
    data: executionDoc.docs[0].data()
  });
});
