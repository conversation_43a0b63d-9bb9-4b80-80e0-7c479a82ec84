const { onRequest } = require("firebase-functions/v2/https");

exports.getWorkflow = (db) => onRequest(async (req, res) => {
  if (req.method !== 'GET') {
    res.status(405).send("Method Not Allowed");
  }

  const { accountId, workflowId } = req.query;

  if (!accountId) {
    return res.status(400).send({ status: 'failure', message: 'Account ID is required.' });
  }

  if (!workflowId) {
    return res.status(400).send({ status: 'failure', message: 'Workflow ID is required.' });
  }

  const workflowRef = db.collection(`Account/${accountId}/Workflows`).doc(workflowId);
  const workflowDoc = await workflowRef.get();
  if (!workflowDoc.exists) {
    res.status(404).send({ status: 'failure', message: `Workflow not found` });
  }

  res.status(200).json({
    status: 'success',
    message: `Workflow fetched successfully.`,
    data: workflowDoc.data()
  });
});
