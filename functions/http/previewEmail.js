const { onRequest } = require('firebase-functions/v2/https');
const admin = require('firebase-admin');
const { logger } = require('firebase-functions/v2');

// Rate limiting - simple in-memory store (use Redis for production)
const rateLimitStore = new Map();

/**
 * Simple rate limiting middleware
 */
function checkRateLimit(ip, windowMs = 15 * 60 * 1000, maxRequests = 100) {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  if (!rateLimitStore.has(ip)) {
    rateLimitStore.set(ip, []);
  }
  
  const requests = rateLimitStore.get(ip);
  // Remove old requests outside the window
  const validRequests = requests.filter(timestamp => timestamp > windowStart);
  
  if (validRequests.length >= maxRequests) {
    return false;
  }
  
  validRequests.push(now);
  rateLimitStore.set(ip, validRequests);
  return true;
}

/**
 * Generates email preview data for local events
 */
function generateLocalEventsEmailPreview(market, events = [], options = {}) {
  const {
    limit = 7,
    includeImages = true,
    userEmail = '<EMAIL>',
    userName = 'Local Events Subscriber'
  } = options;

  // Get top events by popularity
  const topEvents = events
    .filter(event => event.popularity_score && event.isActive)
    .sort((a, b) => (b.popularity_score || 0) - (a.popularity_score || 0))
    .slice(0, limit);
  
  if (topEvents.length === 0) {
    return generateNoEventsEmail(market, userEmail, userName);
  }

  // Format events for email display
  const formattedEvents = topEvents.map(formatEventForEmail);
  const primaryEvent = formattedEvents[0];
  const additionalEvents = formattedEvents.slice(1);

  // Generate email subject
  const marketName = market.area_name || 'Your Local Area';
  const subject = `🎉 ${topEvents.length} Great Events This Week in ${marketName}`;

  // Generate current date
  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return {
    subject,
    from: {
      name: "Local Events Team",
      email: "<EMAIL>"
    },
    to: {
      name: userName,
      email: userEmail
    },
    date: currentDate,
    attachments: 0,
    htmlContent: generateHtmlContent(market, primaryEvent, additionalEvents, includeImages),
    metadata: {
      marketId: market.area_uuid,
      marketName: market.area_name,
      eventCount: topEvents.length,
      generatedAt: new Date().toISOString()
    }
  };
}

/**
 * Format event for email display
 */
function formatEventForEmail(event) {
  return {
    name: event.name || 'Untitled Event',
    description: event.description || '',
    venue: event.venue_name || 'TBA',
    date: event.start_date ? new Date(event.start_date).toLocaleDateString() : 'TBA',
    time: event.start_date ? new Date(event.start_date).toLocaleTimeString() : '',
    eventType: event.event_type || 'Event',
    pricing: event.minimum_price ? `$${event.minimum_price}+` : 'Free',
    popularityScore: event.popularity_score || 0,
    ticketUrl: event.ticket_url,
    imageUrl: event.image_url
  };
}

/**
 * Generates HTML email content
 */
function generateHtmlContent(market, primaryEvent, additionalEvents, includeImages) {
  const marketName = market.area_name || 'Your Local Area';
  const primaryImageHtml = includeImages && primaryEvent.imageUrl 
    ? `<img src="${primaryEvent.imageUrl}" alt="${primaryEvent.name}" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px; margin-bottom: 16px;" />`
    : '';

  const additionalEventsHtml = additionalEvents.map(event => `
    <div style="border-bottom: 1px solid #e5e7eb; padding: 16px 0;">
      <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
        <h4 style="margin: 0; color: #1f2937; font-size: 16px; font-weight: 600;">${event.name}</h4>
        <span style="background: #eff6ff; color: #1d4ed8; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">
          Score: ${event.popularityScore}
        </span>
      </div>
      <p style="margin: 4px 0; color: #6b7280; font-size: 14px;">
        📍 ${event.venue} • 📅 ${event.date}${event.time ? ` • 🕒 ${event.time}` : ''}
      </p>
      <p style="margin: 4px 0; color: #6b7280; font-size: 14px;">
        💰 ${event.pricing}
      </p>
      ${event.ticketUrl ? `
        <a href="${event.ticketUrl}" style="color: #2563eb; text-decoration: none; font-size: 14px; font-weight: 500;">
          Get Tickets →
        </a>
      ` : ''}
    </div>
  `).join('');

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff;">
      <!-- Header -->
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">🎭 Local Events in ${marketName}</h1>
        <p style="color: #e5e7eb; margin: 8px 0 0 0; font-size: 16px;">Your weekly dose of amazing local experiences</p>
      </div>

      <!-- Main Content -->
      <div style="background: #f9fafb; padding: 40px 30px;">
        <!-- Featured Event -->
        <div style="background: white; border-radius: 12px; padding: 24px; margin-bottom: 24px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
          <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
            <h2 style="color: #667eea; margin: 0; font-size: 20px;">⭐ Featured Event</h2>
            <span style="background: #fbbf24; color: white; padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: bold;">
              POPULARITY: ${primaryEvent.popularityScore}
            </span>
          </div>
          
          ${primaryImageHtml}
          
          <h3 style="color: #1f2937; margin: 0 0 12px 0; font-size: 24px; font-weight: bold;">${primaryEvent.name}</h3>
          
          <div style="color: #6b7280; margin-bottom: 16px;">
            <p style="margin: 4px 0; font-size: 16px;">📍 <strong>${primaryEvent.venue}</strong></p>
            <p style="margin: 4px 0; font-size: 16px;">📅 ${primaryEvent.date}${primaryEvent.time ? ` • 🕒 ${primaryEvent.time}` : ''}</p>
            <p style="margin: 4px 0; font-size: 16px;">🎫 ${primaryEvent.eventType}</p>
            <p style="margin: 4px 0; font-size: 16px;">💰 ${primaryEvent.pricing}</p>
          </div>
          
          ${primaryEvent.description ? `
            <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px; font-size: 15px;">
              ${primaryEvent.description.substring(0, 200)}${primaryEvent.description.length > 200 ? '...' : ''}
            </p>
          ` : ''}
          
          ${primaryEvent.ticketUrl ? `
            <div style="text-align: center;">
              <a href="${primaryEvent.ticketUrl}" style="display: inline-block; background: #667eea; color: white; padding: 14px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px;">
                Get Tickets Now 🎟️
              </a>
            </div>
          ` : ''}
        </div>

        ${additionalEvents.length > 0 ? `
          <!-- Additional Events -->
          <div style="background: white; border-radius: 12px; padding: 24px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <h3 style="color: #667eea; margin: 0 0 20px 0; font-size: 18px;">🎪 More Great Events</h3>
            ${additionalEventsHtml}
          </div>
        ` : ''}

        <!-- Market Info -->
        <div style="background: white; border-radius: 12px; padding: 20px; margin-top: 24px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
          <h4 style="color: #667eea; margin: 0 0 12px 0; font-size: 16px;">📍 About ${marketName}</h4>
          <p style="color: #6b7280; margin: 4px 0; font-size: 14px;">Total Events: ${market.event_count || 0}</p>
          <p style="color: #6b7280; margin: 4px 0; font-size: 14px;">Coverage Area: ${market.area_radius || 0} miles radius</p>
          ${market.area_address ? `<p style="color: #6b7280; margin: 4px 0; font-size: 14px;">📍 ${market.area_address}</p>` : ''}
        </div>
      </div>

      <!-- Footer -->
      <div style="background: #1f2937; color: #9ca3af; text-align: center; padding: 20px; font-size: 12px; border-radius: 0 0 10px 10px;">
        <p style="margin: 5px 0;">© 2025 Local Events. All rights reserved.</p>
        <p style="margin: 5px 0;">
          <a href="#" style="color: #9ca3af; margin: 0 10px;">Unsubscribe</a> | 
          <a href="#" style="color: #9ca3af; margin: 0 10px;">Privacy Policy</a> |
          <a href="#" style="color: #9ca3af; margin: 0 10px;">Update Preferences</a>
        </p>
      </div>
    </div>
  `;
}

/**
 * Generates email when no events are available
 */
function generateNoEventsEmail(market, userEmail, userName) {
  const marketName = market.area_name || 'Your Local Area';
  
  return {
    subject: `No Events This Week in ${marketName} - Check Back Soon!`,
    from: {
      name: "Local Events Team",
      email: "<EMAIL>"
    },
    to: {
      name: userName,
      email: userEmail
    },
    date: new Date().toLocaleDateString('en-US'),
    attachments: 0,
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px;">No Events This Week</h1>
        </div>
        <div style="background: #f7f7f7; padding: 40px 30px;">
          <p style="font-size: 16px; color: #333; line-height: 1.6;">Hi ${userName},</p>
          <p style="font-size: 16px; color: #333; line-height: 1.6;">
            We don't have any active events to show you in ${marketName} this week, but don't worry! 
            New events are being added regularly.
          </p>
          <p style="font-size: 16px; color: #333; line-height: 1.6;">
            Check back soon or expand your search radius to discover more events in nearby areas.
          </p>
        </div>
      </div>
    `,
    metadata: {
      marketId: market.area_uuid,
      marketName: market.area_name,
      eventCount: 0,
      generatedAt: new Date().toISOString()
    }
  };
}

/**
 * Preview Email Endpoint
 * Handles the creation of email previews based on market and event data
 */
exports.previewEmail = onRequest({
  cors: true,
  maxInstances: 5,
  memory: '256MiB',
  timeoutSeconds: 30
}, async (req, res) => {
  // Allow only GET requests
  if (req.method !== 'GET') {
    res.status(405).json({ success: false, error: 'Method not allowed. Use GET.' });
    return;
  }

  try {
    const { market, template, start, end } = req.query;
    if (!market || !template || !start || !end) {
      res.status(400).json({ success: false, error: 'Missing required query parameters: market, template, start, end' });
      return;
    }

    // Rate limiting
    const clientIp = req.ip || req.connection.remoteAddress || 'unknown';
    if (!checkRateLimit(clientIp)) {
      res.status(429).json({ success: false, error: 'Too many requests, please try again later.' });
      return;
    }

    // Authentication check
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ success: false, error: 'Unauthorized. Please provide a valid Bearer token.' });
      return;
    }

    // Validate date format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(start) || !dateRegex.test(end)) {
      res.status(400).json({ success: false, error: 'Invalid date format. Use YYYY-MM-DD.' });
      return;
    }

    logger.info(`Generating email preview for market: ${market}, template: ${template}, date range: ${start} to ${end}`);

    // Initialize Firestore
    const db = admin.firestore();

    // Fetch market data from EventMarkets collection
    const marketRef = db.collection('EventMarkets').doc(market);
    const marketSnap = await marketRef.get();
    if (!marketSnap.exists) {
      res.status(404).json({ success: false, error: 'Market not found' });
      return;
    }
    const marketData = marketSnap.data();

    // Convert date strings to Date objects for comparison
    const startDate = new Date(start);
    const endDate = new Date(end);

    // Fetch events within the date range
    const eventsQuery = db.collection('LiveEvents')
      .where('area_uuid', '==', marketData.area_uuid)
      .where('_isActive', '==', true);
    
    const eventsSnap = await eventsQuery.get();
    const events = [];
    
    eventsSnap.forEach(doc => {
      const eventData = doc.data();
      // Filter by date range in memory since Firestore doesn't handle date range queries well
      if (eventData.start_date) {
        const eventDate = eventData.start_date.toDate ? eventData.start_date.toDate() : new Date(eventData.start_date);
        if (eventDate >= startDate && eventDate <= endDate) {
          events.push(eventData);
        }
      }
    });

    logger.info(`Found ${events.length} events for the specified date range`);

    // Generate email preview
    const emailData = generateLocalEventsEmailPreview(marketData, events, {
      limit: 7,
      includeImages: true
    });

    // Return HTML content directly
    const htmlContent = emailData.htmlContent;

    // Set cache headers for better performance
    res.set({
      'Content-Type': 'text/html',
      'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
      'X-Content-Type-Options': 'nosniff'
    });
    
    res.status(200).send(htmlContent);
  } catch (error) {
    logger.error('Error in generating preview:', error);
    res.status(500).json({ success: false, error: 'Internal server error', message: error.message });
  }
});
