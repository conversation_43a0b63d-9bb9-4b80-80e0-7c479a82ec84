{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "index.js", "dependencies": {"@algolia/client-search": "^5.27.0", "@google-cloud/functions-framework": "^4.0.0", "@google-cloud/logging": "^10.5.0", "@google-cloud/pubsub": "^4.11.0", "@google-cloud/secret-manager": "^5.6.0", "@googlemaps/addressvalidation": "^2.4.0", "@googlemaps/google-maps-services-js": "^3.4.1", "@universe/address-parser": "^4.0.0", "adm-zip": "^0.5.16", "algoliasearch": "^5.27.0", "api": "^5.0.8", "axios": "^1.10.0", "axios-retry": "^4.5.0", "busboy": "^1.6.0", "child-process-promise": "^2.2.1", "chrono-node": "^2.8.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron-parser": "^5.3.0", "date-fns": "^4.1.0", "deep-diff": "^1.0.2", "dotenv": "^16.5.0", "exponential-backoff": "^3.1.2", "express": "^4.21.2", "firebase-admin": "^12.7.0", "firebase-functions": "^6.3.2", "firebase-uptime": "^1.0.26", "geo-tz": "^7.0.7", "geofire-common": "^6.0.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment-timezone": "^0.5.48", "node-fetch-commonjs": "^3.3.2", "openai": "^4.104.0", "papaparse": "^5.5.3", "parse-address": "^1.1.2", "parse-address-string": "^0.0.3", "pdfkit": "^0.15.2", "qrcode": "^1.5.4", "randomstring": "^1.3.1", "sharp": "^0.33.5", "shortid": "^2.2.17", "svg-to-pdfkit": "^0.1.8", "twilio": "^5.7.1", "ua-parser-js": "^1.0.40", "unzipper": "^0.10.14", "uuid": "^9.0.1", "uuid-v4": "^0.1.0", "uuidv4": "^6.2.13", "xlsx": "^0.18.5", "zod": "^3.25.65"}, "devDependencies": {"eslint": "^8.57.1", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^0.2.3"}, "private": true}