const { onSchedule } = require("firebase-functions/v2/scheduler");
const { astridConnection } = require("../astrid/astridConnection");
const { handleMissingAccount } = require("../migration/handleMissingAccounts");
const { FieldValue } = require("firebase-admin/firestore");

exports.syncAccounts = (db) =>
  onSchedule("30 1 * * *", async (event) => {
    try {
      // First, find and reset currently paused accounts
      const pausedAccounts = await db
        .collection("Account")
        .where("adsPaused", "==", true)
        .get();
      const resetBatch = db.batch();

      pausedAccounts.forEach((doc) => {
        resetBatch.update(doc.ref, {
          adsPaused: false,
          syncedAt: FieldValue.serverTimestamp(),
        });
      });

      if (!pausedAccounts.empty) {
        await resetBatch.commit();
      }

      // Then process new accounts to be paused
      const accountsPaused = await astridConnection({
        payload: {},
        url: "users/accountUpdate",
        method: "GET",
      });

      if (!accountsPaused?.data?.length) {
        return;
      }

      let updatedCount = 0;
      const updateBatch = db.batch();

      for (const accountId of accountsPaused.data) {
        if (!accountId) continue;

        const docRef = db.collection("Account").doc(accountId.toString());
        const doc = await docRef.get();

        if (doc.exists) {
          updateBatch.update(docRef, {
            adsPaused: true,
            syncedAt: FieldValue.serverTimestamp(),
          });
          updatedCount++;
        } else {
          await handleMissingAccount(accountId);
        }
      }

      if (updatedCount > 0) {
        await updateBatch.commit();
      }
    } catch (error) {
      console.error("Error updating accounts:", error.message);
      throw error;
    }
  });
