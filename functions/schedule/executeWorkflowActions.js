const { onSchedule } = require("firebase-functions/v2/scheduler");
const admin = require('firebase-admin');
const { callMercuryService } = require("../services/mercury/callMercuryService");
const { sleep } = require("../utils/sleep");
const { FieldValue } = require("firebase-admin/firestore");

exports.executeWorkflowActions = onSchedule("* * * * *", async (event) => {

  const batchSize = 100;
  let lastDoc = null;
  do {
    const now = new Date();

    let query = admin
      .firestore()
      .collection("WorkflowExecutions")
      .where("nextActionExecuteOn", "<=", now)
      .where("status", "==", "IN_PROGRESS")
      .orderBy("createdAt", "asc")
      .limit(batchSize);

    if (lastDoc) {
      query = query.startAfter(lastDoc);
    }

    const snapshot = await query.get();
    if (snapshot.empty) break;

    for (const doc of snapshot.docs) {
      console.log(`🔄 Start executing workflow with ID: ${doc.id}`);
      try {
        await handleExecution(doc);
      } catch (error) {
        console.error(`❌ Error in workflow ${doc.id}: ${error.message}`);

        const currentRetries = doc.data().retries || 0;
        const updateData = {
          retries: FieldValue.increment(1),
          error: error.message,
        };

        if (currentRetries >= 2) {
          updateData.status = "FAILED";
        }

        await doc.ref.update(updateData);
      }
    }

    lastDoc = snapshot.docs[snapshot.docs.length - 1];
  } while (lastDoc);
});

async function handleExecution(executionDoc) {
  const execution = executionDoc.data();
  const actions = execution.actions.map((a) => ({ ...a }));
  const now = new Date();

  for (const action of actions) {
    if (action.status !== "PENDING") continue;

    const executeOn = action.executeOn.toDate();
    if (executeOn > now) break;

    try {
      const triggerTypes = execution.triggers.map((t) => t.type);

      if (triggerTypes.includes("LEAD_GENERATED")) {
        action.status = await executeLeadGeneratedWorkflow(execution, action);
      } else {
        throw new Error("Execution trigger not handled");
      }
    } catch (error) {
      console.error(`❌ Error in action execution: ${error.message}`);
      action.status = "FAILED";
      action.error = error.message;
    }

    await sleep(10000);
  }

  const nextAction = actions.find(a => a.status === "PENDING");

  const updateData = {
    actions,
    nextActionExecuteOn: nextAction ? nextAction.executeOn : null,
    status: nextAction ? "IN_PROGRESS" : "COMPLETED",
  };

  await executionDoc.ref.update(updateData);
}

async function executeLeadGeneratedWorkflow({ accountId, leadId }, action) {
  const result = await callMercuryService({
    path: "executeLeadGeneratedWorkflow",
    method: "POST",
    body: { accountId, leadId, action },
  });

  console.log(`✅ Action ${action.type} executed: ${JSON.stringify(result)}`);
  return result?.data?.status ?? "COMPLETED";
}
