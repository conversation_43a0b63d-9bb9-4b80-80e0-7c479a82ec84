// functions/schedule/ogTagSync.js
const { onSchedule } = require('firebase-functions/v2/scheduler');
const admin = require('firebase-admin');
const { logger } = require('firebase-functions/v2');
const axios = require('axios');

// OccasionGenius Tags specific configuration
const AIRTABLE_API_URL = 'https://api.airtable.com/v0';
const FIRESTORE_TAGS_COLLECTION = 'OGTags';
// Rate limiting as per OccasionGenius requirements
const RATE_LIMIT_DELAY = 2000; // 2 seconds between requests
const PAGINATION_DELAY = 1500; // 1.5 seconds between pagination calls
const MAX_RETRIES = 3;

// Memory optimization settings
const DUPLICATE_CHECK_LIMIT = 10000; // Limit duplicate tracking to recent records

/**
 * OccasionGenius Airtable Client for Tags
 */
class OccasionGeniusTagClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.axiosInstance = axios.create({
      baseURL: AIRTABLE_API_URL,
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 35000 // 35 second timeout
    });
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Process records in pages with memory management
   */
  async processRecordsInPages(baseId, tableId, viewId, processCallback) {
    let offset = null;
    let requestCount = 0;
    let totalProcessed = 0;
    let consecutiveErrors = 0;
    
    do {
      let retryCount = 0;
      let success = false;
      
      while (!success && retryCount < MAX_RETRIES) {
        try {
          // Rate limiting
          if (requestCount > 0) {
            const delay = offset ? PAGINATION_DELAY : RATE_LIMIT_DELAY;
            await this.sleep(delay);
          }
          
          const params = {
            pageSize: 100,
            ...(offset && { offset }),
            ...(viewId && { view: viewId })
          };
          
          const url = `/${baseId}/${tableId}`;
          logger.info(`[Tags] Fetching page ${requestCount + 1} from ${tableId}`);
          
          const response = await this.axiosInstance.get(url, { params });
          const data = response.data;
          
          // Add base reference
          data.records.forEach(record => {
            record._baseId = baseId;
          });
          
          // Process immediately and clear from memory
          const processed = await processCallback(data.records);
          totalProcessed += processed;
          
          offset = data.offset;
          requestCount++;
          success = true;
          consecutiveErrors = 0;
          
          // Force garbage collection hint
          if (global.gc && requestCount % 5 === 0) {
            global.gc();
          }

          logger.info(`[Tags] Processed page ${requestCount}: ${processed} records saved`);

        } catch (error) {
          retryCount++;
          consecutiveErrors++;
          
          if (error.response?.data?.error?.type === 'LIST_RECORDS_ITERATOR_NOT_AVAILABLE') {
            logger.warn('[Tags] Iterator expired, restarting from beginning');
            offset = null;
            requestCount = 0;
            totalProcessed = 0;
            retryCount = 0;
            await this.sleep(5000);
            continue;
          }
          
          if (retryCount < MAX_RETRIES) {
            const retryDelay = Math.min(2000 * Math.pow(2, retryCount), 30000);
            logger.warn(`[Tags] Request failed (attempt ${retryCount}/${MAX_RETRIES}), retrying in ${retryDelay}ms`);
            await this.sleep(retryDelay);
            continue;
          }
          
          throw new Error(`Failed after ${MAX_RETRIES} attempts: ${error.message}`);
        }
      }
      
      if (consecutiveErrors > 5) {
        throw new Error('Too many consecutive errors');
      }
      
    } while (offset);
    
    return totalProcessed;
  }
}

/**
 * Parse date helper function
 */
function parseDate(dateStr) {
  if (!dateStr) return null;
  
  try {
    const date = new Date(dateStr);
    
    // Validate date
    if (isNaN(date.getTime())) {
      return null;
    }
    
    const year = date.getFullYear();
    if (year < 1900 || year > 2100) {
      return null;
    }
    
    return date;
  } catch (error) {
    return null;
  }
}

/**
 * Tag transformer for Firestore
 * Based on the actual data structure from the provided sample
 */
function transformTagForFirestore(record) {
  const f = record.fields || {};
  
  // Skip records without a name
  if (!f.Name) {
    return null;
  }
  
  // Transform the tag data
  return {
    // Metadata
    _airtableId: record.id,
    _baseId: record._baseId,
    _createdTime: parseDate(record.createdTime),
    _lastModified: admin.firestore.FieldValue.serverTimestamp(),
    _lastSyncTime: new Date(),
    
    // Tag fields from actual data
    name: f.Name || '',
    displayName: f['Display Name'] || f.Name || '',
    flagType: f['Flag Type'] || 'primary',
    baseName: f['Base Name'] || null,
    
    // Boolean fields
    approved: f.Approved === true,
    sensitivity: f['Sensitivity?'] === true,
    
    // Status
    _isActive: f.Approved === true,
    _isSensitive: f['Sensitivity?'] === true,
    blocked: f['Sensitivity?'] === true ? true : false
  };
}

/**
 * Duplicate tracker with size limit
 */
class LimitedDuplicateTracker {
  constructor(limit = DUPLICATE_CHECK_LIMIT) {
    this.set = new Set();
    this.queue = [];
    this.limit = limit;
  }
  
  has(id) {
    return this.set.has(id);
  }
  
  add(id) {
    if (this.set.has(id)) return;
    
    this.set.add(id);
    this.queue.push(id);
    
    // Remove oldest entries if over limit
    while (this.queue.length > this.limit) {
      const oldest = this.queue.shift();
      this.set.delete(oldest);
    }
  }
  
  clear() {
    this.set.clear();
    this.queue = [];
  }
}

/**
 * Save page to Firestore with streaming
 */
async function savePageToFirestore(records, db, tracker) {
  const collectionRef = db.collection(FIRESTORE_TAGS_COLLECTION);
  let savedCount = 0;
  let skippedCount = 0;
  
  // Process records in smaller chunks to reduce memory
  const CHUNK_SIZE = 50;
  
  for (let i = 0; i < records.length; i += CHUNK_SIZE) {
    const chunk = records.slice(i, i + CHUNK_SIZE);
    const batch = db.batch();
    let batchCount = 0;
    
    for (const record of chunk) {
      try {
        const transformed = transformTagForFirestore(record);
        if (!transformed) {
          skippedCount++;
          continue;
        }
        
        // Use the tag name as the document ID
        const docId = transformed.name || record.id;
        
        // Check duplicates
        if (tracker.has(docId)) {
          skippedCount++;
          continue;
        }
        
        tracker.add(docId);
        
        const docRef = collectionRef.doc(docId);
        batch.set(docRef, transformed, { merge: true });
        batchCount++;
        
      } catch (error) {
        logger.error(`[Tags] Transform error for ${record.id}: ${error.message}`);
        skippedCount++;
      }
    }
    
    if (batchCount > 0) {
      try {
        await batch.commit();
        savedCount += batchCount;
      } catch (error) {
        logger.error(`[Tags] Batch commit error: ${error.message}`);
      }
    }
  }
  
  logger.info(`[Tags] Page saved: ${savedCount} records, ${skippedCount} skipped`);
  return savedCount;
}

/**
 * Process a single base
 */
async function processTagBase(base, client, db, tracker) {
  logger.info(`[Tags] Processing base ${base.appId} - ${base.name}`);
  
  const startTime = Date.now();
  let totalSaved = 0;
  
  try {
    totalSaved = await client.processRecordsInPages(
      base.appId,
      base.tableId,
      base.viewId,
      async (pageRecords) => {
        const saved = await savePageToFirestore(pageRecords, db, tracker);
        
        // Clear records from memory immediately
        pageRecords.length = 0;
        
        return saved;
      }
    );
    
    const duration = Date.now() - startTime;
    logger.info(`[Tags] Completed ${base.appId}: ${totalSaved} saved in ${Math.round(duration / 1000)}s`);
    
    return {
      baseId: base.appId,
      baseName: base.name,
      savedCount: totalSaved,
      duration,
      success: true
    };
    
  } catch (error) {
    logger.error(`[Tags] Failed to process ${base.appId}: ${error.message}`);
    
    return {
      baseId: base.appId,
      baseName: base.name,
      savedCount: totalSaved,
      duration: Date.now() - startTime,
      success: false,
      error: error.message
    };
  }
}

/**
 * Main sync function for Tags
 */
async function syncTagsToFirestore(db, apiKey, tagBases) {
  const startTime = Date.now();
  const results = {
    success: true,
    startTime: new Date(startTime).toISOString(),
    tagBases: [],
    totalSaved: 0
  };
  
  // Use limited tracker
  const tracker = new LimitedDuplicateTracker();
  
  try {
    const client = new OccasionGeniusTagClient(apiKey);
    
    // Process each base
    for (const base of tagBases) {
      const baseResult = await processTagBase(base, client, db, tracker);
      results.tagBases.push(baseResult);
      results.totalSaved += baseResult.savedCount;
      
      // Clear tracker periodically
      if (tracker.queue.length > DUPLICATE_CHECK_LIMIT * 0.8) {
        logger.info('[Tags] Clearing tracker to free memory');
        tracker.clear();
      }
    }
    
    // Update sync metadata
    const syncMeta = {
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      duration: Date.now() - startTime,
      tagsCount: results.totalSaved,
      bases: results.tagBases
    };
    
    await db.collection(FIRESTORE_TAGS_COLLECTION).doc('_lastSync').set(syncMeta);
    
  } catch (error) {
    logger.error('[Tags] Fatal sync error:', error);
    results.success = false;
    results.error = error.message;
  }
  
  results.duration = Date.now() - startTime;
  results.endTime = new Date().toISOString();
  
  return results;
}

/**
 * Scheduled function for Tags
 */
const ogTagSync = onSchedule({
  schedule: '0 2,14 * * *', // 2 AM and 2 PM daily
  timeZone: 'America/New_York',
  memory: '2GiB',
  timeoutSeconds: 540, // 9 minutes
  maxInstances: 1,
  retryCount: 1
}, async (request) => {
  logger.info('[Tags] Starting OccasionGenius Tags sync', {
    eventId: request.eventId,
    timestamp: request.timestamp
  });
  
  const apiKey = process.env.OG_API_KEY;
  if (!apiKey) {
    throw new Error('OG_API_KEY not configured');
  }
  
  const db = admin.firestore();
  
  const tagBases = [{
    appId: 'appf64PoFO85E2VQe',
    tableId: 'tblTt9r3JKwiWKV6F',
    viewId: 'viwdsnirzZN0S96c3',
    name: 'Main Tags Base'
  }];
  
  try {
    const results = await syncTagsToFirestore(db, apiKey, tagBases);
    
    if (!results.success) {
      throw new Error(`Tags sync failed: ${results.error}`);
    }
    
    logger.info('[Tags] Sync completed:', {
      totalSaved: results.totalSaved,
      durationMinutes: Math.round(results.duration / 60000)
    });
    
  } catch (error) {
    logger.error('[Tags] Sync failed:', error);
    throw error;
  }
});

exports.ogTagSync = ogTagSync;