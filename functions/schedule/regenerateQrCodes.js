const { onSchedule } = require("firebase-functions/v2/scheduler");
const { addToTopic } = require("../utils/pubsub/addToTopic.js");

const { Timestamp } = require("firebase-admin/firestore");

exports.regenerateQrCodes = (db) =>
  onSchedule("0 0 * * *", async (event) => {
    console.log("Regenerating QR codes...");
    try {
      const codesRef = db.collection("codes");
      // batch size of 250
      let lastDocument = null;
      let totalProcessed = 0;

      const getFiveDaysAgoTimestamp = () => {
        // Create a date object for current time
        const date = new Date();

        // Subtract 5 days (5 days * 24 hours * 60 minutes * 60 seconds * 1000 milliseconds)
        date.setTime(date.getTime() - 1 * 24 * 60 * 60 * 1000);

        // Convert to Firestore Timestamp
        return Timestamp.fromDate(date);
      };

      while (true) {
        const fiveDaysAgoTimestamp = getFiveDaysAgoTimestamp();
        let query = codesRef
          .orderBy("createdAt", "asc")
          .where("qrCodeGenerated", "<=", fiveDaysAgoTimestamp)
          .limit(250);
        if (lastDocument) {
          query = query.startAfter(lastDocument);
        }

        const querySnapshot = await query.get();
        if (querySnapshot.empty) {
          break;
        }

        console.log("Processing qr codes...");

        const promises = querySnapshot.docs.map(async (doc) => {
          const code = doc.data();
          if (!code.qrCodes) return;

          const message = {
            code,
          };

          await addToTopic({
            topic: "regenerate-qr-codes",
            message: JSON.stringify(message),
          });
        });

        await Promise.all(promises);
        lastDocument = querySnapshot.docs[querySnapshot.docs.length - 1];
        totalProcessed += querySnapshot.docs.length;
        console.log(`Processed ${totalProcessed} documents so far.`);
      }
    } catch (error) {
      console.error("Error refreshing signed URLs:", error);
    }
  });
