const { onSchedule } = require("firebase-functions/v2/scheduler");
const { FieldValue, Timestamp } = require("firebase-admin/firestore");

exports.updateSyncDaily = (db) => onSchedule("30 12 * * *", async (event) => {
  try {

    // Calculate the previous day's start and end timestamps
    const now = new Date();
    const previousDayStart = new Date(now);
    previousDayStart.setDate(previousDayStart.getDate() - 1);
    previousDayStart.setHours(0, 0, 0, 0);

    const previousDayEnd = new Date(now);
    previousDayEnd.setDate(previousDayEnd.getDate() - 1);
    previousDayEnd.setHours(23, 59, 59, 999);

    // Query for records from the previous day
    const syncRecords = await db.collection("SyncRecords")
      .where("createdAt", ">=", Timestamp.fromDate(previousDayStart))
      .where("createdAt", "<=", Timestamp.fromDate(previousDayEnd))
      .get();
    
    let updatedCount = 0;
    let uniqueCount = 0;
    let rgCount = 0;
    for (const record of syncRecords.docs) {
      const data = record.data();
      updatedCount += data.updateCount || 0;
      uniqueCount += data.uniqueCount || 0;
      rgCount += data.recipientGroupCount || 0;
    }

    const dataMetrics = {
      totalUniqueRecords: uniqueCount,
      totalUpdated: updatedCount,
      rgUpdate: rgCount,
      lastUpdated: Timestamp.now(),
      day: previousDayStart.getDate(),
      month: previousDayStart.getMonth() + 1,
      year: previousDayStart.getFullYear(),
    };

    await db.collection("SyncDaily").add(dataMetrics);

  } catch (error) {
    console.error("Error updating accounts:", error.message);
    throw error;
  }
});