const { onSchedule } = require("firebase-functions/v2/scheduler");
const { getStorage } = require("firebase-admin/storage");
const { logger } = require("firebase-functions");
const { defineString } = require("firebase-functions/params");

exports.cleanupEmptyMigrationFolderMarkers = onSchedule(
  {
    schedule: "0 0 * * *", // every day at 12:00 AM (UTC)
    timeZone: "UTC",
  },
  async (context) => {
    try {
      const bucketName = defineString("MIGRATION_STORAGE_BUCKET");
      const bucket = getStorage().bucket(bucketName.value());

      let query = { maxResults: 1000 };
      const folderMarkers = [];
      let nextPageToken = null;

      // Paginate through all objects in the bucket.
      do {
        if (nextPageToken) {
          query.pageToken = nextPageToken;
        }
        const [files, nextQuery] = await bucket.getFiles(query);

        // A folder marker is an object whose name ends with "/" and has a size of 0 bytes.
        const markers = files.filter(
          (file) =>
            file.name.endsWith("/") &&
            file.metadata &&
            Number(file.metadata.size) === 0
        );
        folderMarkers.push(...markers);
        nextPageToken = nextQuery && nextQuery.pageToken;
      } while (nextPageToken);

      logger.info(
        `Found ${folderMarkers.length} folder markers in the bucket.`
      );

      for (const marker of folderMarkers) {
        const folderPrefix = marker.name;
        logger.info(`Processing marker: ${folderPrefix}`);

        let folderQuery = { prefix: folderPrefix, maxResults: 1000 };
        let folderPageToken = null;
        const allObjects = [];

        do {
          if (folderPageToken) {
            folderQuery.pageToken = folderPageToken;
          }
          const [objects, folderNextQuery] = await bucket.getFiles(folderQuery);
          allObjects.push(...objects);
          folderPageToken = folderNextQuery && folderNextQuery.pageToken;
        } while (folderPageToken);

        logger.info(
          `Found ${allObjects.length} objects under prefix ${folderPrefix}`
        );

        // If the only object is the marker itself, delete it.
        if (allObjects.length === 1 && allObjects[0].name === folderPrefix) {
          logger.info(`Deleting empty folder marker: ${folderPrefix}`);
          await marker.delete();
        } else {
          logger.info(
            `Folder ${folderPrefix} is not empty; contains: ${allObjects
              .map((obj) => obj.name)
              .join(", ")}`
          );
        }
      }
    } catch (error) {
      logger.error("Error cleaning up folder markers:", error);
    }
  }
);
