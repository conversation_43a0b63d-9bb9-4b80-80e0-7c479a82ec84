// functions/scheduledExecutor.js
const { onSchedule } = require("firebase-functions/v2/scheduler");
const admin = require('firebase-admin');
const axios = require('axios');
const cron = require('cron-parser'); // Changed: proper import for cron-parser

/**
 * Scheduled function that runs every minute to check for endpoints that need to be executed
 */
exports.scheduleExecutor = onSchedule("15 * * * *", async (context) => {
    try {
      const db = admin.firestore();
      console.log(context?.data || 'No context data');
      
      // Get all enabled scheduled endpoints
      const snapshot = await db.collection('ScheduledEndpoints')
        .where('enabled', '==', true)
        .get();
      
      // Current time
      const now = admin.firestore.Timestamp.now();
      console.log(`Running scheduled executor at ${now.toDate().toISOString()} for ${snapshot.size} endpoints`);
      
      // Process each endpoint
      const executionPromises = [];
      
      for (const doc of snapshot.docs) {
        const endpoint = doc.data();
        const id = doc.id;
        
        // Check if it's time to execute this endpoint
        if (shouldExecute(endpoint, now)) {
          console.log(`Executing scheduled endpoint: ${endpoint.name} (ID: ${id})`);
          
          // Execute asynchronously
          executionPromises.push(
            executeEndpoint(endpoint, id)
              .then(result => {
                console.log(`Successfully executed endpoint ${id} with ${result.savedCount} records`);
              })
              .catch(error => {
                console.error(`Error executing scheduled endpoint ${id}:`, error);
                
                // Update status on error
                return db.collection('ScheduledEndpoints').doc(id).update({
                  lastRun: admin.firestore.FieldValue.serverTimestamp(),
                  lastStatus: 'error',
                  lastError: error.message || 'Unknown error',
                  updatedAt: admin.firestore.FieldValue.serverTimestamp()
                });
              })
          );
        }
      }
      
      // Wait for all executions to complete
      await Promise.all(executionPromises);
      
      console.log(`Completed scheduled execution run for ${executionPromises.length} endpoints`);
      return null;
    } catch (error) {
      console.error('Error in scheduledExecutor:', error);
      return null;
    }
  });

/**
 * Execute an endpoint and save results to Firestore
 * @param {Object} endpoint - The endpoint configuration
 * @param {string} id - The endpoint document ID
 * @returns {Promise<Object>} - Result information
 */
async function executeEndpoint(endpoint, id) {
  const db = admin.firestore();
  
  try {
    // Get the data source
    const dataSourceRef = db.collection('DataSources').doc(endpoint.dataSourceId);
    const dataSourceSnap = await dataSourceRef.get();
    
    if (!dataSourceSnap.exists) {
      throw new Error(`Data source with ID ${endpoint.dataSourceId} not found`);
    }
    
    const dataSource = dataSourceSnap.data();
    
    // Find the endpoint configuration
    const endpointConfig = dataSource.endpoints.find(e => e.id === endpoint.endpointId);
    
    if (!endpointConfig) {
      throw new Error(`Endpoint with ID ${endpoint.endpointId} not found`);
    }
    
    // Check if cycling is enabled
    const shouldCycle = endpoint.cycle === true && endpoint.cycleKey && endpoint.parameters;
    
    // Initialize tracking variables for cycling
    let totalSavedCount = 0;
    let allDataFetched = false;
    let currentOffset = 0;
    let currentBatch = 1;
    let totalRecordsToFetch = Infinity; // Default to infinite records
    
    // Get the target collection reference
    const targetCollection = db.collection('DataSources').doc(endpoint.dataSourceId)
                              .collection('Targets').doc(endpoint.targetCollection);
    const targetCollectionSnap = await targetCollection.get();
    
    if (!targetCollectionSnap.exists) {
      console.log(`[Scheduled Endpoint ${id}] Target collection doesn't exist: ${targetCollection.location || "Not defined"}`);
      throw new Error(`Target collection with ID ${endpoint.targetCollection} not found`);
    }

    const targetCollectionData = targetCollectionSnap.data();
    console.log('Target Collection Data:', targetCollectionData);
    const targetStorageCollection = db.collection(`${targetCollectionData?.location}`);
    
    // If cycling is enabled, we'll loop until we've fetched all data
    do {
      // Clone the parameters to avoid modifying the original
      const currentParameters = shouldCycle ? {...endpoint.parameters} : endpoint.parameters;
      
      // Update the offset parameter if cycling
      if (shouldCycle && currentParameters.offset !== undefined) {
        // Convert offset to number if it's a string
        currentOffset = typeof currentParameters.offset === 'string' 
          ? parseInt(currentParameters.offset, 10) 
          : currentParameters.offset;
        
        if (currentBatch > 1) {
          // Calculate new offset based on limit and current batch
          const limit = typeof currentParameters.limit === 'string' 
            ? parseInt(currentParameters.limit, 10) 
            : currentParameters.limit;
          
          currentOffset = (currentBatch - 1) * limit;
        }
        
        // Update the offset parameter - ensure it's a string
        currentParameters.offset = currentOffset.toString();
      }
      
      // Build the URL
      let url = dataSource.baseUrl;
      if (!url.endsWith('/') && !endpointConfig.path.startsWith('/')) {
        url += '/';
      }
      url += endpointConfig.path;
      
      // Prepare request config
      const axiosConfig = {
        method: endpointConfig.method || 'GET',
        url,
        headers: {
          'Content-Type': 'application/json',
          ...dataSource.headers
        }
      };
      
      // Add parameters
      if (currentParameters && Object.keys(currentParameters).length > 0) {
        axiosConfig.params = {};
        
        // Add default params from data source
        if (dataSource.defaultParams) {
          Object.assign(axiosConfig.params, dataSource.defaultParams);
        }
        
        // Add endpoint-specific params
        Object.entries(currentParameters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            axiosConfig.params[key] = value;
          }
        });
      }
      
      // Handle authentication
      if (dataSource.authMethod === 'apiKey' && dataSource.authConfig) {
        if (dataSource.authConfig.in === 'header') {
          axiosConfig.headers[dataSource.authConfig.name] = dataSource.authConfig.value;
        } else if (dataSource.authConfig.in === 'query') {
          if (!axiosConfig.params) {
            axiosConfig.params = {};
          }
          axiosConfig.params[dataSource.authConfig.name] = dataSource.authConfig.value;
        }
      } else if (dataSource.authMethod === 'basic' && dataSource.authConfig) {
        axiosConfig.auth = {
          username: dataSource.authConfig.username,
          password: dataSource.authConfig.password
        };
      } else if ((dataSource.authMethod === 'jwt' || dataSource.authMethod === 'bearer') && dataSource.authConfig) {
        axiosConfig.headers['Authorization'] = `Bearer ${dataSource.authConfig.token}`;
      } else if (dataSource.authMethod === 'custom' && dataSource.authConfig) {
        axiosConfig.headers[`${dataSource.authConfig.headerName}`] = dataSource.authConfig.headerValue;
      }
      
      // Add request body for non-GET requests
      if (axiosConfig.method !== 'GET' && endpoint.requestBody) {
        axiosConfig.data = endpoint.requestBody;
      }
      
      console.log(`[Scheduled Endpoint ${id}] Making ${axiosConfig.method} request to: ${url}${shouldCycle ? ` (batch ${currentBatch}, offset ${currentOffset})` : ''}`);
      
      // Make the request
      const response = await axios(axiosConfig);

      // Process the response
      let data = response.data;
      
      // Check if we can find the total count in the response
      if (shouldCycle && endpoint.cycleKey && typeof response.data === 'object') {
        if (response.data[endpoint.cycleKey] !== undefined && response.data[endpoint.cycleKey] !== null) {
          // Convert to number if it's a string
          const countValue = typeof response.data[endpoint.cycleKey] === 'string' 
            ? parseInt(response.data[endpoint.cycleKey], 10) 
            : response.data[endpoint.cycleKey];            
          if (!isNaN(countValue)) {
            totalRecordsToFetch = countValue;
            console.log(`[Scheduled Endpoint ${id}] Found total count in response: ${totalRecordsToFetch}`);
          }
        }
      }
      
      if(targetCollectionData?.sourceIndex && targetCollectionData?.sourceIndex !== '') {
        data = data[targetCollectionData.sourceIndex];
      }

      // Apply transformation if configured
      if (endpoint.transformationCode) {
        try {
          const transformFn = new Function('response', endpoint.transformationCode
            .replace('function transform(response) {', '')
            .replace(/}$/, '')
            + 'return response;');
          
          data = transformFn(data);
        } catch (error) {
          console.error(`Error applying transformation: ${error.message}`);
          throw error;
        }
      }
      
      // Apply data source transformation if available
      if (dataSource.transformations && dataSource.transformations[endpoint.endpointId]) {
        try {
          const transformFn = new Function('response', dataSource.transformations[endpoint.endpointId]
            .replace('function transform(response) {', '')
            .replace(/}$/, '')
            + 'return response;');
          
          data = transformFn(data);
        } catch (error) {
          console.error(`Error applying data source transformation: ${error.message}`);
        }
      }
      
      let batchSavedCount = 0;
      const batchCommits = [];
      
      // Determine if data is an array or single object
      const itemsToSave = Array.isArray(data) ? data : [data];
      
      // Process in batches of 500 (Firestore batch limit)
      const batchSize = 500;
      
      for (let i = 0; i < itemsToSave.length; i += batchSize) {
        const batch = db.batch();
        const batchItems = itemsToSave.slice(i, i + batchSize);
        
        for (const item of batchItems) {
          // Skip null or undefined items
          if (item === null || item === undefined) continue;
          
          let itemId = null;

          if (item.hasOwnProperty("uuid")) {
            itemId = item.uuid;
          } else if (item.hasOwnProperty("id")) {
            itemId = item.id;
          }
          
          if (itemId !== null) {
            itemId = itemId.toString();
          }
          
          // Add metadata
          const itemWithMeta = {
            ...item,
            _source: {
              scheduledEndpointId: id,
              dataSourceId: endpoint.dataSourceId,
              endpointId: endpoint.endpointId,
              timestamp: admin.firestore.FieldValue.serverTimestamp()
            }
          };
          
          let docRef = targetStorageCollection.doc();

          if (itemId && itemId !== '') {
            docRef = targetStorageCollection.doc(itemId);
          }

          batch.set(docRef, itemWithMeta);
          batchSavedCount++;
        }
        
        batchCommits.push(batch.commit());
      }
      
      // Wait for all batches to complete
      await Promise.all(batchCommits);
      
      // Update running total
      totalSavedCount += batchSavedCount;
      
      // Determine if we need to continue cycling
      if (shouldCycle) {
        // Check if we've reached the total count from response
        if (totalRecordsToFetch !== Infinity && totalSavedCount >= totalRecordsToFetch) {
          allDataFetched = true;
          console.log(`[Scheduled Endpoint ${id}] Reached target count of ${totalRecordsToFetch} records (saved ${totalSavedCount})`);
        }
        // Check if there's no more data to fetch
        else if (itemsToSave.length === 0 || batchSavedCount === 0) {
          allDataFetched = true;
          console.log(`[Scheduled Endpoint ${id}] No more data received`);
        }
        // Or if the response count is less than the limit, meaning we reached the end
        else if (currentParameters.limit && 
                 parseInt(currentParameters.limit, 10) > 0 && 
                 itemsToSave.length < parseInt(currentParameters.limit, 10)) {
          allDataFetched = true;
          console.log(`[Scheduled Endpoint ${id}] Received fewer records (${itemsToSave.length}) than limit (${currentParameters.limit})`);
        }
        // Continue cycling until we've fetched all available data
        else {
          // Check if we've reached an arbitrary maximum (safety mechanism)
          if (currentBatch >= 50) { // Maximum 50 batches (12,500 records with limit=250)
            console.log(`[Scheduled Endpoint ${id}] Reached maximum batch limit (50), stopping to prevent infinite loops`);
            allDataFetched = true;
          } else {
            // Increment the batch counter for the next iteration
            currentBatch++;
          }
        }
      } else {
        // If cycling is disabled, we're done after one batch
        allDataFetched = true;
      }
      
    } while (shouldCycle && !allDataFetched);
    
    // Update the status in the scheduled endpoint document
    await db.collection('ScheduledEndpoints').doc(id).update({
      lastRun: admin.firestore.FieldValue.serverTimestamp(),
      lastStatus: 'success',
      lastResultCount: totalSavedCount,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    return {
      status: 200,
      savedCount: totalSavedCount,
      timestamp: admin.firestore.Timestamp.now()
    };
  } catch (error) {
    console.error(`[Scheduled Endpoint ${id}] Execution error:`, error);
    
    // Update the status in the scheduled endpoint document
    await db.collection('ScheduledEndpoints').doc(id).update({
      lastRun: admin.firestore.FieldValue.serverTimestamp(),
      lastStatus: 'error',
      lastError: error.message || 'Unknown error',
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    throw error;
  }
}

/**
 * Determine if a scheduled endpoint should be executed based on its schedule
 * @param {Object} endpoint - The scheduled endpoint
 * @param {FirebaseFirestore.Timestamp} now - Current timestamp
 * @returns {boolean} - Whether the endpoint should be executed
 */
function shouldExecute(endpoint, now) {
  const { schedule, lastRun } = endpoint;
  
  // If never run before, execute immediately
  if (!lastRun) {
    return true;
  }
  
  // Convert lastRun to JavaScript Date object
  const lastRunDate = lastRun.toDate ? lastRun.toDate() : new Date(lastRun);
  const nowDate = now.toDate ? now.toDate() : new Date(now);
  
  if (schedule.type === 'interval') {
    // Schedule is in minutes
    const intervalMinutes = parseInt(schedule.value, 10);
    
    if (isNaN(intervalMinutes) || intervalMinutes <= 0) {
      console.error(`Invalid interval value for scheduled endpoint ${endpoint.id}: ${schedule.value}`);
      return false;
    }
    
    // Calculate next run time
    const nextRunDate = new Date(lastRunDate.getTime() + intervalMinutes * 60 * 1000);
    
    return nowDate >= nextRunDate;
  } else if (schedule.type === 'cron') {
    try {
      // Fixed: proper usage of cron-parser
      const interval = cron.parseExpression(schedule.value, { 
        currentDate: lastRunDate,
        tz: schedule.timezone 
      });
      const nextRun = interval.next().toDate();
      return nowDate >= nextRun;
    } catch (error) {
      console.error(`Error parsing cron expression for scheduled endpoint: ${error.message}`);
      return false;
    }
  }
  
  return false;
}