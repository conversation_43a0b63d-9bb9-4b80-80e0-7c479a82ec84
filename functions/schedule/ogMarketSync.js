// functions/schedule/ogMarketSync.js
const { onSchedule } = require('firebase-functions/v2/scheduler');
const admin = require('firebase-admin');
const { logger } = require('firebase-functions/v2');
const axios = require('axios');

// OccasionGenius Markets specific configuration
const AIRTABLE_API_URL = 'https://api.airtable.com/v0';
const FIRESTORE_MARKETS_COLLECTION = 'EventMarkets';
const BATCH_SIZE = 500; // Firestore batch write limit

// Rate limiting as per OccasionGenius requirements
const RATE_LIMIT_DELAY = 2000; // 2 seconds between requests
const PAGINATION_DELAY = 1500; // 1.5 seconds between pagination calls
const MAX_RETRIES = 3;

// Memory optimization settings
const DUPLICATE_CHECK_LIMIT = 5000; // Markets typically have fewer records
const ERROR_LOG_LIMIT = 50;

/**
 * OccasionGenius Airtable Client for Markets
 */
class OccasionGeniusMarketsClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.axiosInstance = axios.create({
      baseURL: AIRTABLE_API_URL,
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 35000 // 35 second timeout
    });
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Process records in pages with memory management
   */
  async processRecordsInPages(baseId, tableId, viewId, processCallback) {
    let offset = null;
    let requestCount = 0;
    let totalProcessed = 0;
    let consecutiveErrors = 0;
    
    do {
      let retryCount = 0;
      let success = false;
      
      while (!success && retryCount < MAX_RETRIES) {
        try {
          // Rate limiting
          if (requestCount > 0) {
            const delay = offset ? PAGINATION_DELAY : RATE_LIMIT_DELAY;
            await this.sleep(delay);
          }
          
          const params = {
            pageSize: 100,
            ...(offset && { offset }),
            ...(viewId && { view: viewId })
          };
          
          const url = `/${baseId}/${tableId}`;
          logger.info(`[Markets] Fetching page ${requestCount + 1} from ${tableId}`);
          
          const response = await this.axiosInstance.get(url, { params });
          const data = response.data;
          
          // Add base reference
          data.records.forEach(record => {
            record._baseId = baseId;
          });
          
          // Process immediately and clear from memory
          const processed = await processCallback(data.records);
          totalProcessed += processed;
          
          offset = data.offset;
          requestCount++;
          success = true;
          consecutiveErrors = 0;
          
          // Force garbage collection hint
          if (global.gc && requestCount % 5 === 0) {
            global.gc();
          }
          
          logger.info(`[Markets] Processed page ${requestCount}: ${processed} records saved`);
          
        } catch (error) {
          retryCount++;
          consecutiveErrors++;
          
          if (error.response?.data?.error?.type === 'LIST_RECORDS_ITERATOR_NOT_AVAILABLE') {
            logger.warn('[Markets] Iterator expired, restarting from beginning');
            offset = null;
            requestCount = 0;
            totalProcessed = 0;
            retryCount = 0;
            await this.sleep(5000);
            continue;
          }
          
          if (retryCount < MAX_RETRIES) {
            const retryDelay = Math.min(2000 * Math.pow(2, retryCount), 30000);
            logger.warn(`[Markets] Request failed (attempt ${retryCount}/${MAX_RETRIES}), retrying in ${retryDelay}ms`);
            await this.sleep(retryDelay);
            continue;
          }
          
          throw new Error(`Failed after ${MAX_RETRIES} attempts: ${error.message}`);
        }
      }
      
      if (consecutiveErrors > 5) {
        throw new Error('Too many consecutive errors');
      }
      
    } while (offset);
    
    return totalProcessed;
  }
}

/**
 * Optimized number parser for Markets
 */
function parseNumber(value, defaultValue = null) {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }
  
  if (typeof value === 'string') {
    const lowerStr = value.toLowerCase().trim();
    if (['nan', 'null', 'undefined', 'none'].includes(lowerStr)) {
      return defaultValue;
    }
  }
  
  const parsed = parseFloat(value);
  return (isNaN(parsed) || !isFinite(parsed)) ? defaultValue : parsed;
}

/**
 * Market transformer for Firestore
 */
function transformEventMarketForFirestore(record) {
  const f = record.fields || {};
  
  const lat = parseNumber(f['area latitude']);
  const lng = parseNumber(f['area longitude']);
  
  return {
    // Metadata
    _airtableId: record.id,
    _baseId: record._baseId,
    _createdTime: new Date(record.createdTime),
    _lastModified: admin.firestore.FieldValue.serverTimestamp(),
    _lastSyncTime: new Date(),
    
    // Area information
    area_name: f['area name'] || '',
    area_uuid: f['area uuid'] || null,
    area_latitude: lat,
    area_longitude: lng,
    area_radius: parseNumber(f['area radius']),
    area_timezone: f['area time_zone'] || 'Unknown Timezone',
    utc_offset_hours: parseNumber(f['area utc_offset_hours'], 0),
    utc_dst_offset_hours: parseNumber(f['area utc_dst_offset_hours'], 0),
    area_address: f['area address'] || '',

    // Event relationships
    event_count: parseInt(f['# of Events']) || 0,
    
    // Location data
    _geopoint: (lat && lng) ? new admin.firestore.GeoPoint(lat, lng) : null
  };
}

/**
 * Duplicate tracker for Markets
 */
class MarketDuplicateTracker {
  constructor(limit = DUPLICATE_CHECK_LIMIT) {
    this.set = new Set();
    this.queue = [];
    this.limit = limit;
  }
  
  has(uuid) {
    return this.set.has(uuid);
  }
  
  add(uuid) {
    if (this.set.has(uuid)) return;
    
    this.set.add(uuid);
    this.queue.push(uuid);
    
    // Remove oldest entries if over limit
    while (this.queue.length > this.limit) {
      const oldest = this.queue.shift();
      this.set.delete(oldest);
    }
  }
  
  clear() {
    this.set.clear();
    this.queue = [];
  }
}

/**
 * Save markets page to Firestore
 */
async function saveMarketsPageToFirestore(records, db, tracker) {
  const collectionRef = db.collection(FIRESTORE_MARKETS_COLLECTION);
  let savedCount = 0;
  let skippedCount = 0;
  
  const batch = db.batch();
  let batchCount = 0;
  
  for (const record of records) {
    try {
      const transformed = transformEventMarketForFirestore(record);
      const uuid = transformed.area_uuid;
      console.log(`[Markets] Processing record ${record.id} with area_uuid: ${uuid}`);
      
      if (!uuid) {
        logger.warn(`[Markets] Record ${record.id} missing area_uuid`);
        skippedCount++;
        continue;
      }
      
      // Check duplicates
      if (tracker.has(uuid)) {
        skippedCount++;
        continue;
      }
      
      tracker.add(uuid);
      
      const docRef = collectionRef.doc(uuid);
      batch.set(docRef, transformed, { merge: true });
      batchCount++;
      
      // Commit batch if it reaches limit
      if (batchCount >= BATCH_SIZE) {
        await batch.commit();
        savedCount += batchCount;
        batchCount = 0;
      }
      
    } catch (error) {
      logger.error(`[Markets] Transform error for ${record.id}: ${error.message}`);
      skippedCount++;
    }
  }
  
  // Commit remaining
  if (batchCount > 0) {
    try {
      await batch.commit();
      savedCount += batchCount;
    } catch (error) {
      logger.error(`[Markets] Batch commit error: ${error.message}`);
    }
  }
  
  logger.info(`[Markets] Page saved: ${savedCount} records, ${skippedCount} skipped`);
  return savedCount;
}

/**
 * Process a single market base
 */
async function processMarketBase(base, client, db, tracker) {
  logger.info(`[Markets] Processing base ${base.appId} - ${base.name}`);
  
  const startTime = Date.now();
  let totalSaved = 0;
  
  try {
    totalSaved = await client.processRecordsInPages(
      base.appId,
      base.tableId,
      base.viewId,
      async (pageRecords) => {
        const saved = await saveMarketsPageToFirestore(pageRecords, db, tracker);
        
        // Clear records from memory immediately
        pageRecords.length = 0;
        
        return saved;
      }
    );
    
    const duration = Date.now() - startTime;
    logger.info(`[Markets] Completed ${base.appId}: ${totalSaved} saved in ${Math.round(duration / 1000)}s`);
    
    return {
      baseId: base.appId,
      baseName: base.name,
      savedCount: totalSaved,
      duration,
      success: true
    };
    
  } catch (error) {
    logger.error(`[Markets] Failed to process ${base.appId}: ${error.message}`);
    
    return {
      baseId: base.appId,
      baseName: base.name,
      savedCount: totalSaved,
      duration: Date.now() - startTime,
      success: false,
      error: error.message
    };
  }
}

/**
 * Main sync function for Markets
 */
async function syncMarketsToFirestore(db, apiKey, marketBases) {
  const startTime = Date.now();
  const results = {
    success: true,
    startTime: new Date(startTime).toISOString(),
    marketBases: [],
    totalSaved: 0
  };
  
  // Use limited tracker
  const tracker = new MarketDuplicateTracker();
  
  try {
    const client = new OccasionGeniusMarketsClient(apiKey);
    
    // Process each base
    for (const base of marketBases) {
      const baseResult = await processMarketBase(base, client, db, tracker);
      results.marketBases.push(baseResult);
      results.totalSaved += baseResult.savedCount;
    }
    
    // Update sync metadata
    const syncMeta = {
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      duration: Date.now() - startTime,
      marketsCount: results.totalSaved,
      bases: results.marketBases
    };
    
    await db.collection(FIRESTORE_MARKETS_COLLECTION).doc('_lastSync').set(syncMeta);
    
  } catch (error) {
    logger.error('[Markets] Fatal sync error:', error);
    results.success = false;
    results.error = error.message;
  }
  
  results.duration = Date.now() - startTime;
  results.endTime = new Date().toISOString();
  
  return results;
}

/**
 * Scheduled function for Markets
 */
const ogMarketSync = onSchedule({
  schedule: '30 2,14 * * *', // 2:30 AM and 2:30 PM daily (30 minutes after events)
  timeZone: 'America/New_York',
  memory: '1GiB', // Markets typically need less memory
  timeoutSeconds: 540, // 9 minutes
  maxInstances: 1,
  retryCount: 1
}, async (request) => {
  logger.info('[Markets] Starting OccasionGenius Markets sync', {
    eventId: request.eventId,
    timestamp: request.timestamp
  });
  
  const apiKey = process.env.OG_API_KEY;
  if (!apiKey) {
    throw new Error('OG_API_KEY not configured');
  }
  
  const db = admin.firestore();
  
  const marketBases = [{
    appId: 'appf64PoFO85E2VQe',
    tableId: 'tbl4TbDVYLS89L3zb',
    viewId: null,
    name: 'EventMarkets'
  }];
  
  try {
    const results = await syncMarketsToFirestore(db, apiKey, marketBases);
    
    if (!results.success) {
      throw new Error(`Markets sync failed: ${results.error}`);
    }
    
    logger.info('[Markets] Sync completed:', {
      totalSaved: results.totalSaved,
      durationMinutes: Math.round(results.duration / 60000)
    });
    
  } catch (error) {
    logger.error('[Markets] Sync failed:', error);
    throw error;
  }
});

exports.ogMarketSync = ogMarketSync;