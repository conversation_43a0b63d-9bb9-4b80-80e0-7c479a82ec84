const { onSchedule } = require("firebase-functions/v2/scheduler");
const {addToTopic} = require('../utils/pubsub/addToTopic');
const admin = require('firebase-admin');

/**
 * Schedule function to process all pending records in the deliverability collection
 * Only processes records that were created before the current time
 * Runs every minute (at minute 1 of each hour)
 */
exports.processPendingDeliverabilityRecords = onSchedule({
  schedule: "0,15,30,45 * * * *",
  timeZone: "America/New_York",
  retryCount: 3,
  maxRetrySeconds: 60,
  memory: "2GiB",
  cpu: 1
}, async (context) => {
    // App is already initialized, so we can use admin directly
    const db = admin.firestore();   
    
    try {
        // Get current timestamp for comparison
        const now = admin.firestore.Timestamp.now();
        
        // Query for all documents with 'pending' status created before now
        const pendingSnapshot = await db.collection('metaQueue')
            .where('status', '==', 'pending')
            .where('createdAt', '<=', now)
            .limit(5000)
            .get();
            
        if (pendingSnapshot.empty) {
            console.log('No pending deliverability records found');
            return null; // Return null as per Firebase Functions requirements
        }
        
        console.log(`Found ${pendingSnapshot.size} pending deliverability records to process`);
        
        // Process each pending record
        const processPromises = [];
        
        pendingSnapshot.forEach(doc => {
            const data = doc.data();
            const id = doc.id;            
            
            processPromises.push(
                (async () => {
                    try {
                        // Import the required functions for updating records
                        const { 
                            completeRecipientCalc
                        } = require('../utils/calculate/recipientCalc');

                        const {
                            completeRecipientGroupCalc
                        } = require('../utils/calculate/recipientGroupCalc');
                        const{ updateDuplicateCheck, completeDuplicateCheck } = require('../utils/calculate/duplicateCheck');

                        // Update status based on record type
                        if (data.type === 'recipient') {
                            await completeRecipientCalc({ id: id });
                        } else if (data.type === 'recipientGroup') {
                            await completeRecipientGroupCalc({ id: id });
                        } else if (data.type === 'DuplicateCheck') {
                            // const message = JSON.stringify({
                            //     accountId: data.accountId
                            // });
                            // const queueItem = {
                            //     topic: "check-duplicates",
                            //     message: message,
                            // };
                            // await addToTopic(queueItem);
                            const docId = `${data.accountId}-DuplicateCheck`;
                            // await updateDuplicateCheck({ id: docId });
                            await completeDuplicateCheck({ id: id });
                        } else {
                            console.warn(`Unknown record type ${data.type} for record ${id}`);
                            return { id, success: false, error: 'Unknown record type' };
                        }
                        
                        return { id, success: true };
                    } catch (error) {
                        console.error(`Error processing record ${id}:`, error);
                        
                        const {
                            failRecipientGroupCalc
                        } = require('../utils/calculate/recipientGroupCalc');

                        const { failDuplicateCheck } = require('../utils/calculate/duplicateCheck');

                        // Update status to failed
                        if (data.type === 'recipientGroup') {
                            await failRecipientGroupCalc({ 
                                id, 
                                message: `Processing error: ${error.message}`
                            });
                        } else if (data.type === 'DuplicateCheck') {
                            const docId = `${data.accountId}-DuplicateCheck`;
                            await failDuplicateCheck({ 
                                id: docId, 
                                message: `Processing error: ${error.message}`
                            });
                        }
                        
                        return { id, success: false, error: error.message };
                    }
                })()
            );
        });
        
        // Wait for all processing to complete
        const results = await Promise.all(processPromises);
        
        // Summarize results
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        
        console.log(`Processing complete. Successfully initiated: ${successful}, Failed: ${failed}`);
        
        return null; // Return null as per Firebase Functions requirements
    } catch (error) {
        console.error('Error in processPendingDeliverabilityRecords:', error);
        return null; // Return null as per Firebase Functions requirements
    }
});