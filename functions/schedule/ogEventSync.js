// functions/schedule/ogEventSync.js
const { onSchedule } = require('firebase-functions/v2/scheduler');
const admin = require('firebase-admin');
const { logger } = require('firebase-functions/v2');
const axios = require('axios');

// OccasionGenius Events specific configuration
const AIRTABLE_API_URL = 'https://api.airtable.com/v0';
const FIRESTORE_EVENTS_COLLECTION = 'LiveEvents';
const BATCH_SIZE = 500; // Firestore batch write limit

// Rate limiting as per OccasionGenius requirements
const RATE_LIMIT_DELAY = 2000; // 2 seconds between requests
const PAGINATION_DELAY = 1500; // 1.5 seconds between pagination calls
const MAX_RETRIES = 3;

// Memory optimization settings
const DUPLICATE_CHECK_LIMIT = 10000; // Limit duplicate tracking to recent records
const ERROR_LOG_LIMIT = 50; // Limit error details to prevent memory bloat

/**
 * OccasionGenius Airtable Client for Events
 */
class OccasionGeniusEventsClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.axiosInstance = axios.create({
      baseURL: AIRTABLE_API_URL,
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 35000 // 35 second timeout
    });
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Process records in pages with memory management
   */
  async processRecordsInPages(baseId, tableId, viewId, processCallback) {
    let offset = null;
    let requestCount = 0;
    let totalProcessed = 0;
    let consecutiveErrors = 0;
    
    do {
      let retryCount = 0;
      let success = false;
      
      while (!success && retryCount < MAX_RETRIES) {
        try {
          // Rate limiting
          if (requestCount > 0) {
            const delay = offset ? PAGINATION_DELAY : RATE_LIMIT_DELAY;
            await this.sleep(delay);
          }
          
          const params = {
            pageSize: 100,
            ...(offset && { offset }),
            ...(viewId && { view: viewId })
          };
          
          const url = `/${baseId}/${tableId}`;
          logger.info(`[Events] Fetching page ${requestCount + 1} from ${tableId}`);
          
          const response = await this.axiosInstance.get(url, { params });
          const data = response.data;
          
          // Add base reference
          data.records.forEach(record => {
            record._baseId = baseId;
          });
          
          // Process immediately and clear from memory
          const processed = await processCallback(data.records);
          totalProcessed += processed;
          
          offset = data.offset;
          requestCount++;
          success = true;
          consecutiveErrors = 0;
          
          // Force garbage collection hint
          if (global.gc && requestCount % 5 === 0) {
            global.gc();
          }
          
          logger.info(`[Events] Processed page ${requestCount}: ${processed} records saved`);
          
        } catch (error) {
          retryCount++;
          consecutiveErrors++;
          
          if (error.response?.data?.error?.type === 'LIST_RECORDS_ITERATOR_NOT_AVAILABLE') {
            logger.warn('[Events] Iterator expired, restarting from beginning');
            offset = null;
            requestCount = 0;
            totalProcessed = 0;
            retryCount = 0;
            await this.sleep(5000);
            continue;
          }
          
          if (retryCount < MAX_RETRIES) {
            const retryDelay = Math.min(2000 * Math.pow(2, retryCount), 30000);
            logger.warn(`[Events] Request failed (attempt ${retryCount}/${MAX_RETRIES}), retrying in ${retryDelay}ms`);
            await this.sleep(retryDelay);
            continue;
          }
          
          throw new Error(`Failed after ${MAX_RETRIES} attempts: ${error.message}`);
        }
      }
      
      if (consecutiveErrors > 5) {
        throw new Error('Too many consecutive errors');
      }
      
    } while (offset);
    
    return totalProcessed;
  }
}

/**
 * Cached date parser to avoid redundant parsing
 */
const dateCache = new Map();
const MAX_DATE_CACHE_SIZE = 1000;

function parseDate(dateStr) {
  if (!dateStr) return null;
  
  // Check cache first
  if (dateCache.has(dateStr)) {
    return dateCache.get(dateStr);
  }
  
  // Clear cache if too large
  if (dateCache.size > MAX_DATE_CACHE_SIZE) {
    dateCache.clear();
  }
  
  // Handle non-date strings
  if (typeof dateStr === 'string') {
    const lowerStr = dateStr.toLowerCase().trim();
    if (['none', 'null', 'undefined', '', 'nan'].includes(lowerStr)) {
      dateCache.set(dateStr, null);
      return null;
    }
  }
  
  try {
    const date = new Date(dateStr);
    
    // Validate date
    if (isNaN(date.getTime())) {
      dateCache.set(dateStr, null);
      return null;
    }
    
    const year = date.getFullYear();
    if (year < 1900 || year > 2100) {
      dateCache.set(dateStr, null);
      return null;
    }
    
    // Handle midnight edge case
    if (dateStr.includes('T00:00:00')) {
      date.setSeconds(date.getSeconds() - 1);
    }
    
    dateCache.set(dateStr, date);
    return date;
  } catch (error) {
    dateCache.set(dateStr, null);
    return null;
  }
}

/**
 * Optimized number parser
 */
function parseNumber(value, defaultValue = null) {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }
  
  if (typeof value === 'string') {
    const lowerStr = value.toLowerCase().trim();
    if (['nan', 'null', 'undefined', 'none'].includes(lowerStr)) {
      return defaultValue;
    }
  }
  
  const parsed = parseFloat(value);
  return (isNaN(parsed) || !isFinite(parsed)) ? defaultValue : parsed;
}

/**
 * Event transformer for Firestore
 */
function transformEventForFirestore(record) {
  const f = record.fields || {};
  
  // Early exit for duplicates
  const cancelledStatus = f.cancelled || null;
  if (cancelledStatus && 
      (Array.isArray(cancelledStatus) ? 
        cancelledStatus.includes('Duplicate') : 
        cancelledStatus === 'Duplicate')) {
    return null;
  }
  
  // Core fields only - reduce memory usage
  return {
    // Metadata
    _airtableId: record.id,
    _baseId: record._baseId,
    _createdTime: parseDate(record.createdTime),
    _lastModified: admin.firestore.FieldValue.serverTimestamp(),
    _lastSyncTime: new Date(),
    
    // Event identifiers
    uuid: f.uuid || null,
    recurring_event_uuid: f.recurring_event_uuid || null,
    umbrella_event_uuid: f.umbrella_event_uuid || null,
    
    // Event details
    name: f.Event_Name || f.name || '',
    description: f.description || '',
    
    // Dates
    start_date: parseDate(f.start_date),
    end_date: parseDate(f.end_date),
    instance_date: parseDate(f.instance_date),
    
    // URLs
    source_url: f.source_url || null,
    ticket_url: f.ticket_url || null,
    image_url: f.image_url || null,
    
    // Event metadata
    popularity_score: Math.min(Math.max(parseInt(f.popularity_score) || 0, 0), 6),
    cancelled: cancelledStatus,
    areas: Array.isArray(f.areas) ? f.areas : (f.areas ? [f.areas] : []),
    
    // Venue info (limited fields)
    venue_name: f['venue name'] || null,
    venue_city: f['venue city'] || null,
    venue_latitude: parseNumber(f['venue latitude']),
    venue_longitude: parseNumber(f['venue longitude']),
    isActive: !cancelledStatus,
    
    // Status
    _isActive: !cancelledStatus,
    _isHidden: cancelledStatus && ['Cancelled', 'Duplicate', 'Event Ended'].includes(
      Array.isArray(cancelledStatus) ? cancelledStatus[0] : cancelledStatus
    )
  };
}

/**
 * Duplicate tracker with size limit
 */
class LimitedDuplicateTracker {
  constructor(limit = DUPLICATE_CHECK_LIMIT) {
    this.set = new Set();
    this.queue = [];
    this.limit = limit;
  }
  
  has(uuid) {
    return this.set.has(uuid);
  }
  
  add(uuid) {
    if (this.set.has(uuid)) return;
    
    this.set.add(uuid);
    this.queue.push(uuid);
    
    // Remove oldest entries if over limit
    while (this.queue.length > this.limit) {
      const oldest = this.queue.shift();
      this.set.delete(oldest);
    }
  }
  
  clear() {
    this.set.clear();
    this.queue = [];
  }
}

/**
 * Save page to Firestore with streaming
 */
async function savePageToFirestore(records, db, tracker) {
  const collectionRef = db.collection(FIRESTORE_EVENTS_COLLECTION);
  let savedCount = 0;
  let skippedCount = 0;
  
  // Process records in smaller chunks to reduce memory
  const CHUNK_SIZE = 50;
  
  for (let i = 0; i < records.length; i += CHUNK_SIZE) {
    const chunk = records.slice(i, i + CHUNK_SIZE);
    const batch = db.batch();
    let batchCount = 0;
    
    for (const record of chunk) {
      try {
        const transformed = transformEventForFirestore(record);
        if (!transformed) {
          skippedCount++;
          continue;
        }
        
        const uuid = transformed.uuid || record.id;
        
        // Check duplicates
        if (tracker.has(uuid)) {
          skippedCount++;
          continue;
        }
        
        tracker.add(uuid);
        
        const docRef = collectionRef.doc(uuid);
        batch.set(docRef, transformed, { merge: true });
        batchCount++;
        
      } catch (error) {
        logger.error(`[Events] Transform error for ${record.id}: ${error.message}`);
        skippedCount++;
      }
    }
    
    if (batchCount > 0) {
      try {
        await batch.commit();
        savedCount += batchCount;
      } catch (error) {
        logger.error(`[Events] Batch commit error: ${error.message}`);
      }
    }
  }
  
  logger.info(`[Events] Page saved: ${savedCount} records, ${skippedCount} skipped`);
  return savedCount;
}

/**
 * Process a single base
 */
async function processEventBase(base, client, db, tracker) {
  logger.info(`[Events] Processing base ${base.appId} - ${base.name}`);
  
  const startTime = Date.now();
  let totalSaved = 0;
  
  try {
    totalSaved = await client.processRecordsInPages(
      base.appId,
      base.tableId,
      base.viewId,
      async (pageRecords) => {
        const saved = await savePageToFirestore(pageRecords, db, tracker);
        
        // Clear records from memory immediately
        pageRecords.length = 0;
        
        return saved;
      }
    );
    
    const duration = Date.now() - startTime;
    logger.info(`[Events] Completed ${base.appId}: ${totalSaved} saved in ${Math.round(duration / 1000)}s`);
    
    return {
      baseId: base.appId,
      baseName: base.name,
      savedCount: totalSaved,
      duration,
      success: true
    };
    
  } catch (error) {
    logger.error(`[Events] Failed to process ${base.appId}: ${error.message}`);
    
    return {
      baseId: base.appId,
      baseName: base.name,
      savedCount: totalSaved,
      duration: Date.now() - startTime,
      success: false,
      error: error.message
    };
  }
}

/**
 * Main sync function for Events
 */
async function syncEventsToFirestore(db, apiKey, eventBases) {
  const startTime = Date.now();
  const results = {
    success: true,
    startTime: new Date(startTime).toISOString(),
    eventBases: [],
    totalSaved: 0
  };
  
  // Use limited tracker
  const tracker = new LimitedDuplicateTracker();
  
  try {
    const client = new OccasionGeniusEventsClient(apiKey);
    
    // Process each base
    for (const base of eventBases) {
      const baseResult = await processEventBase(base, client, db, tracker);
      results.eventBases.push(baseResult);
      results.totalSaved += baseResult.savedCount;
      
      // Clear tracker periodically
      if (tracker.queue.length > DUPLICATE_CHECK_LIMIT * 0.8) {
        logger.info('[Events] Clearing tracker to free memory');
        tracker.clear();
      }
    }
    
    // Update sync metadata
    const syncMeta = {
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      duration: Date.now() - startTime,
      eventsCount: results.totalSaved,
      bases: results.eventBases
    };
    
    await db.collection(FIRESTORE_EVENTS_COLLECTION).doc('_lastSync').set(syncMeta);
    
  } catch (error) {
    logger.error('[Events] Fatal sync error:', error);
    results.success = false;
    results.error = error.message;
  }
  
  results.duration = Date.now() - startTime;
  results.endTime = new Date().toISOString();
  
  // Clear caches
  dateCache.clear();
  
  return results;
}

/**
 * Scheduled function for Events
 */
const ogEventSync = onSchedule({
  schedule: '0 2,14 * * *', // 2 AM and 2 PM daily
  timeZone: 'America/New_York',
  memory: '2GiB',
  timeoutSeconds: 540, // 9 minutes
  maxInstances: 1,
  retryCount: 1
}, async (request) => {
  logger.info('[Events] Starting OccasionGenius Events sync', {
    eventId: request.eventId,
    timestamp: request.timestamp
  });
  
  const apiKey = process.env.OG_API_KEY;
  if (!apiKey) {
    throw new Error('OG_API_KEY not configured');
  }
  
  const db = admin.firestore();
  
  const eventBases = [{
    appId: 'appNhiQAlxtM1ZlWr',
    tableId: 'tblIxZXBxOjQ9JF26',
    viewId: 'viw32C7Eavpod9Rth',
    name: 'Main Events Base'
  }];
  
  try {
    const results = await syncEventsToFirestore(db, apiKey, eventBases);
    
    if (!results.success) {
      throw new Error(`Events sync failed: ${results.error}`);
    }
    
    logger.info('[Events] Sync completed:', {
      totalSaved: results.totalSaved,
      durationMinutes: Math.round(results.duration / 60000)
    });
    
  } catch (error) {
    logger.error('[Events] Sync failed:', error);
    throw error;
  }
});

exports.ogEventSync = ogEventSync;