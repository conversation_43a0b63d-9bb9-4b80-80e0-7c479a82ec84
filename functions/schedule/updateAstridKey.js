const { onSchedule } = require("firebase-functions/v2/scheduler");
const { SecretManagerServiceClient } = require('@google-cloud/secret-manager');
const { defineString } = require("firebase-functions/params");
const axios = require('axios');

// Initialize Secret Manager client
const client = new SecretManagerServiceClient();


// Define the scheduled Cloud Function (v2)
exports.updateAstridKey = onSchedule("0 0 * * *", async (event) => {
    const email = defineString("ASTRID_EMAIL");
    const password = defineString("ASTRID_PASSWORD");
    const baseUrl = defineString("ASTRID_URL");
    const currentKey = defineString("ASTRID_KEY");
    const master = defineString("SECRETS_MASTER")
    const parent = `projects/${master.value()}/secrets/ASTRID_KEY`

    const astridUrl = `${baseUrl.value()}/auth/login`;
    const payload = {
      email: email.value(),
      password: password.value(),
    }
  try {
    const response = await axios({
      method: "POST",
      url: astridUrl,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      data: payload,
    });

    if(response.status === 200 && response.data.token) {
        if(response.data.token !== currentKey.value()){
            const [version] = await client.addSecretVersion({
            parent: parent,
            payload: {
                data: Buffer.from(response.data.token, 'utf8'),
            },
            });
            return true;
        } else {
            return true;
        }
    } else {
        console.error("Error updating secret:", response.statusText);        
    }

    return false;
  } catch (error) {
    console.error("Error updating secret:", error);
    throw new Error("Failed to update secret");
  }
});
