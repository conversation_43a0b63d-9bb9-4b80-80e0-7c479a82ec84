const { onSchedule } = require("firebase-functions/v2/scheduler");
const { astridConnection } = require("../astrid/astridConnection");
const Timestamp = require("firebase-admin/firestore").Timestamp;

exports.syncAccountPlans = (db) =>
  onSchedule("0 0 * * *", async (event) => {
    try {
      const days = 1;

      const planSyncRes = await astridConnection({
        payload: {},
        url: `users/planSync?days=${days}`,
        method: "GET",
      });

      const accountIds = planSyncRes?.data?.data;
      if (!Array.isArray(accountIds) || accountIds.length === 0) {
        return;
      }

      for (const accountIdNumber of accountIds) {
        console.log("accountIdNumber: ", accountIdNumber);
        const accountId = accountIdNumber?.toString();
        if (!accountId) continue;

        const accountDocRef = db.collection("Account").doc(accountId);
        const accountPlansCollectionRef =
          accountDocRef.collection("AccountPlans");

        // load existing AccountPlans docs
        const existingAccountPlansSnapshot =
          await accountPlansCollectionRef.get();

        // get source of truth list of plans from Astrid
        const accountDataResponse = await astridConnection({
          payload: {},
          url: `users/account?accountId=${accountId}`,
          method: "GET",
        });
        const plans = accountDataResponse?.data?.plans || [];
        // normalize to strings
        const currentPlanIds = plans.map((p) => p.plan_id.toString());

        // delete any local docs whose ID isn't in Astrid's list
        const deleteBatch = db.batch();
        for (const accountPlan of existingAccountPlansSnapshot.docs) {
          if (!currentPlanIds.includes(accountPlan.id)) {
            deleteBatch.delete(accountPlan.ref);
          }
        }
        await deleteBatch.commit();

        // From Astrid connection source of truth plans
        for (const plan of plans) {
          const planIdStr = plan.plan_id.toString();
          const planRef = accountPlansCollectionRef.doc(planIdStr);
          await planRef.set(
            {
              accountId,
              accountPlanID: plan.id,
              activeAt: convertMySQLToFirestore(plan.active_at),
              createdAt: convertMySQLToFirestore(plan.created_at),
              updatedAt: convertMySQLToFirestore(plan.updated_at),
              isActive: true,
              planId: plan.plan_id,
            },
            { merge: true }
          );
        }
      }
    } catch (error) {
      console.error("Error updating accounts:", error.message);
      throw error;
    }
  });

const convertMySQLToFirestore = (mysqlTimestamp) => {
  const jsDate = new Date(mysqlTimestamp);
  return Timestamp.fromDate(jsDate);
};
