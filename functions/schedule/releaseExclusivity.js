const { onSchedule } = require("firebase-functions/v2/scheduler");
const { defineString } = require("firebase-functions/params");
const {
  Timestamp,
  FieldValue,
  getFirestore,
} = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");

const {
  hasMailingProductAssociation,
  releaseExclusivity,
} = require("../recipients/exclusivity/helpers");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");

const { deliverabilityStatus } = require("../constants/deliverabilityStatus");
const { GOOD_ADDRESS } = deliverabilityStatus;

//const gracePeriod = defineString("EXCLUSIVITY_RELEASE_GRACE_PERIOD");

exports.releaseExclusivity = onSchedule("0 * * * *", async (event) => {
  try {
    const db = getFirestore();
    //const gracePeriodHours = Number(gracePeriod.value()) || 24;
    const gracePeriodHours = 24;

    const cutoff = Timestamp.fromMillis(
      Date.now() - gracePeriodHours * 60 * 60 * 1000
    );
    let lastDoc = null;
    const batchSize = 100;
    do {
      let query = db
        .collection("ReleaseExclusivity")
        .where("createdAt", "<=", cutoff)
        .orderBy("createdAt")
        .limit(batchSize);

      if (lastDoc) {
        query = query.startAfter(lastDoc);
      }

      const querySnapshot = await query.get();

      if (querySnapshot.empty) {
        break;
      }

      const releasePromises = querySnapshot.docs.map(async (doc) => {
        const { accountId, sourceMailingAddressId, recipientId, action } =
          doc.data();
        const recipientRef = db
          .collection(collectionNames.account)
          .doc(accountId)
          .collection(subCollectionNames.contacts.recipients)
          .doc(recipientId);

        const sourceMailingAddressRef = db
          .collection(collectionNames.mailingAddresses)
          .doc(sourceMailingAddressId);

        const recipientGroupCollectionRef = db
          .collection(collectionNames.account)
          .doc(accountId)
          .collection(subCollectionNames.contacts.recipientGroups);

        /**
         * here we want to ensure the recipient does NOT have mailing product association
         * they were scheduled for exclusivity release because they were either "deleted", removed from a group with a mailing product, or
         * the recipient group that they are assigned to was disassociated with a mailing product and
         * they are not assigned to any other group in the account with a mailing product.
         */
        const hasProductAssociation = await hasMailingProductAssociation({
          recipientRef,
          recipientGroupCollectionRef,
        });

        const shouldRelease = !hasProductAssociation;

        if (shouldRelease) {
          await releaseExclusivity({
            accountId,
            action,
            sourceMailingAddressRef,
            recipientRef,
          });
        }
        await doc.ref.delete();
      });

      await Promise.all(releasePromises);
      lastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
    } while (lastDoc);
  } catch (error) {
    logger.error("Problem releasing exclusivity", error);
  }
});
