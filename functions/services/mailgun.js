const { Firestore } = require("firebase-admin/firestore");
const { defineString, HttpsError } = require("firebase-functions/params");
const axios = require("axios");
const axiosRetry = require("axios-retry").default;
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");

const { logger } = require("firebase-functions");

/**
 * Validates an email address using an mailgun API and updates the Firestore database with the validation status.
 * This function includes retry logic for network or server errors.
 *
 * @async
 * @param {Object} params - The parameters for email validation.
 * @param {string} params.email - The email address to be validated.
 * @param {Object} params.db - Firestore database reference.
 * @param {Object} [params.context] - Context for validation, includes account, recipient, and email address IDs.
 * @param {string} [params.context.accountId=""] - The ID of the account.
 * @param {string} [params.context.recipientId=""] - The ID of the recipient.
 * @param {string} [params.context.emailAddressId=""] - The ID of the email address.
 *
 * @returns {Promise<Object|undefined>} The email validation response data if the request succeeds, otherwise undefined.
 *
 * @throws {Error} If the validation fails and logging retry attempts also fail.
 *
 * @property {string} address - The email address being verified.
 * @property {string} [did_you_mean] - (Optional) A suggestion for potential domain typos, null if none.
 * @property {Object} engagement - Contains engagement information:
 *   @property {boolean} engagement.is_bot - Indicates if the email is associated with a bot.
 *   @property {boolean} engagement.engaged - Indicates if there is engagement with the email.
 *   @property {string} engagement.engagement - Describes the type of engagement with the email.
 * @property {boolean} is_disposable_address - True if the email domain is disposable.
 * @property {boolean} is_role_address - True if the email address is a role-based address (e.g., 'admin', 'sales').
 * @property {Array<string>} reason - List of reasons for validation failure, if any.
 * @property {string} result - The validation result, one of 'deliverable', 'undeliverable', 'do_not_send', 'catch_all', or 'unknown'.
 * @property {string} risk - The risk assessment, one of 'high', 'medium', 'low', or 'unknown'.
 * @property {string} [root_address] - (Optional) The root email address if the provided email is an alias.
 */

async function validateEmailAddress({
  email,
  db,
  context = { accountId: "", recipientId: "", emailAddressId: "" },
}) {
  const { accountId, recipientId, emailAddressId } = context ?? {};

  const emailValidationApiKey = defineString(
    "RM_TTIAN_EMAIL_VALIDATION_API_KEY"
  );

  const emailValidationUsername = defineString(
    "RM_TITAN_EMAIL_VALIDATION_USERNAME"
  );

  const emailValidationURI = defineString("RM_TITAN_EMAIL_VALIDATION_URI");

  const url = emailValidationURI.value();

  // Collection References

  const emailAddressRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients)
    .doc(recipientId)
    .collection(subCollectionNames.contacts.emailAddresses)
    .doc(emailAddressId);

  // Email Address Validation Reference
  const emailAddressValidationFailuresRef = db
    .collection(collectionNames.logs.emailAddressValidationFailures)
    .doc(`${accountId}-${recipientId}-${emailAddressId}`);

  // updates the recipient emailAddress subcollection with the current status
  const logRetryAttempt = (retryCount, error) => {
    return new Promise((resolve, reject) => {
      emailAddressRef
        .update({
          status: "validation_failed",
          errorMessage: error?.message,
          errorCode: error?.code,
        })
        .then(() => {
          resolve();
        })
        .catch((updateError) => {
          reject(updateError);
        });
    });
  };

  const maxRetrys = 1;
  axiosRetry(axios, {
    retries: maxRetrys,
    retryDelay: (retryCount) => {
      return retryCount * 1000;
    },
    retryCondition: (error) => {
      // Retry only if the request failed due to a network error or 5xx server errors
      return axiosRetry.isNetworkOrIdempotentRequestError(error);
    },
    onRetry: async (retryCount, error) => {
      try {
        await logRetryAttempt(retryCount, error);
      } catch (loggingError) {
        logger.error("Error logging retry attempt:", loggingError);
      }
    },
  });

  try {
    const response = await axios.get(url, {
      auth: {
        username: emailValidationUsername.value(),
        password: emailValidationApiKey.value(),
      },
      params: { address: email },
    });

    console.log("Axios Data:", response);
    const {
      address,
      reason,
      result,
      risk,
      is_disposable_address,
      is_role_address,
      did_you_mean,
      engagement,
      root_address,
    } = response.data ?? {};
    return response.data;
  } catch (error) {
    console.error("Failed to validate the address on the last retry:", error);
    await emailAddressValidationFailuresRef.set({
      // ...context,
      email,
      error: JSON.stringify(error),
      errorMessage: error?.message,
      requestId: error?.request_id,
    });
  }
}

async function validateEmailAddressWithClass(emailClass) {
  const { email } = emailClass?.record ?? {};
  console.log("EMAIL: ", email)

  const emailValidationApiKey = defineString(
    "RM_TTIAN_EMAIL_VALIDATION_API_KEY"
  );

  const emailValidationUsername = defineString(
    "RM_TITAN_EMAIL_VALIDATION_USERNAME"
  );

  const emailValidationURI = defineString("RM_TITAN_EMAIL_VALIDATION_URI");

  const url = emailValidationURI.value();

  try {
    const response = await axios.get(url, {
      auth: {
        username: emailValidationUsername.value(),
        password: emailValidationApiKey.value(),
      },
      params: { address: email, mailbox_verification: false },
    });
    const result = {...response.data}
    const validationObj = {
      engaging: result?.engagement?.engaging,
      behavior: result?.engagement?.behavior,
      is_bot: result?.engagement?.is_bot,
      is_disposable_address: result?.is_disposable_address,
      is_role_address: result?.is_role_address,
      result: result.result,
      risk: result.risk,
      reason: result.reason
    }
    console.log(validationObj)

    emailClass.record.validationResults = validationObj
    return emailClass;
  } catch (error) {
    console.error("Failed to validate the address on the last retry:", error);
    await emailClass.refs.emailAddressValidationFailuresRef.set({
      // ...context,
      email: emailClass?.record?.email,
      error: JSON.stringify(error),
      errorMessage: error?.message,
      requestId: error?.request_id,
    });
  }
}

module.exports.validateEmailAddress = validateEmailAddress;
module.exports.validateEmailAddressWithClass = validateEmailAddressWithClass;
