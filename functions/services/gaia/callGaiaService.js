const axios = require("axios");
const { defineString, defineSecret } = require("firebase-functions/params");
const { logger } = require("firebase-functions");
const { generateJwt } = require("../../utils/generateJwt");
const JWT_EXPIRY_SECONDS = 3200;

/**
 * Call a Gaia service endpoint.
 *
 * @param {object} options
 * @param {string} options.path - The path for the Gaia endpoint (e.g. 'syncAccountPlanInfo').
 * @param {string} [options.method='POST'] - The HTTP method ('GET', 'POST', 'PUT', 'DELETE', etc.).
 * @param {object} [options.body={}] - Request body for POST/PUT.
 * @param {object} [options.params={}] - Query params to append to the URL.
 * @returns {Promise<any>} The response data from Gaia
 * @throws Will throw an error if the call fails or if status is non-2xx.
 */

const baseUrlString = defineString("RM_GAIA_SERVICE_URL");
const saEmailString = defineString("RM_GAIA_SERVICE_ACCOUNT_EMAIL");
const audienceString = defineString("RM_GAIA_AUDIENCE_CLAIM");
const apiKeyString = defineString("RM_GAIA_GCP_API_KEY");
const privateKeyString = defineString("RM_GAIA_GCP_PRIVATE_KEY");

exports.callGaiaService = async function callGaiaService({
  path,
  method = "POST",
  body = {},
  params = {},
}) {
  if (!path || typeof path !== "string") {
    throw new Error(
      "callGaiaService: 'path' is required and must be a string."
    );
  }

  const baseUrl = baseUrlString.value();
  const saEmail = saEmailString.value();
  const audience = audienceString.value();
  const apiKey = apiKeyString.value();
  const privateKey = privateKeyString.value();

  const signedJwt = generateJwt(
    saEmail,
    audience,
    JWT_EXPIRY_SECONDS,
    privateKey
  );
  const url = `${baseUrl}${path}`;

  try {
    const response = await axios({
      url,
      method,
      ...(method !== "GET" && body ? { data: body } : {}),
      params: {
        key: apiKey,
        ...params,
      },
      headers: {
        Authorization: `Bearer ${signedJwt}`,
        "Content-Type": "application/json",
      },
      // timeout: 10000,
    });

    if (response.status < 200 || response.status >= 300) {
      logger.error(`Gaia response error (${response.status}):`, response.data);
      throw new Error(`Gaia service call failed: ${response.statusText}`);
    }

    return response.data;
  } catch (error) {
    logger.error("Error calling Gaia service:", {
      url,
      method,
      error: error.message,
      body,
      params,
      response: error.response?.data,
      headers: error.response?.headers,
    });
    throw error;
  }
};
