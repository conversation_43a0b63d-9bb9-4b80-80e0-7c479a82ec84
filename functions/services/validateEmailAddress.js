const { Firestore } = require("firebase-admin/firestore");
const { defineString, HttpsError } = require("firebase-functions/params");
// https://cloud.google.com/nodejs/docs/reference/addressvalidation/latest
const axios = require("axios");
const axiosRetry = require("axios-retry").default;
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");

/**
 * Validates a email address using ipqualityscore service.
 * Writes failures to emailAddresses subcollections mailing address doc and
 * Writes failurs to EmailAddressValidationFailures failures collection.
 * https://www.ipqualityscore.com/documentation/email-validation-api/overview
 * @property {Object} result
 * @property {boolean} result.valid Does this email address appear valid?	boolean
 * @property {bolean} result.disposable	Is this email suspected of belonging to a temporary or disposable mail service? Usually associated with fraudsters and scammers.
 * @property {boolean} result.timed_out Did the email verification connection to the mail service provider timeout during the verification? If so, we recommend increasing the "timeout" variable above the default 7 second value so more time can be spent during the mailbox verification request to mail servers. Lookups that timeout with a "valid" result as false are most likely false and should be not be trusted.
 * @property {string} result.deliverability How likely is this email to be delivered to the user and land in their mailbox. Values can be "high", "medium", or "low".
 * @property {boolean} result.catch_all Is this email likely to be a "catch all" where the mail server verifies all emails tested against it as valid? It is difficult to determine if the address is truly valid in these scenarios, since the email's server will not confirm the account's status.
 * @property {boolean} result.leaked 	Was this email address associated with a recent database leak from a third party? Leaked accounts pose a risk as they may have become compromised during a database breach.
 * @property {boolean} result.suspect This value indicates if the mail server is currently replying with a temporary mail server error or if the email verification system is unable to verify the email address due to a broken SMTP handshake. This status will also be true for "catch all" email addresses as defined below. If this value is true, then we suspect the "valid" result may be tainted and there is not a guarantee that the email address is truly valid. This status is rarely true for popular mailbox providers and typically only returns as true for a small percentage of business mail servers.
 * @property {integer} result.smtp_score Validity score of email server's SMTP setup. Range: "-1" - "3". Scores above "-1" can be associated with a valid email. -1 = invalid email address 0 = mail server exists, but is rejecting all mail 1 = mail server exists, but is showing a temporary error 2 = mail server exists, but accepts all email 3 = mail server exists and has verified the email address
 * @property {integer} result.overall_score Overall email validity score. Range: "0" - "4". Scores above "1" can be associated with a valid email. 0 = invalid email address 1 = dns valid, unreachable mail server 2 = dns valid, temporary mail rejection error 3 = dns valid, accepts all mail 4 = dns valid, verified email exists
 *
 * @property {string} result.spam_trap_score Intelligent confidence level of the email address being an active SPAM trap. Values can be "high", "medium", "low", or "none". We recommend scrubbing emails with a "high" status, typically for any promotional mailings. This data is meant to provide a more accurate result for the "frequent_complainer" and "honeypot" data points, which collect data from spam complaints, spam traps, and similar techniques.
 */

/**
 * Analyzing Email Verification Results
 *
 * Treat email addresses as valid when:
 * For Email Marketing & Deliverability Analysis - "valid" is true, "disposable" is false, AND "spam_trap_score" is NOT high
 * For stricter email marketing deliverability also scrub records with "spam_trap_score" as "medium" and for even stricter filtering, scrub emails with "frequent_complainer" as true.
 * For User Quality Analysis - "valid" is true, "disposable" is false, AND "fraud_score" is less than 90
 * Fraud Scores can also be a good indicator for user quality, where scores 80+ are suspicious and 90+ are risky. To prevent against account takeover and credential stuffing, emails with "leaked" as true indicate user data recently compromised on the dark web. The "First Seen" date is also useful in determining the email's age or creation date.
 */

async function validateEmailAddress({
  email,
  db,
  context = { accountId: "", recipientId: "", emailAddressId: "" },
}) {
  const { accountId, recipientId, emailAddressId } = context ?? {};

  const emailValidationApiKey = defineString(
    "RM_TTIAN_EMAIL_VALIDATION_API_KEY"
  );

  const emailValidationUsername = defineString(
    "RM_TITAN_EMAIL_VALIDATION_USERNAME"
  );

  const emailValidationURI = defineString("RM_TITAN_EMAIL_VALIDATION_URI");

  const url = `${emailValidationURI.value()}${emailValidationApiKey.value()}/${email}`;

  // Collection References
  const emailAddressRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients)
    .doc(recipientId)
    .collection(subCollectionNames.contacts.emailAddresses)
    .doc(emailAddressId);

  // Email Address Validation Reference
  const emailAddressValidationFailuresRef = db
    .collection(collectionNames.logs.emailAddressValidationFailures)
    .doc(`${accountId}-${recipientId}-${emailAddressId}`);

  // updates the recipient emailAddress subcollection with the current status
  const logRetryAttempt = (retryCount, error) => {
    return new Promise((resolve, reject) => {
      emailAddressRef
        .update({
          status: "validation_failed",
          errorMessage: error?.message,
          errorCode: error?.code,
        })
        .then(() => {
          resolve();
        })
        .catch((updateError) => {
          reject(updateError);
        });
    });
  };

  const maxRetrys = 1;
  axiosRetry(axios, {
    retries: maxRetrys,
    retryDelay: (retryCount) => {
      return retryCount * 1000;
    },
    retryCondition: (error) => {
      // Retry only if the request failed due to a network error or 5xx server errors
      return axiosRetry.isNetworkOrIdempotentRequestError(error);
    },
    onRetry: async (retryCount, error) => {
      try {
        await logRetryAttempt(retryCount, error);
      } catch (loggingError) {
        console.error("Error logging retry attempt:", loggingError);
      }
    },
  });

  try {
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error("Failed to validate the address on the last retry:", error);
    await emailAddressValidationFailuresRef.set({
      ...context,
      email,
      error: JSON.stringify(error),
      errorMessage: error?.message,
      requestId: error?.request_id,
    });
  }
}

module.exports.validateEmailAddress = validateEmailAddress;
