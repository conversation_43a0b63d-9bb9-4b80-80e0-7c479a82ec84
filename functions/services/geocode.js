const { Client } = require("@googlemaps/google-maps-services-js");
const axios = require("axios");
const axiosRetry = require("axios-retry").default;
const { defineString, HttpsError } = require("firebase-functions/params");

/**
 *
 * @param {*} address
 * @returns
 * The "status" field within the Geocoding response object contains the status of the request,
 * and may contain debugging information to help you track down why geocoding is not working. The "status" field may contain the following values:
 *
 * "OK" indicates that no errors occurred; the address was successfully parsed and at least one geocode was returned.
 * "ZERO_RESULTS" indicates that the geocode was successful but returned no results. This may occur if the geocoder was passed a non-existent address.
 * "OVER_DAILY_LIMIT" indicates any of the following: The API key is missing or invalid, Billing has not been enabled on account, A self-imposed usage cap has been exceeded, or The provided method of payment is no longer valid (for example, a credit card has expired).
 * "OVER_QUERY_LIMIT" indicates that you are over your quota.
 * "REQUEST_DENIED" indicates that your request was denied, generally because of lack of an invalid key parameter.
 * "INVALID_REQUEST" generally indicates that the query (address, components or latlng) is missing.
 * "UNKNOWN_ERROR" indicates that the request could not be processed due to a server error. The request may succeed if you try again.
 * reference: https://developers.google.com/maps/documentation/geocoding/requests-geocoding#GeocodingResponses
 */

const raxConfig = {
  retry: 3,
  retryDelay: (retryCount) => {
    return retryCount * 100; // time interval between retries
  },
  backoffType: "exponential",
  httpMethodsToRetry: ["GET"], // HTTP methods to retry
  statusCodesToRetry: [
    [100, 199],
    [429, 429],
    [500, 599],
  ], // Status codes to retry
  onRetryAttempt: (err) => {
    const cfg = axiosRetry.getConfig(err);
  },

  retryCondition: (error) => {
    return error.response && error.response.data.status === "UNKNOWN_ERROR";
  },
};

axiosRetry(axios, raxConfig);

// Retrieve the token from environment variables
const geocodingApiKey = defineString("RM_TITAN_GEOCODING_API_KEY");

async function geocodeAddress(address) {
  const args = {
    params: {
      key: geocodingApiKey.value(),
      address,
    },
  };
  const client = new Client({ axiosInstance: axios });
  try {
    const response = await client.geocode(args);
    if (response.data.status !== "OK") {
      throw new Error(`Geocoding error: ${response.data.status}`);
    }
    return response.data.results;
  } catch (error) {
    console.error(error);
  }
}

exports.geocodeAddress = geocodeAddress;
