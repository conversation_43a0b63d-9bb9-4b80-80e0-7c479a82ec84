const { Firestore } = require("firebase-admin/firestore");
// https://cloud.google.com/nodejs/docs/reference/addressvalidation/latest
const { AddressValidationClient, ValidationResult } =
  require("@googlemaps/addressvalidation").v1;
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { backOff } = require("exponential-backoff");
const { logger } = require("firebase-functions");

// Google API Errors
// https://cloud.google.com/apis/design/errors
// Canonical error codes: https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto

/**
 * Validates a mailing address using the Google Address Validation API.
 * Writes failures to user entered mailing address doc and
 * Writes failurs to MailingAddressValidation failures collection.
 * @param {Object} params - The parameters for the address validation.
 * @param {(string | Array)} params.addressLines - The lines of the address to validate.
 * @param {string} params.regionCode - The region code (e.g., "US") for the address.
 * @param {Firestore} params.db - The Firestore database instance.
 * @param {Object} [params.context] - The context information.
 * @param {string} [params.context.accountId] - The account ID related to the address.
 * @param {string} [params.context.recipientId] - The recipient ID related to the address.
 * @param {string} [params.context.userEnteredMailingAddressId] - The ID of the user-entered mailing address.
 *
 * @returns {Promise<ValidationResult>} The validation result containing the validated address and validation status.
 * @typedef {Array<Object>} ValidationResult
 
* @property {Object} verdict - The validation verdict including granularity and other details.
 * @property {string} verdict.inputGranularity - The granularity of the input address (enum).
 * @property {string} verdict.validationGranularity - The granularity level that the API can fully validate the address to. For example, an validationGranularity of PREMISE indicates all address components at the level of PREMISE or more coarse can be validated.
 * @property {string} verdict.geocodeGranularity - Information about the granularity of the geocode (enum).
 * @property {boolean} verdict.addressComplete - The address is considered complete if there are no unresolved tokens, no unexpected or missing address components. If unset, indicates that the value is false. See missingComponentTypes, unresolvedTokens or unexpected fields for more details.
 * @property {boolean} verdict.hasUnconfirmedComponents - At least one address component cannot be categorized or validated, see google.maps.addressvalidation.v1.Address.address_components for details.
 * @property {boolean} verdict.hasInferredComponents - At least one address component was inferred (added) that wasn't in the input, see google.maps.addressvalidation.v1.Address.address_components for details.
 * @property {boolean} verdict.hasReplacedComponents - At least one address component was replaced, see google.maps.addressvalidation.v1.Address.address_components for details.
 *
 * @property {Object} address - The validated address details.
 * @property {string} address.formattedAddress - The post-processed address, formatted as a single-line address following the address formatting rules of the region where the address is located.
 * @property {Array<Object>} address.addressComponents - Unordered list. The individual address components of the formatted and corrected address, along with validation information. This provides information on the validation status of the individual components. Address components are not ordered in a particular way. Do not make any assumptions on the ordering of the address components in the list.
 * @property {Array<String>} address.missingComponentTypes - The types of components that were expected to be present in a correctly formatted mailing address but were not found in the input AND could not be inferred. Components of this type are not present in formattedAddress, postalAddress, or addressComponents. An example might be ['street_number', 'route'] for an input like "Boulder, Colorado, 80301, USA". The list of possible types can be found here: https://developers.google.com/maps/documentation/geocoding/requests-geocoding#Types
 * @property {Array<String>} address.unresolvedTokens - Any tokens in the input that could not be resolved. This might be an input that was not recognized as a valid part of an address (for example in an input like "123235253253 Main St, San Francisco, CA, 94105", the unresolved tokens may look like ["123235253253"] since that does not look like a valid street number.
 * @property {Object} address.postalAddress - The region code of the validated address.
 * 
 * @property {Object} geocode - The geocode information for the validated address.
 * @property {string} geocode.latitude - The latitude of the validated address.
 * @property {string} geocode.longitude - The longitude of the validated address.
 * 
 * @property {Object} metadata - Other information relevant to deliverability. metadata is not guaranteed to be fully populated for every address sent to the Address Validation API.
 * @property {string} metadata.business - Indicates that this is the address of a business. If unset, indicates that the value is unknown.
 * @property {string} metadata.poBox - Indicates that the address of a PO box. If unset, indicates that the value is unknown.
 * @property {string} metadata.residential - Indicates that this is the address of a residence. If unset, indicates that the value is unknown.
 * 
 * @property {Object} uspsData
 * @property {Object} uspsData.standardizedAddress - The standardized address as per USPS standards.
 * @property {string} uspsData.standardizedAddress.firstAddressLine - The first line of the standardized address.
 * @property {string} uspsData.standardizedAddress.firm - The firm or business name, if applicable.
 * @property {string} uspsData.standardizedAddress.secondAddressLine - The second line of the standardized address, if applicable.
 * @property {string} uspsData.standardizedAddress.urbanization - The urbanization name, if applicable (used in Puerto Rico).
 * @property {string} uspsData.standardizedAddress.cityStateZipAddressLine - The combined city, state, and ZIP code line.
 * @property {string} uspsData.standardizedAddress.city - The city of the standardized address.
 * @property {string} uspsData.standardizedAddress.state - The state of the standardized address.
 * @property {string} uspsData.standardizedAddress.zipCode - The ZIP code of the standardized address.
 * @property {string} uspsData.standardizedAddress.zipCodeExtension - The ZIP code extension (e.g., the four extra digits).
 * @property {string} uspsData.deliveryPointCode - The delivery point code.
 * @property {string} uspsData.deliveryPointCheckDigit - The delivery point check digit.
 * @property {string} uspsData.dpvConfirmation - The DPV confirmation status.
 * @property {string} uspsData.dpvFootnote - The DPV footnote.
 * @property {string} uspsData.dpvCmra - The DPV CMRA (Commercial Mail Receiving Agency) status.
 * @property {string} uspsData.dpvVacant - The DPV vacant status.
 * @property {string} uspsData.dpvNoStat - The DPV no stat status.
 * @property {number} uspsData.dpvNoStatReasonCode - The DPV no stat reason code.
 * @property {string} uspsData.dpvDrop - The DPV drop status.
 * @property {string} uspsData.dpvThrowback - The DPV throwback status.
 * @property {string} uspsData.dpvNonDeliveryDays - The DPV non-delivery days.
 * @property {number} uspsData.dpvNonDeliveryDaysValues - The DPV non-delivery days values.
 * @property {string} uspsData.dpvNoSecureLocation - The DPV no secure location status.
 * @property {string} uspsData.dpvPbsa - The DPV PBSA (Post Box Service Address) status.
 * @property {string} uspsData.dpvDoorNotAccessible - The DPV door not accessible status.
 * @property {string} uspsData.dpvEnhancedDeliveryCode - The DPV enhanced delivery code.
 * @property {string} uspsData.carrierRoute - The carrier route.
 * @property {string} uspsData.carrierRouteIndicator - The carrier route indicator.
 * @property {boolean} uspsData.ewsNoMatch - Whether the address is a match in the EWS (Early Warning System).
 * @property {string} uspsData.postOfficeCity - The post office city.
 * @property {string} uspsData.postOfficeState - The post office state.
 * @property {string} uspsData.abbreviatedCity - The abbreviated city name.
 * @property {string} uspsData.fipsCountyCode - The FIPS county code.
 * @property {string} uspsData.county - The county name.
 * @property {string} uspsData.elotNumber - The ELOT (Enhanced Line of Travel) number.
 * @property {string} uspsData.elotFlag - The ELOT flag.
 * @property {string} uspsData.lacsLinkReturnCode - The LACSLink return code.
 * @property {string} uspsData.lacsLinkIndicator - The LACSLink indicator.
 * @property {boolean} uspsData.poBoxOnlyPostalCode - Indicates if the postal code is PO Box only.
 * @property {string} uspsData.suitelinkFootnote - The SuiteLink footnote.
 * @property {string} uspsData.pmbDesignator - The PMB (Private Mailbox) designator.
 * @property {string} uspsData.pmbNumber - The PMB number.
 * @property {string} uspsData.addressRecordType - The address record type.
 * @property {boolean} uspsData.defaultAddress - Indicates if this is the default address.
 * @property {string} uspsData.errorMessage - Error message if there was an issue processing the address.
 * @property {boolean} uspsData.cassProcessed - Indicates if the address was processed through CASS (Coding Accuracy Support System).
 */
async function validateMailingAddressWithGoogle({
  addressLines = [],
  regionCode = "US",
  db,
  context = { accountId: "", recipientId: "", userEnteredMailingAddressId: "" },
}) {
  const { accountId, recipientId, userEnteredMailingAddressId } = context ?? {};

  // Create an instance of the AddressValidationClient
  const addressvalidationClient = new AddressValidationClient();

  const addressValidationRequest = {
    address: {
      regionCode,
      addressLines,
    },
  };

  const userEnteredMailingAddressRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients)
    .doc(recipientId)
    .collection(subCollectionNames.contacts.userEnteredMailingAddress)
    .doc(userEnteredMailingAddressId);

  const mailingAddressValidationFailuresRef = db
    .collection(collectionNames.logs.mailingAddressValidationFailures)
    .doc(`${accountId}-${recipientId}-${userEnteredMailingAddressId}`);

  const backoffOptions = {
    delayFirstAttempt: false,
    numOfAttempts: 10, // this is default
    timeMultiple: 2, // retry after 2^n seconds. this is default
    retry: async (err, attemptNumber) => {
      logger.error(`Retry attempt ${attemptNumber}: ${err}`);
      // update status of user entered mailing address document
      await userEnteredMailingAddressRef.update({
        status: "validation_failed",
        errorMessage: err?.message,
        errorCode: err?.code,
        retryAttempts: attemptNumber,
      });
      // write to mailingAddressValidationFailures collection
      await mailingAddressValidationFailuresRef.set({
        ...context,
        addressLines,
        error: JSON.stringify(err),
        errorMessage: err?.message,
        errorCode: err?.code,
        errorStatus: err?.status,
        retryAttempts: attemptNumber,
      });
      // INVALID_ARGUMENT

      if (
        err?.status === "PERMISSION_DENIED" ||
        err?.status === "RESOURCE_EXHAUSTED" ||
        err?.status === "UNAUTHENTICATED" ||
        err.status === "INVALID_ARGUMENT"
      )
        return false; // returning false ends retry attempts

      return true;
    },
  };

  const response = await backOff(
    () => addressvalidationClient.validateAddress(addressValidationRequest),
    backoffOptions
  );

  return response;
}

module.exports.validateMailingAddressWithGoogle =
  validateMailingAddressWithGoogle;
