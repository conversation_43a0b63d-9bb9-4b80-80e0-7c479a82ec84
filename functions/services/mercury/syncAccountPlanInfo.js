const axios = require("axios");
const { defineString } = require("firebase-functions/params");
const { logger } = require("firebase-functions");
const { generateJwt } = require("../../utils/generateJwt");

exports.syncAccountPlanInfo = async function syncAccountPlanInfo(
  accountPlanInfo
) {
  const body = {
    ...accountPlanInfo,
  };

  const baseUrl = defineString("RM_MERCURY_SERVICE_URL").value();
  const url = `${baseUrl}syncAccountPlanInfo`;
  const saEmail = defineString("RM_MERCURY_SERVICE_ACCOUNT_EMAIL").value();
  const audience = defineString("RM_MERCURY_AUDIENCE_CLAIM").value();
  const privateKey = defineString("RM_MERCURY_GCP_PRIVATE_KEY").value();

  const signedJwt = generateJwt(saEmail, audience, 3200, privateKey);

  const params = {
    key: defineString("RM_MERCURY_GCP_API_KEY").value(),
  };

  const headers = {
    Authorization: `Bearer ${signedJwt}`,
  };

  try {
    const res = await axios.post(url, body, {
      params,
      headers,
    });
    return;
  } catch (error) {
    logger.log("Error sync accountPlan with mercury", error, body);
    return;
  }
};
