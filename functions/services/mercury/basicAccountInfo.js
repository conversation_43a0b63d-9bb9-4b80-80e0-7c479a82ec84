const axios = require("axios");
const { defineString } = require("firebase-functions/params");
const { logger } = require("firebase-functions");
const { generateJwt } = require("../../utils/generateJwt");
const { isString } = require("lodash");

exports.updateBasicAccountInfo = async function updateBasicAccountInfo(
  accountInfo
) {
  const {
    createdAt,
    lastUpdated,
    updatedAt,
    ...titanAccountInfo
  } = accountInfo;

  const currentDateTimeString = new Date().toISOString();

  const createdAtDateTimeString =
    isString(createdAt) && !isNaN(Date.parse(createdAt))
      ? createdAt.toDate().toISOString()
      : currentDateTimeString;

  const lastUpdatedDateTimeString =
    isString(lastUpdated) && !isNaN(Date.parse(lastUpdated))
      ? lastUpdated.toDate().toISOString()
      : currentDateTimeString;

  const updatedAtDateTimeString =
    isString(updatedAt) && !isNaN(Date.parse(updatedAt))
      ? updatedAt.toDate().toISOString()
      : currentDateTimeString;

  const body = {
    ...titanAccountInfo,
    lastUpdated: lastUpdatedDateTimeString,
    createdAt: createdAtDateTimeString,
    updatedAt: updatedAtDateTimeString,
  };

  const baseUrl = defineString("RM_MERCURY_SERVICE_URL").value();
  const url = `${baseUrl}updateBasicAccountInfo`;
  const saEmail = defineString("RM_MERCURY_SERVICE_ACCOUNT_EMAIL").value();
  const audience = defineString("RM_MERCURY_AUDIENCE_CLAIM").value();
  const privateKey = defineString("RM_MERCURY_GCP_PRIVATE_KEY").value();

  const signedJwt = generateJwt(saEmail, audience, 3200, privateKey);

  const params = {
    key: defineString("RM_MERCURY_GCP_API_KEY").value(),
  };

  const headers = {
    Authorization: `Bearer ${signedJwt}`,
  };

  try {
    const res = await axios.post(url, body, {
      params,
      headers,
    });
    return;
  } catch (error) {
    logger.log("Error fetching Incoming Message Logs", error);
    return;
  }
};
