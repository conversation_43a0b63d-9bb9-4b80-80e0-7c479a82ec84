const axios = require("axios");
const { defineString } = require("firebase-functions/params");
const { logger } = require("firebase-functions");
const { generateJwt } = require("../../utils/generateJwt");
const JWT_EXPIRY_SECONDS = 3200;

/**
 * Call a Mercury service endpoint.
 *
 * @param {object} options
 * @param {string} options.path - The path for the Mercury endpoint (e.g. 'syncAccountPlanInfo').
 * @param {string} [options.method='POST'] - The HTTP method ('GET', 'POST', 'PUT', 'DELETE', etc.).
 * @param {object} [options.body={}] - Request body for POST/PUT.
 * @param {object} [options.params={}] - Query params to append to the URL.
 * @returns {Promise<any>} The response data from Mercury
 * @throws Will throw an error if the call fails or if status is non-2xx.
 */
exports.callMercuryService = async function callMercuryService({
  path,
  method = "POST",
  body = {},
  params = {},
}) {
  if (!path || typeof path !== "string") {
    throw new Error(
      "callMercuryService: 'path' is required and must be a string."
    );
  }

  const baseUrl = defineString("RM_MERCURY_SERVICE_URL").value();
  const saEmail = defineString("RM_MERCURY_SERVICE_ACCOUNT_EMAIL").value();
  const audience = defineString("RM_MERCURY_AUDIENCE_CLAIM").value();
  const apiKey = defineString("RM_MERCURY_GCP_API_KEY").value();
  const privateKey = defineString("RM_MERCURY_GCP_PRIVATE_KEY").value();

  const signedJwt = generateJwt(
    saEmail,
    audience,
    JWT_EXPIRY_SECONDS,
    privateKey
  );
  const url = `${baseUrl}${path}`;

  try {
    const response = await axios({
      url,
      method,
      ...(method !== "GET" && body ? { data: body } : {}),
      params: {
        key: apiKey,
        ...params,
      },
      headers: {
        Authorization: `Bearer ${signedJwt}`,
        "Content-Type": "application/json",
      },
      // timeout: 10000,
    });

    if (response.status < 200 || response.status >= 300) {
      logger.error(
        `Mercury response error (${response.status}):`,
        response.data
      );
      throw new Error(`Mercury service call failed: ${response.statusText}`);
    }

    return response.data;
  } catch (error) {
    logger.error("Error calling Mercury service:", {
      url,
      method,
      error: error.message,
      body,
      params,
      response: error.response?.data,
      headers: error.response?.headers,
    });
    throw error;
  }
};
