const { isArray, isEmpty } = require("lodash");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { handleAddressStatusUpdates } = require("./handleAddressStatusUpdates");

async function processGoogleValidationResults({
  db,
  validationResults = [],
  context = {},
}) {
  const result = validationResults?.[0]?.result;

  // Address validation context
  const { accountId, recipientId, userEnteredMailingAddressId, addressLines } =
    context ?? {};

  // Collection references
  const userEnteredMailingAddressRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients)
    .doc(recipientId)
    .collection(subCollectionNames.contacts.userEnteredMailingAddress)
    .doc(userEnteredMailingAddressId);

  const mailingAddressValidationFailuresRef = db
    .collection(collectionNames.logs.mailingAddressValidationFailures)
    .doc(`${accountId}-${recipientId}-${userEnteredMailingAddressId}`);

  const recipientRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients)
    .doc(recipientId);

  // if there aren't any results
  // write to MailingAddressValidationFailures collection and userEnteredMailingAddress
  // then stop execution
  if (!isArray(validationResults) || isEmpty(validationResults)) {
    // update status of user entered mailing address document
    await userEnteredMailingAddressRef.update({
      status: "no_results",
      error: "function processGoogleValidationResults(): No results to process",
      errorMessage: "No results to process",
      errorCode: "No results",
    });

    // write to mailingAddressValidationFailures collection
    await mailingAddressValidationFailuresRef.set({
      ...context,
      status: "no_results",
      error: "function processGoogleValidationResults(): No results to process",
      errorMessage: "No results to process",
      errorCode: "No results",
    });

    return;
  }

  const { verdict, address, geocode, metadata, uspsData } = result ?? {};

  /**
   * The verdict returns the addressComplete property as a signal for a high-quality address,
   * which means specifically that it has no missing, unresolved, or unexpected components
   */
  const {
    inputGranularity,
    validationGranularity,
    geocodeGranularity,
    addressComplete,
    hasUnconfirmedComponents,
    hasInferredComponents,
    hasReplacedComponents,
  } = verdict ?? {};

  const {
    formattedAddress,
    postalAddress,
    addressComponents,
    missingComponentTypes,
    unconfirmedComponentTypes,
    unresolvedTokens,
  } = address ?? {};

  /**
   * Address Complete?
   * Address is considered complete considered complete if there are no unresolved tokens, no unexpected or missing address components.
   * If the addressComplete value is unset (undefined), then addressComplete is false.
   *
   * An Address may be complete and require customer confirmation because of inferred or replaced address components.
   */
  if (addressComplete) {
    /**
     * Check for inferred or replaced components
     * If present Customer will need to confirm the formattedAddress provided.
     */
    if (
      hasUnconfirmedComponents ||
      // hasInferredComponents || // consider not checking inferred components may provide unnecessary confirmations from users.
      hasReplacedComponents
    ) {
      // UNCONFIRMED_AND_SUSPICIOUS
      const unconfirmedAndSuspicious = addressComponents?.filter(
        (component) =>
          component.confirmationLevel === "UNCONFIRMED_AND_SUSPICIOUS"
      );

      // UNCONFIRMED_BUT_PLAUSIBLE
      const unconfirmedButPlausible = addressComponents?.filter(
        (component) =>
          component.confirmationLevel === "UNCONFIRMED_BUT_PLAUSIBLE"
      );

      if (
        unconfirmedAndSuspicious.length > 0 ||
        unconfirmedButPlausible.length > 0
      ) {
        await handleAddressStatusUpdates({
          userEnteredMailingAddressRef,
          recipientRef,
          status: "review",
          validationResult: result,
        });

        // stop execution here
        return;
      }
    }

    /**
     * Check validationGranularity field for "OTHER" e.g. Not deliverable
     * OTHER - All other granularities, which are bucketed together since they are not deliverable.
     *
     */
    if (validationGranularity === "OTHER") {
      await handleAddressStatusUpdates({
        userEnteredMailingAddressRef,
        recipientRef,
        status: "bad_address",
        validationResult: result,
      });
      // stop execution here
      return;
    }

    /**
     * Update userEnteredMailingAddressStatus
     */
    await handleAddressStatusUpdates({
      userEnteredMailingAddressRef,
      recipientRef,
      status: "valid",
      validationResult: result,
    });

    return;
  } else {
    /**
     * Address Incomplete
     * Address is considered incomplete if there are unresolved tokens, unexpected or missing address components.
     * Update the status of user entered mailing address Account/${accountId}/Recipients/{recipientId}/UserEnteredMailingAddress/{userEnteredMailingAddressId}
     */

    /**
     * Check validationGranularity field for "OTHER" e.g. Not deliverable
     * OTHER - All other granularities, which are bucketed together since they are not deliverable.
     *
     */
    if (validationGranularity === "OTHER") {
      await handleAddressStatusUpdates({
        userEnteredMailingAddressRef,
        recipientRef,
        status: "bad_address",
        validationResult: result,
      });
      // stop execution here
      return;
    }

    /**
     * Check for inferred or replaced components
     * If present Customer will need to confirm the formattedAddress provided.
     */
    if (
      hasUnconfirmedComponents ||
      hasInferredComponents ||
      hasReplacedComponents
    ) {
      await handleAddressStatusUpdates({
        userEnteredMailingAddressRef,
        recipientRef,
        status: "review",
        validationResult: result,
      });

      // stop execution here
      return;
    }
  }

  if (
    hasUnconfirmedComponents ||
    hasInferredComponents ||
    hasReplacedComponents
  ) {
    // write to userAddedMailingAddress doc status: "suggestion"
    await handleAddressStatusUpdates({
      userEnteredMailingAddressRef,
      recipientRef,
      status: "suggestion",
      validationResult: result,
    });

    return;
  }
}

module.exports.processGoogleValidationResults = processGoogleValidationResults;
