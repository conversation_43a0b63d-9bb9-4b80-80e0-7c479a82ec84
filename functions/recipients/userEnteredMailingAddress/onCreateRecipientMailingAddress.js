const { onDocumentCreated } = require("firebase-functions/v2/firestore");

const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames.js");

const {
  handleUserEnteredMailingAddress,
} = require("./handleUserEnteredMailingAddress");
const { sleep } = require("../../utils/sleep");
exports.onCreateRecipientMailingAddress = (db) =>
  onDocumentCreated(
    `${collectionNames.account}/{accountId}/${subCollectionNames.contacts.recipients}/{recipientId}/${subCollectionNames.contacts.userEnteredMailingAddress}/{userEnteredMailingAddressId}`,
    async (event) => {
      await handleUserEnteredMailingAddress({ event, db });
    }
  );
