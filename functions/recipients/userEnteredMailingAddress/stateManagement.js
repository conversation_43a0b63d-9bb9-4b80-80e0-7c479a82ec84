const { FieldValue } = require("firebase-admin/firestore");
const {
  mailingAddressStateCases,
} = require("../../constants/mailingAddressStateCases.js");
const {
  deliverabilityStatus,
} = require("../../constants/deliverabilityStatus.js");
const {
  BAD_ADDRESS,
  FAILED,
  INCOMPLETE_ADDRESS,
  UNSUBSCRIBED,
  GOOD_ADDRESS,
  WAITING_LIST,
  WILL_SEND,
  NO_MAILING_ADDRESS,
} = deliverabilityStatus;
const { processingStatus } = require("../../constants/processingStatus");
const {
  VALIDATION,
  MAILING_ADDRESS_SOURCE_CREATION,

  ERROR_VALIDATION,
  ERROR_MAILING_ADDRESS_SOURCE_CREATION,
  ERROR_PROCESS_EXCLUSIVITY,
  ERROR_PROCESS,
  ERROR_CHECK_MAGAZINE_PRODUCT_ASSIGNMENT,
} = processingStatus;

const { logger } = require("firebase-functions");

const {
  SET_INCOMPLETE_ADDRESS,
  CHECK_SOURCE_MAILING_ADDRESS,
  CHECK_SOURCE_MAILING_ADDRESS_ERROR,
  SOURCE_CREATION_SUCCESS,
  SOURCE_CREATION_ERROR,
  VALIDATE_ADDRESS_COMPLETE,
  VALIDATE_ADDRESS_ERROR,
  CHECK_MAGAZINE_PRODUCT_ASSIGNMENT,
  CHECK_MAGAZINE_PRODUCT_ASSIGNMENT_ERROR,
  PROCESS_EXCLUSIVITY_SUCCESS,
  PROCESS_EXCLUSIVITY_ERROR,
  DONE,
} = mailingAddressStateCases;

exports.stateManagement = function stateManagement(state, action) {
  switch (action.type) {
    case SET_INCOMPLETE_ADDRESS:
      return {
        ...state,
        addressDeliverability: {
          ...state.addressDeliverability,
          code: INCOMPLETE_ADDRESS.code,
          message: INCOMPLETE_ADDRESS.message,
          status: INCOMPLETE_ADDRESS.status,
        },
        finalStatus: DONE, // use DONE as the final processing step for incomplete addresses
      };

    case CHECK_SOURCE_MAILING_ADDRESS:
      return {
        ...state,
        addressData: {
          ...state?.addressData,
          ...action?.payload?.addressData,
          processingStatus: CHECK_SOURCE_MAILING_ADDRESS,
        },
        addressDeliverability: {
          ...state?.addressDeliverability,
          ...action?.payload?.addressDeliverability,
        },
        rootRecipient: {
          ...state?.rootRecipient,
          ...(action?.payload?.sourceMailingAddressId && {
            mailingAddresses: [{ id: action.payload.sourceMailingAddressId }],
          }),
        },
        switches: {
          ...state.switches,
          skipAddressValidation: action?.payload?.skipAddressValidation,
          skipMailingAddressSourceCreation:
            action?.payload?.skipMailingAddressSourceCreation,
        },
      };

    case CHECK_SOURCE_MAILING_ADDRESS_ERROR:
      return {
        ...state,
        finalStatus: CHECK_SOURCE_MAILING_ADDRESS_ERROR,
      };

    case SOURCE_CREATION_SUCCESS:
      logger.info("SOURCE_CREATION_SUCCESS state: ", state);
      return {
        ...state,
        addressData: {
          ...state?.addressData,
          ...action?.payload?.addressData,
          processingStatus: SOURCE_CREATION_SUCCESS,
        },
        rootRecipient: {
          ...state?.rootRecipient,
          ...(action?.payload?.sourceMailingAddressId && {
            mailingAddresses: [{ id: action.payload.sourceMailingAddressId }],
          }),
        },
      };

    case SOURCE_CREATION_ERROR:
      return {
        ...state,
        addressData: {
          ...state?.addressData,
          ...action?.payload?.addressData,
          processingStatus: SOURCE_CREATION_ERROR,
        },
        finalStatus: SOURCE_CREATION_ERROR,
      };

    case VALIDATE_ADDRESS_COMPLETE:
      logger.info(
        "VALIDATE_ADDRESS_COMPLETE addressData: ",
        action?.payload?.addressData
      );
      logger.info(
        "VALIDATE_ADDRESS_COMPLETE addressDeliverability: ",
        action?.payload?.addressDeliverability
      );
      logger.info(
        "VALIDATE_ADDRESS_COMPLETE searchTags: ",
        action?.payload?.searchTags
      );
      logger.info(
        "VALIDATE_ADDRESS_COMPLETE overrideDeliverability: ",
        action?.payload?.overrideDeliverability
      );
      console.log("state inside of state management: ", state);
      return {
        ...state,
        addressData: {
          ...state?.addressData,
          ...action?.payload?.addressData,
          processingStatus: VALIDATE_ADDRESS_COMPLETE,
        },
        addressDeliverability: {
          ...state?.addressDeliverability,
          ...action?.payload?.addressDeliverability,
        },
        searchTags: action?.payload?.searchTags,
        overrideDeliverability: action?.payload?.overrideDeliverability,
      };

    case VALIDATE_ADDRESS_ERROR:
      return {
        ...state,
        addressDeliverability: {
          ...state.addressDeliverability,
          code: FAILED.code,
          message: FAILED.message,
          status: FAILED.status,
        },
        finalStatus: VALIDATE_ADDRESS_ERROR,
      };

    case CHECK_MAGAZINE_PRODUCT_ASSIGNMENT:
      logger.info("CHECK_MAGAZINE_PRODUCT_ASSIGNMENT state: ", state);
      return {
        ...state,
        addressDeliverability: {
          ...state.addressDeliverability,
          ...action.payload.addressDeliverability,
        },
        switches: {
          ...state.switches,
          ...action.payload.switches,
        },
      };

    case CHECK_MAGAZINE_PRODUCT_ASSIGNMENT_ERROR:
      return {
        ...state,
        finalStatus: CHECK_MAGAZINE_PRODUCT_ASSIGNMENT_ERROR,
      };

    case PROCESS_EXCLUSIVITY_SUCCESS:
      logger.info("PROCESS_EXCLUSIVITY_SUCCESS state: ", state);
      return {
        ...state,
        addressDeliverability: {
          ...state?.addressDeliverability,
          ...action?.payload?.addressDeliverability,
        },
        overrideDeliverability: {
          ...state?.overrideDeliverability,
          address: action?.payload?.overrideDeliverability?.address,
        },
      };

    case PROCESS_EXCLUSIVITY_ERROR:
      return {
        ...state,
        finalStatus: PROCESS_EXCLUSIVITY_ERROR,
      };

    case "ERROR_PROCESS":
      return {
        ...state,
        finalStatus: ERROR_PROCESS,
      };

    default:
      return state;
  }
};
