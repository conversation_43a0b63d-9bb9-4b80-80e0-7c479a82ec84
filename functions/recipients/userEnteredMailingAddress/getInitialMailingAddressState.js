const {
  deliverabilityStatus,
} = require("../../constants/deliverabilityStatus.js");
const { BAD_ADDRESS, INCOMPLETE_ADDRESS, FAILED } = deliverabilityStatus;
const { processingStatus } = require("../../constants/processingStatus");
const { VALIDATION, MAILING_ADDRESS_SOURCE_CREATION, DONE } = processingStatus;

exports.getInitialMailingAddressState = function getInitialState(
  userEnteredMailingAddressData
) {
  const {
    address1,
    address2,
    city,
    state,
    postalCode,
    preprocessedAddressString,
    hashedAddress,
    keepOriginalAddress,
    status,
    overrideDeliverability,
  } = userEnteredMailingAddressData || {};

  return {
    addressData: {
      address1: address1 ?? "",
      address2: address2 ?? "",
      city: city ?? "",
      state: state ?? "",
      postalCode: postalCode ?? "",
      status: status ?? "unknown",
      validationResult: { address: {} },
      preprocessedAddressString: preprocessedAddressString || "",
      hashedAddress: hashedAddress || "",
      processingStatus: VALIDATION, // validation is the first step
      keepOriginalAddress: !!keepOriginalAddress, // cast to boolean if it isn't already boolean
    },
    addressDeliverability: {
      status: INCOMPLETE_ADDRESS.status,
      code: INCOMPLETE_ADDRESS.code,
      message: INCOMPLETE_ADDRESS.message,
    },
    overrideDeliverability: {
      address: overrideDeliverability?.address ?? false,
    },
    searchTags: { formattedMailingAddress: "" },
    rootRecipient: { mailingAddresses: [] },
    finalStatus: undefined,
  };
};
