const { Timestamp, FieldValue } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const { isString } = require("lodash");
const { isJsonString } = require("../../utils/isJsonString");
const {
  mailingAddressStateCases,
} = require("../../constants/mailingAddressStateCases.js");
const {
  deliverabilityStatus,
} = require("../../constants/deliverabilityStatus");
const { processingStatus } = require("../../constants/processingStatus");

const { DONE } = mailingAddressStateCases;

// processingStatus steps used in the this worker
const {
  ERROR_VALIDATION,
  MAILING_ADDRESS_SOURCE_CREATION,
  PROCESS_EXCLUSIVITY,
} = processingStatus;

const {
  GOOD_ADDRESS,
  BAD_ADDRESS,
  NO_MAILING_ADDRESS,
  WILL_SEND,
  FAILED,
  WAITING_LIST,
} = deliverabilityStatus;
const { astridConnection } = require("../../astrid/astridConnection");
const { propertyServiceByAddress } = require("../../utils/propertyService");
const {
  logMailingAddressFailure,
} = require("../../utils/mailingAddressFailureLogger.js");
const {
  hashString,
  preProcessAddressString,
} = require("../../utils/hash/hashStrings.js");
const {
  getNextInLineExclusivity,
} = require("../../recipients/exclusivity/helpers");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const { isEmpty, isNil } = require("lodash");
const { getFirestore } = require("firebase-admin/firestore");
const { addressRestrictions } = require("../../constants/addressRestrictions");
const { magazineProductIds } = require("../../constants/magazineProductIds");
const util = require("util");
const { cleanDataForFirestore } = require("../../utils/cleanDataForFirestore");
const { logToFirestore } = require("../../utils/functionLogger");
exports.isMailingAddressComplete = function isMailingAddressComplete(
  userEnteredMailingAddressData
) {
  const { address1, city, state, postalCode } =
    userEnteredMailingAddressData ?? {};
  const isComplete = address1 && city && state && postalCode;
  return isComplete;
};

exports.validMailingAddressSourceExists =
  async function validMailingAddressSourceExists(address) {
    const db = getFirestore();
    const { address1, address2, city, state, postalCode } = address ?? {};
    const standardizedAddress = {
      address1: address1?.toUpperCase(),
      address2: address2?.toUpperCase(),
      city: city?.toUpperCase(),
      state: state?.toUpperCase(),
      postalCode: String(postalCode) ?? "",
    };

    try {
      const hashedAddress = hashString(
        standardizedAddress,
        preProcessAddressString
      );

      // check for source mailing address and "validationResult" property (the "validationResult" property lets us know that the source is valid)
      const sourceMailingAddressDocRef = db
        .collection(collectionNames.mailingAddresses)
        .doc(hashedAddress);

      const sourceMailingAddressDocSnapshot =
        await sourceMailingAddressDocRef.get();
      if (sourceMailingAddressDocSnapshot.exists) {
        const sourceMailingAddressData = sourceMailingAddressDocSnapshot.data();
        const raw = sourceMailingAddressData?.validationResult ?? {};
        const validationResult = isJsonString(raw) ? JSON.parse(raw) : raw;

        return {
          hasValidSource: !!validationResult?.address,
          sourceMailingAddressId: hashedAddress,
          validationResult,
        };
      } else {
        return {};
      }
    } catch (error) {
      await logToFirestore({
        functionName: "validMailingAddressSourceExists",
        type: "error",
        message: error.message,
        data: {
          accountId: accountId,
          email: email,
          name: name,
          lowerCasedEmail: lowerCasedEmail,
          error: error.message,
          stack: error.stack,
          standardizedAddress: standardizedAddress,
        },
      });
    }
  };

async function syncSourceAddressFields({
  validationResult,
  addressData,
  sourceMailingAddressDocRef,
}) {
  const {
    address1: currentSourceAddress1 = "",
    address2: currentSourceAddress2 = "",
    city: currentSourceCity = "",
    state: currentSourceState = "",
    postalCode: currentSourcePostalCode = "",
  } = addressData ?? {};

  const {
    address1: validated1 = "",
    address2: validated2 = "",
    city: validatedCity = "",
    state: validatedState = "",
    postalCode: validatedPostal = "",
  } = validationResult ?? {};

  const currentSource = {
    address1: currentSourceAddress1,
    address2: currentSourceAddress2,
    city: currentSourceCity,
    state: currentSourceState,
    postalCode: currentSourcePostalCode,
  };

  const newAddr = {
    address1: validated1,
    address2: validated2,
    city: validatedCity,
    state: validatedState,
    postalCode: validatedPostal,
  };

  const hasCompleteValidated =
    isString(validated1) &&
    validated1.trim() !== "" &&
    isString(validatedCity) &&
    validatedCity.trim() !== "" &&
    isString(validatedState) &&
    validatedState.trim() !== "" &&
    isString(validatedPostal) &&
    validatedPostal.trim() !== "";

  if (
    hasCompleteValidated &&
    JSON.stringify(currentSource) !== JSON.stringify(newAddr)
  ) {
    await sourceMailingAddressDocRef.update(newAddr);
    return newAddr;
  }

  return {};
}

exports.isRestrictedAddress = function isRestrictedAddress({
  address,
  countryKeys,
}) {
  const { state } = address;
  return countryKeys.some((countryKey) => {
    const restrictedAddresses =
      addressRestrictions.countries[countryKey]?.provence || [];
    return restrictedAddresses.includes(state);
  });
};

exports.validateUserEnteredMailingAddress =
  async function validateMailingAddress({
    userEnteredMailingAddressRef,
    currentAddressState,
  }) {
    const db = getFirestore();
    const {
      overrideDeliverability,
      addressDeliverability,
      addressData: currentAddressData,
    } = currentAddressState ?? {};
    console.log("currentAddressState: ", currentAddressState);
    console.log("overrideDeliverability: ", overrideDeliverability);
    const {
      accountId,
      recipientId,
      userEnteredMailingAddressId,
      preprocessedAddressString,
      migratedDeliverabilityStatus,
      isMigratedOverridden,
      isMigratedExclusive,
      isMigratedValid,
      isRmcMigration,
      address1,
      address2,
      city,
      state,
      postalCode,
      hashedAddress,
      processingStatus,
      migrationMetadata,
      sourceAddressExists,
      keepOriginalAddress,
    } = currentAddressData ?? {};

    const addressData = {
      address1: address1?.toUpperCase() ?? "",
      address2: address2?.toUpperCase() ?? "",
      city: city?.toUpperCase() ?? "",
      state: state?.toUpperCase() ?? "",
      postalCode: postalCode ?? "",
      status: "valid",
      keepOriginalAddress,
      validationResult: { address: {} },
      preprocessedAddressString: preprocessedAddressString ?? "",
      hashedAddress: hashedAddress ?? "",
      processingStatus: processingStatus ?? "",
      // remove sourceAddressExists field - no longer needed
      ...(sourceAddressExists && { sourceAddressExists: FieldValue.delete() }),
      // remove migration data (it is moved to history field)
      ...(isMigratedExclusive && { isMigratedExclusive: FieldValue.delete() }),
      ...(isMigratedExclusive && { isMigratedValid: FieldValue.delete() }),
      ...(isRmcMigration && { isRmcMigration: FieldValue.delete() }),
      ...(migrationMetadata && { migrationMetadata: FieldValue.delete() }),
      // create a history object from migration data
      history: {
        ...(isRmcMigration &&
          isMigratedExclusive &&
          isMigratedValid &&
          migrationMetadata && {
            isRmcMigration,
            isMigratedExclusive,
            isMigratedValid,
            migrationMetadata,
          }),
      },
    };

    const mailingAddressValidationFailuresRef = db
      .collection(collectionNames.logs.mailingAddressValidationFailures)
      .doc(`${accountId}-${recipientId}-${userEnteredMailingAddressRef.id}`);
    /**
     * Start: Address Validation
     * initialize base deliverability object
     * intitalize searchTags object
     * initialize overrideDeliverability object
     * begin experian validation
     */

    const searchTags = {};
    searchTags.formattedMailingAddress = `${address1?.toUpperCase()} ${address2?.toUpperCase() ?? ""} ${city?.toUpperCase()} ${state?.toUpperCase()} ${postalCode}`;

    // if keepOriginalAddress is true then return early
    // no need to validate with Experian
    if (keepOriginalAddress) {
      return {
        addressData,
        addressDeliverability: {
          status: BAD_ADDRESS.status,
          code: BAD_ADDRESS.code,
          message: BAD_ADDRESS.message,
        },
        overrideDeliverability,
        searchTags,
      };
    }

    const experianResult = await validateWithExperian({
      address1,
      address2,
      city,
      state,
      postalCode,
    });

    if (experianResult.status === "ERROR") {
      delete experianResult.subRanges;
      delete experianResult.streetRanges;
      // update addressData
      addressData.status = "bad_address";
      addressData.validationResult = { address: experianResult };
      // update addressDeliverability object
      addressDeliverability.status = BAD_ADDRESS.status;
      addressDeliverability.code = BAD_ADDRESS.code;
      addressDeliverability.message = BAD_ADDRESS.message;
      // set next processing step ----------> DONE
      addressData.processingStatus = DONE;

      // experian may send back a validated address on status ERROR so we must check for this
      if (experianResult.validatedAddress) {
        const standardizedAddress = {
          address1:
            experianResult?.validatedAddress?.address1?.toUpperCase() ??
            experianResult?.line1?.toUpperCase(),
          address2:
            experianResult?.validatedAddress?.address2?.toUpperCase() ??
            experianResult?.line2?.toUpperCase() ??
            "",
          city: experianResult?.validatedAddress?.city?.toUpperCase(),
          state: state?.toUpperCase(),
          postalCode: experianResult?.validatedAddress?.zip,
        };

        // save what preprocessed string looks like to support debugging
        const preprocessedAddressString =
          preProcessAddressString(standardizedAddress);
        // create address hash
        const hashedAddress = hashString(
          standardizedAddress,
          preProcessAddressString
        );

        addressData.address1 = standardizedAddress.address1;
        addressData.address2 = standardizedAddress.address2;
        addressData.city = standardizedAddress.city;
        addressData.state = standardizedAddress.state;
        addressData.postalCode = standardizedAddress.postalCode;
        addressData.validationResult = { address: experianResult };
        addressData.preprocessedAddressString = preprocessedAddressString;
        addressData.hashedAddress = hashedAddress;
        addressDeliverability.status = GOOD_ADDRESS.status; // all we know at this point is that this is a good address
        addressDeliverability.code = GOOD_ADDRESS.code;
        addressDeliverability.message = GOOD_ADDRESS.message;

        // set next processing step -----> MAILING_ADDRESS_SOURCE_CREATION
        addressData.processingStatus = MAILING_ADDRESS_SOURCE_CREATION;

        // update searchTags object
        searchTags.formattedMailingAddress = `${standardizedAddress.address1} ${standardizedAddress.address2 ?? ""} ${standardizedAddress.city} ${standardizedAddress.state} ${standardizedAddress.postalCode}`;
      }
    } else if (experianResult.status === "FAILED") {
      delete experianResult.subRanges;
      delete experianResult.streetRanges;
      addressData.status = "failed";
      addressData.validationResult = { address: experianResult };
      addressDeliverability.status = FAILED.status;
      addressDeliverability.code = FAILED.code;
      addressDeliverability.message = FAILED.message;
      // set next processing step ----------> ERROR_VALIDATION
      addressData.processingStatus = ERROR_VALIDATION;
      // log failure
      await mailingAddressValidationFailuresRef.set({
        error: JSON.stringify(experianResult),
        errorMessage: experianResult.message ?? "",
        accountId,
        recipientId,
        path: userEnteredMailingAddressRef.path,
        addressLines: [address1, address2, city, state, postalCode],
        createdAt: Timestamp.now(),
      });
    } else if (
      experianResult.status === "OK" ||
      experianResult.status === "CHANGED"
    ) {
      delete experianResult.subRanges;
      delete experianResult.streetRanges;
      // address validation may return with line1 line2 instead of address1 address2
      addressData.status = "valid";
      const { address1, line1, address2, line2, city, state, zip, zip4 } =
        experianResult ?? {};

      const standardizedAddress = {
        address1: address1?.toUpperCase() ?? line1?.toUpperCase(),
        address2: address2?.toUpperCase() ?? line2?.toUpperCase(),
        city: city?.toUpperCase(),
        state: state?.toUpperCase(),
        postalCode: zip,
      };
      // save what preprocessed string looks like to support debugging
      const preprocessedAddressString =
        preProcessAddressString(standardizedAddress);
      // create address hash
      const hashedAddress = hashString(
        standardizedAddress,
        preProcessAddressString
      );

      // update base address object before writing to userEnteredMailingAddress document
      addressData.address1 = standardizedAddress.address1;
      addressData.address2 = standardizedAddress.address2;
      addressData.city = standardizedAddress.city;
      addressData.state = standardizedAddress.state;
      addressData.postalCode = standardizedAddress.postalCode;
      addressData.validationResult = { address: experianResult };
      addressData.preprocessedAddressString = preprocessedAddressString;
      addressData.hashedAddress = hashedAddress;

      addressDeliverability.status = GOOD_ADDRESS.status; // all we know at this point is that this is a good address
      addressDeliverability.code = GOOD_ADDRESS.code;
      addressDeliverability.message = GOOD_ADDRESS.message;

      // set next processing step -----> MAILING_ADDRESS_SOURCE_CREATION
      addressData.processingStatus = MAILING_ADDRESS_SOURCE_CREATION;

      // update searchTags object
      searchTags.formattedMailingAddress = `${standardizedAddress.address1} ${standardizedAddress.address2 ?? ""} ${standardizedAddress.city} ${standardizedAddress.state} ${standardizedAddress.postalCode}`;
    }

    return {
      addressData,
      addressDeliverability,
      overrideDeliverability,
      searchTags,
    };
  };

exports.createMailingAddressSource = async function createMailingAddressSource({
  recipientRef,
  currentAddressState,
}) {
  const db = getFirestore();
  const {
    hashedAddress,
    preprocessedAddressString,
    address1,
    address2,
    city,
    state,
    postalCode,
    validationResult = {},
  } = currentAddressState.addressData ?? {};

  const sourceMailingAddressRef = db
    .collection(collectionNames.mailingAddresses)
    .doc(hashedAddress);

  // get source mailing address
  // get prroperty service data
  const [sourceMailingAddressSnapshot, propertyServiceResponse] =
    await Promise.all([
      sourceMailingAddressRef.get(),
      propertyServiceByAddress(db, hashedAddress),
    ]);

  if (sourceMailingAddressSnapshot.exists) {
    await Promise.all([
      recipientRef.update({
        avm_score: propertyServiceResponse?.avm_score ?? -1,
      }),
      sourceMailingAddressRef.update({
        validationResult: cleanDataForFirestore(
          JSON.stringify(validationResult)
        ),
        ...propertyServiceResponse,
        avm_score: propertyServiceResponse?.avm_score ?? -1,
      }),
    ]);
    return sourceMailingAddressRef;
  } else {
    await Promise.all([
      sourceMailingAddressRef.set({
        createdAt: Timestamp.now(),
        validatedAt: Timestamp.now(),
        ...(preprocessedAddressString && { preprocessedAddressString }),
        address1,
        ...(address2 && { address2 }),
        city,
        state,
        postalCode,
        validationResult: cleanDataForFirestore(
          JSON.stringify(validationResult)
        ), // guards against
        ...propertyServiceResponse,
        avm_score: propertyServiceResponse?.avm_score ?? -1,
      }),
      // write avm score to recipient
      recipientRef.update({
        avm_score: propertyServiceResponse?.avm_score ?? -1,
      }),
    ]);
    return sourceMailingAddressRef;
  }
};

exports.hasMagazineProductAssociation =
  async function hasMagazineProductAssociation({ recipientRef, accountId }) {
    /**
     * Checks if the recipient is associated with a group that has a mailing product assigned to it.
     * Returns true if at least one associated group contains a magazine product in its productPlans.
     * Returns false if the recipient is not associated with any group or if no group contains a magazine product.
     */
    try {
      const db = getFirestore();

      const recipientSnapshot = await recipientRef.get();
      if (!recipientSnapshot.exists) return false;

      const { recipientGroupIds = [] } = recipientSnapshot.data();

      if (!recipientGroupIds.length) {
        return false;
      }

      const recipientGroupPromises = recipientGroupIds.map((groupId) =>
        db
          .collection(collectionNames.account)
          .doc(accountId)
          .collection(subCollectionNames.contacts.recipientGroups)
          .doc(groupId)
          .get()
      );

      const recipientGroupSnapshots = await Promise.all(recipientGroupPromises);

      const uniqueProductPlans = new Set();
      recipientGroupSnapshots.forEach((snapshot) => {
        const groupData = snapshot.data();
        if (groupData && Array.isArray(groupData.productPlans)) {
          groupData.productPlans.forEach((plan) =>
            uniqueProductPlans.add(plan)
          );
        }
      });

      const productPlansArray = Array.from(uniqueProductPlans);

      const magazineProductFound = magazineProductIds.some((productId) =>
        productPlansArray.includes(productId)
      );

      return magazineProductFound;
    } catch (error) {
      return false;
    }
  };

exports.processExclusivity = async function processExclusivity({
  accountId,
  recipientRef,
  currentAddressState,
  sourceMailingAddressRef,
}) {
  const db = getFirestore();
  const { hashedAddress } = currentAddressState.addressData ?? {};
  const { addressDeliverability, overrideDeliverability = {} } =
    currentAddressState ?? {};
  // check if hashedAddress is empty then the address did not pass validation
  // so we skip the exclusivity process
  if (!isNil(hashedAddress) && !isEmpty(hashedAddress)) {
    const waitingListCollectionRef = db
      .collection(collectionNames.mailingAddresses)
      .doc(hashedAddress)
      .collection(subCollectionNames.mailingAddresses.waitingList);

    const waitingListDocRef = waitingListCollectionRef.doc(accountId);

    const [
      sourceMailingAddressSnapshot,
      waitingListDocSnapshot,
      waitingListCollectionSnapshot,
    ] = await Promise.all([
      sourceMailingAddressRef.get(),
      waitingListDocRef.get(),
      waitingListCollectionRef.get(),
    ]);

    const sourceMailingAddressData = sourceMailingAddressSnapshot.data();

    const { exclusive = [] } = sourceMailingAddressData ?? {};
    const filteredExclusive = exclusive.filter((id) => id !== "000");
    const isAlreadyExclusive = filteredExclusive.find((id) => id === accountId);

    const waitingListTenants = waitingListCollectionSnapshot.docs.map(
      (doc) => ({
        id: doc.id,
        ...doc.data(),
      })
    );

    const nextInLineExclusivity = getNextInLineExclusivity(waitingListTenants);

    /**
     * Start: Handle Exclusivity
     * scenario 1: exclusive array contains current account -> grant exclusivity -> check if account is in waiting list and remove if in waiting list
     * scenario 2: exclusive array is empty & there is no waiting list queue -> grant exclusivity
     * scenario 3: exclusive array is empty & account is next in line  -> remove from waiting list -> grant exclusivity
     * scenario 4: exclusive array is empty & next in line is a different account -> set current account to waiting list -> grant next in line account exclusivity
     * scenario 5: set current account to waiting list
     */
    if (isAlreadyExclusive) {
      // Grant Exclusivity 🤩

      //exclusiveHistoryCollectionRef.add({}) (createdAt, path, type addRecipient | addWaiting | removeWaiting | addExclusivity | removeExclusivity) We don't want to write history every time we validate only write history new additon
      await sourceMailingAddressRef.update({
        // updated 4/02/2025:  use path instead of id, path points right to the recipient document
        recipients: FieldValue.arrayUnion(recipientRef.id),
        recipientPaths: FieldValue.arrayUnion(recipientRef.path),
      });
      addressDeliverability.status = WILL_SEND.status;
      addressDeliverability.code = WILL_SEND.code;
      addressDeliverability.message = WILL_SEND.message;
      overrideDeliverability.address = false;
    } else if (
      isEmpty(filteredExclusive) &&
      isEmpty(nextInLineExclusivity) // ensure we are not skipping the line
    ) {
      // Grant Exclusivity 🤩
      await sourceMailingAddressRef.update({
        exclusive: FieldValue.arrayUnion(accountId),
        // updated 4/02/2025:  use path instead of id, path points right to the recipient document
        recipients: FieldValue.arrayUnion(recipientRef.id),
        recipientPaths: FieldValue.arrayUnion(recipientRef.path),
      });
      addressDeliverability.status = WILL_SEND.status;
      addressDeliverability.code = WILL_SEND.code;
      addressDeliverability.message = WILL_SEND.message;
      overrideDeliverability.address = false;
    } else if (
      isEmpty(filteredExclusive) &&
      nextInLineExclusivity?.id === accountId
    ) {
      // Remove from waiting list
      // Grant Exclusivity 🤩
      Promise.all([
        waitingListDocRef.delete(),
        sourceMailingAddressRef.update({
          exclusive: FieldValue.arrayUnion(accountId),
          // updated 4/02/2025:  use path instead of id, path points right to the recipient document
          recipients: FieldValue.arrayUnion(recipientRef.id),
          recipientPaths: FieldValue.arrayUnion(recipientRef.path),
        }),
      ]);
      addressDeliverability.status = WILL_SEND.status;
      addressDeliverability.code = WILL_SEND.code;
      addressDeliverability.message = WILL_SEND.message;
      overrideDeliverability.address = false;
    } else if (
      isEmpty(filteredExclusive) &&
      !isEmpty(nextInLineExclusivity) &&
      nextInLineExclusivity.id !== accountId
    ) {
      // Add to waiting list 🫡
      // Move the next in line to the empty exclusive array 🙂‍↔️
      await Promise.all([
        waitingListDocRef.set({ dateAdded: Timestamp.now() }),
        sourceMailingAddressRef.update({
          exclusive: FieldValue.arrayUnion(nextInLineExclusivity.id),
          // updated 4/02/2025:  use path instead of id, path points right to the recipient document
          recipients: FieldValue.arrayUnion(recipientRef.id),
          recipientPaths: FieldValue.arrayUnion(recipientRef.path),
        }),
      ]);

      // update addressDeliverability object
      addressDeliverability.status = WAITING_LIST.status;
      addressDeliverability.code = WAITING_LIST.code;
      addressDeliverability.message = WAITING_LIST.message;
    } else {
      // Add to waiting list 🫡
      await waitingListDocRef.set({ dateAdded: Timestamp.now() });
      // update addressDeliverability object
      addressDeliverability.status = WAITING_LIST.status;
      addressDeliverability.code = WAITING_LIST.code;
      addressDeliverability.message = WAITING_LIST.message;
    }
  }

  return { addressDeliverability, overrideDeliverability };
};

exports.updateFirestoreRefs = async function updateFirestoreRefs({
  userEnteredMailingAddressRef,
  recipientRef,
  mailingAddressState,
}) {
  const {
    address1,
    address2 = "",
    city,
    state,
    postalCode,
  } = mailingAddressState?.addressData ?? {};

  const formattedMailingAddress =
    mailingAddressState?.searchTags?.formattedMailingAddress;
  const overrideDeliverabilityStatus =
    mailingAddressState?.overrideDeliverability;

  const mailingAddressHistory = userEnteredMailingAddressRef
    .collection("History")
    .add({
      createdAt: Timestamp.now(),
      type: "mailingAddress",
      addressDeliverability: mailingAddressState?.addressDeliverability,
      address: { address1, address2, city, state, postalCode },
      finalStatus: mailingAddressState?.finalStatus ?? DONE,
    });
  await Promise.all([
    userEnteredMailingAddressRef.update({
      address1,
      ...(address2 && { address2 }),
      city,
      state,
      postalCode,

      validationResult: JSON.parse(
        JSON.stringify(mailingAddressState?.addressData?.validationResult)
      ),
      processingStatus: mailingAddressState?.finalStatus ?? DONE,
    }),
    recipientRef.update({
      addressDeliverability: mailingAddressState.addressDeliverability,
      ...mailingAddressState.rootRecipient,
      "overrideDeliverability.address":
        overrideDeliverabilityStatus?.address || false,
      "searchTags.formattedMailingAddress": formattedMailingAddress,
    }),
    mailingAddressHistory,
  ]);
};

async function validateWithExperian({
  address1,
  address2,
  city,
  state,
  postalCode,
}) {
  try {
    const response = await astridConnection({
      payload: { address1, address2, city, state, zip: postalCode },
      url: "address",
      method: "POST",
    });

    logger.info("astrid response:", response);

    const status = response?.data?.status;

    if (response?.data) {
      switch (status) {
        case "OK":
        case "CHANGED":
          return { status, ...response?.data };

        case "ERROR":
          return { status, ...response?.data };
        default:
          return { status: "FAILED" };
      }
    } else {
      return { status: "FAILED" };
    }
  } catch (error) {
    return { status: "FAILED" };
  }
}
