const { Timestamp } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const { isFunction } = require("lodash");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames.js");
const { processingStatus } = require("../../constants/processingStatus");
const {
  logMailingAddressFailure,
} = require("../../utils/mailingAddressFailureLogger.js");
const { logToFirestore } = require("../../utils/functionLogger");
const {
  isMailingAddressComplete,
  isRestrictedAddress,
  validMailingAddressSourceExists,
  validateUserEnteredMailingAddress,
  createMailingAddressSource,
  processExclusivity,
  updateFirestoreRefs,
  hasMagazineProductAssociation,
} = require("./addressValidationHelpers.js");
const {
  mailingAddressStateCases,
} = require("../../constants/mailingAddressStateCases.js");
const {
  deliverabilityStatus,
} = require("../../constants/deliverabilityStatus.js");
const { BAD_ADDRESS, FAILED, GOOD_ADDRESS, INCOMPLETE_ADDRESS } =
  deliverabilityStatus;

const {
  getInitialMailingAddressState,
} = require("./getInitialMailingAddressState");
const { stateManagement } = require("./stateManagement");

const {
  SET_INCOMPLETE_ADDRESS,
  CHECK_SOURCE_MAILING_ADDRESS,
  CHECK_SOURCE_MAILING_ADDRESS_ERROR,
  SOURCE_CREATION_SUCCESS,
  SOURCE_CREATION_ERROR,
  VALIDATE_ADDRESS_COMPLETE,
  VALIDATE_ADDRESS_ERROR,
  CHECK_MAGAZINE_PRODUCT_ASSIGNMENT,
  CHECK_MAGAZINE_PRODUCT_ASSIGNMENT_ERROR,
  PROCESS_EXCLUSIVITY_SUCCESS,
  PROCESS_EXCLUSIVITY_ERROR,
  REPROCESS,
} = mailingAddressStateCases;

exports.handleUserEnteredMailingAddress =
  async function handleUserEnteredMailingAddress({ event, db }) {
    const { accountId, recipientId } = event.params || {};

    // Are we in a create or update operation?
    const isUpdateOperation = event.data.before && event.data.after;
    logger.info("isUpdateOperation: ", isUpdateOperation);
    // If yes then guard against runaway process
    if (isUpdateOperation) {
      const beforeData = event.data.before.data();
      const afterData = event.data.after.data();
      const { address1, address2, city, state, zip } = beforeData;
      const {
        address1: newAddress1,
        address2: newAddress2,
        city: newCity,
        state: newState,
        zip: newZip,
        processingStatus: newProcessingStatus,
      } = afterData;
      logger.info("processingStatus: ", newProcessingStatus);
      const oldAddress = { address1, address2, city, state, zip };
      const newAddress = {
        address1: newAddress1,
        address2: newAddress2,
        city: newCity,
        state: newState,
        zip: newZip,
      };
      logger.info(
        "addresses are same: ",
        JSON.stringify(oldAddress) === JSON.stringify(newAddress)
      );
      logger.info("Is REPROCESS?", newProcessingStatus === REPROCESS);
      if (
        JSON.stringify(oldAddress) === JSON.stringify(newAddress) &&
        newProcessingStatus !== REPROCESS
      ) {
        // stop execution if address has not changed
        logger.info("exit early", "no address change or reprocess");
        return;
      }
    }

    const recipientRef = db
      .collection(collectionNames.account)
      .doc(accountId)
      .collection(subCollectionNames.contacts.recipients)
      .doc(recipientId);

    const recipientSnapshot = await recipientRef.get();
    const recipientData = recipientSnapshot.data();
    const { overrideDeliverability = { address: false, email: false } } =
      recipientData ?? {};

    // handle crate or update events differently
    // because this function is used in both create and update triggers
    const userEnteredMailingAddressRef =
      event.data?.ref ?? event?.data?.after?.ref;
    const userEnteredMailingAddressData = isFunction(event?.data?.data)
      ? event.data.data()
      : event.data.after.data();

    logger.info(
      "event.data.data() isFunction: ",
      isFunction(event?.data?.data)
    );
    logger.info(
      "event.data.after.data() isFunction: ",
      isFunction(event?.data?.after?.data())
    );
    logger.info(
      "userEnteredMailingAddressData: ",
      userEnteredMailingAddressData
    );
    /**
     * Get initial mailing address state
     */
    const initalStateData = {
      ...userEnteredMailingAddressData,
      overrideDeliverability,
    };

    let mailingAddressState = getInitialMailingAddressState(initalStateData);

    const {
      address1,
      address2,
      city,
      state,
      postalCode,
      preprocessedAddressString,
      hashedAddress,
    } = userEnteredMailingAddressData ?? {};

    /**
     * Check for address completeness. If not complete, set state to incomplete and write to Firestore
     * - Missing address1, city, state, or postalCode
     * - Address containes restricted state/province
     */
    logger.info("Checking for incomplete address or restricted address");
    if (!isMailingAddressComplete(userEnteredMailingAddressData)) {
      mailingAddressState = stateManagement(mailingAddressState, {
        type: SET_INCOMPLETE_ADDRESS,
      });
      // write to firestore and stop execution
      await updateFirestoreRefs({
        userEnteredMailingAddressRef,
        recipientRef,
        mailingAddressState,
      });
      // stop execution
      return;
    }

    /**
     * Step 1: Check for source mailing address and "validationResult" property (the "validationResult" property lets us know that the source is valid)
     * - source mailing addressess (e.g. MailingAddresses/{docId}) should only be created for addresses that pass validation
     * - there may have been instances where invalid addresses were written to source that is why we are also checking the "validationResult" property
     *
     *  */
    const IGNORE_SOURCE = true;
    try {
      logger.info("Checking for valid source mailing address");
      const result = await validMailingAddressSourceExists(
        userEnteredMailingAddressData
      ); // result shape {hasValidSource, sourceMailingAddressId, validationResult} or undefined
      if (result?.hasValidSource && !IGNORE_SOURCE) {
        /**
         * valid source found
         * skip address validation
         * skip mailing address source creation
         */
        mailingAddressState = stateManagement(mailingAddressState, {
          type: CHECK_SOURCE_MAILING_ADDRESS,
          payload: {
            sourceMailingAddressId: result?.sourceMailingAddressId,
            addressDeliverability: {
              code: GOOD_ADDRESS.code,
              message: GOOD_ADDRESS.message,
              status: GOOD_ADDRESS.status,
            },
            addressData: {
              validationResult: JSON.parse(
                JSON.stringify(result?.validationResult)
              ),
            }, // adds the validation result from source to the userEnteredMailingAddress
            skipAddressValidation: true,
            skipMailingAddressSourceCreation: true,
          },
        });
      } else {
        /**
         * No valid source found
         * do not skip address validation
         * do not skip mailing address source creation
         */
        logger.info(
          "No vaild source logic branch: ",
          "hasValidSource: ",
          result?.hasValidSource,
          "IGNORE_SOURCE: ",
          IGNORE_SOURCE
        );
        mailingAddressState = stateManagement(mailingAddressState, {
          type: CHECK_SOURCE_MAILING_ADDRESS,
          payload: {
            addressDeliverability: {},
            addressData: {},
            skipAddressValidation: false,
            skipMailingAddressSourceCreation: false,
          },
        });
      }
    } catch (error) {
      logger.error(
        "Check for valid source mailing address",
        error.message,
        error.stack
      );
      await logToFirestore({
        functionName: "onCreateRecipientMailingAddress",
        type: "error",
        message: "Failed to check for valid mailing address source",
        timestamp: Timestamp.now(),
        data: {
          userEnteredMailingAddressPath: userEnteredMailingAddressRef.path,
          error: error.message,
          stack: error.stack,
          mailingAddressState,
        },
      });
      mailingAddressState = stateManagement(mailingAddressState, {
        type: CHECK_SOURCE_MAILING_ADDRESS_ERROR,
        payload: { error: error.message },
      });
      await updateFirestoreRefs({
        userEnteredMailingAddressRef,
        recipientRef,
        mailingAddressState,
      });
      // stop exection
      return;
    }

    /**
     * Step 2: Validate Address if there is no existing validated source address
     * uses switches.skipAddressValidation to determine if we should skip validation
     * Need to handle this result when validateAddress is called  Account/4070783/Recipients/d01cce3c-f2e5-4a36-a0de-2b3800c813b2 note the validation result.
     */
    try {
      logger.info("Validating Address");
      /**
       * Check if we are skipping address validation
       */
      if (!mailingAddressState?.switches?.skipAddressValidation) {
        const result = await validateUserEnteredMailingAddress({
          userEnteredMailingAddressData,
          userEnteredMailingAddressRef,
          currentAddressState: mailingAddressState,
        });

        const {
          addressData,
          addressDeliverability,
          overrideDeliverability,
          searchTags,
        } = result ?? {};

        logger.info(
          `
          
          
          
          `,
          "addressData!!!!: ",
          addressData,
          "addressDeliverability: ",
          addressDeliverability,
          "searchTags: ",
          searchTags,
          "overrideDeliverability: ",
          overrideDeliverability,
          `
          
          
          
          `
        );
        mailingAddressState = stateManagement(mailingAddressState, {
          type: VALIDATE_ADDRESS_COMPLETE,
          payload: {
            addressData,
            addressDeliverability,
            overrideDeliverability,
            searchTags,
          },
        });
        // continue excution
      } else {
        // continue execution without validating address
      }
    } catch (error) {
      logger.error("Error validating address", error.message, error.stack);
      await logToFirestore({
        functionName: "onCreateRecipientMailingAddress",
        type: "error",
        message: "Failed to validate user entered mailing address",
        timestamp: Timestamp.now(),
        data: {
          userEnteredMailingAddressPath: userEnteredMailingAddressRef.path,
          error: error.message,
          stack: error.stack,
          mailingAddressState,
        },
      });
      mailingAddressState = stateManagement(mailingAddressState, {
        type: VALIDATE_ADDRESS_ERROR,
        payload: { error: error.message },
      });
      await updateFirestoreRefs({
        userEnteredMailingAddressRef,
        recipientRef,
        mailingAddressState,
      });
      // stop execution
      return;
    }

    /**
     * Step 3: Create/Update Source Mailing Address if there is no existing validated source address
     * - Only attempt to create a source if the deliverability codes are acceptable.
     * - And we are not skipping mailing address source creation (source already exists)
     */
    let sourceMailingAddressRef;
    try {
      if (
        mailingAddressState.addressDeliverability.code !== FAILED.code &&
        mailingAddressState.addressDeliverability.code !== BAD_ADDRESS.code &&
        mailingAddressState.addressDeliverability.code !==
          INCOMPLETE_ADDRESS.code &&
        !mailingAddressState?.switches?.skipMailingAddressSourceCreation // not skipping mailing address source creation
      ) {
        sourceMailingAddressRef = await createMailingAddressSource({
          userEnteredMailingAddressRef,
          recipientRef,
          currentAddressState: mailingAddressState,
        });
        mailingAddressState = stateManagement(mailingAddressState, {
          type: SOURCE_CREATION_SUCCESS,
          payload: {
            sourceMailingAddressId: sourceMailingAddressRef?.id ?? "",
          },
        });
      } else {
        // continue execution
      }
    } catch (error) {
      logger.error(
        "Error create/update source mailing address: ",
        error.message,
        error.stack
      );
      await logToFirestore({
        functionName: "onCreateRecipientMailingAddress",
        type: "error",
        message: "Failed to create mailing address source",
        timestamp: Timestamp.now(),
        data: {
          userEnteredMailingAddressPath: userEnteredMailingAddressRef.path,
          mailingAddressState: {
            ...mailingAddressState,
            ...mailingAddressState.addressData,
          },
          error: error.message,
          stack: error.stack,
        },
      });
      mailingAddressState = stateManagement(state, {
        type: SOURCE_CREATION_ERROR,
        payload: { error: error.message },
      });
      await updateFirestoreRefs({
        userEnteredMailingAddressRef,
        recipientRef,
        mailingAddressState,
      });
      return;
    }

    /**
     * Step 4: Check Group Assignment
     * - Check if the recipient group association is assigned to a magazine product
     * - If recipient is associted with magazine product, proceed to process exclusivity
     * - If recipient is not associated with magazine product, skip process exclusivity
     */

    try {
      const enrolledInMagazineProduct = await hasMagazineProductAssociation({
        recipientRef,
        accountId,
      });
      if (
        enrolledInMagazineProduct &&
        mailingAddressState.addressDeliverability.code !== FAILED.code &&
        mailingAddressState.addressDeliverability.code !== BAD_ADDRESS.code &&
        mailingAddressState.addressDeliverability.code !==
          INCOMPLETE_ADDRESS.code
      ) {
        // proceed to process exclusivity
        mailingAddressState = stateManagement(mailingAddressState, {
          type: CHECK_MAGAZINE_PRODUCT_ASSIGNMENT,
          payload: {
            addressDeliverability: {
              // still set to good address - process exclusivity step will override with WILL_SEND or WAITING_LIST
              // in case of failure this ensures we have a good address
              code: GOOD_ADDRESS.code,
              message: GOOD_ADDRESS.message,
              status: GOOD_ADDRESS.status,
            },
            switches: {
              skipProcessExclusivity: false,
            },
          },
        });

        // continue execution
      } else if (
        mailingAddressState.addressDeliverability.code !== FAILED.code &&
        mailingAddressState.addressDeliverability.code !== BAD_ADDRESS.code &&
        mailingAddressState.addressDeliverability.code !==
          INCOMPLETE_ADDRESS.code
      ) {
        // skip process exclusivity and set to GOOD_ADDRESS
        mailingAddressState = stateManagement(mailingAddressState, {
          type: CHECK_MAGAZINE_PRODUCT_ASSIGNMENT,
          payload: {
            addressDeliverability: {
              code: GOOD_ADDRESS.code,
              message: GOOD_ADDRESS.message,
              status: GOOD_ADDRESS.status,
            },
            switches: {
              skipProcessExclusivity: true,
            },
          },
        });
      }
    } catch (error) {
      logger.error("Error check group assignment", error.message, error.stack);
      await logToFirestore({
        functionName: "onCreateRecipientMailingAddress",
        type: "error",
        message: "Failed to check mailing product association",
        timestamp: Timestamp.now(),
        data: {
          userEnteredMailingAddressPath: userEnteredMailingAddressRef.path,
          mailingAddressState,
          error: error.message,
          stack: error.stack,
        },
      });
      mailingAddressState = stateManagement(mailingAddressState, {
        type: CHECK_MAGAZINE_PRODUCT_ASSIGNMENT_ERROR,
        payload: { error: error.message },
      });
      await updateFirestoreRefs(
        userEnteredMailingAddressRef,
        recipientRef,
        mailingAddressState
      );
      return;
    }

    /**
     * Step 5: Process Exclusivity
     * Should only process exclusivity if the recipient is associated with a magazine product
     */
    try {
      if (
        !mailingAddressState?.switches?.skipProcessExclusivity &&
        mailingAddressState.addressDeliverability.code !== FAILED.code &&
        mailingAddressState.addressDeliverability.code !== BAD_ADDRESS.code &&
        mailingAddressState.addressDeliverability.code !==
          INCOMPLETE_ADDRESS.code
      ) {
        const { addressDeliverability, overrideDeliverability } =
          await processExclusivity({
            accountId,
            recipientRef,
            currentAddressState: mailingAddressState,
            sourceMailingAddressRef,
          });
        mailingAddressState = stateManagement(mailingAddressState, {
          type: PROCESS_EXCLUSIVITY_SUCCESS,
          payload: { addressDeliverability, overrideDeliverability },
        });
      } else {
        // continue execution without processing exclusivity
        // if we've made it this far then we know the recipient has a GOOD_ADDRESS
      }
    } catch (error) {
      logger.error("Error process exclusivity", error.message, error.stack);
      await logToFirestore({
        functionName: "onCreateRecipientMailingAddress",
        type: "error",
        message: "Failed to process exclusivity",
        timestamp: Timestamp.now(),
        data: {
          userEnteredMailingAddressPath: userEnteredMailingAddressRef.path,
          mailingAddressState,
          error: error.message,
        },
      });
      mailingAddressState = stateManagement(mailingAddressState, {
        type: PROCESS_EXCLUSIVITY_ERROR,
        payload: { error: error.message },
      });
      await updateFirestoreRefs(
        userEnteredMailingAddressRef,
        recipientRef,
        mailingAddressState
      );
      return;
    }
    logger.info(
      "DONE! ALL STEPS SUCCEEDED mailingAddressState: ",
      mailingAddressState
    );
    // Final update: All steps succeeded
    try {
      await updateFirestoreRefs({
        userEnteredMailingAddressRef,
        recipientRef,
        mailingAddressState,
      });
    } catch (error) {
      logger.error("Error updating firestore: ", error);
    }
  };
