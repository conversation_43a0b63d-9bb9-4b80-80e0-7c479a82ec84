const { onDocumentUpdated } = require("firebase-functions/v2/firestore");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames.js");

const {
  handleUserEnteredMailingAddress,
} = require("./handleUserEnteredMailingAddress");

exports.onUpdateRecipientMailingAddress = (db) =>
  onDocumentUpdated(
    `${collectionNames.account}/{accountId}/${subCollectionNames.contacts.recipients}/{recipientId}/${subCollectionNames.contacts.userEnteredMailingAddress}/{userEnteredMailingAddressId}`,
    async (event) => {
      await handleUserEnteredMailingAddress({ event, db });
    }
  );
