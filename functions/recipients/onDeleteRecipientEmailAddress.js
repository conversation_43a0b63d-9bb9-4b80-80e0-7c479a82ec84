const {
  onDocumentDeleted
} = require("firebase-functions/v2/firestore");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames.js");

const { emailDeliverabilityStatus } = require("../constants/emailDeliverabilityStatus.js");

exports.onDeleteRecipientEmailAddress = (db) =>
  onDocumentDeleted(
    `${collectionNames.account}/{accountId}/${subCollectionNames.contacts.recipients}/{recipientId}/${subCollectionNames.contacts.emailAddresses}/{emailAddressId}`,
    async (event) => {

      // document ids
      const accountId = event.params.accountId;
      const recipientId = event.params.recipientId;

      const recipientRef = db
        .collection(collectionNames.account)
        .doc(accountId)
        .collection(subCollectionNames.contacts.recipients)
        .doc(recipientId);
      
      const dataUpdate = {
        "primaryEmail":""
      }
      await recipientRef.update({emailDeliverability: emailDeliverabilityStatus.NO_EMAIL_ADDRESS, searchTags: dataUpdate , emailAddress: ""}, {merge: true});  
    }
  );
