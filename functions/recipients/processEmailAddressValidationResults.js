const { logger } = require("firebase-functions");
const { Timestamp, FieldValue } = require("firebase-admin/firestore");
const { isArray, isEmpty } = require("lodash");
const {
  hashString,
  preProcessEmailString,
} = require("../utils/hash/hashStrings");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { emailDeliverabilityStatus } = require("../constants/emailDeliverabilityStatus.js");


async function processEmailAddressValidationResults(emailClass) {
  /**
   * Deliverability Analysis
   * deliverable	The recipient address is considered to be valid and should accept email.
   * undeliverable	The recipient address is considered to be invalid and will result in a bounce if sent to.
   * do_not_send	The recipient address is considered to be highly risky and will negatively impact sending reputation if sent to.
   * catch_all	The validity of the recipient address cannot be determined as the provider accepts any and all email regardless of whether or not the recipient's mailbox exists.
   * unknown	The validity of the recipient address cannot be determined for a variety of potential reasons. Please refer to the associated 'reason' array returned in the response.
   * https://documentation.mailgun.com/docs/inboxready/mailgun-validate/single-valid-ir/
   */

  const validation = emailClass.record.validationResults;
  const originalData = emailClass.source || null;
  const source = emailClass.record.check;  
  const deliverability = {
    emailStatus: "failed",
    emailRisk: "none",
    code: "FA",
    reason: "Failed",
  }
  if(source === 'mailgun'){
    const { result, reason, risk } = validation || {};
      emailClass.record.status = result;
      emailClass.record.risk = risk;
      emailClass.record.reason = reason;
      deliverability.emailRisk = risk;
      deliverability.emailStatus = result;
      deliverability.reason = reason;
    if (result === "deliverable") {
      deliverability.code = emailDeliverabilityStatus.WILL_SEND.code;
      emailClass.record.code = emailDeliverabilityStatus.WILL_SEND.code;
    } else if ( result === "undeliverable" ) {
      deliverability.code = emailDeliverabilityStatus.INVALID.code;
      emailClass.record.code = emailDeliverabilityStatus.INVALID.code;
    } else  if (result === 'do_not_send'){
      deliverability.code = emailDeliverabilityStatus.BAD_EMAIL.code;
      emailClass.record.code = emailDeliverabilityStatus.BAD_EMAIL.code;
    } else if (result === 'catch_all'){
      deliverability.code = emailDeliverabilityStatus.WILL_SEND.code;
      emailClass.record.code = emailDeliverabilityStatus.WILL_SEND.code;
    } else {
      deliverability.code = emailDeliverabilityStatus.INVALID.code;
      emailClass.record.code = emailDeliverabilityStatus.INVALID.code;
    }
  } else if(source === 'astrid'){
    const { is_valid, reason = "", risk="" } = validation || {};
    emailClass.record.status = is_valid ? 'deliverable': "undeliverable";
    emailClass.record.risk = risk;
    emailClass.record.reason = reason;
    deliverability.emailRisk = risk;
    deliverability.emailStatus = is_valid ? 'deliverable': "undeliverable";
    deliverability.reason = reason;
    if(is_valid){
      deliverability.code = emailDeliverabilityStatus.WILL_SEND.code;
      emailClass.record.code = emailDeliverabilityStatus.WILL_SEND.code;
    } else {
      deliverability.code = emailDeliverabilityStatus.BAD_EMAIL.code;
      emailClass.record.code = emailDeliverabilityStatus.BAD_EMAIL.code;
    }
  } else if(originalData){
    emailClass.record.status = originalData.status !== "unknown" ? originalData.status : "undeliverable";
    emailClass.record.risk = originalData.risk || "none";
    emailClass.record.reason = originalData.reason || "Unknown reason";
    deliverability.emailRisk = originalData.risk || "none";
    deliverability.emailStatus = originalData.is_valid ? 'deliverable' : "undeliverable";
    deliverability.reason = originalData.reason || "Unknown reason";
    if(emailClass.record.status === "deliverable"){
      deliverability.code = emailDeliverabilityStatus.WILL_SEND.code;
      emailClass.record.code = emailDeliverabilityStatus.WILL_SEND.code;
    } else {
      deliverability.code = emailDeliverabilityStatus.INVALID.code;
      emailClass.record.code = emailDeliverabilityStatus.INVALID.code;
    }
  }
  emailClass.deliverability = deliverability;
  return emailClass  
}

module.exports.processEmailAddressValidationResults =
  processEmailAddressValidationResults;
