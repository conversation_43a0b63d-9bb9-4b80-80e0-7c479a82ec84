const functions = require('firebase-functions');

// Collection names constant
const collectionNames = {
  account: 'accounts' // Update this to match your actual collection name
};

/**
 * Get magazine recipients statistics for an account
 */
exports.getAccountMagazineRecipients = (db) => 
  functions.https.onCall(async (data, context) => {
    console.log('getAccountMagazineRecipients called with data:', data);
    const { accountId } = data;
    
    if (!accountId) {
      throw new functions.https.HttpsError('invalid-argument', 'accountId is required');
    }

    // Step 1: Query RecipientGroups where productPlans array contains any of the values
    const productPlansQuery = db
      .collection(`${collectionNames.account}/${accountId}/RecipientGroups`)
      .where('productPlans', 'array-contains-any', ['1', '11', '12', '3', '7']);
    const recipientGroupsSnapshot = await productPlansQuery.get();
    const recipientGroups = recipientGroupsSnapshot.docs.map((doc) => doc.data());

    // Step 2: Initialize counters for activeMembers, addressWillSend, and addressWillNotSend
    let totalActiveMembers = 0;
    let totalAddressWillSend = 0;
    let totalAddressWillNotSend = 0;

    // Step 3: Loop through each recipient group's meta field and sum the counts
    recipientGroups.forEach((group) => {
      if (group.meta) {
        totalActiveMembers += group.meta.activeMembers || 0;
        totalAddressWillSend += group.meta.addressWillSend || 0;
        totalAddressWillNotSend += group.meta.addressWillNotSend || 0;
      }
    });

    // Step 4: Return the total counts
    return {
      totalRecipients: totalActiveMembers,
      willSend: totalAddressWillSend,
      willNotSend: totalAddressWillNotSend,
    };
  });

/**
 * Get recipient group product deliverables data
 */
exports.getRecipientGroupCounts = (db) =>
  functions.https.onCall(async (req, context) => {
    const { accountId, recipientGroupId } = req.data;
    console.log('getAccountMagazineRecipients called with data:', req.data);
    if (!accountId || !recipientGroupId) {
      throw new functions.https.HttpsError('invalid-argument', 'accountId and recipientGroupId are required');
    }

    // Fetch product plan data
    let totalCount = 0;

    // Query for active recipients in the specified group
    const recipientsRef = db
      .collection(`Account/${accountId.toString()}/Recipients`)
      .where('recipientGroupIds', 'array-contains', recipientGroupId)
      .where('isActive', '==', true);
    console.log(`Querying recipients in group ${recipientGroupId} for account ${accountId}`);

    // Get active recipients
    const activeRecipientsSnapshot = await recipientsRef.get();
    totalCount = activeRecipientsSnapshot.size;
    console.log(`Total active recipients found: ${totalCount}`);
    
    // Initialize counters for different recipient categories
    let pausedAddressCount = 0;
    let badAddressCount = 0;
    let waitingListCount = 0;
    let hasAddressCount = 0;
    let goodEmailCount = 0;
    let badEmailCount = 0;
    let deliverAnywayCount = 0;
    let sendAnywayCount = 0;
    let goodAddressCount = 0;
    let willSendCount = 0;
    let noAddress = 0;
    
    // Process each recipient to categorize them
    activeRecipientsSnapshot.forEach((recipientDoc) => {
      const recipientData = recipientDoc.data();          
      if (recipientData?.mailingsPaused === true) {
        pausedAddressCount++;
      } else if(recipientData?.overrideDeliverability?.address === true) {
        sendAnywayCount++;
      }else if (recipientData?.addressDeliverability?.code === "IA" || recipientData?.addressDeliverability?.code === "INC") {
        badAddressCount++;
      }else if (recipientData?.addressDeliverability?.code === "WL" || 
          recipientData?.addressDeliverability?.code === "NE") {
        waitingListCount++;
      } else if(recipientData?.addressDeliverability?.code === "GA" || recipientData?.addressDeliverability?.code === "GOOD"){
        goodAddressCount++;
      } else if(recipientData?.addressDeliverability?.code === "NA" || recipientData?.addressDeliverability?.code === "NON") {
        noAddress++;
      } else if(recipientData?.addressDeliverability?.code === "WS") {
        willSendCount++;
      } 
      if((recipientData?.mailingAddress && recipientData?.mailingAddresses && recipientData?.mailingAddresses.length > 0) || (recipientData?.addressDeliverability && recipientData?.addressDeliverability?.code !== "NON")) {
        hasAddressCount++;
      }
      if (recipientData?.emailAddress && recipientData?.emailDeliverability?.code === "WE") {
        goodEmailCount++;
      } else {
        badEmailCount++;
      }
      if (recipientData?.overrideDeliverability?.email === true) {
        deliverAnywayCount++;
      }
    });

    const totalNoAddresses = totalCount - hasAddressCount;

    // Calculate the total number of recipients that will not receive mail
    const totalNotSending = (
      waitingListCount + 
      badAddressCount +
      goodAddressCount +
      noAddress
    ) + pausedAddressCount;
    // Calculate the total number of postcards that will not be sent
    const postcardNotSending = (badAddressCount + noAddress) + pausedAddressCount;

    // Calculate total sending recipients
    const postcardWillSendCount = goodAddressCount + waitingListCount + sendAnywayCount + willSendCount;
    const addressWillSendCount = willSendCount + sendAnywayCount;

    const meta = {
      activeMembers: totalCount,
      addressWillSend: addressWillSendCount,
      addressWillNotSend: totalNotSending,
      postcardWillSend: postcardWillSendCount,
      postcardsWillNotSend: postcardNotSending,
      emailValid: goodEmailCount + deliverAnywayCount,
      emailBad: badEmailCount,
      deliverAnyway: deliverAnywayCount,
      noAddress: noAddress,
      pausedAddress: pausedAddressCount,
      badAddress: badAddressCount,
      waitingList: waitingListCount,
      hasAddress: hasAddressCount,
      goodEmail: goodEmailCount,
      badEmail: badEmailCount,
    };

    await db
      .collection(`${collectionNames.account}/${accountId}/RecipientGroups`)
      .doc(recipientGroupId)
      .set({ meta }, { merge: true });

    return meta;
  });

/**
 * Get email recipients statistics for an account
 */
exports.getAccountEmailRecipients = (db) =>
  functions.https.onCall(async (data, context) => {
    const { accountId } = data;
    
    if (!accountId) {
      throw new functions.https.HttpsError('invalid-argument', 'accountId is required');
    }

    const productPlansQuery = db
      .collection(`${collectionNames.account}/${accountId}/RecipientGroups`)
      .where('productPlans', 'array-contains-any', ['9', '6', '5', '2']);
    const recipientGroupsSnapshot = await productPlansQuery.get();
    const recipientGroups = recipientGroupsSnapshot.docs.map((doc) => doc.data());
    
    let totalActiveMembers = 0;
    let totalEmailValid = 0;
    let totalEmailBad = 0;

    recipientGroups.forEach((group) => {
      if (group.meta) {
        totalActiveMembers += group.meta.activeMembers || 0;
        totalEmailValid += group.meta.emailValid || 0;
        totalEmailBad += group.meta.emailBad || 0;
      }
    });

    return {
      totalRecipients: totalActiveMembers,
      willSend: totalEmailValid,
      willNotSend: totalEmailBad,
    };
  });