const { getFunctions } = require("firebase-admin/functions");
const { onDocumentWritten } = require("firebase-functions/v2/firestore");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const {
  wasExclusiveProductAddedToGroup,
  wasExclusiveProductRemovedFromGroup,
  groupHasExclusiveProduct,
} = require("../recipients/exclusivity/helpers");
const {
  getGroupIdsByProductAssociation,
} = require("./productHelpers/productHelpers");
const {
  wasEmailProductAddedToGroup,
} = require("../recipients/emailHelpers/emailHelpers");
const { isNil } = require("lodash");
const { defineString } = require("firebase-functions/params");
const { collectionHistory } = require("../utils/collectionHistory");
const { group } = require("console");

const projectIdEnv = defineString("TITAN_PROJECT_ID");
const projectId = projectIdEnv.value();
const location = "us-central1";
const handleMailingProductRemovedTask = `projects/${projectId}/locations/${location}/functions/handleMailingProductRemovedFromGroup`;
const handleMailingProductAddedTask = `projects/${projectId}/locations/${location}/functions/handleMailingProductAddedToGroup`;

const handleEmailProductAddedToGroupTask = `projects/${projectId}/locations/${location}/functions/handleEmailProductAddedToGroup`;
exports.onWriteRecipientGroup = (db) =>
  onDocumentWritten(
    `${collectionNames.account}/{accountId}/${subCollectionNames.contacts.recipientGroups}/{recipientGroupId}`,
    async (event) => {
      const { accountId, recipientGroupId } = event.params;
      const recipientGroupBefore = event.data.before.data();
      const recipientGroupAfter = event.data.after.data();
      const recipientGroupRef = event.data?.after?.ref;
      const functions = getFunctions();

      const productPlansBefore = recipientGroupBefore?.productPlans ?? [];
      const productPlansAfter = recipientGroupAfter?.productPlans ?? [];
      const compareBefore = JSON.stringify(productPlansBefore);
      const compareAfter = JSON.stringify(productPlansAfter);
      const isProductPlansEqual = compareBefore === compareAfter;

      if (isNil(recipientGroupAfter)) {
        return;
      }

      if (isProductPlansEqual) {
        return;
      }

      await collectionHistory({
        collection: `${collectionNames.account}/${accountId}/${subCollectionNames.contacts.recipientGroups}`,
        id: recipientGroupId,
        before: recipientGroupBefore,
        after: recipientGroupAfter,
        action: "UPDATE",
        context: {
          notes: ``,
        },
      });
      /**
       * Email Product Added
       * #2 - Digital Edition
       * #5 - Local Events
       * #6 - Branded Posts
       * #9 - Landing Pages
       */

      const emailProductAdded = await wasEmailProductAddedToGroup({
        productPlansBefore,
        productPlansAfter,
      });

      if (emailProductAdded) {
        console.log("emailProductAdded: ", emailProductAdded);
        const emailTaskPayload = {
          accountId,
          recipientGroupId,
        };
        const taskQueue = functions.taskQueue(
          handleEmailProductAddedToGroupTask
        );
        await taskQueue.enqueue(emailTaskPayload);
      }

      /**
       * Schedule Exclusivity Release
       * check if mailing product was removed from productPlans
       *
       */

      const exclusiveProductRemoved = await wasExclusiveProductRemovedFromGroup(
        {
          productPlansAfter,
          productPlansBefore,
        }
      );

      const exclusiveProductAdded = await wasExclusiveProductAddedToGroup({
        productPlansBefore,
        productPlansAfter,
      });

      const hasExclusiveProduct =
        await groupHasExclusiveProduct(productPlansAfter);

      const taskPayload = {
        accountId,
        recipientGroupId,
      };
      if (exclusiveProductRemoved && !hasExclusiveProduct) {
        const taskQueue = functions.taskQueue(handleMailingProductRemovedTask);
        await taskQueue.enqueue(taskPayload);
      } else if (exclusiveProductAdded) {
        const taskQueue = functions.taskQueue(handleMailingProductAddedTask);
        await taskQueue.enqueue(taskPayload);
      }
    }
  );
