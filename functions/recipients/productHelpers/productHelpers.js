const { getFirestore } = require("firebase-admin/firestore");

const { isArray } = require("lodash");

exports.getGroupIdsByProductAssociation =
  async function getGroupIdsByProductAssociation({ accountId, products = [] }) {
    if (!isArray(products)) {
      console.warn(
        "products parameter for getGroupsByProduct must be an array"
      );
      return [];
    }

    const db = getFirestore();
    try {
      const recipientGroupRef = db
        .collection("Account")
        .doc(accountId.toString())
        .collection("RecipientGroups")
        .where("isActive", "==", true)
        .where("productPlans", "array-contains-any", products);

      const recipientGroupSnapshot = await recipientGroupRef.get();
      console.log(
        "recipientGroupSnapshot: ",
        recipientGroupSnapshot.docs.length
      );
      if (recipientGroupSnapshot.empty) {
        console.warn(`No groups with products ${products} found`);
        return [];
      }
      const groupIds = recipientGroupSnapshot.docs.map((doc) => doc.id);
      return groupIds;
    } catch (error) {
      console.log(error);
      return [];
    }
  };
