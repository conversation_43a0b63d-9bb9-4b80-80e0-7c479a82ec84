const { logger } = require("firebase-functions");
async function handleAddressStatusUpdates({
  userEnteredMailingAddressRef,
  recipientRef,
  status,
  validationResult,
}) {
  try {
    await userEnteredMailingAddressRef.update({
      status,
      validationResult,
    });

    if (status === "valid") {
      await recipientRef.update({
        "addressDeliverability.isValidMailingAddress": true,
      });
    } else {
      await recipientRef.update({
        "addressDeliverability.isValidMailingAddress": false,
      });
    }
  } catch (error) {
    logger.error(
      "Error updating user entered mailing address status: ",
      error,
      "status: ",
      status,
      "validationResult",
      JSON.stringify(validationResult)
    );
  }
}

module.exports.handleAddressStatusUpdates = handleAddressStatusUpdates;
