const { FieldValue } = require("firebase-admin/firestore");
const { onDocumentDeleted } = require("firebase-functions/v2/firestore");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { logger } = require("firebase-functions");

const {
  removeAccountReferencesFromMailingAddresses,
  removeRecipientReferencesFromMailingAddresses,
} = require("../recipients/exclusivity/helpers");

exports.onDeleteRecipient = (db) =>
  onDocumentDeleted(
    `${collectionNames.account}/{accountId}/${subCollectionNames.contacts.recipients}/{recipientId}`,
    async (event) => {
      const { accountId, recipientId } = event.params;
      const snapshot = event.data;
      const { mailingAddresses = [] } = snapshot.data() ?? {};
      const recipient = snapshot.data();
      const recipientGroupId = recipient?.recipientGroupIds?.[0];

      for (const mailingAddress of mailingAddresses) {
        await Promise.all([
          removeAccountReferencesFromMailingAddresses({
            db,
            accountId,
            mailingAddressId: mailingAddress.id,
          }),
          removeRecipientReferencesFromMailingAddresses({
            db,
            recipientId,
            mailingAddressId: mailingAddress.id,
          }),
        ]);
      }
    }
  );
