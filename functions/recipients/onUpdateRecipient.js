const { FieldValue, Timestamp } = require("firebase-admin/firestore");
const { onDocumentUpdated } = require("firebase-functions/v2/firestore");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { isEmpty, isNil } = require("lodash");
const { logger } = require("firebase-functions");
const {
  handleGroupSummaryRecalculation,
  handleExclusivity,
} = require("./helpers");
const {
  handleRecipientAddedToEmailProductGroup,
} = require("../recipients/emailHelpers/emailHelpers");
const { logToFirestore } = require("../utils/functionLogger");
const diff = require("deep-diff");
const { collectionHistory } = require("../utils/collectionHistory");

/**
 * Triggered when a recipient document is updated.
 *
 * This function processes updates to recipient documents by first checking whether any
 * meaningful changes (aside from the updatedAt timestamp) have occurred. If there are no changes,
 * it exits early. Otherwise, it concurrently handles several update-related tasks:
 *
 * 1. **Inactive Status Handling:**
 *    Invokes `handleInactiveStatus` to remove the recipient from any recipient groups if they have been deactivated.
 *
 * 2. **Follow-Up Status Update:**
 *    Calls `updateFollowupStatus` to record changes in the recipient's follow-up status by updating the
 *    corresponding history subcollection.
 *
 * 3. **Group Summary Recalculation:**
 *    Uses `handleGroupSummaryRecalculation` to detect changes in group memberships or key deliverability attributes
 *    (such as address deliverability, active status, or mailing settings) and triggers recalculation of group summaries if needed.
 *
 * 4. **Exclusivity Handling:**
 *    Executes `handleExclusivity` to determine if any exclusivity release logic should be applied based on changes
 *    in mailing product associations and the recipient’s active status.
 *
 * @param {Firestore} db - The Firestore database instance.
 * @returns {Function} A Firestore onDocumentUpdated trigger function.
 */
exports.onUpdateRecipient = (db) =>
  onDocumentUpdated(
    `${collectionNames.account}/{accountId}/${subCollectionNames.contacts.recipients}/{recipientId}`,
    async (event) => {
      const { accountId, recipientId } = event.params || {};
      console.log("onUpdateRecipient", event.params);
      const recipientRef = event.data.after.ref;
      const recipientBefore = event.data.before.data();
      const recipientAfter = event.data.after.data();

      const {
        createdAt: createdAtBefore,
        updatedAt: updatedAtBefore,
        ...recipientBeforeData
      } = recipientBefore;
      const { createdAt, updatedAt, ...recipientAfterData } = recipientAfter;

      const beforeData = base64EncodeJson(recipientBeforeData);
      const afterData = base64EncodeJson(recipientAfterData);
      logger.log("has changed: ", beforeData !== afterData);
      if (beforeData === afterData) return;

      // keep mailingAddresses in sync with new mailingAddress field - see notes on function definition below
      // mailingAddresses is the old field we want to move move away from , mailingAddress is the new field
      const { mailingAddresses, mailingAddress } = recipientAfterData ?? {};
      await syncMailingAddressField({
        recipientRef,
        mailingAddresses,
        mailingAddress,
      });

      // Get user information from the authentication context
      const userId = event.auth?.uid || "system";
      const userEmail = event.auth?.token?.email || "system";

      // using deep-diff to log what changed
      const diffResult = diff(recipientBefore, recipientAfter);
      // convert diff output into plain object
      // serializes the diff to a JSON string and
      // then parses it back into a plain object,
      // stripping out any custom prototypes
      // firestore will yell at you if you feed it objects with custom prototypes

      /**
       * Diff Record Structure (returned by deep-diff):
       * https://github.com/flitbit/diff#readme
       *
       * Each diff record is an object with the following properties:
       *
       * - kind: {string}
       *   - Indicates the type of change.
       *   - Possible values:
       *     - 'N': A newly added property/element.
       *     - 'D': A property/element was deleted.
       *     - 'E': A property/element was edited.
       *     - 'A': A change occurred within an array.
       *
       * - path: {Array}
       *   - An array representing the property path (from the left-hand-side root) where the change occurred.
       *
       * - lhs: {any}
       *   - The value on the left-hand-side of the comparison.
       *   - Will be undefined if kind === 'N' (i.e., the property is newly added).
       *
       * - rhs: {any}
       *   - The value on the right-hand-side of the comparison.
       *   - Will be undefined if kind === 'D' (i.e., the property was deleted).
       *
       * - index: {number} (only present when kind === 'A')
       *   - Indicates the array index where the change occurred.
       *
       * - item: {object} (only present when kind === 'A')
       *   - Contains a nested change record detailing the change that occurred at the specified array index.
       */

      const plainDiff = JSON.parse(JSON.stringify(diffResult));

      await Promise.all([
        handleInactiveStatus(event),
        updateFollowupStatus({ event, db }),
        handleExclusivity({
          accountId,
          recipientBefore,
          recipientAfter,
          recipientRef,
        }),
        handleRecipientAddedToEmailProductGroup({
          accountId,
          recipientBefore,
          recipientAfter,
          recipientRef,
        }),
        collectionHistory({
          collection: `${collectionNames.account}/${accountId}/${subCollectionNames.contacts.recipients}`,
          id: recipientId,
          before: recipientBefore,
          after: recipientAfter,
          context: {
            auth: {
              uid: userId,
              token: { email: userEmail },
            },
            // Include the raw request if available
            rawRequest: event.rawRequest ?? JSON.stringify(event),
            // Include the calculated diff for more detailed change tracking
            diff: plainDiff,
          },
          action: "UPDATE",
        }),
      ]);
    }
  );

// If recipient becomes inactive, remove them from any recipient groups
async function handleInactiveStatus(event) {
  const recipientBefore = event.data.before.data();
  const recipientAfter = event.data.after.data();
  const recipientRef = event.data.after.ref;

  const { isActive: isActiveBefore } = recipientBefore || {};
  const { isActive, recipientGroupIds } = recipientAfter || {};

  try {
    // Only update if the recipient is changing from active to inactive
    // AND if recipientGroupIds exists and is not already empty.
    if (
      isActiveBefore &&
      !isActive &&
      Array.isArray(recipientGroupIds) &&
      recipientGroupIds.length > 0
    ) {
      await recipientRef.update({
        recipientGroupIds: [],
        updatedAt: Timestamp.now(),
      });
    }
  } catch (error) {
    logger.error("onUpdateRecipient-StatusCheck error", error);
    await logToFirestore({
      functionName: "onUpdateRecipient",
      type: "error",
      message: `invocation for ${recipientRef.path}`,
      timestamp: Timestamp.now(),
      data: {
        path: recipientRef.path,
        error: error.message,
        stack: error.stack,
      },
    });
  }
}

async function updateFollowupStatus({ event, db }) {
  const recipientBefore = event.data.before.data();
  const recipientAfter = event.data.after.data();

  // Exit early if followUpStatus hasn't changed.
  if (recipientBefore.followUpStatus === recipientAfter.followUpStatus) return;

  const newFollowUpStatus = recipientAfter.followUpStatus;
  const recipientId = event.params.recipientId;
  const accountId = event.params.accountId;

  try {
    const followUpStatusHistoryRef = db
      .collection(collectionNames.account)
      .doc(accountId)
      .collection(subCollectionNames.contacts.recipients)
      .doc(recipientId)
      .collection("FollowUpStatusHistory");

    const statusSettingsRef = db
      .collection(collectionNames.settings)
      .doc(`recipient.followUpStatus.${newFollowUpStatus}`);

    const statusSettingsDoc = await statusSettingsRef.get();

    if (!statusSettingsDoc.exists) {
      return;
    }

    // Extract display and icon fields from the status settings document
    const { display, icon } = statusSettingsDoc.data();

    await followUpStatusHistoryRef.add({
      followUpStatus: newFollowUpStatus,
      display,
      icon: icon || "",
      updatedAt: Timestamp.now(),
    });
  } catch (error) {
    logger.error("Error updating FollowUpStatusHistory:", error);
  }
}

function base64EncodeJson(jsonObject) {
  try {
    // Convert object to JSON string
    const jsonString = JSON.stringify(jsonObject);

    // For Node.js environment
    if (typeof Buffer !== "undefined") {
      return Buffer.from(jsonString).toString("base64");
    }
  } catch (error) {
    console.error("Error encoding object to base64:", error);
    throw error;
  }
}

// keep mailingAddresses (array of objects) in sync with mailingAddress (string) - there should only be one mailing address per recipient
// we are moving away from the array of objects because it limits the ability to query
// the purpose of this function is to keep the mailingAddress field in sync with the mailingAddresses array to maintain backwards compatibility
async function syncMailingAddressField({
  recipientRef,
  mailingAddresses,
  mailingAddress,
}) {
  console.log("mailingAddresses", mailingAddresses);
  const addressId = mailingAddresses?.[0]?.id ?? "";

  if (isEmpty(mailingAddresses) || isNil(mailingAddresses)) return;

  if (mailingAddress === addressId) return;

  try {
    await recipientRef.update({ mailingAddress: addressId });
  } catch (error) {
    console.error("Error updating mailingAddress field", error);
  }
}
