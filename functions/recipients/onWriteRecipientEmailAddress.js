const { onDocumentWritten } = require("firebase-functions/v2/firestore");
const { validateEmailAddressWithClass } = require("../services/mailgun.js");
const { isNil, isEmpty } = require("lodash");
const { FieldValue } = require("firebase-admin/firestore");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames.js");
const { astridConnection } = require("../astrid/astridConnection");
const { logger } = require("firebase-functions");
const {
  hashString,
  preProcessEmailString,
} = require("../utils/hash/hashStrings");
const {
  getControlPanel,
  logControlPanel,
} = require("../utils/controlPanel.js");
const {
  emailDeliverabilityStatus,
} = require("../constants/emailDeliverabilityStatus.js");
const {
  processEmailAddressValidationResults,
} = require("./processEmailAddressValidationResults.js");
const {
  recipientInProduct,
} = require("../utils/recipients/recipientInProduct");
const { hash } = require("node:crypto");

exports.onWriteRecipientEmailAddress = (db) =>
  onDocumentWritten(
    `${collectionNames.account}/{accountId}/${subCollectionNames.contacts.recipients}/{recipientId}/${subCollectionNames.contacts.emailAddresses}/{emailAddressId}`,
    async (event) => {
      const emailAddressAfter = event.data.after.data();
      const emailAddressBefore = event.data.before.data();

      if (
        !emailAddressAfter?.email ||
        typeof emailAddressAfter.email !== "string"
      ) {
        return;
      }

      const reporcessing = emailAddressAfter?.processingStatus === "REPROCESS";
      const processing = emailAddressAfter?.processingStatus === "PROCESS";

      const changedFields = Object.keys(emailAddressAfter).filter(
        (k) => emailAddressAfter[k] !== emailAddressBefore?.[k]
      );

      const runFunction =
        emailAddressBefore?.email !== emailAddressAfter?.email ||
        reporcessing ||
        processing;

      const newEmail =
        (isNil(emailAddressBefore?.email) ||
          isEmpty(emailAddressBefore?.email)) &&
        !isEmpty(emailAddressAfter?.email) &&
        /\S+@\S+\.\S+/.test(emailAddressAfter?.email.trim());

      const deletedEmail = isNil(emailAddressAfter?.email);
      const emptyEmail = isEmpty(emailAddressAfter?.email);

      const emailChanged =
        emailAddressBefore?.email &&
        emailAddressAfter?.email &&
        emailAddressBefore?.email.trim().toLowerCase() !==
          emailAddressAfter?.email.trim().toLowerCase();

      if (!runFunction) {
        console.log("runFunction: ", runFunction);
        return;
      }
      if (emptyEmail) {
        console.log("emptyEmail: ", emptyEmail);
        return;
      }
      if (
        emailAddressAfter?.code === "BE" ||
        emailAddressAfter?.code === "UN"
      ) {
        console.log("emailAddressAfter: ", emailAddressAfter);
        return;
      }

      const accountId = event.params.accountId;
      const recipientId = event.params.recipientId;
      const emailAddressId = event.params.emailAddressId;

      const inProduct = await recipientInProduct({
        recipientId,
        accountId,
        method: "email",
        db,
      });

      if (reporcessing && !inProduct) {
        return;
      }

      const emailAddressRef = db
        .collection(collectionNames.account)
        .doc(accountId)
        .collection(subCollectionNames.contacts.recipients)
        .doc(recipientId)
        .collection(subCollectionNames.contacts.emailAddresses)
        .doc(emailAddressId);

      const emailAddressValidationFailuresRef = db
        .collection(collectionNames.logs.emailAddressValidationFailures)
        .doc(`${accountId}-${recipientId}-${emailAddressId}`);

      const recipientRef = db
        .collection(collectionNames.account)
        .doc(accountId)
        .collection(subCollectionNames.contacts.recipients)
        .doc(recipientId);

      if (deletedEmail) {
        const dataUpdate = {
          primaryEmail: "",
        };
        await recipientRef.update(
          {
            emailDeliverability: emailDeliverabilityStatus.NO_EMAIL_ADDRESS,
            searchTags: dataUpdate,
            emailAddress: "",
          },
          { merge: true }
        );
        await emailAddressRef.delete();
        return;
      }

      if (newEmail && !inProduct) {
        await recipientRef.update(
          {
            searchTags: {
              primaryEmail: emailAddressAfter?.email || "",
            },
          },
          { merge: true }
        );
        return;
      }

      const controlPanel = await getControlPanel();
      const isValidationActive = controlPanel?.mailgunValidation?.isActive;

      const cleanEmail = preProcessEmailString(emailAddressAfter?.email);
      const emailHashValue = hashString(cleanEmail, preProcessEmailString);

      let emailClass = {
        record: {
          email: deletedEmail ? null : emailAddressAfter?.email,
          isPrimary: emailAddressAfter?.isPrimary || false,
          validationResults: {},
          label: emailAddressAfter?.label || "Other",
          processingStatus: "DONE",
          status:
            emailAddressAfter?.status ||
            emailDeliverabilityStatus.INVALID.emailStatus,
          risk:
            emailAddressAfter?.risk ||
            emailDeliverabilityStatus.INVALID.emailRisk,
          code:
            emailAddressAfter?.code || emailDeliverabilityStatus.INVALID.code,
          reason:
            emailAddressAfter?.reason ||
            emailDeliverabilityStatus.INVALID.reason,
          emailAddressHash: emailHashValue || "",
          check: "unchecked",
          lastRun: FieldValue.serverTimestamp(),
        },
        deliverability: emailAddressAfter?.email
          ? {
              ...emailDeliverabilityStatus.INVALID,
            }
          : null,
        tags: {
          primaryEmail: emailAddressAfter?.email || null,
        },
        failure: [],
        context: {
          accountId,
          recipientId,
          emailAddressId,
        },
        reporcessing,
        processing,
        newEmail,
        refs: {
          emailAddressRef,
          recipientRef,
          emailAddressValidationFailuresRef,
        },
      };

      const emailAddressesSourceRef = db
        .collection(collectionNames.emailAddresses)
        .doc(emailClass.record.emailAddressHash);

      emailClass.refs.emailSourceRef = emailAddressesSourceRef;

      const emailAddressSnapshot = await emailAddressesSourceRef.get();
      const emailAddressExists = emailAddressSnapshot.exists;
      const currentData = emailAddressSnapshot.data();

      let hasOldValidation = false;
      if (emailAddressExists) {
        console.log("emailAddressExists: ", emailAddressExists);
        if (hasKeys(currentData.validationResults)) {
          console.log("hasKeys: ", hasKeys(currentData.validationResults));
          hasOldValidation = true;
        }
      }

      emailClass.source = currentData || {};
      let sourceFound = false;

      try {
        if (
          emailAddressExists &&
          !deletedEmail &&
          !isNil(currentData?.code) &&
          !isNil(currentData?.validationResults) &&
          hasOldValidation &&
          currentData?.code !== "FA" &&
          currentData?.code !== "IE"
        ) {
          console.log("currentData: ", currentData);
          emailClass.record.validationResults = currentData.validationResults;
          emailClass.record.check = currentData.check || "mailgun";
          sourceFound = true;
        }
      } catch (error) {
        emailClass.deliverability = emailDeliverabilityStatus.FAILED;
        emailClass.failure.push({
          message: error?.message,
          point: "source_lookup_check",
        });
      }

      let astridResult = null;
      if (
        (newEmail || reporcessing || processing || emailChanged) &&
        !deletedEmail
      ) {
        if (!sourceFound) {
          console.log("sourceFound: ", sourceFound);
          let astridRefire = false;
          try {
            astridResult = await checkForExistingValidation(
              emailAddressAfter?.email
            );
            if (!astridResult) {
              return;
            }
            astridRefire = astridResult?.refire;
          } catch (error) {
            emailClass.deliverability = emailDeliverabilityStatus.FAILED;
            emailClass.failure.push({
              message: error?.message,
              point: "astrid_request",
            });
          }

          // FORCE MAILGUN: uncomment if needed for debugging
          // astridRefire = true;

          if (astridRefire === false) {
            console.log(
              "astridRefire === false: ",
              astridRefire,
              isValidationActive
            );
            emailClass.record.validationResults = astridResult;
            emailClass.record.check = "astrid";
          } else if (isValidationActive && astridRefire) {
            try {
              emailClass = await validateEmailAddressWithClass(emailClass);
              emailClass.record.check = "mailgun";
            } catch (error) {
              emailClass.deliverability = emailDeliverabilityStatus.FAILED;
              emailClass.failure.push({
                message: error?.message,
                point: "mailgun_request",
              });
            }
            await logControlPanel({
              controlId: "mailgunValidation",
              isActive: isValidationActive,
            });
          }
        }

        emailClass = await processEmailAddressValidationResults(emailClass);

        let record = emailClass.record;
        delete record.isPrimary;
        delete record.label;

        if (isNil(emailClass.source)) {
          console.log("SOURCE: ", emailClass.source);
          const sourceRec = {
            recipients: [recipientId],
            recipientPaths: [recipientRef.path],
            createdAt: FieldValue.serverTimestamp(),
            ...record,
          };
          await emailAddressesSourceRef.set(sourceRec);
        } else {
          const newRec = {
            ...record,
            recipients: FieldValue.arrayUnion(recipientId),
            recipientPaths: FieldValue.arrayUnion(recipientRef.path),
          };
          await emailAddressesSourceRef.update(newRec, { merge: true });
        }

        if (emailClass.failure.length > 0) {
          console.log("FAILURE: ", emailClass.failure);
          const failureRec = {
            accountId,
            emailAddressId,
            recipientId,
            status: emailClass.failure,
          };
          await emailAddressValidationFailuresRef.set(failureRec);
        }

        await recipientRef.update(
          {
            emailDeliverability: emailClass.deliverability,
            searchTags: emailClass.tags,
            emailAddress: emailClass.record.emailAddressHash,
          },
          { merge: true }
        );

        await emailAddressRef.update(emailClass.record, { merge: true });
      }
    }
  );

async function checkForExistingValidation(email) {
  try {
    const astridResult = await astridConnection({
      url: `email-validation?email=${email}`,
      method: "GET",
    });
    return astridResult?.data || null;
  } catch (error) {
    logger.error("Error checking for existing validation:", error);
    return { data: { error: error.message } };
  }
}

function hasKeys(obj) {
  return obj && typeof obj === "object" && Object.keys(obj).length > 0;
}
