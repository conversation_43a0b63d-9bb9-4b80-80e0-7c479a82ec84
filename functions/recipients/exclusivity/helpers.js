const {
  Timestamp,
  FieldValue,
  getFirestore,
} = require("firebase-admin/firestore");
const { getFunctions } = require("firebase-admin/functions");
const { difference, isEmpty } = require("lodash");
const { logger } = require("firebase-functions");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const {
  deliverabilityStatus,
} = require("../../constants/deliverabilityStatus.js");
const { WILL_SEND, GOOD_ADDRESS } = deliverabilityStatus;
const { PRODUCTS } = require("../../constants/products");
const {
  getGroupIdsByProductAssociation,
} = require("../../recipients/productHelpers/productHelpers.js");
const {
  hasAddressEnrolledInExclusiveProduct,
} = require("../../tasks/reconcileAddressClaims/exclusiveMailingHelpers");
const { defineString } = require("firebase-functions/params");
const projectIdEnv = defineString("TITAN_PROJECT_ID");
const projectId = projectIdEnv.value();
const location = "us-central1";
const handleReprocessUserEnteredMailingAddressTask = `projects/${projectId}/locations/${location}/functions/handleReprocessUserEnteredMailingAddressTask`;

module.exports.accountHasExclusivity = async function accountHasExclusivity({
  accountId,
  sourceMailingAddressId,
  db,
}) {
  try {
    const sourceMailingAddressRef = db
      .collection(collectionNames.mailingAddresses)
      .doc(sourceMailingAddressId);
    const sourceMailingAddressSnapshot = await sourceMailingAddressRef.get();
    if (!sourceMailingAddressSnapshot.exists) return false;

    const sourceMailingAddressData = sourceMailingAddressSnapshot.data();
    const { exclusive = [] } = sourceMailingAddressData ?? {};
    return exclusive.some(
      (exclusiveAccountId) => accountId === exclusiveAccountId
    );
  } catch (error) {
    logger.error("Problem checking if account has exclusivity", error);
  }
};

module.exports.addAccountToMailingAddressExclusivity =
  async function addAccountToMailingAddressExclusivity({
    hashedAddressString,
    recipientId,
    accountId,
    db,
  }) {
    try {
      const mailingAddressRef = db
        .collection(collectionNames.mailingAddresses)
        .doc(hashedAddressString);
      const recipientRef = db
        .collection(collectionNames.account)
        .doc(accountId)
        .collection(subCollectionNames.contacts.recipients)
        .doc(recipientId);
      await mailingAddressRef.update({
        exclusive: FieldValue.arrayUnion(accountId),
        recipients: FieldValue.arrayUnion(recipientId),
        recipientPaths: FieldValue.arrayUnion(recipientRef.path),
      });
      await recipientRef.update({
        addressDeliverability: {
          code: WILL_SEND.code,
          message: WILL_SEND.message,
          status: WILL_SEND.status,
        },
      });
    } catch (error) {
      logger.error(
        "Problem updating exclusivity",
        "accountId: ",
        accountId,
        "hashedaddresssting: ",
        hashedAddressString,
        "recipientId: ",
        recipientId,
        "error: ",
        error
      );
    }
  };

module.exports.addAccountToMailingAddressWaitingList =
  async function addAccountToMailingAddressWaitingList({
    waitingListRef,
    recipientRef,
    accountId,
  }) {
    try {
      // Check to see if account is already on WaitingList
      const waitingListSnapshot = await waitingListRef.doc(accountId).get();
      if (waitingListSnapshot.exists) {
        // just update
        await recipientRef.update({
          "addressDeliverability.isExclusive": false,
          "addressDeliverability.isWaitingList": true,
          "addressDeliverability.isValidMailingAddress": true,
        });
      } else {
        await waitingListRef.doc(accountId).set({ dateAdded: Timestamp.now() });
        await recipientRef.update({
          "addressDeliverability.isExclusive": false,
          "addressDeliverability.isWaitingList": true,
          "addressDeliverability.isValidMailingAddress": true,
        });
      }
    } catch (error) {
      logger.error(
        "Problem adding account to Mailing Address Waiting List.",
        "accountId: ",
        accountId,
        "waitingListRef: ",
        waitingListRef,
        "recipientRef: ",
        recipientRef,
        "error: ",
        error
      );
    }
  };

module.exports.createMailingAddressAndAddExclusivity =
  async function createMailingAddressAndAddExclusivity({
    db,
    hashedAddressString,
    recipientId,
    accountId,
    preprocessedAddressString,
    standardizedAddress,
    recipientRef,
    isMigratedValid,
  }) {
    let mailingAddressValues = {
      createdAt: Timestamp.now(),
      validatedAt: Timestamp.now(),
      preprocessedAddressString,
      validationService: "E",
      recipients: FieldValue.arrayUnion(recipientId),
      exclusive: FieldValue.arrayUnion(accountId),
      ...standardizedAddress,
    };

    const mailingAddressesRef = db.collection(collectionNames.mailingAddresses);
    await mailingAddressesRef
      .doc(hashedAddressString)
      .set(mailingAddressValues);
    await recipientRef.update({
      "addressDeliverability.isExclusive": true,
      "addressDeliverability.isWaitingList": false,
      "addressDeliverability.isValidMailingAddress": true,
    });
  };

module.exports.filterExclusiveMailingAddresses =
  async function filterExclusiveMailingAddresses({
    accountId,
    sourceMailingAddressIds,
    db,
  }) {
    try {
      const exclusiveMailingAddresses = [];

      if (isEmpty(sourceMailingAddressIds)) return exclusiveMailingAddresses;

      for (const mailingAddressId of sourceMailingAddressIds) {
        const sourceMailingAddressRef = db
          .collection(collectionNames.mailingAddresses)
          .doc(mailingAddressId);
        const sourceMailingAddressSnapshot =
          await sourceMailingAddressRef.get();
        // if snapshot doesn't exist then skip this iteration
        if (!sourceMailingAddressSnapshot.exists) continue;
        // exclusive contains the accountId that holds exclusivity
        const { exclusive = [] } = sourceMailingAddressSnapshot.data();
        const hasExclusivity = exclusive.some(
          (exclusiveAccountId) => exclusiveAccountId === accountId
        );
        if (hasExclusivity) {
          exclusiveMailingAddresses.push(mailingAddressId);
        }
      }
      return exclusiveMailingAddresses;
    } catch (error) {
      logger.error("Problem filtering exclusive mailing addreses", error);
    }
  };

module.exports.filterWaitingListMailingAddresses =
  async function filterWaitingListMailingAddresses({
    accountId,
    sourceMailingAddressIds,
    db,
  }) {
    const waitingListMailingAddresses = [];
    for (const mailingAddressId of sourceMailingAddressIds) {
      const sourceMailingAddressWaitingListRef = db
        .collection(collectionNames.mailingAddresses)
        .doc(mailingAddressId)
        .collection(subCollectionNames.contacts.waitingList)
        .doc(accountId);
      const sourceMailingAddressWaitingListSnapshot =
        await sourceMailingAddressWaitingListRef.get();

      if (sourceMailingAddressWaitingListSnapshot.exists) {
        waitingListMailingAddresses.push(mailingAddressId);
      }
    }
    return waitingListMailingAddresses;
  };

module.exports.hasMailingProductAssociation =
  async function hasMailingProductAssociation({
    recipientRef,
    recipientGroupCollectionRef,
  }) {
    /**
     * Use this function to check the status of mailing product associations
     * returns true if the recipient is associated with a group that has a mailing product assigned to it
     * returns false if the recipient is not associated with any group or if they are associated with a group without a mailing product
     */
    try {
      const mailingProductIds = getExclusiveProductIds();

      const recipientSnapshot = await recipientRef.get();

      if (!recipientSnapshot.exists) return false;

      const { recipientGroupIds = [] } = recipientSnapshot.data();
      let hasMailingProduct = false;

      if (isEmpty(recipientGroupIds)) {
        hasMailingProduct = false;
        return hasMailingProduct;
      } else {
        const recipientGroupPromises = recipientGroupIds.map((groupId) => {
          const recipientGroupRef = recipientGroupCollectionRef.doc(groupId);
          return recipientGroupRef.get();
        });

        const recipientGroupSnapshots = await Promise.all(
          recipientGroupPromises
        );
        for (const recipientGroupSnapshot of recipientGroupSnapshots) {
          if (!recipientGroupSnapshot.exists) continue;

          const { productPlans = [] } = recipientGroupSnapshot.data();
          // Check if any productPlans match the mailingProductIds
          const hasMailingProductId = productPlans.some((id) =>
            mailingProductIds.includes(id)
          );

          if (hasMailingProductId) {
            hasMailingProduct = true;
            return hasMailingProduct;
          }
        }
        return hasMailingProduct;
      }
    } catch (error) {
      logger.error("Problem checknig for mailing product association", error);
    }
  };

module.exports.hasMailingAddressReleaseRecord =
  async function hasMailingAddressReleaseRecord({
    accountId,
    sourceMailingAddressIds,
  }) {
    try {
      const db = getFirestore();
      for (const sourceMailingAddressId of sourceMailingAddressIds) {
        const releaseExclusivityId = `${accountId}-${sourceMailingAddressId}`;
        const releaseExclusivityRef = db
          .collection(collectionNames.releaseExclusivity)
          .doc(releaseExclusivityId);
        const releaseExclusivitySnapshot = await releaseExclusivityRef.get();
        if (releaseExclusivitySnapshot.exists) return true;
      }
      return false;
    } catch (error) {
      logger.error(
        "Problem checking if mailing address release record exists.",
        error
      );
    }
  };

module.exports.wasRemovedFromExclusiveProductGroup =
  async function wasRemovedFromExclusiveProductGroup({
    recipientGroupIdsBefore,
    recipientGroupIdsAfter,
    recipientGroupCollectionRef,
  }) {
    // was an exclusive mailing address plan removed?
    try {
      const exclusiveProductIds = getExclusiveProductIds();
      // https://lodash.com/docs/4.17.15#difference
      const removedGroupIds = difference(
        recipientGroupIdsBefore,
        recipientGroupIdsAfter
      );
      // determine if the removed groups were assigned mailing products

      if (isEmpty(removedGroupIds)) {
        return false;
      } else {
        // get the removed recipient group data to identify the plans associated with each group
        const recipientGroupPromises = removedGroupIds.map((groupId) => {
          const recipientGroupRef = recipientGroupCollectionRef.doc(groupId);
          return recipientGroupRef.get();
        });

        const recipientGroupSnapshots = await Promise.all(
          recipientGroupPromises
        );
        for (const recipientGroupSnapshot of recipientGroupSnapshots) {
          if (!recipientGroupSnapshot.exists) continue;

          const { productPlans = [] } = recipientGroupSnapshot.data();
          // Check if any productPlans match the mailingProductIds
          const hadExclusiveProductId = productPlans.some((id) =>
            exclusiveProductIds.includes(id)
          );

          return hadExclusiveProductId;
        }
      }
    } catch (error) {
      logger.error("Problem in wasMailingProductRemoved", error);
    }
  };

module.exports.wasAddedToExclusiveProductGroup =
  async function wasAddedToExclusiveProductGroup({
    recipientGroupIdsBefore,
    recipientGroupIdsAfter,
    recipientGroupCollectionRef,
  }) {
    // was a mailing address plan removed?
    try {
      const exclusiveProductIds = getExclusiveProductIds();
      // https://lodash.com/docs/4.17.15#difference
      const addedGroupIds = difference(
        recipientGroupIdsAfter,
        recipientGroupIdsBefore
      );
      // determine if the removed groups were assigned mailing products

      if (isEmpty(addedGroupIds)) {
        return false;
      } else {
        // get the added recipient group data to identify the plans associated with each group
        const recipientGroupPromises = addedGroupIds.map((groupId) => {
          const recipientGroupRef = recipientGroupCollectionRef.doc(groupId);
          return recipientGroupRef.get();
        });

        const recipientGroupSnapshots = await Promise.all(
          recipientGroupPromises
        );

        for (const recipientGroupSnapshot of recipientGroupSnapshots) {
          if (!recipientGroupSnapshot.exists) continue;

          const { productPlans = [] } = recipientGroupSnapshot.data();
          // Check if any productPlans match the exclusiveProductIds
          const hasExclusiveProductId = productPlans.some((id) =>
            exclusiveProductIds.includes(id)
          );

          return hasExclusiveProductId;
        }
      }
    } catch (error) {
      logger.error("Problem in wasAddedToExclusiveProductGroup", error);
    }
  };

module.exports.wasExclusiveProductAddedToGroup =
  async function wasExclusiveProductAddedToGroup({
    productPlansBefore,
    productPlansAfter,
  }) {
    try {
      const exclusiveProductIds = getExclusiveProductIds();
      const addedPlans = difference(productPlansAfter, productPlansBefore);
      const hasExclusiveProductId = addedPlans.some((id) =>
        exclusiveProductIds.includes(id)
      );

      return hasExclusiveProductId;
    } catch (error) {
      logger.error(error);
    }
  };

module.exports.wasExclusiveProductRemovedFromGroup =
  async function wasExclusiveProductRemovedFromGroup({
    productPlansAfter,
    productPlansBefore,
  }) {
    try {
      const exclusiveProductIds = getExclusiveProductIds();
      const removedPlans = difference(productPlansBefore, productPlansAfter);
      const hadExclusiveProductId = removedPlans.some((id) =>
        exclusiveProductIds.includes(id)
      );

      return hadExclusiveProductId;
    } catch (error) {
      logger.error(error);
    }
  };

function getExclusiveProductIds() {
  try {
    const exclusiveProductIds = PRODUCTS.filter(
      (product) => product.isExclusive === true
    ).map((product) => product.plan_id);

    return exclusiveProductIds;
  } catch (error) {
    logger.error("Problem getting exclusive product ids", error);
    return [];
  }
}

module.exports.groupHasExclusiveProduct =
  async function groupHasExclusiveProduct(productPlans) {
    try {
      const exclusiveProductIds = getExclusiveProductIds();
      const hasExclusiveProduct = productPlans.some((id) =>
        exclusiveProductIds.includes(id)
      );
      return hasExclusiveProduct;
    } catch (error) {
      logger.info("Problem checking if group has mailing product", error);
    }
  };

module.exports.addReleaseRecord = async function addReleaseRecord({
  accountId,
  recipientId,
  sourceMailingAddressId,
  action,
  userId,
}) {
  /**
   * A release record schedules a mailing address's release from exclusivity from an account
   * Mailing productId's:
   * 12: Branded Magazines,
   * 1: American Lifestyle Magazine,
   * 3: Start Healthy Magazine,
   * 7: Good To Be Home Magazine,
   * 11: Business in Action
   *
   * Release causes:
   * 1. A recipient is removed from a group that has a mailing product (and is no longer associated with any group assigned a mailing product)
   * 2. A mailing product is removed from a group (and recipients are not associated with any group assigned a mailing products)
   *
   * actions: recipientDeactivated, productRemoved,
   *
   * Assumptions
   * A recipient be associated with multiple groups that have mailing products?
   */
  const db = getFirestore();

  const releaseExclusivityCollectionRef = db.collection(
    collectionNames.releaseExclusivity
  );

  await releaseExclusivityCollectionRef
    .doc(`${accountId}-${sourceMailingAddressId}`)
    .set({
      accountId,
      recipientId,
      sourceMailingAddressId,
      createdAt: Timestamp.now(),
      action,
      userId,
    });
};

module.exports.removeReleaseRecord = async function removeReleaseRecord({
  accountId,
  sourceMailingAddressId,
}) {
  const db = getFirestore();
  const releaseExclusivityRef = db
    .collection(collectionNames.releaseExclusivity)
    .doc(`${accountId}-${sourceMailingAddressId}`);
  try {
    await releaseExclusivityRef.delete();
  } catch (error) {
    logger.error(
      "Problem deleting exclusivity release record: ",
      releaseExclusivityRef.path
    );
  }
};

module.exports.removeRecipientReferencesFromMailingAddresses =
  async function removeRecipientReferencesFromMailingAddresses({
    db,
    recipientId,
    mailingAddressId,
  }) {
    try {
      const mailingAddressesDocumentRef = db
        .collection(collectionNames.mailingAddresses)
        .doc(mailingAddressId);

      await mailingAddressesDocumentRef.update({
        recipients: FieldValue.arrayRemove(recipientId),
      });
    } catch (error) {
      logger.error(
        "Problem removing recipientId: ",
        recipientId,
        "from mailingAddressesId: ",
        mailingAddressId,
        "error: ",
        error
      );
    }
  };

module.exports.removeAccountReferencesFromMailingAddresses =
  async function removeAccountReferencesFromMailingAddresses({
    db,
    accountId,
    mailingAddressId,
  }) {
    try {
      const mailingAddressesDocumentRef = db
        .collection(collectionNames.mailingAddresses)
        .doc(mailingAddressId);

      await mailingAddressesDocumentRef.update({
        exclusive: FieldValue.arrayRemove(accountId),
      });

      const waitingListDocumentRef = mailingAddressesDocumentRef
        .collection(subCollectionNames.contacts.waitingList)
        .doc(accountId);

      const waitingListSnapshot = await waitingListDocumentRef.get();
      if (waitingListSnapshot.exists) {
        await waitingListDocumentRef.delete();
      }
    } catch (error) {
      logger.error(
        "Problem removing accountId: ",
        accountId,
        "from mailingAddressesId: ",
        mailingAddressId,
        "document (exclusive or waiting list)",
        "error: ",
        error
      );
    }
  };

module.exports.addNextInLineExclusivity =
  async function addNextInLineExclusivity(mailingAddressSourceRef) {
    try {
      // grab the address document and its data
      const addressSnap = await mailingAddressSourceRef.get();
      const addressId = addressSnap.id;
      const addressData = addressSnap.data();

      //sort waiting‐list entries oldest to newest
      const waitingListRef = mailingAddressSourceRef.collection(
        subCollectionNames.mailingAddresses.waitingList
      );
      const waitingListSnap = await waitingListRef.get();
      if (waitingListSnap.empty) {
        logger.info("Waiting list is empty. No exclusivity update required.");
        return;
      }
      const waitingListRecords = waitingListSnap.docs
        .map((doc) => ({
          id: doc.id,
          dateAdded: doc.data().dateAdded,
        }))
        .sort((a, b) => a.dateAdded.toMillis() - b.dateAdded.toMillis());

      const exclusiveProductIds = getExclusiveProductIds();
      logger.info("exclusiveProductIds: ", exclusiveProductIds);

      for (const { id: accountId } of waitingListRecords) {
        // does this account have any exclusive products
        const groupIds = await getGroupIdsByProductAssociation({
          accountId,
          products: exclusiveProductIds,
        });
        logger.info("groupIds: ", groupIds);
        if (isEmpty(groupIds)) {
          // no groups with exclusive product drop from wait list & continue
          logger.info(
            `No groups with exclusive product at accountId: ${accountId}`
          );
          await waitingListRef.doc(accountId).delete();
          continue;
        }

        // account has exclusive product check for verified claims
        const recipientPathsWithVerifiedClaimsToAddress =
          await hasAddressEnrolledInExclusiveProduct({
            accountId,
            addressData,
            groupIds,
            sourceAddressId: addressId,
          });

        const recipientIdsWithVerifiedClaimsToAddress =
          recipientPathsWithVerifiedClaimsToAddress.map(
            (path) => path.split("/")[3]
          );
        logger.info(
          `accountId: ${accountId} recipientPathsWithVerifiedClaimsToAddress: `,
          recipientPathsWithVerifiedClaimsToAddress
        );
        if (isEmpty(recipientPathsWithVerifiedClaimsToAddress)) {
          // has the product but no verified claims, drop from wait list
          await waitingListRef.doc(accountId).delete();
          continue;
        } else {
          // has the product and verified claims, grant exclusivity
          logger.info(
            `has product and verified claims, grant exclusivity to accountId: ${accountId}`
          );
          await Promise.all([
            mailingAddressSourceRef.update({
              exclusive: FieldValue.arrayUnion(accountId),
              recipientPaths: recipientPathsWithVerifiedClaimsToAddress,
              recipients: recipientIdsWithVerifiedClaimsToAddress,
            }),
            waitingListRef.doc(accountId).delete(),
          ]);
          const reprocessTaskPayload = {
            recipientPaths: recipientPathsWithVerifiedClaimsToAddress,
          };
          const functions = getFunctions();
          const taskQueue = functions.taskQueue(
            handleReprocessUserEnteredMailingAddressTask
          );
          await taskQueue.enqueue(reprocessTaskPayload);
          logger.info(
            `Granted exclusivity to account=${accountId} and removed from waiting list.`
          );
          return;
        }
      }

      logger.info("No accounts in waiting list qualified for exclusivity.");
    } catch (error) {
      logger.error("Error in addNextInLineExclusivity:", error);
    }
  };

module.exports.getNextInLineExclusivity = function getNextInLineExclusivity(
  waitingListRecords
) {
  if (isEmpty(waitingListRecords)) {
    return;
  }

  let oldestRecord = waitingListRecords[0];
  for (let i = 1; i < waitingListRecords.length; i++) {
    if (
      waitingListRecords[i].dateAdded.toMillis() <
      oldestRecord.dateAdded.toMillis()
    ) {
      oldestRecord = waitingListRecords[i];
    }
  }
  return oldestRecord;
};

module.exports.releaseExclusivity = async function releaseExclusivity({
  accountId,
  sourceMailingAddressRef,
  recipientRef,
  action,
}) {
  /**
   * Removes the account relationship form the source MailingAddresses collection or WaitingList subcollection (exclusivty and waiting list)
   * also removes the recipient id from the recipients array on the source mailing address on the recipientDeactivated action
   */
  try {
    await sourceMailingAddressRef.update({
      exclusive: FieldValue.arrayRemove(accountId),
      ...(action === "recipientDeactivated" && {
        recipients: FieldValue.arrayRemove(recipientRef.id),
      }),
    });

    const userEnteredMailingAddressRef = recipientRef
      .collection(subCollectionNames.contacts.userEnteredMailingAddress)
      .doc("0");

    const waitingListRef = sourceMailingAddressRef
      .collection(subCollectionNames.contacts.waitingList)
      .doc(accountId);

    const waitingListDoc = await waitingListRef.get();

    if (waitingListDoc.exists) {
      await waitingListRef.delete();
    }

    await Promise.all([
      userEnteredMailingAddressRef
        .collection("History")
        .add({ type: "exclusivityReleased", createdAt: Timestamp.now() }),
      recipientRef.update({
        addressDeliverability: {
          code: GOOD_ADDRESS.code,
          message: GOOD_ADDRESS.message,
          status: GOOD_ADDRESS.status,
        },
      }),
    ]);
  } catch (error) {
    logger.error(
      "Error releasing exclusivity for accountId:",
      accountId,
      "MailingAddresses path: ",
      sourceMailingAddressRef.path,
      "Error:",
      error
    );
  }
};
