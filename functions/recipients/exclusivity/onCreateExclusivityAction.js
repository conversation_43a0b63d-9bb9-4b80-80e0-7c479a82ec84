const { onDocumentCreated } = require("firebase-functions/v2/firestore");
const { Timestamp, FieldValue } = require("firebase-admin/firestore");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const { logToFirestore } = require("../../utils/functionLogger");
const { collectionHistory } = require("../../utils/collectionHistory");

exports.onCreateExclusivityAction = (db) =>
  onDocumentCreated(
    `${collectionNames.exclusivityActions}/{id}`,
    async (event) => {
      const { id } = event.params || {};
      const data = event.data.data();
      const { mailingAddressId, targetId, sourceId, userId, who, action } = data;

      // Get user information from the authentication context
      const authUserId = userId;

      try {

        const addressSnap = await db.collection(collectionNames.mailingAddresses).doc(mailingAddressId).get();
        const addressData = addressSnap.data();

        const {exclusive=[], recipients=[], recipientPaths=[]} = addressData || {};
        const previousData = {
          exclusive,
          recipients,
          recipientPaths
        }
        const changeRequest = {
          targetId,
          sourceId,
          action
        }

        // Track which accounts need revalidation
        const accountsToRevalidate = new Set();

        // Process the action based on the action type
        if (action === "remove" && mailingAddressId) {
          // Remove all accounts from exclusive array
          for (const exclusiveId of exclusive) {
            await removeExclusiveByAccount(db, {
              mailingAddressId,
              accountId: exclusiveId
            });
            accountsToRevalidate.add(exclusiveId);
          }
        } else if (action === "share" && mailingAddressId && targetId) {
          await addExclusiveByAccount(db, {
            mailingAddressId,
            accountId: targetId
          });
          accountsToRevalidate.add(targetId);
        } else if (action === "promote" && mailingAddressId && targetId) {
          // Replace all existing exclusive accounts with the target account
          await replaceAllExclusiveAccounts(db, {
            mailingAddressId,
            accountId: targetId,
            oldExclusiveAccounts: exclusive
          });
          
          // Add target account for revalidation
          accountsToRevalidate.add(targetId);
          
          // Add all previous exclusive accounts for revalidation
          for (const exclusiveId of exclusive) {
            accountsToRevalidate.add(exclusiveId);
          }
        }

        // Revalidate all affected accounts once
        for (const accountId of accountsToRevalidate) {
          await revalidateByAccount(db, {
            mailingAddressId,
            accountId
          });
        }

        // Save to history before deletion
        const history = {
          action: action,
          previous: previousData,
          changes: changeRequest,
          userId: authUserId,
          who,
          timestamp: Timestamp.now()
        };
        // Add HISTORY to Mailing Address
        await db.collection(collectionNames.mailingAddresses).doc(mailingAddressId).collection(subCollectionNames.mailingAddresses.exclusiveHistory).add(history);

        // Delete the document after processing
        await db.collection(collectionNames.exclusivityActions).doc(id).delete();

        // Log the deletion
        await logToFirestore(db, {
          action: `${action} exclusivity on ${mailingAddressId}`,
          documentId: mailingAddressId,
          userId: authUserId,
          who,
          processedAction: action,
          timestamp: Timestamp.now()
        });

        console.log(`Successfully processed and deleted exclusivity action: ${id}`);

      } catch (error) {
        console.error("Error in onCreateExclusivityAction:", error);
        
        await logToFirestore(db, {
          action: "error_create_exclusivity_action",
          error: error.message,
          documentId: id,
          userId: authUserId,
          who,
          timestamp: Timestamp.now()
        });

        // Optionally delete the document even on error
        try {
          await db.collection(collectionNames.exclusivityActions).doc(id).delete();
        } catch (deleteError) {
          console.error("Error deleting document after failure:", deleteError);
        }
      }
    }
  );

// Helper functions updated for Admin SDK
async function removeExclusiveByAccount(db, {
  mailingAddressId,
  accountId
}) {
  try {
    const addressRef = db.collection("MailingAddresses").doc(mailingAddressId);
    await addWaitingList(db, {
      mailingAddressId,
      accountId
    });
    
    // Use FieldValue.arrayRemove to remove the specific accountId from the exclusive array
    await addressRef.update({
      exclusive: FieldValue.arrayRemove(accountId)
    });
    
  } catch (error) {
    console.error("Error removing account from exclusive array:", error);
    throw error; // Re-throw to allow calling code to handle it
  }
}

async function addExclusiveByAccount(db, {
  mailingAddressId,
  accountId
}) {
  try {
    const addressRef = db.collection("MailingAddresses").doc(mailingAddressId);
    await removeWaitingList(db, {
      mailingAddressId,
      accountId
    });
    
    // Use FieldValue.arrayUnion to add the specific accountId to the exclusive array
    await addressRef.update({
      exclusive: FieldValue.arrayUnion(accountId)
    });
    
  } catch (error) {
    console.error("Error adding account to exclusive array:", error);
    throw error;
  }
}

async function removeWaitingList(db, {
  mailingAddressId,
  accountId
}) {
  try {
    const waitinglistRef = db.collection("MailingAddresses").doc(mailingAddressId)
      .collection("WaitingList").doc(accountId);
    
    await waitinglistRef.delete();
    
  } catch (error) {
    // Log but don't throw - it's OK if the waiting list entry doesn't exist
    console.log(`Note: WaitingList entry for account ${accountId} may not exist:`, error.message);
  }
}

async function addWaitingList(db, {
  mailingAddressId,
  accountId
}) {
  try {
    const waitinglistRef = db.collection("MailingAddresses").doc(mailingAddressId)
      .collection("WaitingList").doc(accountId);
    
    // Calculate timestamp for 10 years ago to ensure this account is first in line
    const tenYearsAgo = new Date();
    tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);
    const tenYearsAgoTimestamp = Timestamp.fromDate(tenYearsAgo);
    
    const waitingListEntry = {
      dateAdded: tenYearsAgoTimestamp
    };
    
    await waitinglistRef.set(waitingListEntry);
    
  } catch (error) {
    console.error("Error adding waitingList Item:", error);
    throw error;
  }
}

async function replaceAllExclusiveAccounts(db, {
  mailingAddressId,
  accountId,
  oldExclusiveAccounts
}) {
  try {
    console.log(`Replacing all exclusive accounts with ${accountId} for address ${mailingAddressId}`);
    const addressRef = db.collection("MailingAddresses").doc(mailingAddressId);

    // Use a transaction to ensure atomic operation
    await db.runTransaction(async (transaction) => {
      // Get the current document to ensure it exists
      const addressDoc = await transaction.get(addressRef);
      
      if (!addressDoc.exists) {
        throw new Error(`MailingAddress ${mailingAddressId} does not exist`);
      }
      
      // Check if new accountId is already in exclusive array
      const currentExclusive = addressDoc.data().exclusive || [];
      if (currentExclusive.includes(accountId) && currentExclusive.length === 1) {
        console.log(`Account ${accountId} is already the only account in the exclusive array for address ${mailingAddressId}`);
        return; // No need to do anything
      }
      
      // Replace the entire exclusive array with just the new account
      transaction.update(addressRef, {
        exclusive: [accountId],
        updatedAt: FieldValue.serverTimestamp()
      });
    });
    
    // Handle waiting list operations outside the transaction
    // Remove new account from waiting list
    await removeWaitingList(db, {
      mailingAddressId,
      accountId
    });
    
    // Add all old exclusive accounts to waiting list
    for (const oldAccountId of oldExclusiveAccounts) {
      if (oldAccountId !== accountId) {
        await addWaitingList(db, {
          mailingAddressId,
          accountId: oldAccountId
        });
      }
    }
    
    console.log(`Successfully replaced all exclusive accounts with ${accountId} for address ${mailingAddressId}`);
  } catch (error) {
    console.error("Error replacing exclusive accounts:", error);
    throw error;
  }
}

async function revalidateByAccount(db, {
  mailingAddressId,
  accountId
}) {
  try {
    const recipientsQuery = db.collection("Account")
      .doc(accountId)
      .collection("Recipients")
      .where("mailingAddress", "==", mailingAddressId);
    
    const recipientsSnapshot = await recipientsQuery.get();
    
    if (recipientsSnapshot.empty) {
      console.log(`No recipients found for account ${accountId} with mailing address ${mailingAddressId}`);
      return;
    }
    
    console.log(`Found ${recipientsSnapshot.size} recipients to revalidate for account ${accountId}`);
    
    // Process all recipients in parallel
    const revalidationPromises = recipientsSnapshot.docs.map(async (doc) => {
      const recipientId = doc.id;
      
      try {
        // Update the recipient's mailing address processing status
        await db.collection("Account")
          .doc(accountId)
          .collection("Recipients")
          .doc(recipientId)
          .collection("UserEnteredMailingAddress")
          .doc("0")
          .update({
            processingStatus: "REPROCESS",
            reprocessRequestedAt: FieldValue.serverTimestamp()
          });
        
        console.log(`Revalidated recipient ${recipientId} for account ${accountId} in mailing address ${mailingAddressId}`);
      } catch (updateError) {
        console.error(`Error updating recipient ${recipientId}:`, updateError.message);
        // Continue with other recipients even if one fails
      }
    });
    
    await Promise.all(revalidationPromises);
    
  } catch (error) {
    console.error("Error revalidating account:", error);
    throw error;
  }
}