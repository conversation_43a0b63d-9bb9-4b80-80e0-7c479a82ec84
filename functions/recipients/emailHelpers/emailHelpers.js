const { getFirestore } = require("firebase-admin/firestore");
const { difference } = require("lodash");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const { PRODUCTS } = require("../../constants/products");

const emailProducts = PRODUCTS.filter((product) => product.method === "email");
const emailProductIds = emailProducts.map((product) => product.planId);

exports.hasEmailEnrolledInProduct = async function hasEmailEnrolledInProduct({
  accountId,
  email,
  groupIds = [], // pass groupIds with product
}) {
  const db = getFirestore();

  try {
    // Base query for active recipients at that email
    let baseQuery;

    baseQuery = db
      .collection("Account")
      .doc(accountId.toString())
      .collection("Recipients")
      .where("isActive", "==", true)
      .where("searchTags.primaryEmail", "==", email);

    //  split the array into chunks of a set size
    function chunkArray(arr, size) {
      const chunks = [];
      for (let i = 0; i < arr.length; i += size) {
        chunks.push(arr.slice(i, i + size));
      }
      return chunks;
    }

    let matchingDocs = [];

    if (Array.isArray(groupIds) && groupIds.length > 0) {
      const MAX_ARRAY_CONTAINS = 10;

      if (groupIds.length <= MAX_ARRAY_CONTAINS) {
        const snap = await baseQuery
          .where("recipientGroupIds", "array-contains-any", groupIds)
          .get();
        matchingDocs = snap.docs;
      } else {
        // Multiple queries in chunks of 10
        const seen = new Map();
        for (const chunk of chunkArray(groupIds, MAX_ARRAY_CONTAINS)) {
          const snap = await baseQuery
            .where("recipientGroupIds", "array-contains-any", chunk)
            .get();
          for (const doc of snap.docs) {
            seen.set(doc.id, doc);
          }
        }
        matchingDocs = Array.from(seen.values());
      }
    }

    if (matchingDocs.length === 0) {
      console.log(
        `No active recipients with product at ${email}. AccountId: ${accountId}` +
          (groupIds.length ? ` in groups [${groupIds.join(", ")}]` : "")
      );
      return [];
    }

    console.log(
      `Active Recipients with product at ${email}. AccountId: ${accountId}`
    );
    return matchingDocs.map((doc) => doc.ref.path);
  } catch (error) {
    console.error("Problem in hasEmailEnrolledInProduct", error);
    throw error;
  }
};

exports.wasEmailProductAddedToGroup =
  async function wasExclusiveProductAddedToGroup({
    productPlansBefore,
    productPlansAfter,
  }) {
    try {
      const addedPlans = difference(productPlansAfter, productPlansBefore);
      const hasEmailProductId = addedPlans.some((id) =>
        emailProductIds.includes(id)
      );

      return hasEmailProductId;
    } catch (error) {
      console.error(error);
    }
  };

exports.handleRecipientAddedToEmailProductGroup =
  async function handleRecipientAddedToEmailProductGroup({
    accountId,
    recipientBefore,
    recipientAfter,
    recipientRef,
  }) {
    const db = getFirestore();
    const recipientGroupIdsBefore = recipientBefore.recipientGroupIds;
    const recipientGroupIdsAfter = recipientAfter.recipientGroupIds;
    const addedRecipientGroupIds = difference(
      recipientGroupIdsAfter,
      recipientGroupIdsBefore
    );

    for (const groupId of addedRecipientGroupIds) {
      const groupRef = db
        .collection(collectionNames.account)
        .doc(accountId)
        .collection(subCollectionNames.contacts.recipientGroups)
        .doc(groupId);
      const groupSnapshot = await groupRef.get();
      if (!groupSnapshot.exists) continue;
      const { productPlans = [] } = groupSnapshot.data();

      const hasEmailProductId = productPlans.some((id) =>
        emailProductIds.includes(id)
      );

      if (hasEmailProductId) {
        const recipientEmailAddressesRef = recipientRef.collection(
          subCollectionNames.contacts.emailAddresses
        );
        const recipientEmailAddressesSnapshot =
          await recipientEmailAddressesRef.get();

        if (!recipientEmailAddressesSnapshot.empty) {
          const recipientEmailAddresses = recipientEmailAddressesSnapshot.docs;
          for (const emailDoc of recipientEmailAddresses) {
            console.log(
              "reprocessing email",
              emailDoc.id,
              " path: ",
              emailDoc.ref.path
            );
            const emailData = emailDoc.data();
            if (emailData.processingStatus !== "REPROCESS") {
              await emailDoc.ref.update({
                processingStatus: "REPROCESS",
              });
            }
          }
        }
      }
    }
  };
