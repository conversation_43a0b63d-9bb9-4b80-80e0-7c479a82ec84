const { onDocumentCreated } = require("firebase-functions/v2/firestore");
const { Timestamp } = require("firebase-admin/firestore");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { logToFirestore } = require("../utils/functionLogger");
const { deliverabilityStatus } = require("../constants/deliverabilityStatus");
const { NO_MAILING_ADDRESS } = deliverabilityStatus;
const { collectionHistory } = require("../utils/collectionHistory");

exports.onCreateRecipient = (db) =>
  onDocumentCreated(
    `${collectionNames.account}/{accountId}/${subCollectionNames.contacts.recipients}/{recipientId}`,
    async (event) => {
      const { accountId, recipientId } = event.params || {};
      const recipientRef = event.data?.ref;
      const recipientData = event.data.data();

       // Get user information from the authentication context
      const userId = event.auth?.uid || "system";
      const userEmail = event.auth?.token?.email || "system";

      // Call collectionHistory with user information in context
      await collectionHistory({
        collection: `${collectionNames.account}/${accountId}/${subCollectionNames.contacts.recipients}`,
        id: recipientId,
        before: null,
        after: recipientData,
        userId,
        userEmail,
        context: {
          auth: {
            uid: userId,
            token: { email: userEmail }
          },
          // Include the raw request if available
          rawRequest: event.rawRequest
        },
        action: 'CREATE'
      });

      await initializeDeliverability(recipientRef);
    }
  );

async function initializeDeliverability(recipientRef) {
  await recipientRef.update({
    "addressDeliverability.status": NO_MAILING_ADDRESS.status,
    "addressDeliverability.code": NO_MAILING_ADDRESS.code,
    "addressDeliverability.message": NO_MAILING_ADDRESS.message,
  });
}
