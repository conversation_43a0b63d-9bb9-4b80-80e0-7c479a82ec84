const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const {
  wasAddedToExclusiveProductGroup,
  wasRemovedFromExclusiveProductGroup,
  hasMailingAddressReleaseRecord,
  hasMailingProductAssociation,
  filterExclusiveMailingAddresses,
  filterWaitingListMailingAddresses,
} = require("../recipients/exclusivity/helpers");
const { logger } = require("firebase-functions");
const { addToTopic } = require("../utils/pubsub/addToTopic");
const { isNil, isEmpty, isEqual } = require("lodash");
const {
  startRecipientGroupCalc,
} = require("../utils/calculate/recipientGroupCalc");
const { collectionHistory } = require("../utils/collectionHistory");
const { getFirestore } = require("firebase-admin/firestore");
const diff = require("deep-diff");
const { topic } = require("firebase-functions/v1/pubsub");
const {
  mailingAddressStateCases,
} = require("../constants/mailingAddressStateCases");
const { REPROCESS, DONE } = mailingAddressStateCases;

/**
 * Recalculates recipient group summaries based on changes in a recipient's record.
 * **Note:**Does not rewrite to the recipient record
 *
 * This function is used within the recipient trigger function when a recipient document is updated. It compares the previous
 * (recipientBefore) and current (recipientAfter) states of the recipient to determine if any
 * key properties have changed that would require recalculating group summaries. The following
 * checks are performed:
 *
 * - **Group Membership Change:**
 *   If the recipient's group IDs have changed, it recalculates summaries for the groups from the
 *   previous state.
 *
 * - **Deliverability Attribute Changes:**
 *   The function checks if any of the following properties have changed:
 *     - Address deliverability code
 *     - Recipient active status
 *     - Mailings paused status
 *     - Override deliverability address
 *     - Deletion of a formatted mailing address (via search tags)
 *
 * If any of these changes are detected, it triggers recalculation for each group in the recipient's
 * current group IDs by invoking the `startRecipientGroupCalc` function.
 * **Note:** This startRecipientGroupCalc function does not update the recipient record itself. It writes to the metaQueue collection
 *
 * @param {string} accountId - The account identifier.
 * @param {Object} recipientBefore - The recipient data before the update.
 * @param {Object} recipientAfter - The recipient data after the update.
 *
 * @returns {Promise<void>} A promise that resolves once all relevant group summaries have been recalculated.
 */
exports.handleGroupSummaryRecalculation =
  async function handleGroupSummaryRecalculation({
    accountId,
    recipientBefore,
    recipientAfter,
  }) {
    try {
      // =============================================================
      // Section: Calculate Recipient Group Summaries
      // =============================================================

      // calculate deliverability switch
      let calculateDeliverabilityForGroups = false;

      // ensure we're comparing arrays
      const activeRecipientGroupIds = Array.isArray(
        recipientAfter?.recipientGroupIds
      )
        ? recipientAfter.recipientGroupIds
        : [];
      const previousRecipientGroupIds = Array.isArray(
        recipientBefore?.recipientGroupIds
      )
        ? recipientBefore.recipientGroupIds
        : [];

      // compares array contents
      const recipientGroupsChanged = isEqual(
        activeRecipientGroupIds,
        previousRecipientGroupIds
      );

      /**
       * If groups changed set trigger to calculate group summaries
       */
      if (recipientGroupsChanged) {
        calculateDeliverabilityForGroups = true;
      }

      const addressDeliverabilityBefore =
        recipientBefore?.addressDeliverability?.code;

      const addressDeliverabilityAfter =
        recipientAfter?.addressDeliverability?.code;

      /**
       * if addressDeliverability changed set trigger to calculate group summaries
       */
      if (
        !calculateDeliverabilityForGroups &&
        addressDeliverabilityBefore !== addressDeliverabilityAfter
      ) {
        calculateDeliverabilityForGroups = true;
      }

      /**
       * if recipient active status changed set trigger to calculate group summaries
       */

      if (
        !calculateDeliverabilityForGroups &&
        recipientBefore.isActive !== recipientAfter.isActive
      ) {
        calculateDeliverabilityForGroups = true;
      }

      /**
       * if mailing paused changed set trigger to calculate group summaries
       */
      if (
        !calculateDeliverabilityForGroups &&
        recipientBefore.mailingsPaused !== recipientAfter.mailingsPaused
      ) {
        calculateDeliverabilityForGroups = true;
      }

      /**
       * if Send Anyway (Override Deliverability) Changed set trigger to calculate group summaries
       */
      if (
        !calculateDeliverabilityForGroups &&
        recipientBefore?.overrideDeliverability?.address !==
          recipientAfter?.overrideDeliverability?.address
      ) {
        calculateDeliverabilityForGroups = true;
      }

      /**
       * If formatted mailing address is deleted set trigger to calculate group summaries
       */
      if (
        !calculateDeliverabilityForGroups &&
        recipientBefore?.searchTags?.formattedMailingAddress &&
        isNil(recipientAfter?.searchTags?.formattedMailingAddress)
      ) {
        calculateDeliverabilityForGroups = true;
      }

      /**
       * Calculate Group Summaries if switch is triggered
       */
      if (calculateDeliverabilityForGroups) {
        for (const afterGroupId of recipientAfter.recipientGroupIds) {
          await startRecipientGroupCalc({
            accountId,
            recipientGroupId: afterGroupId,
          });
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

/**
 * Handles exclusivity release processing for a recipient based on changes in their status and mailing product associations.
 *
 * This function compares the recipient's state before and after an update to determine if an exclusivity action is required.
 * It performs the following checks:
 *
 * - Determines if the recipient's active status has changed ( isActive true or false ).
 * - Extracts the mailing address IDs from the updated recipient data and filters them to identify
 *   mailing addresses that are either exclusively held or on a waiting list.
 * - Checks if the recipient's group memberships have changed in a way that implies a mailing product
 *   was either added or removed.
 * - Verifies if the recipient is currently associated with any group that has a mailing product.
 *
 * Based on these checks, it publishes a message to a Pub/Sub topic to either schedule or remove an exclusivity release:
 *
 * - If a mailing product was removed, the recipient has no mailing product association, and they have exclusive
 *   or waiting list mailing addresses, a message is sent to schedule an exclusivity release.
 *
 * - If a mailing product was added and an exclusivity release record exists, a message is sent to remove that record.
 *
 * - If the recipient was deactivated and has exclusive or waiting list mailing addresses, a message is sent to schedule
 *   an exclusivity release.
 *
 * - If the recipient was activated and an exclusivity release record exists, a message is sent to remove that record.
 *
 * @param {string} accountId - The account identifier.
 * @param {Object} recipientBefore - The recipient's state prior to the update.
 * @param {Object} recipientAfter - The recipient's state following the update.
 * @param {Object} recipientRef - Firestore reference to the recipient document.
 *
 * @returns {Promise<void>} A promise that resolves when the exclusivity handling process is complete.
 */
exports.handleExclusivity = async function handleExclusivity({
  accountId,
  recipientBefore,
  recipientAfter,
  recipientRef,
}) {
  try {
    // =================================================
    // Section: Exclusivity Release
    // =================================================
    const db = getFirestore();
    // using deep-diff to log what changed
    const diffResult = diff(recipientBefore, recipientAfter);
    const plainDiff = JSON.parse(JSON.stringify(diffResult));

    const wasInactiveToActive =
      recipientBefore.isActive === false && recipientAfter.isActive === true;

    const wasActiveToInactive =
      recipientBefore.isActive === true && recipientAfter.isActive === false;

    const sourceMailingAddressIds =
      recipientAfter?.mailingAddresses?.map((addressObj) => addressObj?.id) ??
      [];

    const exclusiveMailingAddresses = await filterExclusiveMailingAddresses({
      db,
      accountId,
      sourceMailingAddressIds,
    });

    const waitingListMailingAddresses = await filterWaitingListMailingAddresses(
      {
        db,
        accountId,
        sourceMailingAddressIds,
      }
    );

    // check if any mailing addresses associated with recipient are exclusively held or waiting list
    // for the current accountId
    const hasExclusivityOrWaitingList =
      !isEmpty(exclusiveMailingAddresses) ||
      !isEmpty(waitingListMailingAddresses);

    const recipientId = recipientRef.id;
    const recipientGroupCollectionRef = db
      .collection(collectionNames.account)
      .doc(accountId)
      .collection(subCollectionNames.contacts.recipientGroups);

    // Check if mailing address product was removed
    const recipientGroupIdsBefore = recipientBefore.recipientGroupIds;
    const recipientGroupIdsAfter = recipientAfter.recipientGroupIds;

    const wasExclusiveProductRemoved =
      await wasRemovedFromExclusiveProductGroup({
        recipientGroupIdsBefore,
        recipientGroupIdsAfter,
        recipientGroupCollectionRef,
      });

    // Check if mailing address product was added
    const wasExclusiveProductAdded = await wasAddedToExclusiveProductGroup({
      recipientGroupIdsBefore,
      recipientGroupIdsAfter,
      recipientGroupCollectionRef,
    });

    // Check if recipient is assigned to any group with a mailing product
    const hasProductAssociation = await hasMailingProductAssociation({
      recipientRef,
      recipientGroupCollectionRef,
    });

    if (
      wasExclusiveProductRemoved &&
      !hasProductAssociation &&
      hasExclusivityOrWaitingList
    ) {
      /**
       * schedule exclusivity release creation if mailing product removed AND
       * recipient is not assigned to any group with a mailing product AND
       * the account has exclusivity or waiting list
       */

      const message = JSON.stringify({
        accountId,
        recipientId,
        action: "productRemoved",
        userId: "", // TODO associate userId to action
      });
      await addToTopic({
        topic: "schedule-release-exclusivity",
        message,
      });
      // write scheduled exclusivity release to recipient history
      await collectionHistory({
        collection: `${collectionNames.account}/${accountId}/${subCollectionNames.contacts.recipients}`,
        id: recipientId,
        before: recipientBefore,
        after: recipientAfter,
        context: {
          wasInactiveToActive,
          wasActiveToInactive,
          wasExclusiveProductRemoved,
          wasExclusiveProductAdded,
          hasExclusivityOrWaitingList,
          hasProductAssociation,
          diff: plainDiff,
          action: "productRemoved",
          topic: "schedule-release-exclusivity",
        },
        action: "UPDATE",
      });
    } else if (wasExclusiveProductAdded) {
      /**
       * Remove Exclusivity Release Records, if present
       * if recipient is now associated with mailing products
       * check if there is an exclusivity release record
       */
      const hasReleaseRecord = await hasMailingAddressReleaseRecord({
        accountId,
        sourceMailingAddressIds,
      });
      if (hasReleaseRecord) {
        const message = JSON.stringify({
          accountId,
          recipientId,
          action: "productAdded",
          userId: "", // TODO associate userId to action
        });
        await addToTopic({
          topic: "remove-release-exclusivity",
          message,
        });
        await collectionHistory({
          collection: `${collectionNames.account}/${accountId}/${subCollectionNames.contacts.recipients}`,
          id: recipientId,
          before: recipientBefore,
          after: recipientAfter,
          context: {
            wasInactiveToActive,
            wasActiveToInactive,
            wasExclusiveProductRemoved,
            wasExclusiveProductAdded,
            hasExclusivityOrWaitingList,
            hasProductAssociation,
            diff: plainDiff,
            action: "productAdded",
            topic: "remove-release-exclusivity",
          },
          action: "UPDATE",
        });
      }
      /**
       * Revalidate mailing address
       *
       */
      const userEnteredMailingAddressRef = recipientRef
        .collection("UserEnteredMailingAddress")
        .doc("0");

      const userEnteredMailingAddressSnapshot =
        await userEnteredMailingAddressRef.get();

      if (!userEnteredMailingAddressSnapshot.exists) return;

      const { processingStatus } = userEnteredMailingAddressSnapshot.data();

      // Its possible that a record has been "stuck" in REPROCESS state
      // if that is the case then we temporarily write to the DONE state
      // so that we can trigger a REPROCESS
      if (processingStatus === REPROCESS) {
        await userEnteredMailingAddressRef.update({
          processingStatus: DONE,
        });
      }

      await userEnteredMailingAddressRef.update({
        processingStatus: REPROCESS,
      });
    } else if (wasActiveToInactive && hasExclusivityOrWaitingList) {
      /**
       * schedule exclusivity release creation if recipient is set to inactive AND
       * they are associated with an exclusive or waitinglisted mailing address
       */
      const message = JSON.stringify({
        accountId,
        recipientId,
        action: "recipientDeactivated",
        userId: "", // TODO associate userId to action
      });
      await addToTopic({
        topic: "schedule-release-exclusivity",
        message,
      });
      await collectionHistory({
        collection: `${collectionNames.account}/${accountId}/${subCollectionNames.contacts.recipients}`,
        id: recipientId,
        before: recipientBefore,
        after: recipientAfter,
        context: {
          wasInactiveToActive,
          wasActiveToInactive,
          wasExclusiveProductRemoved,
          wasExclusiveProductAdded,
          hasExclusivityOrWaitingList,
          hasProductAssociation,
          diff: plainDiff,
          action: "recipientDeactivated",
          topic: "schedule-release-exclusivity",
        },
        action: "UPDATE",
      });
    } else if (wasInactiveToActive && hasExclusivityOrWaitingList) {
      /**
       * remove exclusivity release creation if recipient is set to active AND
       * they are associated with an exclusive or waitinglisted mailing address
       */
      const hasReleaseRecord = await hasMailingAddressReleaseRecord({
        accountId,
        sourceMailingAddressIds,
      });
      if (hasReleaseRecord) {
        const message = JSON.stringify({
          accountId,
          recipientId,
          action: "recipientActivated",
          userId: "", // TODO associate userId to action
        });
        await addToTopic({
          topic: "remove-release-exclusivity",
          message,
        });
        await collectionHistory({
          collection: `${collectionNames.account}/${accountId}/${subCollectionNames.contacts.recipients}`,
          id: recipientId,
          before: recipientBefore,
          after: recipientAfter,
          context: {
            wasInactiveToActive,
            wasActiveToInactive,
            wasExclusiveProductRemoved,
            wasExclusiveProductAdded,
            hasExclusivityOrWaitingList,
            hasProductAssociation,
            diff: plainDiff,
            action: "recipientActivated",
            topic: "remove-release-exclusivity",
          },
          action: "UPDATE",
        });
      }
    }
  } catch (error) {
    console.log(error);
  }
};
