// https://firebase.google.com/docs/functions/gcp-storage-events?gen=2nd
const { onObjectFinalized } = require("firebase-functions/v2/storage");
const { getStorage } = require("firebase-admin/storage");
const { defineString } = require("firebase-functions/params");
const {
  handleRecipientFileImport,
} = require("./recipient-file-import/handleRecipientFileImport.js");

const RECIPIENT_IMPORTER_BUCKET = defineString(
  "TITAN_STORAGE_RECIPIENT_IMPORTER"
);
const RECIPIENT_IMPORTS_PATH = "recipient-file-imports/";

/**
 *
 * onObjectFinalized
 * Sent when a new object (or a new generation of an existing object) is successfully created in the bucket.
 * This includes copying or rewriting an existing object. A failed upload does not trigger this event.
 */

exports.onRecipientFileImport = (db) =>
  onObjectFinalized(
    {
      bucket: RECIPIENT_IMPORTER_BUCKET,
      memory: "1GiB",
      timeoutSeconds: 300,
    },
    async (event) => {
      const fileBucket = event.data.bucket; // Storage bucket containing the file.
      const filePath = event.data.name; // File path in the bucket.
      const contentType = event.data.contentType; // File content type.
      const bucket = getStorage().bucket(fileBucket);
      const file = bucket.file(filePath);

      const [url] = await file.getSignedUrl({
        version: "v4",
        action: "read",
        expires: Date.now() + 60 * 60 * 1000, // 1 hour from now
      });
      /**
       * Handle Recipient Uploads
       */
      if (filePath.includes(RECIPIENT_IMPORTS_PATH)) {
        await handleRecipientFileImport({
          db,
          bucket,
          filePath,
          contentType,
        });
        return;
      }
    }
  );
