exports.expectedHeaders = [
  {
    label: "First Name",
    value: "name.firstName",
    type: "name",
    isDisabled: false,
  },
  {
    label: "Last Name",
    value: "name.lastName",
    type: "name",
    isDisabled: false,
  },
  {
    label: "Nickname",
    value: "name.nickname",
    type: "name",
    isDisabled: false,
  },
  {
    label: "Phonetic First Name",
    value: "name.phoneticFirstName",
    type: "name",
    isDisabled: false,
  },
  {
    label: "Phonetic Last Name",
    value: "name.phoneticLastName",
    type: "name",
    isDisabled: false,
  },
  { label: "Prefix", value: "name.prefix", type: "name", isDisabled: false },
  { label: "Suffix", value: "name.suffix", type: "name", isDisabled: false },

  // significant other details

  {
    label: "SO First Name",
    value: "signifiantOther.firstName",
    type: "signifiantOther",
    isDisabled: false,
  },
  {
    label: "SO Last Name",
    value: "signifiantOther.lastName",
    type: "signifiantOther",
    isDisabled: false,
  },
  {
    label: "SO Middle Name",
    value: "signifiantOther.middleName",
    type: "signifiantOther",
    isDisabled: false,
  },
  {
    label: "SO Prefix",
    value: "signifiantOther.prefix",
    type: "signifiantOther",
    isDisabled: false,
  },
  {
    label: "SO Suffix",
    value: "signifiantOther.suffix",
    type: "signifiantOther",
    isDisabled: false,
  },
  {
    label: "SO Birthday",
    value: "significantOther.birthday",
    type: "significantOther",
    isDisabled: false,
  },

  // salutations
  {
    label: "Comapny",
    value: "salutation.company",
    type: "salutation",
    isDisabled: false,
  },
  {
    label: "Job Title",
    value: "salutation.jobTitle",
    type: "salutation",
    isDisabled: false,
  },
  {
    label: "Letter Salutation",
    value: "salutation.letterSalutation",
    type: "salutation",
    isDisabled: false,
  },
  {
    label: "Mailing Salutation",
    value: "salutation.mailingSalutation",
    type: "salutation",
    isDisabled: false,
  },

  // addresses
  {
    label: "Address Line 1",
    value: "address1",
    type: "address",
    isDisabled: false,
  },
  {
    label: "Address Line 2",
    value: "address2",
    type: "address",
    isDisabled: false,
  },
  { label: "City", value: "city", type: "address", isDisabled: false },
  { label: "State", value: "state", type: "address", isDisabled: false },
  { label: "Zip", value: "zip", type: "address", isDisabled: false },
  { label: "Zip4", value: "zip4", type: "address", isDisabled: false },
  {
    label: "Full Address",
    value: "fullAddress",
    type: "address",
    isDisabled: false,
  },

  { label: "Company", value: "company", type: "company", isDisabled: false },
  { label: "Notes", value: "notes", type: "notes", isDisabled: false },

  { label: "Email", value: "email", type: "email", isDisabled: false },

  { label: "Phone Number", value: "phone", type: "phone", isDisabled: false },
  { label: "Phone Number 2", value: "phone", type: "phone", isDisabled: false },
  { label: "Phone Number 3", value: "phone", type: "phone", isDisabled: false },
];

exports.possibleHeaders = [
  // Recipient Name
  "Name",
  "Full Name",
  "First Name",
  "Last Name",
  "Middle Name",
  "Surname",
  // Significant Other Name
  "Significant Other",
  "Significant Other First Name",
  "Significant Other Last Name",
  "Spouse Name",
  "Spouse Full Name",
  "Spouse",
  "Spouse First Name",
  "Spouse Last Name",
  "Spouse Middle Name",
  "Spouse Surname",
  // Salutations
  "Letter Salutation",
  "Mailing Salutation",
  "Salutation",
  "Titles",
  "Title",
  // Mailing Address
  "Mailing Address",
  "Address",
  "Pysical Address",
  "Home Address",
  "Office Address",
  "Physical Address",
  "Work Address",
  "Mailing Address 1",
  "Mailing Address 2",
  "Mailing Address 3",
  "City",
  "State",
  "Zip",
  "Country",
  "Postal Code",
  "Postal Address",
  // Email
  "Email",
  "E-mail",
  "Email Address",
  // Phone Numbers
  "Phone",
  "Phone Number",
  "Phonenumber",
  "Mobile Phone",
  "Cell Phone",
  "Landline Phone",
  "Landline",
  "Fax",
  // Notes
  "Notes",
  "Note",
  // Social Media
  "Social Media Account",
  "Social Media",
  "Social Media Platform",
  "Social Media Link",
  "Social Media URL",
  "Website",
  // Abbreviations
  "Fn", // First Name
  "Ln", // Last Name
  "Sofn", // Significant Other First Name
  "Soln", // Significant Other Last Name
  "An",
  "Dn",
  "A1",
  "C",
  "St",
  "Z",
];
