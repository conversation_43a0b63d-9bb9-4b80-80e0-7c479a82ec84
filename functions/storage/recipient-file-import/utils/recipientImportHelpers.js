const Papa = require("papaparse");
const { possibleHeaders } = require("../constants");

// https://www.papaparse.com/docs#local-files
exports.isPossibleHeaderRow = async function isPossibleHeaderRow(csvFile) {
  return new Promise((resolve) => {
    Papa.parse(csvFile, {
      header: false,
      preview: 1, // Only parse the first row (If > 0, only that many rows will be parsed.)
      complete: (results) => {
        if (results.data.length > 0 && results.data[0].length > 0) {
          const firstRow = results.data[0];
          const exists = possibleHeaders.some((value) =>
            firstRow.some(
              (cell) =>
                cell && cell.trim().toLowerCase() === value.trim().toLowerCase()
            )
          );
          resolve(exists);
        } else {
          resolve(false);
        }
      },
      error: (error) => {
        console.error("Error parsing CSV:", error);
        resolve(false);
      },
    });
  });
};
