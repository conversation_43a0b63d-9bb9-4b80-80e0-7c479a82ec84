const { logger } = require("firebase-functions");
const AdmZip = require("adm-zip");
const Papa = require("papaparse");
const XLSX = require("xlsx");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const { expectedHeaders } = require("./constants");
const { isPossibleHeaderRow } = require("./utils/recipientImportHelpers");
const { isEmpty, isNil } = require("lodash");
const { defineString } = require("firebase-functions/params");
const { astridConnection } = require("../../astrid/astridConnection");
const { addToTopic } = require("../../utils/pubsub/addToTopic");
const {
  parseDateToFirestoreTimestamp,
} = require("../../utils/parseDateToFirestoreTimestamp");
const { Timestamp } = require("firebase-admin/firestore");
const { processingStatus } = require("../../constants/processingStatus");
const { createUUID } = require("../../utils/createUUID");

const { PRE_PROCESS } = processingStatus;
const path = require("path");

exports.handleRecipientFileImport = async ({
  db,
  bucket,
  contentType,
  filePath,
}) => {
  const addressParser = await import("@universe/address-parser");
  /**
   * Check for zip files
   * unpack zip files
   * for each file check file extension
   * (json files are either the headerMap or the columns selected for import)
   * (csv, xlsx, and xls files is the original recipients file)
   *
   */

  const delimiter = ":::"; // separates original header from the matched (the header the system expects)

  try {
    if (
      contentType !== "application/zip" &&
      contentType !== "application/x-zip-compressed"
    ) {
      return;
    }

    // get accountId
    const filePathElements = filePath.split("/");
    const accountId = filePathElements[1];
    const storageDirectory = path.dirname(filePath);

    // download file
    const downloadResponse = await bucket.file(filePath).download();
    const zipBuffer = downloadResponse[0];

    const zip = new AdmZip(zipBuffer);

    const zipEntries = zip.getEntries();

    // selections object expects headerMap, selectedFields, expectedHeaders, recipients properties, and recipientGroupId
    // selections object is populated with data in handleJsonFile as information used to process the file is sent by the client in json
    const selections = {};
    const fileType = { isUnformatted: false }; // unformatted means not a csv, xls, or xlsx
    const originalFile = { path: "" }; // appending path here after storing original file in handleOriginalFile
    for (const entry of zipEntries) {
      const fileName = entry.entryName.toLowerCase();

      const fileExtension = fileName.split(".").pop();
      switch (fileExtension) {
        case "json":
          handleJsonFile({ entry, selections });
          fileType.name = entry.entryName;
          break;

        case "xlsx":
        case "xls":
        case "csv":
          originalFile.name = entry.entryName;
          originalFile.path = await handleOriginalFile({
            entry,
            bucket,
            storageDirectory,
          });
          break;
        default:
          fileType.isUnformatted = true;
          fileType.name = entry.entryName;
          originalFile.name = entry.entryName;
          originalFile.path = await handleOriginalFile({
            entry,
            bucket,
            storageDirectory,
          });

          break;
      }
    }

    /**
     * If the file is unformatted then stop processing here
     * we've sent the request to CRM for customer support
     * in this context unformatted means any file that is not csv, xls, or xlsx
     */
    if (fileType.isUnformatted) {
      // get refs for later
      try {
        const accountRef = db
          .collection(collectionNames.account)
          .doc(accountId);

        const recipientGroupRef = accountRef
          .collection(subCollectionNames.contacts.recipientGroups)
          .doc(selections?.recipientGroup?.recipientGroupId);

        await handleUnformattedFile({
          accountId,
          recipientGroupRef,
          fileLocation: `${storageDirectory}/${originalFile.name}`,
        });
        await addImportFileToFileHistory({
          db,
          accountId,
          filePath: originalFile.path,
          bucketName: bucket.name,
          fileName: originalFile.name,
          recipientGroupId: selections?.recipientGroup?.recipientGroupId,
        });
        await deleteFile({ bucket, filePath });
        return;
      } catch (error) {
        logger.error(
          "Problem handling unformatted file. Attempted to send to astrid.",
          error
        );
        return;
      }
    }

    // process recipients

    const recipientObject = handleRecipientCollections(selections.recipients);
    const processedHeaderMap = selections?.headerMap?.map((header) =>
      header.split(delimiter)
    );

    // which columns are we keeping?
    /**
     * {
     * headerMap: ["Column 1", "First Name"]
     * shouldImport: true
     * }
     */
    const mappingsAndSelectedFields =
      Object.keys(selections?.selectedFields ?? {}).map((columnName) => {
        return {
          headerMap: processedHeaderMap.find((header) => {
            return header[0] === columnName;
          }),
          shouldImport: !!selections?.selectedFields[columnName],
        };
      }) ?? [];

    try {
      if (recipientObject?.data) {
        await writeRecipientBatch({
          db,
          accountId,
          recipientRecords: recipientObject?.data,
          mappingsAndSelectedFields,
          recipientGroupId: selections?.recipientGroup?.recipientGroupId,
          addressParser,
          bucket,
          storageDirectory,
          selections,
        });
      }
    } catch (error) {
      logger.error("problem formatting data: ", error);
    }

    await addImportFileToFileHistory({
      db,
      accountId,
      filePath: originalFile.path,
      bucketName: bucket.name,
      fileName: originalFile.name,
      recipientGroupId: selections?.recipientGroup?.recipientGroupId,
    });

    // delete zip file (file that contains the metadata)
    await deleteFile({ bucket, filePath });
  } catch (error) {
    logger.error("Error processing the zip file: ", error);
  }
};

const handleJsonFile = ({ entry, selections, recipients }) => {
  /**
   * The Json files processed in this function represent
   * the selected columns to import and the header matching
   * we expect two Json files to be processed
   */
  try {
    const fileName = entry.entryName;
    const fileNameElements = fileName.split(".");
    const fileData = entry.getData().toString("utf-8");
    // file type is either "header-map" or "selected-fields"

    const type = fileNameElements[0];
    switch (type) {
      case "header-map":
        selections.headerMap = JSON.parse(fileData);
        break;
      case "label-map":
        selections.labelMap = JSON.parse(fileData);
        break;
      case "selected-fields":
        selections.selectedFields = JSON.parse(fileData);
        break;
      case "recipientGroupid":
        selections.recipientGroup = JSON.parse(fileData);
        break;
      case "recipients":
        selections.recipients = JSON.parse(fileData);
      default:
        break;
    }
  } catch (error) {
    logger.error("Error processing JSON file: ", error);
  }
};

async function handleOriginalFile({ entry, bucket, storageDirectory }) {
  const fileName = entry.entryName;
  const fileData = entry.getData();

  const destinationPath = `${storageDirectory}/${fileName}`;
  try {
    const file = bucket.file(destinationPath);

    await file.save(fileData);
    return destinationPath;
  } catch (error) {
    logger.error(
      "Error saving original file to bucket: ",
      error,
      "path: ",
      destinationPath
    );
  }
}

async function handleUnformattedFile({
  accountId,
  recipientGroupRef,
  fileLocation,
}) {
  try {
    const recipientGroupSnapshot = await recipientGroupRef.get();
    const recipientGroupData = recipientGroupSnapshot.data();
    const { name: recipientGroupName } = recipientGroupData;
    const response = await astridConnection({
      payload: { accountId, recipientGroupName, fileLocation },
      url: "crm/unformattedFile",
      method: "POST",
    });
    const projectId = defineString("TITAN_PROJECT_ID");
    await addToTopic({
      topic: "create-notification",
      projectId: projectId.value(),
      message: JSON.stringify({
        type: "unformattedRecipientFileUploaded",
        data: {
          accountId,
          url: fileLocation,
          type: "fileReady",
          isRead: false,
          title: `Recipient import complete!`,
          body: `Your recipients import is complete`,
        },
      }),
    });
  } catch (error) {
    logger.error("There was a problem calling astrid", error);
  }
}

function handleRecipientCollections(data) {
  try {
    return { hasHeader: hasHeader(data), data };
  } catch (error) {
    logger.error("Error processing CSV file: ", error);
  }
}

function hasHeader(collection) {
  // Check if the collection is empty
  if (collection?.length === 0) return false;

  // Check the type of the first element in the collection
  const firstElement = collection?.[0];

  // If the first element is an object, return true
  if (typeof firstElement === "object" && !Array.isArray(firstElement)) {
    return true;
  }

  // If the first element is an array of strings, return false
  if (
    Array.isArray(firstElement) &&
    firstElement.every((item) => typeof item === "string")
  ) {
    return false;
  }

  // Return false for any other data shape
  return false;
}

async function writeRecipientBatch({
  db,
  accountId,
  recipientRecords,
  mappingsAndSelectedFields,
  recipientGroupId,
  selections,
  bucket,
  storageDirectory,
}) {
  let batch = db.batch();
  let batchOperationCount = 0;
  let batchSizeBytes = 0;
  const MAX_BATCH_OPERATIONS = 450; // Max batch operations is 500
  const MAX_BATCH_SIZE_BYTES = 9 * 1024 * 1024; // 9 MiB to stay under the 10 MiB limit
  const MAX_RECORDS = 10000; // Maximum number of records to process

  let recordsProcessed = 0; // Counter for processed records

  // Records with address issues. Add these to an excel file and send to CRM.
  const recordsForManualReview = [];

  for (const record of recipientRecords) {
    // Increment the records processed counter
    recordsProcessed++;

    // Check if the maximum records limit has been reached
    if (recordsProcessed > MAX_RECORDS) {
      break; // Exit the loop to stop processing further records
    }

    const uuid = createUUID();

    const recipientRef = db
      .collection(collectionNames.account)
      .doc(accountId)
      .collection(subCollectionNames.contacts.recipients)
      .doc(uuid);

    const recipientData = {
      name: {},
      isActive: true,
      accountId,
      createdAt: Timestamp.now(),
      salutation: {},
      significantOther: {},
    };
    const mailingAddressData = {};
    const emailAddressData = {};
    const phoneNumberData = {};
    const significantDatesData = {};
    const websitesData = {};
    const socialMediaAccountsData = {};
    const notesData = {};

    // Mailing Address Reference
    const userEnteredMailingAddressRef = recipientRef
      .collection(subCollectionNames.contacts.userEnteredMailingAddress)
      .doc("0");
    // Email Address Reference
    const emailAddressRef = recipientRef
      .collection(subCollectionNames.contacts.emailAddresses)
      .doc("0");
    // Phone Number Reference
    const phoneNumberRef = recipientRef
      .collection(subCollectionNames.contacts.phoneNumbers)
      .doc("0");

    // Significant Dates Ref
    const significantDatesCollectionRef = recipientRef.collection(
      subCollectionNames.contacts.significantDates
    );

    // Websites
    const websitesCollectionRef = recipientRef.collection(
      subCollectionNames.contacts.websites
    );

    // Social Media
    const socialMediaAccountsCollectionRef = recipientRef.collection(
      subCollectionNames.contacts.socialMediaAccounts
    );

    // Notes
    const notesCollectionRef = recipientRef.collection(
      subCollectionNames.contacts.notes
    );

    // Sets data on appropriate data object before adding to operations
    await setRecipientDataTypes({
      record,
      recipientData,
      mailingAddressData,
      emailAddressData,
      phoneNumberData,
      significantDatesData,
      notesData,
      websitesData,
      socialMediaAccountsData,
      mappingsAndSelectedFields,
      recordsForManualReview,
      selections,
      callback: addRecordToCollection,
    });

    // Process Street Name and Street Number if present
    if (
      !isNil(mailingAddressData.streetNumber) &&
      !isNil(mailingAddressData.streetName) &&
      (isNil(mailingAddressData.address1) ||
        isEmpty(mailingAddressData.address1))
    ) {
      mailingAddressData.address1 = `${mailingAddressData.streetNumber} ${mailingAddressData.streetName}`;
    }

    // if no firstName or lastName is provided use current Resident
    if (
      isNil(recipientData?.name?.firstName) ||
      isEmpty(recipientData?.name?.firstName) ||
      isNil(recipientData?.name?.lastName) ||
      isEmpty(recipientData?.name?.lastName)
    ) {
      recipientData.name.lastName = isNil(recipientData.name.lastName)
        ? "Current Resident"
        : recipientData.name.lastName;
    }

    // Create default letter salutation if not provided
    if (!recipientData?.salutation?.letterSalutation) {
      // is significant other first name present?
      if (recipientData?.significantOther?.firstname) {
        recipientData.salutation.letterSalutation = `${recipientData?.name?.firstName} & ${recipientData?.significantOther?.firstName}`;
      } else {
        recipientData.salutation.letterSalutation =
          recipientData?.name?.firstName ||
          recipientData?.name?.lastName ||
          "Current Resident";
      }
    }
    // Create default mailing salutation if not provided
    if (!recipientData?.salutation?.mailingSalutation) {
      // check for existence of significant other
      // verify last names match
      if (
        recipientData?.significantOther?.firstName &&
        recipientData?.significantOther?.lastName
      ) {
        // check for matching last names
        // if they match then use the matching last name in mailing salutation
        if (
          recipientData?.significantOther?.lastName?.trim()?.toLowerCase() ===
          recipientData?.name?.lastName?.trim()?.toLowerCase()
        ) {
          recipientData.salutation.mailingSalutation = `${recipientData.name.firstName} & ${recipientData.significantOther.firstName} ${recipientData.name.lastName}`;
        } else if (
          isNil(recipientData?.significantOther?.lastName) ||
          isEmpty(recipientData?.significantOther?.lastName)
        ) {
          // if the significant other last name is empty then use the primary recipient last name
          recipientData.salutation.mailingSalutation = `${recipientData?.name?.firstName} & ${recipientData?.significantOther?.firstName} ${recipientData?.name?.lastName}`;
        } else {
          // if the significant other last name is not empty and so and primary names dont match then use the primary recipient last name and significant other last name
          recipientData.salutation.mailingSalutation = `${recipientData?.name?.firstName} ${recipientData?.name?.lastName} & ${recipientData?.significantOther?.firstName} ${recipientData?.significantOther?.lastName}`;
        }
      } else if (
        isNil(recipientData.name.lastName) ||
        isEmpty(recipientData.name.lastName)
      ) {
        recipientData.salutation.mailingSalutation =
          recipientData.name.firstName || "Current Resident";
      } else {
        recipientData.salutation.mailingSalutation =
          (recipientData.name.firstName || "") +
          " " +
          (recipientData.name.lastName || "");
      }
    }

    // Prepare write operations
    const operations = [];

    const addressDeliverability = {
      status: "NO ADDRESS",
      code: "NON",
      message: "No Mailing Address",
    };
    // Combine recipientGroupIds and recipientData into a single write
    const recipientSetData = {
      recipientGroupIds: [recipientGroupId],
      ...recipientData,
    };
    operations.push({
      ref: recipientRef,
      data: recipientSetData,
      options: { merge: true },
    });

    if (!isEmpty(mailingAddressData)) {
      operations.push({
        ref: userEnteredMailingAddressRef,
        data: { ...mailingAddressData },
        options: { merge: true },
      });
    } else {
      operations.push({
        ref: recipientRef,
        data: addressDeliverability,
        options: { merge: true },
      });
    }
    if (!isEmpty(emailAddressData)) {
      operations.push({
        ref: emailAddressRef,
        data: emailAddressData,
        options: { merge: true },
      });
    }
    if (!isEmpty(phoneNumberData)) {
      operations.push({
        ref: phoneNumberRef,
        data: phoneNumberData,
        options: { merge: true },
      });
    }

    if (!isEmpty(significantDatesData)) {
      operations.push({
        ref: significantDatesCollectionRef.doc(),
        data: significantDatesData,
        options: { merge: true },
      });
    }

    if (!isEmpty(notesData)) {
      operations.push({
        ref: notesCollectionRef.doc(),
        data: notesData,
        options: { merge: true },
      });
    }

    if (!isEmpty(websitesData)) {
      operations.push({
        ref: websitesCollectionRef.doc(),
        data: websitesData,
        options: { merge: true },
      });
    }

    if (!isEmpty(socialMediaAccountsData)) {
      operations.push({
        ref: socialMediaAccountsCollectionRef.doc(),
        data: socialMediaAccountsData,
        options: { merge: true },
      });
    }

    // Estimate the size of the operations
    let operationSizeBytes = 0;
    for (const op of operations) {
      const dataSize = estimateFirestoreDocumentSize(op.data);
      operationSizeBytes += dataSize;
    }

    // Check if adding these operations exceeds batch limits
    if (
      batchOperationCount + operations.length >= MAX_BATCH_OPERATIONS ||
      batchSizeBytes + operationSizeBytes >= MAX_BATCH_SIZE_BYTES
    ) {
      // Commit the current batch
      try {
        await batch.commit();
      } catch (error) {
        logger.error("Batch write failed:", error);
        return { success: false, error };
      }
      // Reset batch and counters
      batch = db.batch();
      batchOperationCount = 0;
      batchSizeBytes = 0;
    }

    // Add operations to the batch
    for (const op of operations) {
      batch.set(op.ref, op.data, op.options);
      batchOperationCount++;
    }
    batchSizeBytes += operationSizeBytes;
  }

  // Commit any remaining operations in the batch
  if (batchOperationCount > 0) {
    try {
      await batch.commit();
    } catch (error) {
      logger.info("Final batch write failed:", error);
      return { success: false, error };
    }
  }

  const projectId = defineString("TITAN_PROJECT_ID");

  // Determine if the processing was stopped due to the record limit
  const totalRecords = recipientRecords.length;
  const messageBody =
    totalRecords > MAX_RECORDS
      ? `Your recipients import is complete. Only the first ${MAX_RECORDS} records were imported due to the limit.`
      : `Your recipients import is complete.`;

  await addToTopic({
    topic: "create-notification",
    projectId: projectId.value(),
    message: JSON.stringify({
      type: "recipientFileUploaded",
      data: {
        accountId,
        url: `/account/recipient-groups/${recipientGroupId}`,
        type: "recipientFileUploaded",
        isRead: false,
        title: `Recipient import complete!`,
        body: messageBody,
      },
    }),
  });

  // handle problem records
  if (recordsForManualReview.length > 0) {
    await processRecordsForManualReview({
      recordsForManualReview,
      bucket,
      storageDirectory,
      fileName: "records-with-mailing-address-issues",
      accountId,
      recipientGroupId,
      db,
    });
  }

  return { success: true };
}

// Helper function to estimate the size of a Firestore document
function estimateFirestoreDocumentSize(doc) {
  const jsonString = JSON.stringify(doc);
  // UTF-8 encoding: 1 character can be up to 4 bytes
  // JSON.stringify outputs strings in UTF-16,
  // conservative estimate assume 2 bytes per character
  return jsonString.length * 2;
}

function cleanPhoneNumber(phoneNumber) {
  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, "");

  // If the number has exactly 10 digits, it's a standard U.S. number.
  if (digits.length === 10) {
    return "+1" + digits;
  }
  // If the number has 11 digits and starts with "1", assume it already includes the country code.
  else if (digits.length === 11 && digits.startsWith("1")) {
    return "+" + digits;
  }
  // otherwise just return the cleaned digits
  else {
    return digits;
  }
}

async function addImportFileToFileHistory({
  db,
  accountId,
  filePath,
  bucketName,
  fileName,
  recipientGroupId,
}) {
  try {
    const accountFileRef = db
      .collection(collectionNames.account)
      .doc(accountId)
      .collection(subCollectionNames.account.files);
    await accountFileRef.add({
      type: "recipient-import",
      fileName,
      filePath,
      bucketName,
      recipientGroupId,
      createdAt: Timestamp.now(),
      downloadStarted: Timestamp.now(),
      status: "finished",
    });
  } catch (error) {
    logger.error("Problem adding recipient file import to history.", error);
  }
}

async function setRecipientDataTypes({
  record,
  recipientData,
  mailingAddressData,
  emailAddressData,
  phoneNumberData,
  significantDatesData,
  notesData,
  websitesData,
  socialMediaAccountsData,
  mappingsAndSelectedFields,
  recordsForManualReview,
  selections,
  callback,
}) {
  const isArrayRecord = Array.isArray(record);

  // Define an iterator based on the type of record
  const entries = isArrayRecord
    ? record.map((value, index) => [index, value])
    : Object.entries(record);

  for (const [keyOrIndex, value] of entries) {
    let mapping, columnName;

    if (isArrayRecord) {
      mapping = mappingsAndSelectedFields[keyOrIndex];
    } else {
      mapping = mappingsAndSelectedFields.find(
        (mappingObj) => mappingObj?.headerMap?.[0] === keyOrIndex
      );
    }

    columnName = mapping?.headerMap?.[1];

    switch (columnName) {
      // Name Fields
      case "First Name":
        if (mapping?.shouldImport && value)
          recipientData.name.firstName = value;
        break;
      case "Last Name":
        if (mapping?.shouldImport && value) recipientData.name.lastName = value;
        break;
      case "Full Name":
        if (mapping?.shouldImport && value) {
          // in the event both names are used in the same field
          const parsedNames = handleSpouseInFullName(value);
          if (parsedNames?.significantOther) {
            const spouseName = splitFullName(parsedNames?.significantOther);
            recipientData.significantOther.firstName = spouseName.firstName;
            recipientData.significantOther.lastName = spouseName.lastName;
          }

          const processedFullName = splitFullName(
            parsedNames?.primaryRecipient
          );
          // firstName field takes precedence
          if (!recipientData.name.firstName) {
            recipientData.name.firstName = processedFullName.firstName;
          }
          // removed for business request to add parse everything after full name into last name field
          // middle name/initial takes precedence
          // if (!recipientData.middle) {
          //   recipientData.name.middle = processedFullName.middleName;
          // }
          // last name takes precedence
          if (!recipientData.lastName) {
            recipientData.name.lastName =
              !recipientData.middle && processedFullName.middleName
                ? `${processedFullName.middleName} ${processedFullName.lastName}`
                : `${processedFullName.lastName}`;
          }
          // prefix field takes precedence
          if (!recipientData.prefix) {
            recipientData.name.prefix = processedFullName.prefix;
          }
          // suffix field takes precedence
          if (!recipientData.suffix) {
            recipientData.name.suffix = processedFullName.suffix;
          }
        }
        break;
      case "Middle Name or Initial":
        if (
          mapping?.shouldImport &&
          value &&
          isEmpty(recipientData.name.middle)
        ) {
          recipientData.name.middle = value;
        }
        break;
      case "Nickname":
        if (mapping?.shouldImport && value) {
          recipientData.name.nickname = value;
        }
        break;
      case "SO First Name":
        if (mapping?.shouldImport && value) {
          recipientData.significantOther.firstName = value;
        }
        break;
      case "SO Last Name":
        if (mapping?.shouldImport && value) {
          recipientData.significantOther.lastName = value;
        }
        break;
      case "Phonetic First Name":
        if (mapping?.shouldImport && value) {
          recipientData.name.phoneticFirstName = value;
        }
        break;
      case "Phonetic Last Name":
        if (mapping?.shouldImport && value) {
          recipientData.name.phoneticLastName = value;
        }
        break;
      case "Job Title":
        if (mapping?.shouldImport && value) {
          recipientData.salutation.jobTitle = value;
        }
        break;
      case "Company":
        if (mapping?.shouldImport && value) {
          recipientData.salutation.company = value;
        }
        break;
      case "Prefix":
        if (mapping?.shouldImport && value) recipientData.name.prefix = value;
        break;
      case "Suffix":
        if (mapping?.shouldImport && value) recipientData.name.suffix = value;
        break;
      // Salutations
      case "Letter Salutation":
        if (mapping?.shouldImport && value)
          recipientData.salutation.letterSalutation = value;
        break;
      case "Mailing Salutation":
        if (mapping?.shouldImport && value)
          recipientData.salutation.mailingSalutation = value;
        break;
      // Mailing Addresses
      case "Full Address":
        if (mapping?.shouldImport && value) {
          const address = await parseFullAddress(value);
          if (address.hasError === false) {
            const { address1, address2, city, state, postalCode } =
              address ?? {};
            mailingAddressData.address1 = address1;
            mailingAddressData.address2 = address2;
            mailingAddressData.city = city;
            mailingAddressData.state = state;
            mailingAddressData.postalCode = postalCode;
            const label = getLabelValue({
              selections,
              header: "Address",
            });
            if (label) {
              mailingAddressData.label = label;
            }
          } else {
            callback({ collection: recordsForManualReview, record }); // writes records that need to be reviewed
          }
        }
        break;
      case "Address Line 1":
        if (mapping?.shouldImport && value) {
          mailingAddressData.address1 = value;
          const label = getLabelValue({
            selections,
            header: "Address",
          });
          if (label) {
            mailingAddressData.label = label;
          }
        }
        break;
      case "Address Line 2":
        if (mapping?.shouldImport && value) {
          mailingAddressData.address2 = value;
          const label = getLabelValue({
            selections,
            header: "Address",
          });
          if (label) {
            mailingAddressData.label = label;
          }
        }
        break;
      case "City":
        if (mapping?.shouldImport && value) {
          mailingAddressData.city = value;
          const label = getLabelValue({
            selections,
            header: "City",
          });
          if (label) {
            mailingAddressData.label = label;
          }
        }
        break;
      case "State":
        if (mapping?.shouldImport && value) {
          mailingAddressData.state = value;
          const label = getLabelValue({
            selections,
            header: "State",
          });

          if (label) {
            mailingAddressData.label = label;
          }
        }
        break;
      case "Zip":
        if (mapping?.shouldImport && value) {
          const postalCodeStr = value.toString().padStart(5, "0");
          mailingAddressData.postalCode = postalCodeStr;

          const label = getLabelValue({
            selections,
            header: "Zip",
          });
          if (label) {
            mailingAddressData.label = label;
          }
        }
        break;
      case "Zip4":
        if (mapping?.shouldImport && value) {
          mailingAddressData.zip4 = value;
          const label = getLabelValue({
            selections,
            header: "Zip",
          });
          if (label) {
            mailingAddressData.label = label;
          }
        }
        break;
      case "Street Name":
        if (mapping?.shouldImport && value) {
          mailingAddressData.streetName = value;
          const label = getLabelValue({
            selections,
            header: "Address",
          });
          if (label) {
            mailingAddressData.label = label;
          }
        }
        break;
      case "Street Number":
        if (mapping?.shouldImport && value) {
          mailingAddressData.streetNumber = value;
          const label = getLabelValue({
            selections,
            header: "Address",
          });
          if (label) {
            mailingAddressData.label = label;
          }
        }
        break;
      // Email Addresses
      case "Email":
        if (mapping?.shouldImport && value) {
          emailAddressData.email = value;
          emailAddressData.isPrimary = true;
          const label = getLabelValue({
            selections,
            header: "Email",
          });
          if (label) {
            emailAddressData.label = label;
          }
        }
        break;
      // Phone Numbers
      case "Phone Number":
        if (mapping?.shouldImport && value) {
          phoneNumberData.phoneNumber = cleanPhoneNumber(value);
          const label = getLabelValue({
            selections,
            header: "Phone Number",
          });
          if (label) {
            phoneNumberData.label = label;
          }
        }
        break;
      // Significant Dates
      case "Birthday":
        if (mapping?.shouldImport && parseDateToFirestoreTimestamp(value)) {
          significantDatesData.date = parseDateToFirestoreTimestamp(value);
          significantDatesData.label = "Birthday";
        }
        break;
      // Notes
      case "Notes":
        if (mapping?.shouldImport && value) notesData.value = value;
        break;
      // Websites
      case "Website":
        if (mapping?.shouldImport && value) websitesData.url = value;
        break;
      // Social Media Accounts
      case "Social Media Accounts":
        if (mapping?.shouldImport && value)
          socialMediaAccountsData.value = value;
        break;
      default:
        break;
    }
  }
}

function splitFullName(fullName) {
  // case insensitive
  const prefixPattern =
    /^(Mr\.?|Mrs\.?|Ms\.?|Miss\.?|Dr\.?|Prof\.?|Rev\.?|Sir\.?|Madam\.?|Capt\.?|Major\.?)$/i;
  const suffixPattern =
    /^(Jr\.?|Sr\.?|II\.?|III\.?|IV\.?|PhD\.?|MD\.?|Esq\.?|DDS\.?|DVM\.?|RN\.?|REALTOR|ABR|CRS|GRI|SRES|SRS|CRE|CCIM|CPM|RCE|SIOR)$/i;

  let nameParts = fullName.trim().split(/\s+/);

  let prefix = "";
  let suffix = "";

  // Extract prefixes from the beginning
  while (nameParts.length > 0 && prefixPattern.test(nameParts[0])) {
    prefix += (prefix ? " " : "") + nameParts.shift();
  }

  // Extract suffixes from the end
  while (
    nameParts.length > 0 &&
    suffixPattern.test(nameParts[nameParts.length - 1])
  ) {
    suffix = nameParts.pop() + (suffix ? " " : "") + suffix;
  }

  let firstName = "";
  let middleName = "";
  let lastName = "";

  if (nameParts.length === 1) {
    // Only one part, assume first name
    firstName = nameParts[0];
  } else if (nameParts.length === 2) {
    // Two parts, assume first and last name
    [firstName, lastName] = nameParts;
  } else if (nameParts.length > 2) {
    // More than two parts, assume first is first name, last is last name, middle is middle name
    firstName = nameParts[0];
    middleName = nameParts.slice(1, -1).join(" ");
    lastName = nameParts[nameParts.length - 1];
  }

  return {
    prefix,
    firstName,
    middleName,
    lastName,
    suffix,
  };
}

function handleSpouseInFullName(fullNameField) {
  // 1) Split on "&" or "and" (case-insensitive).
  //    Captures two groups:
  //    - group[1]: text before
  //    - group[2]: text after
  const regex = /^(.+?)\s*(?:&|and)\s+(.+)$/i;
  const match = fullNameField.trim().match(regex);

  // If no match, return the entire string as primaryRecipient
  if (!match) {
    return {
      primaryRecipient: fullNameField.trim(),
      significantOther: "",
    };
  }

  // Extract the parts
  let leftRaw = match[1]; // Correct: Use the first capturing group
  let rightRaw = match[2];

  // 2) Format the right side (significantOther):
  //    - Split into words
  //    - Convert each word to Title Case
  let rightWords = rightRaw
    .split(/\s+/)
    .map((w) => w[0].toUpperCase() + w.slice(1).toLowerCase());
  let significantOther = rightWords.join(" ");

  // 3) Check if the left side is missing a last name:
  //    - If left has only one word, grab the *last* word from the right side
  //      to use as the last name.
  let leftWords = leftRaw.trim().split(/\s+/);
  if (leftWords.length === 1 && rightWords.length > 1) {
    leftWords.push(rightWords[rightWords.length - 1]);
  }

  return {
    primaryRecipient: leftWords.join(" "),
    significantOther,
  };
}

async function deleteFile({ bucket, filePath }) {
  const file = bucket.file(filePath);
  try {
    await file.delete();
  } catch (error) {
    logger.error(`Failed to delete file: ${filePath}`, error);
  }
}

async function parseFullAddress(fullAddress) {
  const addressParser = await import("@universe/address-parser");
  try {
    const parsedAddress = addressParser.parse(fullAddress);
    const {
      care,
      city,
      country,
      facility,
      facilityType,
      number,
      pinNum,
      pinType,
      state,
      streetName,
      streetPostDir,
      streetPreDir,
      streetType,
      unitAbbr,
      unitNum,
      zip,
      zip4,
    } = parsedAddress ?? {};
    const address1 = `${number} ${streetPreDir ?? ""} ${streetName} ${streetType ?? ""} ${streetPostDir ?? ""}`;
    const address2 = `${unitAbbr ?? ""} ${unitNum ?? ""}`;
    const postalCode = zip;
    const response = await astridConnection({
      payload: { address1, address2, city, state, zip: postalCode },
      url: "address",
      method: "POST",
    });
    const experianResult = response?.data;
    const standardizedAddress = {
      hasError: false,
      address1:
        experianResult?.address1 ?? experianResult?.line1 ?? address1 ?? "",
      address2:
        experianResult?.address2 ?? experianResult?.line2 ?? address2 ?? "",
      city: experianResult?.city ?? city ?? "",
      state: experianResult?.state ?? state ?? "",
      postalCode: experianResult?.zip ?? postalCode ?? "",
    };

    const isAddressInvalid =
      isValueInvalid(experianResult?.address1) &&
      isValueInvalid(experianResult?.line1);

    const isCityAndPostalCodeInvalid =
      isValueInvalid(experianResult?.city) &&
      isValueInvalid(experianResult?.postalCode);

    const isStateAndPostalCodeInvalid =
      isValueInvalid(experianResult?.state) &&
      isValueInvalid(experianResult?.postalCode);

    if (
      isAddressInvalid ||
      isCityAndPostalCodeInvalid ||
      isStateAndPostalCodeInvalid
    ) {
      return { hasError: true };
    } else {
      return standardizedAddress;
    }
  } catch (error) {
    logger.error(error);
    return { hasError: true };
  }
}

async function processRecordsForManualReview({
  recordsForManualReview,
  bucket,
  storageDirectory,
  fileName,
  accountId,
  recipientGroupId,
  db,
}) {
  try {
    const excelBuffer = convertJSONtoWorksheet(recordsForManualReview);
    const destinationPath = `${storageDirectory}/${fileName}`;
    const file = bucket.file(destinationPath);

    // Upload the buffer to Cloud Storage
    await file.save(excelBuffer, {
      metadata: {
        contentType:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      },
    });

    // send to CRM

    const accountRef = db.collection(collectionNames.account).doc(accountId);

    const recipientGroupRef = accountRef
      .collection(subCollectionNames.contacts.recipientGroups)
      .doc(recipientGroupId);

    const recipientGroupSnapshot = await recipientGroupRef.get();
    const recipientGroupData = recipientGroupSnapshot.data();
    const { name: recipientGroupName } = recipientGroupData;
    const response = await astridConnection({
      payload: { accountId, recipientGroupName, destinationPath },
      url: "crm/unformattedFile",
      method: "POST",
    });

    const projectId = defineString("TITAN_PROJECT_ID");
    await addToTopic({
      topic: "create-notification",
      projectId: projectId.value(),
      message: JSON.stringify({
        type: "unformattedRecipientFileUploaded",
        data: {
          accountId,
          url: destinationPath,
          type: "fileReady",
          isRead: false,
          title: `Recipient import complete!`,
          body: `Your recipients import is complete`,
        },
      }),
    });
  } catch (error) {
    logger.error("Error processing records for manual review:", error);
    throw error; // Optionally rethrow the error
  }
}

function convertJSONtoWorksheet(data) {
  try {
    // Convert JSON data to a worksheet
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Create a new workbook and append the worksheet
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Contacts");

    // Generate a buffer from the workbook
    const excelBuffer = XLSX.write(workbook, { type: "buffer" });

    return excelBuffer;
  } catch (error) {
    logger.error("Problem converting data to worksheet", error);
    throw error; // Rethrow the error to be handled by the calling function
  }
}

function addRecordToCollection({ collection, record }) {
  try {
    if (Array.isArray(collection)) {
      collection.push(record);
    }
  } catch (error) {
    logger.error("Problem added record to manual review: ", error);
  }
}

function isValueInvalid(value) {
  return (
    isNil(value) ||
    (typeof value === "string" && (value.trim() === "" || value === "null"))
  );
}

function getLabelValue({ selections, header }) {
  const labelMap = selections.labelMap ?? [];
  const label = labelMap.find((selection) => selection.startsWith(header));

  if (label) {
    const values = splitByTripleColon(label);
    // example value: [Address, Home]
    return values[1];
  }
}

function splitByTripleColon(str) {
  return str.split(":::");
}
