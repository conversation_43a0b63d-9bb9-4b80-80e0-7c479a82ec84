const { Timestamp, getFirestore } = require("firebase-admin/firestore");
exports.setMigrationLog = async function setMigrationLog({
  id,
  record,
  type,
  isSuccess,
  message = "",
  errorStack = "",
}) {
  const db = getFirestore();
  const idString = id.toString();
  const logRef = db.collection("MigrationLogs").doc(idString);
  try {
    await logRef.set({
      type,
      createdAt: Timestamp.now(),
      record,
      message,
      errorStack,
      isSuccess,
    });
  } catch (error) {
    logger.error("Error saving migration log: ", error.message, error.stack);
  }
};
