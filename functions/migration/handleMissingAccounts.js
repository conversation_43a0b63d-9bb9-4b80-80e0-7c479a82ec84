const { logger } = require("firebase-functions");
const {
  Timestamp,
  FieldValue,
  getFirestore,
} = require("firebase-admin/firestore");
const { dateStringToTimestamp } = require("../utils/dateStringToTimestamp");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { defineSecret, defineString } = require("firebase-functions/params");
const { isNil } = require("lodash");
const axios = require("axios");
const { getAstridCreds } = require("../utils/getAstridCreds");

exports.handleMissingAccount = async function handleMissingAccount(accountId) {
  const payload = { accountId };
  const { token, baseUrl } = await getAstridCreds();
  const astridRequest = {
    url: `users/account`,
    method: "GET",
    payload,
  };
  try {
    const astridUrl = `${baseUrl}/users/account?accountId=${accountId}`;
    const response = await axios({
      method: "GET",
      url: astridUrl,
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });
    const account = response?.data ?? {};
    const accountPlans = account?.plans ?? [];
    await setAccount(account);
    await setAccountPlan(accountPlans);
  } catch (error) {
    logger.error("handleMissingAccount: ", error);
    throw error;
  }
};

exports.isAccountDataMissing = async function isAccountDataMissing(accountId) {
  try {
    const db = getFirestore();
    const accountRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString());

    const accountSnapshot = await accountRef.get();

    const accountPlansRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.account.accountPlans);

    const accountPlansSnapshot = await accountPlansRef.get();

    if (accountSnapshot.exists) {
      const accountData = accountSnapshot.data();
      const {
        id: legacyAccountId,
        name,
        display_name: displayName,
        slug,
        timezone,
        market_uuid: marketUuid,
      } = accountData;
      const hasMissingData =
        isNil(name) ||
        isNil(displayName) ||
        isNil(slug) ||
        isNil(timezone) ||
        isNil(marketUuid);
      const hasNoPlans = accountPlansSnapshot.empty;
      if (hasMissingData || hasNoPlans) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  } catch (error) {
    console.error("Error checking account data missing: ", error);
    throw error;
  }
};

async function setAccount(accountRecord) {
  // logger.info("setAccount: ", accountRecord);
  const {
    id: legacyAccountId,
    name,
    display_name: displayName,
    slug,
    timezone,
    market_uuid: marketUuid,
    created_at: createdAt,
    updated_at: updatedAt,
    email_addresses: emailAddressRecords,
  } = accountRecord ?? {};
  // logger.info(
  //   "name: ",
  //   name,
  //   "displayName: ",
  //   displayName,
  //   "slug: ",
  //   slug,
  //   "timezone: ",
  //   timezone
  // );
  try {
    const db = getFirestore();
    const accountId = legacyAccountId?.toString();
    const accountRef = db.collection(collectionNames.account).doc(accountId);
    await accountRef.set(
      {
        name,
        displayName,
        slug,
        timezone,
        marketUuid,
        createdAt: dateStringToTimestamp(createdAt),
        isActive: true,
        lastUpdated: Timestamp.now(),
        ...{ updatedAt: dateStringToTimestamp(updatedAt) },
        isRmcMigration: true,
      },
      { merge: true }
    );
  } catch (error) {
    logger.error(
      "Problem writing accountDetails for accountId: ",
      legacyAccountId
    );
  }
}

async function setAccountPlan(accountPlans) {
  for (const accountPlanRecord of accountPlans) {
    // logger.info("accountPlanRecord: ", accountPlanRecord);
    const {
      id: accountPlanId,
      plan_id: planId,
      account_id: accountId,
      active_at: activeAt,
      created_at: createdAt,
      updated_at: updatedAt,
    } = accountPlanRecord;
    try {
      const db = getFirestore();
      const accountPlanRef = db
        .collection(collectionNames.account)
        .doc(accountId.toString())
        .collection(subCollectionNames.account.accountPlans)
        .doc(planId.toString());

      await accountPlanRef.set({
        planId: planId.toString(),
        accountId: accountId.toString(),
        accountPlanId: accountPlanId.toString(),
        createdAt: dateStringToTimestamp(createdAt),
        activeAt: dateStringToTimestamp(activeAt),
        isActive: true,
        ...{ updatedAt: dateStringToTimestamp(updatedAt) },
      });
    } catch (error) {
      logger.error(error);
    }
  }
}

async function setAccountGroups(accountGroup) {
  const { groupId, groupName, accountId } = accountGroup;
  const db = getFirestore();
  const accountGroupRef = db
    .collection(collectionNames.account)
    .doc(accountId.toString())
    .collection(subCollectionNames.account.groups)
    .doc(groupId.toString());

  await accountGroupRef.set(
    {
      name: groupName,
    },
    { merge: true }
  );
}
