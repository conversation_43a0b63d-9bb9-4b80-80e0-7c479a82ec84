// https://firebase.google.com/docs/functions/gcp-storage-events?gen=2nd
const { onObjectFinalized } = require("firebase-functions/v2/storage");
const { getStorage } = require("firebase-admin/storage");
const { Timestamp } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const { dateStringToTimestamp } = require("../../utils/dateStringToTimestamp");
const { handleRecipientGroup } = require("./handleRecipientGroup");
const { handleRecipient } = require("./handleRecipient");
const { defineString } = require("firebase-functions/params");

const RECIPIENT_GROUP_PATH = "/recipient-groups/";
const RECIPIENT_PATH = "/recipients/";
const migrationStorageBucket = defineString("MIGRATION_STORAGE_BUCKET");
/**
 * onRecipientGroupFileCreated
 * watches for files uploaded to recipient-group cloud storage directory
 * parses the JSON file and writes data to RecipientGroups subcollection
 *
 * onObjectFinalized
 * Sent when a new object (or a new generation of an existing object) is successfully created in the bucket.
 * This includes copying or rewriting an existing object. A failed upload does not trigger this event.
 */

exports.onMigrationFileCreate = (db) =>
  onObjectFinalized(
    {
      bucket: migrationStorageBucket.value(),
      timeoutSeconds: 540,
      concurrency: 20,
      memory: "512MB",
    },
    async (event) => {
      const fileBucket = event.data.bucket; // Storage bucket containing the file.
      const filePath = event.data.name; // File path in the bucket.
      const contentType = event.data.contentType; // File content type.
      const bucket = getStorage().bucket(fileBucket);

      /**
       * Handle Recipient Groups
       */
      if (filePath.includes(RECIPIENT_GROUP_PATH)) {
        await handleRecipientGroup({ db, bucket, filePath, contentType });
        return;
      }
      if (filePath.includes(RECIPIENT_PATH)) {
        await handleRecipient({ db, bucket, filePath, contentType });
        return;
      }
    }
  );
