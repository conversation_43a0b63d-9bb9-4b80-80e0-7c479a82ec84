const { onCall } = require("firebase-functions/v2/https");
const { defineString, HttpsError } = require("firebase-functions/params");
const axios = require("axios");

// Retrieve the token from environment variables
// const token = defineString("DAM_TOKEN");

const baseUrl = defineString("DAM_URL");

exports.callDam = onCall(async (request) => {
  const { payload, url, method = "GET" } = request.data;
  const damUrl = `${baseUrl.value()}/${url}`;

  try {
    const response = await axios({
      method: method,
      url: damUrl,
      headers: {
        // hard coded, we switching to JWT soon(tm)
        Authorization: `Bearer d1xzOk7kZjCrMCL7UkGJOKC8vn9Nx7lR`,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      data: payload,
    });
    return { data: response.data };
  } catch (error) {
    console.error("Error making HTTP request:", error);
    return { error: error.message };
  }
});
