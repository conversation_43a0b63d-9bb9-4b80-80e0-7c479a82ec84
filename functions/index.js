const admin = require("firebase-admin");
const { onCall } = require("firebase-functions/v2/https");
const { getFirestore } = require("firebase-admin/firestore");
const { rmcAccountSync } = require("./http/account");
const {
  beforeCreateUser,
  verifyInternalUserEmail,
} = require("./users/auth/user");
const { adminCreateUser } = require("./users/userManagement/admin-create-user");
const { onWriteAccount } = require("./accounts/accountCollection");
const { callDam } = require("./dam/damInterface");
const { callMercury } = require("./callable/callMercury");
const { callGaia } = require("./callable/callGaia");
const {
  reconcileMailingAddressClaims,
} = require("./callable/exclusiveMailings/reconcileMailingAddressClaims");
const { addToTopic } = require("./callable/addToTopic");
const { addNotificationWorker } = require("./pubsubs/addNotificationWorker");
const {
  markEmailAndMailingDuplicatesWorker,
} = require("./pubsubs/markEmailAndMailingDuplicatesWorker");
const { getEnterpriseStats } = require("./enterprise/enterpriseStats");
const { onCreateEnterprise } = require("./enterprise/onCreateEnterprise");
const {
  checkEnterpriseAssociation,
  enterpriseList,
  removeFromEnterprise,
  assignToEnterprise,
} = require("./enterprise/enterpriseAccountCalls");
const {
  massAssignToEnterprise,
} = require("./enterprise/massAssigntoEnterprise");
const {
  shortLivedTokenForCustomers,
  shortLivedTokenForAdmins,
} = require("./http/shortLivedTokenForCustomers");
const { lead } = require("./http/lead/lead");
const { recordRecipientVisit } = require("./http/lead/recordRecipientVisit");
const { onDeleteRecipient } = require("./recipients/onDeleteRecipient");
const { onUpdateRecipient } = require("./recipients/onUpdateRecipient");

const {
  onCreateRecipientMailingAddress,
} = require("./recipients/userEnteredMailingAddress/onCreateRecipientMailingAddress");
const {
  onUpdateRecipientMailingAddress,
} = require("./recipients/userEnteredMailingAddress/onUpdateRecipientMailingAddress");
const {
  onWriteRecipientEmailAddress,
} = require("./recipients/onWriteRecipientEmailAddress");
const {
  onDeleteRecipientEmailAddress,
} = require("./recipients/onDeleteRecipientEmailAddress");
const {
  onCreateExclusivityAction,
} = require("./recipients/exclusivity/onCreateExclusivityAction.js");

const {
  createShortUrl,
  trackClick,
  linkDomains,
  linkData,
  lpData,
  domainUpdate,
  shortURLTraffic,
  lpAnalytics,
} = require("./http/shortening/shorten");
const { crmEventWorker } = require("./pubsubs/crmEventWorker");
const { askChatGPT } = require("./callable/chatGPT");
const {
  extendRecipientProperty,
} = require("./callable/extendRecipientProperty");
const {
  onCreateProductPlan,
  onDeleteProductPlan,
} = require("./accounts/accountPlansSubCollection");
const { productPlansWorker } = require("./pubsubs/productPlansWorker");
const {
  onMigrationFileCreate,
} = require("./migration/storageTriggers/migration");
const { recipient } = require("./http/recipient/recipient");
const { recipients } = require("./http/recipient/recipients");
const {
  recipientGroupAssignments,
} = require("./http/recipient/recipientGroupAssignments");
const { recipientGroups } = require("./http/recipient/recipientGroups");
const {
  recipientGroupsForPrint,
} = require("./http/recipient/recipientGroupsForPrint");
const { updateAstridKey } = require("./schedule/updateAstridKey");
const { callAstrid } = require("./astrid/astridInterface");
const { onWriteRecipientGroup } = require("./recipients/onWriteRecipientGroup");
const { searchByPhoneWorker } = require("./pubsubs/searchByPhoneWorker");
const {
  downloadRecipientsWorker,
} = require("./pubsubs/downloadRecipientsWorker");
const {
  recipientsByCampaign,
} = require("./http/recipient/recipientsByCampaign");
const {
  recipientGroupSummary,
} = require("./http/recipient/recipientGroupSummary");
const { getSuggestions } = require("./callable/GetSuggestions");

const {
  scheduleReleaseExclusivityWorker,
} = require("./pubsubs/scheduleReleaseExclusivityWorker");
const {
  removeScheduledReleaseExclusivityWorker,
} = require("./pubsubs/removeScheduledReleaseExclusivityWorker");
const { onRecipientFileImport } = require("./storage/recipientImportsBucket");
const { generateSignedUrl } = require("./callable/generateSignedUrl");
const { releaseExclusivity } = require("./schedule/releaseExclusivity");
const { releaseExclusivityNow } = require("./callable/releaseExclusivityNow");
const {
  handleMailingProductRemovedFromGroup,
} = require("./tasks/handleMailingProductRemovedFromGroup");
const {
  handleMailingProductAddedToGroup,
} = require("./tasks/handleMailingProductAddedToGroup");
const {
  handleEmailProductAddedToGroup,
} = require("./tasks/handleEmailProductAddedToGroup");
const {
  reconcileMailingAddressClaimsTask,
} = require("./tasks/reconcileAddressClaims/reconcileMailingAddressClaimsTask");
const {
  handleReprocessUserEnteredMailingAddressTask,
} = require("./tasks/handleReprocessUserEnteredMailingAddressTask");
const { fetchCredentials } = require("./callable/fetchCredentials");
const { check } = require("./checks/stateCheck");
const { trackEvent } = require("./http/shortening/trackEvent");
const { onCreateAnalyticsData } = require("./analytics/onCreateAnalyticsData");

// API V1 functions
const { api } = require("./api/api");
const { apiKeyGenerate } = require("./callable/apiKeyGenerate");
const {
  syncAccountPlanWithMercury,
} = require("./accounts/syncAccountPlanWithMercury");
const { featureFlags } = require("./http/featureFlags/featureFlags");
const { syncAccounts } = require("./schedule/syncAccount");

const { syncAccountPlans } = require("./schedule/syncAccountPlans");
const { updateSyncDaily } = require("./schedule/updateSyncDaily");
const { regenerateQrCodes } = require("./schedule/regenerateQrCodes");
const {
  regenerateQrCodesWorker,
} = require("./pubsubs/regenerateQrCodesWorker");
const {
  createWorkflowExecutionWorker,
} = require("./pubsubs/createWorkflowExecutionWorker");
const { executeWorkflowActions } = require("./schedule/executeWorkflowActions");
const { recipientsForLegacy } = require("./http/recipient/recipientsForLegacy");
const {
  cleanupEmptyMigrationFolderMarkers,
} = require("./schedule/cleanupEmptyMigrationFolderMarkers");

const {
  checkForDuplicatesWorker,
} = require("./pubsubs/checkForDuplicatesWorker");
const {
  processPendingDeliverabilityRecords,
} = require("./schedule/processPendingDeliverabilityRecords");

const { getAccount } = require("./http/account/getAccount");
const {
  getAccountIntegrations,
} = require("./http/account/getAccountIntegrations");
const { dataSourceRequest } = require("./callable/dataSourceCalls");
const {
  executeScheduledEndpoint,
} = require("./callable/executeScheduledEndpoint");
const { scheduleExecutor } = require("./schedule/scheduleExecutor");
const { createAccount } = require("./http/account/createAccount");
const { migratedRecipient } = require("./http/recipient/migratedRecipient");
const {
  migratedRecipientGroup,
} = require("./http/recipient/migratedRecipientGroup");
const {
  onUpdateMailingAddressSource,
} = require("./mailingAddresses/onUpdateMailingAddressSource");
const {
  createWorkflowFromTemplate,
} = require("./callable/workflows/createWorkflowFromTemplate");
const { updateAccount } = require("./http/account/updateAccount");
const { onWriteMarket } = require("./localEvents/onWriteMarket");
const { getImagesFromBucket } = require("./unlayer/editorImages.js");

const {
  recipientGroupCounts,
} = require("./http/recipient/recipientGroupCounts");
const {
  onCreateWaitingList,
} = require("./mailingAddresses/onCreateWaitingList");
const {
  onDeleteWaitingList,
} = require("./mailingAddresses/onDeleteWaitingList");

const { cyclrApiInterface } = require("./integrations/cyclrApiInterface");
const { cyclrWebhook } = require("./integrations/cyclrWebhook");
const { integrationsUtils } = require("./integrations/integrationsUtils");
const { emailPreProcess } = require("./http/email/emailPreProcess");
const { emailPreProcessWorker } = require("./pubsubs/emailPreProcessWorker");
const {
  emailRecipientChunkWorker,
} = require("./pubsubs/emailRecipientChunkWorker");
const { getWorkflow } = require("./http/workflows/getWorkflow.js");
const { ogEventSync } = require("./schedule/ogEventSync");
const { ogMarketSync } = require("./schedule/ogMarketSync");
const { ogTagSync } = require("./schedule/ogTagSync");
const { getEventsByArea, searchAreas } = require("./http/localEvents/api.js");
const {
  getAccountMagazineRecipients,
  getRecipientGroupCounts,
} = require("./recipients/getRecipientGroupCounts.js");
const { accountPlans } = require("./http/account/accountPlans.js");
const { syncAccountManager } = require("./http/account/syncAccountManager");
const { updateUserEmail } = require("./http/email/updateUserEmail");
const { getLiveEvents } = require("./http/localEvents/allEvents.js");
const {
  getAllMarkets,
  getMarketStats,
} = require("./http/localEvents/getAllMarkets.js");
const {
  uploadPublishedTemplate,
} = require("./unlayer/uploadPublishedTemplate");

const { uploadOptionFiles } = require("./unlayer/uploadOptionFiles");
const { getWorkflowExecution } = require("./http/workflows/getWorkflowExecution.js");
const { previewEmail } = require("./http/previewEmail.js")

admin.initializeApp();
const db = getFirestore();

// User Management
exports.adminCreateUser = adminCreateUser(db);
// RMC USER MANAGEMENT
exports.syncAccountManager = syncAccountManager(admin, db);
exports.updateUserEmail = updateUserEmail(admin, db);

// AUTH
exports.authBeforeCreateUser = beforeCreateUser(admin);
exports.verifyInternalUserEmail = verifyInternalUserEmail(admin);

// ACCOUNTS
exports.onWriteAccount = onWriteAccount;
exports.featureFlag = featureFlags(db);
exports.getAccount = getAccount(db);
exports.getAccountIntegrations = getAccountIntegrations(db);
exports.updateAccount = updateAccount(db);
exports.createAccount = createAccount;
exports.accountPlans = accountPlans(db);

// WORKFLOWS
exports.getWorkflow = getWorkflow(db);
exports.getWorkflowExecution = getWorkflowExecution(db);
exports.createWorkflowFromTemplate = createWorkflowFromTemplate(db);

// ACCOUNT_PLANS
exports.syncAccountPlanWithMercury = syncAccountPlanWithMercury;

//ADDRESS
exports.getSuggestions = getSuggestions;

// Product Plans
exports.onCreateProductPlan = onCreateProductPlan(db);
exports.onDeleteProductPlan = onDeleteProductPlan(db);

// Sync RMC Account Data
exports.rmcAccountSync = rmcAccountSync(db);

// Leads & Visits
exports.lead = lead(db);
exports.recordRecipientVisit = recordRecipientVisit(db);

// Mailing Addresses
exports.onUpdateMailingAddressSource = onUpdateMailingAddressSource;

// Waiting List
exports.onCreateWaitingList = onCreateWaitingList;
exports.onDeleteWaitingList = onDeleteWaitingList;

// Recipients

exports.onUpdateRecipient = onUpdateRecipient(db);
exports.onDeleteRecipient = onDeleteRecipient(db);
exports.onCreateRecipientMailingAddress = onCreateRecipientMailingAddress(db);
exports.onUpdateRecipientMailingAddress = onUpdateRecipientMailingAddress(db);
exports.onWriteRecipientEmailAddress = onWriteRecipientEmailAddress(db);
exports.onDeleteRecipientEmailAddress = onDeleteRecipientEmailAddress(db);
exports.markEmailAndMailingDuplicatesWorker =
  markEmailAndMailingDuplicatesWorker;
exports.extendRecipientProperty = extendRecipientProperty(db);
exports.onWriteRecipientGroup = onWriteRecipientGroup(db);
exports.searchByPhoneWorker = searchByPhoneWorker(db);
exports.downloadRecipientsWorker = downloadRecipientsWorker(admin);
exports.recipientsByCampaign = recipientsByCampaign(db);
exports.scheduleReleaseExclusivityWorker = scheduleReleaseExclusivityWorker(db);
exports.removeScheduledReleaseExclusivityWorker =
  removeScheduledReleaseExclusivityWorker(db);
exports.migratedRecipient = migratedRecipient;
exports.migratedRecipientGroup = migratedRecipientGroup;
exports.recipientGroupCounts = recipientGroupCounts(db);
exports.onCreateExclusivityAction = onCreateExclusivityAction(db);
exports.getAccountMagazineRecipients = getAccountMagazineRecipients(db);
exports.getRecipientGroupCounts = getRecipientGroupCounts(db);

// Recipient Endpoint
exports.recipient = recipient(db);

// Recipient Group Endpoint
exports.recipients = recipients(db);

//Recipients For Legacy
exports.recipientsForLegacy = recipientsForLegacy(db);

// Recipient Group Summary
exports.recipientGroupSummary = recipientGroupSummary(db);

// Recipient Group Assignments
exports.recipientGroupAssignments = recipientGroupAssignments(db);

// Recipient Groups Endpoint
exports.recipientGroups = recipientGroups(db);
exports.recipientGroupsForPrint = recipientGroupsForPrint(db);

//Enterprise
exports.getEnterpriseStats = getEnterpriseStats(db);
exports.checkEnterpriseAssociation = checkEnterpriseAssociation(db);

//ASTRID
exports.callAstrid = callAstrid;

//DAM
exports.callDam = callDam;
exports.onCreateEnterprise = onCreateEnterprise(db);
exports.enterpriseList = enterpriseList(db);
exports.removeFromEnterprise = removeFromEnterprise(db);
exports.assignToEnterprise = assignToEnterprise(db);
exports.massAssignToEnterprise = massAssignToEnterprise(db);

// Callable Functions
exports.addToTopic = addToTopic;
exports.callMercury = callMercury;
exports.callGaia = callGaia;
exports.dataSourceRequest = dataSourceRequest;
exports.executeScheduledEndpoint = executeScheduledEndpoint;
exports.releaseExclusivityNow = releaseExclusivityNow;
exports.reconcileMailingAddressClaims = reconcileMailingAddressClaims;

// Local Events
exports.onWriteMarket = onWriteMarket;
exports.getLiveEvents = getLiveEvents;
exports.getEventsByArea = getEventsByArea;
exports.searchAreas = searchAreas;
exports.getAllMarkets = getAllMarkets;
exports.getMarketStats = getMarketStats;
exports.previewEmail = previewEmail;

// PubSubs
exports.addNotificationWorker = addNotificationWorker;
exports.crmEventWorker = crmEventWorker(db);
exports.productPlansWorker = productPlansWorker(db);
exports.regenerateQrCodesWorker = regenerateQrCodesWorker(db);
exports.createWorkflowExecutionWorker = createWorkflowExecutionWorker(db);
exports.checkForDuplicatesWorker = checkForDuplicatesWorker(db);
exports.regenerateQrCodesWorker = regenerateQrCodesWorker(db);

// Storage Triggers
exports.onMigrationFileCreate = onMigrationFileCreate(db);
exports.onRecipientFileImport = onRecipientFileImport(db);
exports.generateSignedUrl = generateSignedUrl(admin);

// external logins
exports.shortLivedTokenForCustomers = shortLivedTokenForCustomers(admin);
exports.shortLivedTokenForAdmins = shortLivedTokenForAdmins(admin);

// QR Codes
exports.shortenUrl = createShortUrl(db);
exports.trackClick = trackClick(db);
exports.linkDomains = linkDomains(db);
exports.linkData = linkData(db);
exports.lpCodes = lpData(db);
exports.domainUpdate = domainUpdate(db);
exports.shortURLTraffic = shortURLTraffic(db);
exports.lpAnalytics = lpAnalytics(db);

// ChatGPT
exports.askChatGPT = askChatGPT(db);

//Scheduled
exports.updateAstridKey = updateAstridKey;
exports.releaseExclusivity = releaseExclusivity;
exports.syncAccounts = syncAccounts(db);
exports.syncAccountPlans = syncAccountPlans(db);
exports.updateSyncDaily = updateSyncDaily(db);
exports.regenerateQrCodes = regenerateQrCodes(db);
exports.executeWorkflowActions = executeWorkflowActions;
exports.cleanupEmptyMigrationFolderMarkers = cleanupEmptyMigrationFolderMarkers;
exports.processPendingDeliverabilityRecords =
  processPendingDeliverabilityRecords;
exports.regenerateQrCodes = regenerateQrCodes(db);
exports.scheduleExecutor = scheduleExecutor;
exports.ogMarketSync = ogMarketSync;
exports.ogEventSync = ogEventSync;
exports.ogTagSync = ogTagSync;

// Tasks
exports.handleMailingProductRemovedFromGroup =
  handleMailingProductRemovedFromGroup;
exports.handleMailingProductAddedToGroup = handleMailingProductAddedToGroup;
exports.handleEmailProductAddedToGroup = handleEmailProductAddedToGroup;
exports.reconcileMailingAddressClaimsTask = reconcileMailingAddressClaimsTask;
exports.handleReprocessUserEnteredMailingAddressTask =
  handleReprocessUserEnteredMailingAddressTask;

  // Integrations
exports.cyclrApi = cyclrApiInterface(db);
exports.cyclrWebhook = cyclrWebhook;
exports.integrationsUtils = integrationsUtils(db);

// fetch api keys
exports.fetchCredentials = fetchCredentials;

// Analytics
exports.trackEvent = trackEvent(db);

//Checks
exports.heartbeat = check(db);

// External API
exports.api = api(db);
exports.apiKeyGenerate = apiKeyGenerate;

// Analytics Data Cleanup
exports.onCreateAnalyticsData = onCreateAnalyticsData(db);

//Unlayer
exports.getImagesFromBucket = getImagesFromBucket;
exports.uploadPublishedTemplate = uploadPublishedTemplate;
exports.uploadOptionFiles = uploadOptionFiles;

// Email Pre-Process
exports.emailPreProcess = emailPreProcess(db);
exports.emailPreProcessWorker = emailPreProcessWorker(admin, db);
exports.emailRecipientChunkWorker = emailRecipientChunkWorker(admin, db);
