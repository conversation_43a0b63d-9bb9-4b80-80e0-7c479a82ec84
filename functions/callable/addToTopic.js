const { onCall } = require("firebase-functions/v2/https");
const { PubSub } = require("@google-cloud/pubsub");
const pubSubClient = new PubSub();
const { logger } = require("firebase-functions");
const { defineString } = require("firebase-functions/params");
const { logToFirestore} = require("../utils/functionLogger");

const projectIdEnv = defineString("TITAN_PROJECT_ID");

exports.addToTopic = onCall(async (request) => {
  const { auth, data } = request;
  const { projectId=projectIdEnv.value(), topic, message } = data;

  await logToFirestore({
      functionName: "addToTopic",
      type: "error",
      message: `invocation for ${projectId} & ${topic}`,
      data: {
        projectId,
        topic,
        message,
      },
    });

  try {
    const fullTopicPath = `projects/${projectId}/topics/${topic}`;
    const messageString =
      typeof message === "string" ? message : JSON.stringify(message);
    const dataBuffer = Buffer.from(messageString);
    const messageId = await pubSubClient
      .topic(fullTopicPath)
      .publishMessage({ data: dataBuffer });
    await logToFirestore({
      functionName: "addToTopic",
      type: "info",
      message: `invocation for ${messageId}`,
      data: {
        projectId,
        topic,
        message,
      },
    });
    return { status: 200, message: `Message published with ID: ${messageId}` };
  } catch (error) {
    logger.error("Error publishing message:", error.message, error);
    return { status: 400, error: `Error publishing message: ${error.message}` };
  }
});
