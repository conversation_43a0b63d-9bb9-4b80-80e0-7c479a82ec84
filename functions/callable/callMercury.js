const { onCall } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const {
  callMercuryService,
} = require("../services/mercury/callMercuryService");

exports.callMercury = onCall(
  {
    memory: "1GiB",
  },
  async (request) => {
    const { data } = request;
    const { path, method, ...requestData } = data;

    // logger.info("path: ", path, "method: ", method, "requestData: ", requestData);
    try {
      let result;
      if (method === "GET") {
        // For GET and DELETE, pass requestData as params
        result = await callMercuryService({
          path,
          method,
          params: requestData,
        });
      } else if (
        method === "POST" ||
        method === "PUT" ||
        method === "PATCH" ||
        method === "DELETE"
      ) {
        // For POST, PUT, and PATCH, pass requestData as body
        result = await callMercuryService({
          path,
          method,
          body: requestData,
        });
      } else {
        throw new Error(`Unsupported HTTP method: ${method}`);
      }
      // logger.info("Success:", result);
      return result;
    } catch (error) {
      logger.error("Error in Mercury call:", error);
      return {
        status: "error",
        message: error?.response?.data?.message || error?.message,
      };
    }
  }
);
