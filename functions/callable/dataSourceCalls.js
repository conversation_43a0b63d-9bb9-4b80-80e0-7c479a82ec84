const functions = require('firebase-functions/v2');
const admin = require('firebase-admin');
const axios = require('axios');

exports.dataSourceRequest = functions.https.onCall(async (data, context) => {
  // Get the request parameters from the client
  const { dataSourceId, endpointId, params, body, method = 'GET' } = data?.data || {};
  
  try {
    // Validate the request
    if (!dataSourceId) {
      throw new functions.https.HttpsError('invalid-argument', 'Data source ID is required');
    }
    
    if (!endpointId) {
      throw new functions.https.HttpsError('invalid-argument', 'Endpoint ID is required');
    }
    
    // Fetch data source configuration from Firestore
    const dataSourceRef = admin.firestore().collection('DataSources').doc(dataSourceId);
    const dataSourceDoc = await dataSourceRef.get();
    
    if (!dataSourceDoc.exists) {
      throw new functions.https.HttpsError('not-found', `Data source with ID ${dataSourceId} not found`);
    }
    
    const dataSource = dataSourceDoc.data();
    
    // Find the requested endpoint
    const endpoint = dataSource.endpoints.find(e => e.id === endpointId);
    
    if (!endpoint) {
      throw new functions.https.HttpsError('not-found', `Endpoint with ID ${endpointId} not found`);
    }
    
    // Build the request URL
    let url = dataSource.baseUrl;
    if (!url.endsWith('/') && !endpoint.path.startsWith('/')) {
      url += '/';
    }
    url += endpoint.path;
    
    // Prepare request configuration
    const axiosConfig = {
      method: method || endpoint.method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...dataSource.headers
      }
    };
    
    // Add query parameters
    if (params && Object.keys(params).length > 0) {
      axiosConfig.params = {};
      
      // Add default params from data source
      if (dataSource.defaultParams) {
        Object.assign(axiosConfig.params, dataSource.defaultParams);
      }
      
      // Add request-specific params
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          axiosConfig.params[key] = value;
        }
      });
    }
    
    // Handle authentication
    if (dataSource.authMethod === 'apiKey' && dataSource.authConfig) {
      if (dataSource.authConfig.in === 'header') {
        axiosConfig.headers[dataSource.authConfig.name] = dataSource.authConfig.value;
      } else if (dataSource.authConfig.in === 'query') {
        // Add API key as query parameter
        if (!axiosConfig.params) {
          axiosConfig.params = {};
        }
        axiosConfig.params[dataSource.authConfig.name] = dataSource.authConfig.value;
      }
    } else if (dataSource.authMethod === 'basic' && dataSource.authConfig) {
      axiosConfig.auth = {
        username: dataSource.authConfig.username,
        password: dataSource.authConfig.password
      };
    } else if ((dataSource.authMethod === 'jwt' || dataSource.authMethod === 'bearer') && dataSource.authConfig) {
      axiosConfig.headers['Authorization'] = `Bearer ${dataSource.authConfig.token}`;
    } else if (dataSource.authMethod === 'custom' && dataSource.authConfig) {
      // Custom authentication header
      axiosConfig.headers[`${dataSource.authConfig.headerName}`] = `${dataSource.authConfig.headerValue}`;
    }
    
    // Add request body for non-GET requests
    if (axiosConfig.method !== 'GET' && body) {
      axiosConfig.data = body;
    }
    
    console.log(`Making ${axiosConfig.method} request to: ${url}`);
    
    // Make the request
    const response = await axios(axiosConfig);
    
    // Response data handling is simpler with axios as it automatically 
    // parses JSON and handles different content types
    let responseData = response.data;
    
    // Apply transformation if configured
    if (dataSource.transformations && dataSource.transformations[endpointId]) {
      try {
        const transformFn = new Function('response', dataSource.transformations[endpointId]
          .replace('function transform(response) {', '')
          .replace(/}$/, '')
          + 'return response;');
        
        responseData = transformFn(responseData);
      } catch (error) {
        console.error(`Error applying transformation: ${error.message}`);
      }
    }
    
    // Return the result
    return {
      statusCode: response.status,
      headers: response.headers,
      data: responseData,
      success: true
    };
    
  } catch (error) {
    console.error('Error in makeApiRequest:', error);
    
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    
    // Handle Axios errors
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      return {
        statusCode: error.response.status,
        headers: error.response.headers,
        data: error.response.data,
        success: false
      };
    } else if (error.request) {
      // The request was made but no response was received
      throw new functions.https.HttpsError(
        'failed-precondition',
        'No response received from the server',
        { originalError: error.toString() }
      );
    }
    
    throw new functions.https.HttpsError(
      'internal',
      error.message || 'An unknown error occurred',
      { originalError: error.toString() }
    );
  }
});