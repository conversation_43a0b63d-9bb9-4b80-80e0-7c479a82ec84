const { propertyServiceByAddress } = require("../utils/propertyService");
const { onCall } = require("firebase-functions/v2/https");

exports.extendRecipientProperty = (db) =>
  onCall(async (request) => {
    const { auth, data } = request;
    const { addressId, recipientId, accountId } = data;

    if (!addressId) {
      console.error("Error: Missing address ID.");
      return { error: "Missing addressId." };
    }

    try {
      const response = await propertyServiceByAddress(db, addressId);

      const recipientRef = db
        .collection("Account")
        .doc(accountId)
        .collection("Recipients")
        .doc(recipientId);
      recipientRef.update({ avm_score: response.avm_score });
      return response;
    } catch (error) {
      console.error("Error fetching property data:", error);
      return { error: "Failed to fetch property data." };
    }
  });
