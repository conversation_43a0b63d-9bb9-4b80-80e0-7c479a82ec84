const { onCall } = require("firebase-functions/v2/https");
const { getAuth } = require("firebase-admin/auth");
const { algoliasearch } = require("algoliasearch");
const { defineString, defineSecret } = require("firebase-functions/params");
const { logger } = require("firebase-functions");

const titanWriteAPIKey = defineSecret("TITAN_WRITE_API_KEY");
const mercuryWriteAPIKey = defineSecret("MERCURY_WRITE_API_KEY");
const algoliaAppId = defineSecret("ALGOLIA_APP_ID");

const titanAppID = defineSecret("TITAN_ALGOLIA_APP_ID");
const titanReadAPIKey = defineSecret("TITAN_ALGOLIA_API_KEY");
const mercuryAppID = defineSecret("MERCURY_ALGOLIA_APP_ID");
const mercuryReadAPIKey = defineSecret("MERCURY_ALGOLIA_API_KEY");

const tinymceApiKey = defineSecret("TINYMCE_API_KEY");

exports.fetchCredentials = onCall(async (request) => {
  const validUntil = Math.floor(Date.now() / 1000) + 28800;
  try {
    // Verify that the user is authenticated
    if (!request.auth || !request.auth.uid) {
      throw new Error("Unauthenticated user. Please log in.");
    }

    let uid, userRecord;
    try {
      uid = request.auth.uid;
      userRecord = await getAuth().getUser(uid);
    } catch (error) {
      logger.error("Error fetching user from Firebase Authentication:", error);
    }

    // Define environment variables
    let credentials;
    const tinymceKeyValue = tinymceApiKey.value();
    try {
      credentials = {
        VITE_TINYMCE_API_KEY: tinymceKeyValue.replace(/\["|"\]|\s/g, ""),
      };
    } catch (error) {
      logger.error("Error loading environment variables:", error);
    }

    if (userRecord.customClaims) {
      const titanWriteAPIKeyString = titanWriteAPIKey.value();
      const mercuryWriteAPIKeyString = mercuryWriteAPIKey.value();
      const algoliaAppIdString = algoliaAppId.value();
      const titanAppIDString = titanAppID.value();
      const titanReadAPIKeyString = titanReadAPIKey.value();
      const mercuryAppIDString = mercuryAppID.value();
      const mercuryReadAPIKeyString = mercuryReadAPIKey.value();

      console.log(
        "titanWriteAPIKeyString",
        titanWriteAPIKeyString,
        mercuryWriteAPIKeyString,
        algoliaAppIdString,
        titanAppIDString,
        titanReadAPIKeyString,
        mercuryAppIDString,
        mercuryReadAPIKeyString
      );

      const titanWriteAPIKeyValue = titanWriteAPIKeyString;
      const mercuryWriteAPIKeyValue = mercuryWriteAPIKeyString;
      const algoliaAppIdValue = algoliaAppIdString;
      const titanAppIDValue = titanAppIDString;
      const titanReadAPIKeyValue = titanReadAPIKeyString;
      const mercuryAppIDValue = mercuryAppIDString;
      const mercuryReadAPIKeyValue = mercuryReadAPIKeyString;

      try {
        let algoliaClient, titanClient, mercuryClient;

        try {
          algoliaClient = algoliasearch(
            algoliaAppIdValue[0],
            titanWriteAPIKeyValue[0]
          );
          titanClient = algoliasearch(
            titanAppIDValue[0],
            titanWriteAPIKeyValue[0]
          );
          mercuryClient = algoliasearch(
            mercuryAppIDValue[0],
            mercuryWriteAPIKeyValue[0]
          );
        } catch (error) {
          logger.error("Error creating Algolia clients:", error);
        }

        let algoliaSecureKey, titanSecureKey, mercurySecureKey;

        try {
          algoliaSecureKey = await algoliaClient.generateSecuredApiKey({
            parentApiKey: titanReadAPIKeyValue[0],
            restrictions: {
              validUntil: validUntil,
            },
          });
        } catch (error) {
          logger.error(
            "Error generating secured API key:",
            error,
            titanReadAPIKeyValue[0]
          );
        }

        try {
          titanSecureKey = titanClient.generateSecuredApiKey({
            parentApiKey: titanReadAPIKeyValue[0],
            restrictions: {
              validUntil: validUntil,
            },
          });
        } catch (error) {
          logger.error(
            "Error generating secured API key for titan:",
            error,
            titanReadAPIKeyValue[0]
          );
        }

        try {
          mercurySecureKey = mercuryClient.generateSecuredApiKey({
            parentApiKey: mercuryReadAPIKeyValue[0],
            restrictions: {
              validUntil: validUntil,
            },
          });
        } catch (error) {
          logger.error(
            "Error generating secured API key for mercury",
            error.message,
            mercuryReadAPIKeyValue[0]
          );
        }

        return {
          status: "success",
          credentials: {
            ...credentials,
            REACT_APP_ALGOLIA_API_KEY: algoliaSecureKey,
            REACT_APP_MERCURY_ALGOLIA_API_KEY: mercurySecureKey,
            REACT_APP_TITAN_ALGOLIA_API_KEY: titanSecureKey,
          },
        };
      } catch (error) {
        logger.error(
          "Error generating secured API keys for internal user:",
          error
        );
      }
    } else {
      throw new Error("Invalid user type or missing custom claims.");
    }
  } catch (error) {
    logger.error("Error fetching credentials:", error);
    return {
      status: "error",
      message: error.message,
    };
  }
});
