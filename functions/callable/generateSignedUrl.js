const { onCall, HttpsError } = require("firebase-functions/v2/https");
const { isNil } = require("lodash");
const { logger } = require("firebase-functions");
exports.generateSignedUrl = (admin) =>
  onCall(async (request) => {
    try {
      const { data, auth } = request;
      const { filePath, bucketName } = data;
      // logger.info("Request data:", data);

      // Check authentication
      if (!auth) {
        logger.error("No authentication provided");
        throw new HttpsError("unauthenticated", "User not authenticated");
      }
      let file;
      if (isNil(bucketName)) {
        file = admin.storage().bucket().file(filePath);
      } else {
        file = admin.storage().bucket(bucketName).file(filePath);
      }

      // Generate a signed URL valid for 10 minutes
      const [url] = await file.getSignedUrl({
        action: "read",
        expires: Date.now() + 10 * 60 * 1000, // 10 minutes from now
      });

      // logger.info("Signed URL:", url);

      return { url };
    } catch (error) {
      logger.error("Problem generating signed url: ", error);
    }
  });
