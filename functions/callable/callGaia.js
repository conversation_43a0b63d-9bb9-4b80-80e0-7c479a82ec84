const { onCall } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const { callGaiaService } = require("../services/gaia/callGaiaService");

exports.callGaia = onCall(
  {
    memory: "1GiB",
  },
  async (request) => {
    const { data } = request;
    const { path, method, params: clientParams, body: clientBody } = data;

    // logger.info("path: ", path, "method: ", method, "requestData: ", requestData);
    try {
      let result;
      if (method === "GET") {
        // For GET and DELETE, pass requestData as params
        result = await callGaiaService({
          path,
          method,
          params: clientParams || {},
        });
      } else if (
        method === "POST" ||
        method === "PUT" ||
        method === "PATCH" ||
        method === "DELETE"
      ) {
        result = await callGaiaService({
          path,
          method,
          body: clientBody || {},
        });
      } else {
        throw new Error(`Unsupported HTTP method: ${method}`);
      }
      // logger.info("Success:", result);
      return result;
    } catch (error) {
      logger.error("Error in Gaia call:", error);
      return {
        status: "error",
        message: error?.response?.data?.message || error?.message,
      };
    }
  }
);
