const { propertyServiceByAddress } = require("../utils/propertyService");
const { onCall } = require("firebase-functions/v2/https");

exports.getRecipientGroupData = (db) => onCall(async (request) => {
    const { auth, data } = request;
    const { addressId } = data;

    if (!addressId) {
        console.error("Error: Missing address ID.");
        return { error: "Missing addressId." };
    }

    try {
        const response = propertyServiceByAddress(db, addressId); 

        return response;
    } catch (error) {
        console.error("Error fetching property data:", error);
        return { error: "Failed to fetch property data." };
    }
});