const { onCall } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const crypto = require("crypto");

exports.apiKeyGenerate = onCall(async (request) => {
  const { auth } = request;
  try {
    function generateApiKey(length = 32) {
      const apiKey = crypto.randomBytes(length).toString("hex");
      // logger.info("apiKey: ", apiKey);
      return apiKey;
    }
    const apiKey = generateApiKey();
    return { status: 200, apiKey: apiKey };
  } catch (error) {
    logger.error("Error publishing message:", error.message, error);
    return { status: 400, error: `Error publishing message: ${error.message}` };
  }
});
