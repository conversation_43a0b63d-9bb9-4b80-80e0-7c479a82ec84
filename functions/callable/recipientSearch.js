const { get } = require("axios");
const { onCall } = require("firebase-functions/v2/https");
const {hashString, preProcessAddressString} = require("../utils/hash/hashStrings");
const { collectionNames } = require("../constants/collectionNames");

exports.recipientSearch = (db) => onCall(async (request) => {
    const { auth, data } = request;
    const { address, radius, score = 900 } = data;
    let pid = null;

    const url = process.env.SMARTZIP_URL;
    const key = process.env.SMARTZIP_KEY;

    if(address && address?.street !== " "){
        const propertySearch = await get(`${url}/properties/details.json`, {
            params: {
                api_key: key,
                street_address: address?.street,
                city: address?.city,
                state: address?.state,
                zip: address?.zip,
            }
        });
        pid = propertySearch.data?.property.id || null;
    }

    let response = {};
    let summary = {};

    try {
        if(!pid){
            response = await get(`${url}/properties/bulk_score.json`, {
                params: {
                    api_key: key,
                    zip: address?.zip,
                    max_results: "500",
                    deliverable_owner_only: "Yes",
                    include_mailing_addresses: true,
                    llc_trust_business: "False",
                    min_score: Number(score),
                    radius: radius,
                }
            });
            summary = await get(`${url}/properties/bulk_score_summary.json`, {
                params: {
                    api_key: key,
                    zip: address?.zip,
                    deliverable_owner_only: "Yes",
                    include_mailing_addresses: true,
                    llc_trust_business: "False",
                    min_score: score,
                    radius: radius,
                }
            });
        } else {
            response = await get(`${url}/properties/bulk_score.json`, {
                params: {
                    api_key: key,
                    property_id: pid,
                    max_results: "500",
                    deliverable_owner_only: "Yes",
                    include_mailing_addresses: true,
                    llc_trust_business: "False",
                    min_score: score,
                    radius: radius,
                }
            });
            summary = await get(`${url}/properties/bulk_score_summary.json`, {
                params: {
                    api_key: key,
                    property_id: pid,
                    deliverable_owner_only: "Yes",
                    include_mailing_addresses: true,
                    llc_trust_business: "False",
                    min_score: score,
                    radius: radius,
                }
            });
        }

        const filteredProperties = [];

        const properties = response.data?.properties || [];
        for (const property of properties) {
            const address = {
                address1: property.address,
                address2: "",
                city: property.city,
                state: property.state,
                zip: property.zip,
            }
            const addressId = hashString(address, preProcessAddressString);
            const addressRef = db.collection(collectionNames.mailingAddresses).doc(addressId);
            const addressFound = await addressRef.get();
            if (!addressFound.exists) {
                filteredProperties.push(property);
            }
        }
        const resp =  { count: filteredProperties.length, properties: filteredProperties, totalCount: summary.data?.properties_count };
        
        return resp;
    } catch (error) {
        console.error("Error fetching property data:", error);
        return { error: "Failed to fetch property data." };
    }
});