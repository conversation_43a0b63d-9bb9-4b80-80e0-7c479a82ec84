const { onCall } = require("firebase-functions/v2/https");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const {
  Timestamp,
  FieldValue,
  getFirestore,
} = require("firebase-admin/firestore");
const {
  hasMailingProductAssociation,
  releaseExclusivity,
} = require("../recipients/exclusivity/helpers");

exports.releaseExclusivityNow = onCall(async (request) => {
  const { accountId, sourceMailingAddressId, recipientId, action } =
    request.data;
  try {
    const db = getFirestore();
    const recipientRef = db
      .collection(collectionNames.account)
      .doc(accountId)
      .collection(subCollectionNames.contacts.recipients)
      .doc(recipientId);

    const sourceMailingAddressRef = db
      .collection(collectionNames.mailingAddresses)
      .doc(sourceMailingAddressId);

    const recipientGroupCollectionRef = db
      .collection(collectionNames.account)
      .doc(accountId)
      .collection(subCollectionNames.contacts.recipientGroups);

    /**
     * here we want to ensure the recipient does NOT have mailing product association
     * they were scheduled for exclusivity release because they were either "deleted", removed from a group with a mailing product, or
     * the recipient group that they are assigned to was disassociated with a mailing product and
     * they are not assigned to any other group in the account with a mailing product.
     */
    const hasProductAssociation = await hasMailingProductAssociation({
      recipientRef,
      recipientGroupCollectionRef,
    });

    const shouldRelease = !hasProductAssociation;

    if (shouldRelease) {
      await releaseExclusivity({
        accountId,
        action,
        sourceMailingAddressRef,
        recipientRef,
      });
    }
  } catch (error) {}
});
