const { onCall } = require("firebase-functions/v2/https");
const { getFunctions } = require("firebase-admin/functions");

const { defineString } = require("firebase-functions/params");
const projectIdEnv = defineString("TITAN_PROJECT_ID");
const projectId = projectIdEnv.value();
const location = "us-central1";
const reconcileMailingAddressClaimsTask = `projects/${projectId}/locations/${location}/functions/reconcileMailingAddressClaimsTask`;

exports.reconcileMailingAddressClaims = onCall(async (request) => {
  const functions = getFunctions();
  const { accountId, code = "ALL" } = request.data;

  console.log(
    `Running reconcileMailingAddressClaims... with accountIds: ${accountId} and code: ${code}`
  );

  const taskPayload = {
    accountId,
    code,
  };

  if (accountId && code) {
    const taskQueue = functions.taskQueue(reconcileMailingAddressClaimsTask);
    await taskQueue.enqueue(taskPayload);
  } else {
  }
});
