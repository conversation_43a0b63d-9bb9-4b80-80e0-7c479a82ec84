const { get } = require("axios");
const { onCall } = require("firebase-functions/v2/https");
const { collectionNames } = require("../../constants/collectionNames");
const { OpenAI } = require("openai");
require('dotenv').config();

const openaiApiKey = process.env.OPENAI_API_KEY;


const openai = new OpenAI({
  apiKey: openaiApiKey.toString(),
});

exports.contentOutput = (db) => onCall(async (request) => {
    const { auth, data } = request;
    try{
      if(!articleId){return {error: "article ID is undefined"}}
      const articleRef = await db.collection("ArticleContent").doc(articleId);
      const articleSnapshot = await articleRef.get();
      const article = articleSnapshot.data();
      
      if(!articleSnapshot.exists) {
        throw new Error("Article document does not exist.");
      }
      
      if(!article?.markdown){
        throw new Error("Article data is missing or undefined.");
      }

      // Call OpenAI API with markdown to generate HTML
      const openAiHTML = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: article?.markdown,
          },
          {
            role: "user",
            content: `Convert to usable HTML in a moderen style for display as marketing landing page`,
          },
        ],
        temperature: 1,
        max_tokens: 2048,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      });
      const extractHTMLContent = (input) => {
        // Regular expression to match content between ```html and ```
        const regex = /```html([\s\S]*?)```/g;
        
        // Use matchAll to find all occurrences
        const matches = [...input.matchAll(regex)];

        // Extract and clean up the matches
        const htmlContents = matches.map(match => match[1].trim());

        return htmlContents;
      };
      const html = openAiHTML.choices[0].message.content

      await articleRef
        .update({
          html: extractHTMLContent(html),
        }); 

      // Return both Zillow data and generated article content
      return {
        html: extractHTMLContent(html),
      };
    } catch (error) {
        console.error("Error fetching property data:", error);
        return { error: "Failed to fetch property data." };
    }
});
