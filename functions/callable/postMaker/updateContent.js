const { get } = require("axios");
const { onCall } = require("firebase-functions/v2/https");
const { collectionNames } = require("../../constants/collectionNames");
const { OpenAI } = require("openai");
require('dotenv').config();

const openaiApiKey = process.env.OPENAI_API_KEY;


const openai = new OpenAI({
  apiKey: openaiApiKey.toString(),
});

exports.updateContent = (db) =>
  onCall(async (request) => {
    const { data } = request;
    const address = data;
    try {
      // Fetch data from Zillow API
      const resp = await axios.get(
        "https://zillow56.p.rapidapi.com/search_address",
        {
          params: {
            address: address,
          },
          headers: {
            "x-rapidapi-key": secretKey.value(),
            "x-rapidapi-host": host.value(),
            "Content-Type": "application/json",
          },
        }
      );

      // Prepare the data for OpenAI call
      const zillowData = resp.data;
      const researchData = {
        abbreviatedAddress: zillowData.abbreviatedAddress || null,
        address: zillowData.address || null,
        bathrooms: zillowData.bathrooms || null,
        bedrooms: zillowData.bedrooms || null,
        city: zillowData.city || null,
        citySearchUrl: zillowData.citySearchUrl || null,
        country: zillowData.country || null,
        county: zillowData.county || null,
        countyFIPS: zillowData.countyFIPS || null,
        countyId: zillowData.countyId || null,
        currency: zillowData.currency || null,
        datePostedString: zillowData.datePostedString || null,
        datePriceChanged: zillowData.datePriceChanged || null,
        dateSold: zillowData.dateSold || null,
        dateSoldString: zillowData.dateSoldString || null,
        description: zillowData.description || null,
        hiResImageLink: zillowData.hiResImageLink || null,
        homeType: zillowData.homeType || null,
        livingArea: zillowData.livingArea || null,
        livingAreaUnits: zillowData.livingAreaUnits || null,
        livingAreaUnitsShort: zillowData.livingAreaUnitsShort || null,
        lotAreaUnits: zillowData.lotAreaUnits || null,
        lotAreaValue: zillowData.lotAreaValue || null,
        lotSize: zillowData.lotSize || null,
        resoFacts: {
          appliances: zillowData.resoFacts.appliances || null,
          architecturalStyle: zillowData.resoFacts.architecturalStyle || null,
          attic: zillowData.resoFacts.attic || null,
          basement: zillowData.resoFacts.basement || null,
          basementYN: zillowData.resoFacts.basementYN || null,
          bathrooms: zillowData.resoFacts.bathrooms || null,
          bathroomsFull: zillowData.resoFacts.bathroomsFull || null,
          bathroomsHalf: zillowData.resoFacts.bathroomsHalf || null,
          bathroomsOneQuarter: zillowData.resoFacts.bathroomsOneQuarter || null,
          bathroomsPartial: zillowData.resoFacts.bathroomsPartial || null,
          bathroomsThreeQuarter: zillowData.resoFacts.bathroomsThreeQuarter || null,
          bedrooms: zillowData.resoFacts.bedrooms || null,
          elementarySchool: zillowData.resoFacts.elementarySchool || null,
          elementarySchoolDistrict:
            zillowData.resoFacts.elementarySchoolDistrict || null,
          flooring: zillowData.resoFacts.flooring || null,
          fireplaceFeatures: zillowData.resoFacts.fireplaceFeatures || null,
          fireplaces: zillowData.resoFacts.fireplaces || null,
          livingArea: zillowData.resoFacts.livingArea || null,
          lotSize: zillowData.resoFacts.lotSize || null,
          middleOrJuniorSchool: zillowData.resoFacts.middleOrJuniorSchool || null,
          middleOrJuniorSchoolDistrict:
            zillowData.resoFacts.middleOrJuniorSchoolDistrict || null,
          municipality: zillowData.resoFacts.municipality || null,
          parkingFeatures: zillowData.resoFacts.parkingFeatures || null,
          storiesTotal: zillowData.resoFacts.storiesTotal || null,
        },
        schools: zillowData.schools || null,
        photosHuge: zillowData.hugePhotos || null,
        photosBig: zillowData.big || null,
        //photosSmall: zillowData.photo || null,
        nearbyCities: zillowData.nearbyCities || null,
        nearbyNeighborhoods: zillowData.nearbyNeighborhoods || null,
        neighborhoodRegion: zillowData.neighborhoodRegion || null,

      };

      const article = await db
        .collection("ArticleContent")
        .add({
          prompt: "gGGFQ53GR17vivyT1EdP",
          researchData: researchData,
          address: address,
        });

      // Return both Zillow data and generated article content
      return {
        researchData: researchData,
        prompt: "gGGFQ53GR17vivyT1EdP",
        articleID: article.id,
      };
    } catch (error) {
        console.error("Error fetching property data:", error);
        return { error: "Failed to fetch property data." };
    }
});
