const { get } = require("axios");
const { onCall } = require("firebase-functions/v2/https");
const { collectionNames } = require("../../constants/collectionNames");
const { OpenAI } = require("openai");
require("dotenv").config();

const openaiApiKey = process.env.OPENAI_API_KEY;

const openai = new OpenAI({
  apiKey: openaiApiKey.toString(),
});

exports.articleContent = (db) =>
  onCall(async (request) => {
    const { data } = request;
    const articleId = data;
    try {
      if (!articleId) {
        return { error: "article ID is undefined" };
      }

      const articleRef = await db.collection("ArticleContent").doc(articleId);
      const articleSnapshot = await articleRef.get();
      const article = articleSnapshot.data();
      // console.log(article);

      const promptDoc = await db
        .collection("AiPrompts")
        .doc("gGGFQ53GR17vivyT1EdP")
        .get();

      if (!promptDoc.exists) {
        throw new Error("Prompt document does not exist.");
      }

      const prompt = promptDoc.data().prompt;
      if (!prompt) {
        throw new Error("Prompt field is missing or undefined.");
      }
      if (!articleSnapshot.exists) {
        throw new Error("Article document does not exist.");
      }
      if (!article?.researchData) {
        throw new Error("Article data is missing or undefined.");
      }

      // Call OpenAI API with Zillow data as part of the context
      const openaiResponse = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: prompt,
          },
          {
            role: "user",
            content: `Here is the data for the home listing: ${JSON.stringify(article?.researchData)}`,
          },
        ],
        temperature: 1,
        max_tokens: 2048,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      });

      const markdown = openaiResponse.choices[0].message.content;
      // console.log(markdown);

      const openAiHTMLRes = await db
        .collection("ArticleContent")
        .doc(articleId)
        .update({
          prompt: "gGGFQ53GR17vivyT1EdP",
          markdown: markdown,
        });

      return {
        article: markdown,
        prompt: "gGGFQ53GR17vivyT1EdP",
      };
    } catch (error) {
      console.error("Error fetching property data:", error);
      return { error: "Failed to fetch property data." };
    }
  });
