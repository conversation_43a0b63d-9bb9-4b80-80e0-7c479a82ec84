const { onCall } = require("firebase-functions/v2/https");
const { FieldValue } = require("firebase-admin/firestore");
const { OpenAI } = require('openai');
require('dotenv').config();

const openaiApiKey = process.env.OPENAI_API_KEY;

// Initialize OpenAI with your API key
const openai = new OpenAI({
  apiKey: openaiApiKey,
});

// Callable Cloud Function to handle interactions with ChatGPT and save responses
exports.askChatGPT = (db) => onCall(async (request) => {
  const { auth, data } = request;  
  try {
    const { prompt, userID, type } = data;

    // Check if the prompt is provided
    if (!prompt) {
      console.error('No prompt provided.');
      return {
        message: 'No prompt provided.',
      };
    }

    // Interact with the ChatGPT API
    const chatGptResponse = await openai.chat.completions.create({
      model: 'gpt-4', // Specify the model you wish to use
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 2048,
    });

    // Extract the response text from the API response
    const responseText = chatGptResponse.choices[0].message.content;
    const tokensUsed = chatGptResponse.usage.total_tokens;
    const finishReason = chatGptResponse.choices[0].finish_reason;

    // Save the prompt, response, userID, type, and additional data to Firestore
    const docRef = await db.collection('minerva').add({
      userID: userID || null,
      type: type || 'default',
      prompt: prompt,
      tokensUsed: tokensUsed,
      timestamp: FieldValue.serverTimestamp(),
      ...chatGptResponse.choices[0]
    });

    // Return the response back to the caller
    return {
      message: 'Response saved successfully.',
      documentId: docRef.id,
      prompt: prompt,
      response: responseText,
      tokensUsed: tokensUsed,
      finishReason: finishReason,
      timestamp: new Date().toISOString(),
    };

  } catch (error) {
    console.error('Error handling ChatGPT interaction:', error.message);
    return {
      type: 'internal',
      message: error.message,
      code: error.code || 'unknown_error',
    };
  }
});