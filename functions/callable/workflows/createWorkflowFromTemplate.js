const { onRequest } = require("firebase-functions/v2/https");
const { FieldValue } = require("firebase-admin/firestore");
const { onCall } = require("firebase-functions/v2/https");

exports.createWorkflowFromTemplate = (db) => onCall(async (request) => {
  const { auth, data } = request;

  const { accountId, templateId } = data;

  if (!accountId) {
    return { status: 'failure', message: 'Account ID is required.' };
  }

  if (!templateId) {
    return { status: 'failure', message: 'Workflow Template ID is required.' };
  }

  const accountRef = db.collection('Account').doc(accountId);
  const accountDoc = await accountRef.get();

  if (!accountDoc.exists) {
    return { status: 'failure', message: `Account not found.` };
  }

  try {
    const workflowTemplateDoc = await db.collection('WorkflowTemplates').doc(templateId).get();
    if (!workflowTemplateDoc.exists) {
      return { status: 'failure', message: 'Workflow template not found.' };
    }

    const workflowId = await createWorkflowFromTemplate(db, accountDoc, workflowTemplateDoc);

    return {
      status: 'success',
      message: `Workflow created successfully.`,
      data: { id: workflowId }
    };
  } catch (error) {
    console.error(`Error creating workflow for account ${accountId}. ${error.message}`);
    return { status: 'failure', message: `Error creating workflow: ${error.message}` };
  }
});


async function createWorkflowFromTemplate(db, accountDoc, workflowTemplateDoc) {
  console.log(`createWorkflowFromTemplate for account ${accountDoc.id}`);

  const workflowTemplate = workflowTemplateDoc.data();

  const actions = workflowTemplate.actions.map(action => {
    return {
      ...action,
      ...(action.messageBody && { messageBody: action.messageBody.replace(/{CLIENT_NAME}/g, accountDoc.data().name) }),
    }
  });

  console.log(`actions ${JSON.stringify(actions)}`);

  const workflowRef = await db.collection("Account").doc(accountDoc.id)
    .collection("Workflows")
    .add({
      ...workflowTemplate,
      actions,
      createdAt: FieldValue.serverTimestamp()
    });

  workflowRef.update({ id: workflowRef.id });

  return workflowRef.id;
};