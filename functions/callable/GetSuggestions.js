const { onCall } = require('firebase-functions/v2/https');
const axios = require('axios');

exports.getSuggestions = onCall(async (request) => {
  const address = request.data?.address;
  if (!address) {
    console.error('No address provided', request.data);
    return { suggestions: [], isValid: false };
  }

  try {
    const response = await axios.get(`${process.env.SMARTZIP_URL}/addresses/suggest.json`, {
      params: {
        address: address,
        api_key: process.env.SMARTZIP_KEY,
      },
    });

    return { suggestions: response.data?.properties || [] };
  } catch (error) {
    console.error('Error fetching suggestions: ', error);
    return { suggestions: [], isValid: false };
  }
});