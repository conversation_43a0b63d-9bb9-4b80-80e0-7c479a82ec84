const {
  onDocumentCreated,
  onDocumentDeleted,
} = require("firebase-functions/v2/firestore");
const { Timestamp, FieldValue } = require("firebase-admin/firestore");
const { defineString, HttpsError } = require("firebase-functions/params");
const { logger } = require("firebase-functions");
const { isNil } = require("lodash");
const { addToTopic } = require("../utils/pubsub/addToTopic");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { collectionHistory } = require("../utils/collectionHistory");

exports.onCreateProductPlan = (db) =>
  onDocumentCreated(
    `Account/{accountId}/AccountPlans/{planId}`,
    async (event) => {
      const projectIdEnv = defineString("TITAN_PROJECT_ID");

      const accountPlan = event.data.data();
      const accountId = event.params.accountId;

      const planId = event.params.planId;

      // when an accountPlan is added publish
      const messageData = {
        accountId,
      };

      const topic = "product-plan-added";
      const message = JSON.stringify(messageData);
      const projectId = projectIdEnv.value();

      const queueItem = {
        topic,
        message,
        projectId,
      };

      const topicStat = await addToTopic(queueItem);

      await collectionHistory({
        collection: `${collectionNames.account}/${accountId}/${subCollectionNames.account.accountPlans}`,
        id: planId,
        before: null,
        after: accountPlan,
        action: "CREATE",
        context: {
          notes: ``,
        },
      });
    }
  );

exports.onDeleteProductPlan = (db) =>
  onDocumentDeleted(
    `Account/{accountId}/AccountPlans/{planId}`,
    async (event) => {
      const accountId = event.params.accountId;
      const planId = event.params.planId;
      const accountRef = db.collection(collectionNames.account).doc(accountId);

      await propagatePlanRemovalToRecipientGroups({ accountRef, planId });
    }
  );

/**
 * Finds every RecipientGroups doc under Account whose
 * productPlans array contains this planId, and removes it.
 */
async function propagatePlanRemovalToRecipientGroups({ accountRef, planId }) {
  try {
    console.log(
      "propagating plan removal to recipient groups:",
      accountRef.path,
      "planId: ",
      planId
    );
    const groupsCol = accountRef.collection("RecipientGroups");

    // find all groups still containing that now-deleted/disabled plan
    const snapshot = await groupsCol
      .where("productPlans", "array-contains", planId)
      .get();

    if (snapshot.empty) {
      console.log("no recipient groups need updating");
      return;
    }

    // build updates: remove from productPlans & delete assignments.<planId>
    const updates = [];
    for (const docSnap of snapshot.docs) {
      const dataUpdate = {
        // remove the planId from the array
        productPlans: FieldValue.arrayRemove(planId),
        // delete that key from the assignments map
        [`assignments.${planId}`]: FieldValue.delete(),
      };
      updates.push(docSnap.ref.update(dataUpdate));
    }

    await Promise.all(updates);
    console.log(`removed plan ${planId} from ${updates.length} group(s)`);
  } catch (error) {
    console.error("Error propagating plan removal:", error);
  }
}
