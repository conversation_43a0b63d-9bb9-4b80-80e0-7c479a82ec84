const { onDocumentWritten } = require("firebase-functions/v2/firestore");
const { Timestamp } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const { isNil } = require("lodash");
const {
  updateBasicAccountInfo,
} = require("../services/mercury/basicAccountInfo");
const { syncAccountPlanInfo } = require("../services/mercury/syncAccountPlanInfo");

exports.syncAccountPlanWithMercury = onDocumentWritten(
  `Account/{accountId}/AccountPlans/{planId}`, 
  async (event) => {
  // on create "before" is undefined
  // on delete "after" is undefined
  const accountId = event.params.accountId.toString();
  const planId = event.params.planId.toString();

  const accountPlanAfter = event.data.after.data();
  
  let isDeleted = false;
  if (isNil(accountPlanAfter)) {
    // account plan deleted
    isDeleted = true;
  }

  await syncAccountPlanInfo({
    ...accountPlanAfter,
    accountId,
    planId,
    isDeleted,
  });
});
