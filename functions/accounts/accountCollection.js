const { onDocumentWritten } = require("firebase-functions/v2/firestore");
const { isNil } = require("lodash");
const {
  updateBasicAccountInfo,
} = require("../services/mercury/basicAccountInfo");
const { FieldValue } = require("firebase-admin/firestore");

exports.onWriteAccount = onDocumentWritten(
  {
    document: `Account/{id}`,
    memory: "1GB", // Allocate 1GB of memory
  },
  async (event) => {
    // on create "before" is undefined
    // on delete "after" is undefined
    const accountAfter = event.data.after.data();
    const accountBefore = event.data.before.data();
    const accountId = event.params.id;

    const {
      name: nameBefore,
      displayName: displayNameBefore,
      slug: slugBefore,
      timezone: timezoneBefore,
      marketUuid: marketUuidBefore,
      isActive: isActiveBefore,
      productPlans: productPlansBefore,
      groupId: groupIdBefore,
      groupName: groupNameBefore,
      adsPaused: adsPausedBefore,
      verifiedTwilioNumber: verifiedTwilioNumberBefore,
      aiResponseContext: aiResponseContextBefore,
      aiLeadQualification: aiLeadQualificationBefore,
      aiLeadQualificationTerms: aiLeadQualificationTermsBefore,
      aiAgentId: aiAgentIdBefore,
    } = accountBefore ?? {};

    const {
      name,
      displayName,
      slug,
      timezone,
      marketUuid,
      isActive,
      productPlans,
      groupId,
      groupName,
      adsPaused,
      verifiedTwilioNumber,
      aiResponseContext,
      aiLeadQualification,
      aiLeadQualificationTerms,
      aiAgentId,
    } = accountAfter ?? {};

    /**
     * Fields we watch for changes
     * Comparing only the fields that may trigger events
     */

    const watchedFieldsBefore = {
      nameBefore,
      displayNameBefore,
      timezoneBefore,
      slugBefore,
      marketUuidBefore,
      isActiveBefore,
      productPlansBefore,
      groupIdBefore,
      groupNameBefore,
      adsPausedBefore,
      verifiedTwilioNumberBefore,
      aiResponseContextBefore,
      aiLeadQualificationBefore,
      aiLeadQualificationTermsBefore,
      aiAgentIdBefore,
    };
    const watchedFieldsAfter = {
      name,
      displayName,
      timezone,
      slug,
      marketUuid,
      isActive,
      productPlans,
      groupId,
      groupName,
      adsPaused,
      verifiedTwilioNumber,
      aiResponseContext,
      aiLeadQualification,
      aiLeadQualificationTerms,
      aiAgentId,
    };

    if (isNil(accountAfter)) {
      // account deleted stop execution
      return;
    }
    // compare the values
    if (
      JSON.stringify(watchedFieldsBefore) === JSON.stringify(watchedFieldsAfter)
    ) {
      // no changes stop execution
      return;
    }
    /**
     * Events: On any Account data changes
     */
    await updateBasicAccountInfo({
      accountId,
      name,
      displayName,
      timezone,
      slug,
      marketUuid,
      isActive,
      groupId,
      groupName,
      adsPaused,
      verifiedTwilioNumber,
      aiResponseContext,
      aiLeadQualification,
      aiLeadQualificationTerms,
      aiAgentId,
      updatedAt: FieldValue.serverTimestamp(),
    });
  }
);
