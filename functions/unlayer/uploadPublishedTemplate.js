const { onCall } = require("firebase-functions/v2/https");
const { getStorage } = require("firebase-admin/storage");
const { getAuth } = require("firebase-admin/auth");
const { getFirestore, Timestamp } = require("firebase-admin/firestore");
const functions = require("firebase-functions");

const SOURCE_BUCKET = "rm-titan-landing-page-user-files";
const DEST_BUCKET = "rm-titan-dev-pages";

exports.uploadPublishedTemplate = onCall(async (request) => {
  try {
    const { accountId, landingPageId, html, designJson, pdfFileName } =
      request.data;
    const context = request.auth;

    if (!context) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be signed in."
      );
    }

    if (!accountId || !landingPageId || !html || !designJson) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Missing required fields."
      );
    }

    const folderPath = `${accountId}/${landingPageId}/`;
    const destBucket = getStorage().bucket(DEST_BUCKET);
    let finalHtml = html;

    // Step 1: Copy PDF to CDN bucket and generate final URL
    if (pdfFileName) {
      const srcBucket = getStorage().bucket(SOURCE_BUCKET);
      const sourcePath = `${folderPath}${pdfFileName}`;
      const destPath = `${folderPath}${pdfFileName}`;

      await srcBucket.file(sourcePath).copy(destBucket.file(destPath));

      const cdnUrl = `https://storage.googleapis.com/${DEST_BUCKET}/${destPath}`;

      // Step 2: Inject into first <a href="#"> in HTML
      finalHtml = html.replace(
        /<a([^>]+?)href=(["'])\s*(#|)\s*\2/gi,
        `<a$1href="${cdnUrl}" download`
      );

      // Step 3: Update Firestore with new cdnUrl
      const db = getFirestore();
      const fileRef = db.collection(`Account/${accountId}/LandingPageFiles`);
      const querySnap = await fileRef
        .where("landingPageId", "==", landingPageId)
        .where("name", "==", pdfFileName)
        .get();

      if (!querySnap.empty) {
        const docRef = querySnap.docs[0].ref;
        await docRef.update({
          cdnUrl,
          updatedAt: Timestamp.now(),
        });
      } else {
        // If no existing doc, create new one (shouldn’t happen unless orphaned)
        await fileRef.add({
          name: pdfFileName,
          landingPageId,
          cdnUrl,
          uploadedAt: Timestamp.now(),
        });
      }
    }

    // Step 4: Save updated HTML and design to CDN bucket
    await destBucket.file(`${folderPath}index.html`).save(finalHtml, {
      contentType: "text/html",
    });

    await destBucket
      .file(`${folderPath}design.json`)
      .save(JSON.stringify(designJson), {
        contentType: "application/json",
      });

    // console.log(`✅ Landing page published to ${folderPath}`);
    return { success: true };
  } catch (error) {
    // console.error("🔥 uploadPublishedTemplate error:", error.message);
    throw new functions.https.HttpsError("internal", error.message);
  }
});