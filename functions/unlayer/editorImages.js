const functions = require("firebase-functions");
const { getStorage } = require("firebase-admin/storage");
const { getAuth } = require("firebase-admin/auth");

const BUCKET_NAME = "rm-dev-cdn"; // 👈 use your actual bucket name
const FOLDER_PATH = "template-images/"; // 👈 folder inside the bucket

exports.getImagesFromBucket = functions.https.onRequest(async (req, res) => {
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

  if (req.method === "OPTIONS") {a
    return res.status(204).send("");
  }

  // 🔐 1. Get the token from the Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "Unauthorized: No token provided" });
  }

  const idToken = authHeader.split("Bearer ")[1];

  try {
    // 🔐 2. Verify the token using Firebase Auth
    await getAuth().verifyIdToken(idToken);

    // ✅ If token is valid, proceed to fetch images
    const storage = getStorage();
    const bucket = storage.bucket(BUCKET_NAME);
    const [files] = await bucket.getFiles({ prefix: FOLDER_PATH });

    const imageUrls = files
      .filter((file) => /\.(jpg|jpeg|png)$/i.test(file.name))
      .map(
        (file) => `https://storage.googleapis.com/${BUCKET_NAME}/${file.name}`
      );

    console.log("🖼️ Images found in bucket:", imageUrls);
    res.status(200).json(imageUrls);
  } catch (error) {
    console.error("🔥 Auth or image fetch error:", error.message);
    res.status(403).json({ error: "Forbidden: Invalid or expired token" });
  }
});
