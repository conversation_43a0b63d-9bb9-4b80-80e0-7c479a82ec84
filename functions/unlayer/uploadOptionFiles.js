const { onCall } = require("firebase-functions/v2/https");
const { getStorage } = require("firebase-admin/storage");
const functions = require("firebase-functions");
const BUCKET_NAME = "rm-titan-landing-page-user-files";

exports.uploadOptionFiles = onCall(async (request) => {
  //   console.log("📥 uploadOptionFiles triggered");
  try {
    const {
      accountId,
      landingPageId,
      customerSiteId,
      fileName,
      fileType,
      fileData,
    } = request.data;
    request.data;

    if (
      !fileData ||
      !fileName ||
      !accountId ||
      (!landingPageId && !customerSiteId)
    ) {
      // throw new Error("Missing required fields.");
    }

    const buffer = Buffer.from(fileData, "base64");
    // console.log("📦 Converted base64 to buffer, size:", buffer.length);

    const entityId = landingPageId || customerSiteId;
    const destination = `${accountId}/${entityId}/${fileName}`;

    const bucket = getStorage().bucket(BUCKET_NAME);
    // console.log("🪣 Storage bucket loaded:", BUCKET_NAME);

    const file = bucket.file(destination);
    // console.log("📝 Saving file...");

    await file.save(buffer, {
      contentType: fileType || "application/pdf",
      resumable: false,
    });

    // console.log("✅ File saved successfully");

    const publicUrl = `https://storage.googleapis.com/${BUCKET_NAME}/${destination}`;
    // console.log("🔗 Public URL generated:", publicUrl);

    return { url: publicUrl, name: fileName };
  } catch (error) {
    // console.error("❌ Upload error:", error.message);
    throw new functions.https.HttpsError("internal", error.message);
  }
});
