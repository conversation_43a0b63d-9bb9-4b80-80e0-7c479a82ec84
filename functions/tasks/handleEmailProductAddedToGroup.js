const { getFirestore } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const { onTaskDispatched } = require("firebase-functions/v2/tasks");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const {
  mailingAddressStateCases,
} = require("../constants/mailingAddressStateCases");

const { REPROCESS } = mailingAddressStateCases;

exports.handleEmailProductAddedToGroup = onTaskDispatched(
  {
    retryConfig: { maxAttempts: 5 },
    timeoutSeconds: 540,
    memory: "1GiB",
  },
  async (task) => {
    try {
      await processGroupEmailProductAdded(task.data);
    } catch (err) {
      logger.error("handleEmailProductAddedToGroup failed:", err);
      throw err; // trigger retry
    }
  }
);

async function processGroupEmailProductAdded({ accountId, recipientGroupId }) {
  const db = getFirestore();
  const batchSize = 100;
  let lastDoc = null;
  let totalProcessed = 0;
  let totalSkipped = 0;

  const recipientsRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients);

  while (true) {
    let query = recipientsRef
      .where("recipientGroupIds", "array-contains", recipientGroupId)
      .orderBy("__name__")
      .limit(batchSize);

    if (lastDoc) query = query.startAfter(lastDoc);

    const snap = await query.get();
    if (snap.empty) break;

    // Collect up to 500 updates per WriteBatch
    let batch = db.batch();
    let opsInBatch = 0;

    for (const recipientDoc of snap.docs) {
      const emailsRef = recipientDoc.ref.collection(
        subCollectionNames.contacts.emailAddresses
      );
      const emailsSnap = await emailsRef.get();
      if (emailsSnap.empty) continue;

      for (const emailDoc of emailsSnap.docs) {
        const data = emailDoc.data();
        if (data.processingStatus === REPROCESS) {
          totalSkipped++;
          continue;
        }
        batch.update(emailDoc.ref, { processingStatus: REPROCESS });
        opsInBatch++;
        totalProcessed++;

        // commit & reset when we hit 500‐op limit
        if (opsInBatch === 500) {
          await batch.commit();
          batch = db.batch();
          opsInBatch = 0;
        }
      }
    }

    // commit any leftover ops
    if (opsInBatch > 0) {
      await batch.commit();
    }

    lastDoc = snap.docs[snap.docs.length - 1];
    if (snap.docs.length < batchSize) break;
  }

  logger.log(
    `Email‐product task done for account=${accountId}, group=${recipientGroupId}.` +
      ` Updated=${totalProcessed}, Skipped=${totalSkipped}.`
  );
}
