const {
  Timestamp,
  FieldValue,
  getFirestore,
} = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const {
  onTaskDispatched,
  getFunctions,
} = require("firebase-functions/v2/tasks");
const { defineString } = require("firebase-functions/params");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const {
  addToTopicConfigured,
} = require("../utils/pubsub/addToTopicConfigured");
const { PRODUCTS } = require("../constants/products");
const { collectionHistory } = require("../utils/collectionHistory");

// Environment and task queue names
const projectIdEnv = defineString("TITAN_PROJECT_ID");
const projectId = projectIdEnv.value();
const location = "us-central1";
const removedTaskName = `projects/${projectId}/locations/${location}/functions/handleMailingProductRemovedFromGroup`;

exports.handleMailingProductRemovedFromGroup = onTaskDispatched(
  { retryConfig: { maxAttempts: 5 }, timeoutSeconds: 540, memory: "1GiB" },
  async (task) => {
    const { accountId, recipientGroupId, startAfterId = null } = task.data;
    try {
      await processRemovedBatch({ accountId, recipientGroupId, startAfterId });
    } catch (error) {
      logger.error("Error processing removed batch:", error);
      throw error;
    }
  }
);

async function processRemovedBatch({
  accountId,
  recipientGroupId,
  startAfterId,
}) {
  const db = getFirestore();
  const batchSize = 100;

  const mailingProductIds = PRODUCTS.filter((p) => p.type === "magazine").map(
    (p) => p.plan_id
  );
  const groupsSnap = await db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipientGroups)
    .where("isActive", "==", true)
    .get();

  const groupHasMailMap = {};
  groupsSnap.docs.forEach((g) => {
    const plans = g.data().productPlans || [];
    groupHasMailMap[g.id] = plans.some((id) => mailingProductIds.includes(id));
  });

  let query = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients)
    .where("recipientGroupIds", "array-contains", recipientGroupId)
    .where("isActive", "==", true)
    .orderBy("__name__")
    .limit(batchSize);

  if (startAfterId) {
    const lastSnap = await db.doc(startAfterId).get();
    if (lastSnap.exists) query = query.startAfter(lastSnap);
  }

  const recipientsSnapshot = await query.get();
  if (recipientsSnapshot.empty) return;

  for (const recipientDoc of recipientsSnapshot.docs) {
    try {
      const data = recipientDoc.data();
      const otherGroups = (data.recipientGroupIds || []).filter(
        (id) => id !== recipientGroupId
      );
      const stillHasMail = otherGroups.some((id) => groupHasMailMap[id]);
      if (!stillHasMail) {
        const message = JSON.stringify({
          accountId,
          recipientId: recipientDoc.id,
          action: "productRemoved",
          userId: "",
        });
        await addToTopicConfigured({
          topic: "schedule-release-exclusivity",
          message,
        });
        await collectionHistory({
          collection: `${collectionNames.account}/${accountId}/${subCollectionNames.contacts.recipients}`,
          id: doc.id,
          context: {
            action: "productRemoved",
            topic: "schedule-release-exclusivity",
            notes:
              "Product removed from group. Schedule an exclusivity release record",
          },
          action: "UPDATE",
        });
      }
    } catch (err) {
      logger.error(`Error processing recipient ${recipientDoc.id}:`, err);
    }
  }

  // Enqueue next batch
  const lastDoc = recipientsSnapshot.docs[recipientsSnapshot.docs.length - 1];
  const nextPayload = {
    accountId,
    recipientGroupId,
    startAfterId: lastDoc.ref.path,
  };
  const queue = getFunctions().taskQueue(removedTaskName);
  await queue.enqueue(nextPayload);
}
