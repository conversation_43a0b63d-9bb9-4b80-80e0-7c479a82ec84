const { getFirestore } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const { onTaskDispatched } = require("firebase-functions/v2/tasks");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");

const { isEmpty } = require("lodash");

exports.handleReprocessUserEnteredMailingAddressTask = onTaskDispatched(
  {
    retryConfig: {
      maxAttempts: 5,
    },
    timeoutSeconds: 540,
    memory: "1GiB",
  },
  async (task) => {
    try {
      const { recipientPaths } = task.data;
      console.log("task.data", task.data);
      console.log("recipientPaths", recipientPaths);
      await reprocessUserEnteredMailingAddresses(recipientPaths);
    } catch (error) {
      logger.error("Error processing task:", error);
      // Rethrow the error to trigger a retry
      throw error;
    }
  }
);

async function reprocessUserEnteredMailingAddresses(recipientPaths = []) {
  const db = getFirestore();

  if (isEmpty(recipientPaths)) {
    console.warn("No recipient paths provided; nothing to reprocess.");
    return;
  }

  try {
    const updates = recipientPaths.map((path) => {
      const addrRef = db
        .doc(path)
        .collection("UserEnteredMailingAddress")
        .doc("0");
      return addrRef.update({ processingStatus: "REPROCESS" });
    });

    await Promise.all([updates]);
  } catch (error) {
    console.error(error);
  }
}
