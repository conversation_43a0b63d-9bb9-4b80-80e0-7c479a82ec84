const { onCall } = require("firebase-functions/v2/https");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const {
  hashString,
  preProcessAddressString,
} = require("../../utils/hash/hashStrings");
const {
  getRecipientsByAccountIdAndStatus,
  getRecipientAddressIds,
  getMailingAddressDocsBatch,
  getRecipientGroupIds,
  getGroupsWithExclusiveProduct,
  hasAddressEnrolledInExclusiveProduct,
  reprocessUserEnteredMailingAddresses,
} = require("./exclusiveMailingHelpers");
const { FieldValue } = require("firebase-admin/firestore");
const { onTaskDispatched } = require("firebase-functions/v2/tasks");
const { isEmpty, isString } = require("lodash");

exports.reconcileMailingAddressClaimsTask = onTaskDispatched(
  {
    retryConfig: {
      maxAttempts: 5,
    },
    timeoutSeconds: 540,
    memory: "1GiB",
  },
  async (task) => {
    const { accountId, code = "ALL" } = task.data;
    console.log("Running reconcileMailingAddressClaimsTask...");

    await processAccounts(accountId);

    async function processAccounts(accountId) {
      try {
        console.log("Processing acccountId: ", accountId);
        const recipientData = await getRecipientsByAccountIdAndStatus({
          accountId,
          code,
        }); // process all on "ALL" code
        const recipientAddressIds = getRecipientAddressIds(recipientData);
        console.log("recipientAddressIds: ", recipientAddressIds);
        for await (const addressDocsBatch of getMailingAddressDocsBatch({
          batchSize: 500,
          addressIds: recipientAddressIds,
        })) {
          //console.log("addressDocsBatch: ", addressDocsBatch);

          await addressFix(addressDocsBatch);
        }
        console.log("All batches processed");
      } catch (err) {
        console.error("Error processing batches:", err);
      }
    }

    async function addressFix(addressDocs) {
      // Takes in an array of address documents

      for (const address of addressDocs) {
        const addressData = address.data();
        const { validationResult, ...addressDetails } = addressData ?? {};

        // Tidy up source MailingAddress documents
        // sync source fields if necessary
        await syncSourceAddressFields({
          validationResult,
          addressData: addressDetails,
          sourceMailingAddressDocRef: address.ref,
        });

        /**
         * For each address document reconcile the waiting list
         * - verify if an account should
         *   A. remain on the waiting list OR
         *   B. Added to Exclusive OR
         *   C. removed from waiting list
         *
         * Validation of Waiting List and Exclusive Claims:
         * validation of proper waiting list or exclusive claims is determined by querying
         * active recipients, against the source address, and recipient groups with an exclusive product
         */
        await reconcileWaitingList({
          sourceMailingAddressDocRef: address.ref,
          addressData,
        });
      }
      console.log("Address fix process completed.");
    }

    /**
     * Clears WaitingList records that should not be listed.
     *
     * Asks:
     *  - Should the accounts listed in WatingList remain in the Waiting List?
     *  - Should one of the accounts listed in WaitingList be exclusive?
     *
     * 1. Query WaitingList by ascending date order (this is important - first in first out of waiting list)
     * 2. For each waiting list account Get all recipient group ids for the current accountId
     * 3. Filter out all groupIds that DONT have an exclusive mailing product - we're left with groups with exclusive products or none at all.
     * 4. Query for any ACTIVE recipients that are assigned to the EXCLUSIVE product groupIds AND are using the current source address
     * 5. We recieve EITHER a list of all recipients that have a claim to exclusivity or waiting list
     *    OR an empty list and the account has no claim to exclusivity or waiting list
     *
     * 6. If we have a list of recipients  then we check if exclusive array is empty
     *    a. if the exclusive array is empty then ADD the accountId to the array.
     *        a1. Iterate over the list of recipients and REPROCESS their userEnteredMailingAddress
     *        a2. END.
     *    b. if the exclusive array is not empty then run the reconcileExclusivity function on the id(s) holding exclusivity
     *        b1. This determines if the account(s) in the array should remain or not.
     *        b2. END.
     *
     **/
    async function reconcileWaitingList({
      sourceMailingAddressDocRef,
      addressData,
    }) {
      const sourceAddressId = sourceMailingAddressDocRef.id;

      const exclusiveArray = addressData?.exclusive ?? [];
      const isExclusiveEmpty = isEmpty(exclusiveArray);

      const waitingListCollectionRef =
        sourceMailingAddressDocRef.collection("WaitingList");

      // ordered by dateAdded ascending
      const waitingListCollectionSnapshot = await waitingListCollectionRef
        .orderBy("dateAdded", "asc")
        .get();

      // iterate through waiting list account's recipent groups and check for exclusive products
      for (const waitingListItem of waitingListCollectionSnapshot.docs) {
        console.log(`
  
            Reconciling next account in waitingList ${waitingListItem.id} for addressId: ${sourceAddressId}
        
        `);
        const accountId = waitingListItem.id;
        const groupIds = await getRecipientGroupIds(accountId);
        // check for account in exclusive array.
        const isAlreadyExclusive = exclusiveArray.includes(accountId);

        // do any groups have an exclusive product? check productPlans
        const groupIdsWithExclusiveProduct =
          await getGroupsWithExclusiveProduct({
            accountId,
            groupIds,
          });
        console.log(
          `AccountId: ${accountId} GroupIds with exclusive product -----> `,
          groupIdsWithExclusiveProduct
        );
        // for groups with exclusive products check for recipient with matching source address using searchTags.formattedAddress or matching mailingaddress field)

        // verified claims are addresses associated with a recipient that is enrolled in an exclusive product
        const recipientPathsWithVerifiedClaimsToAddress =
          await hasAddressEnrolledInExclusiveProduct({
            accountId,
            addressData,
            groupIds: groupIdsWithExclusiveProduct,
            sourceAddressId,
          });
        console.log(
          `#### Recipients with verified claims to addressId: ${sourceAddressId} `,
          recipientPathsWithVerifiedClaimsToAddress
        );
        // If our claims array is NOT empty
        if (!isEmpty(recipientPathsWithVerifiedClaimsToAddress)) {
          // Determine if account should be moved to exclusive
          // If exclusive array is empty then remove account from waiting list and add to exclusive array.
          //  - also add recipientPaths and recipients (array of ids)
          // Then REPROCESS recipients
          if (isExclusiveEmpty) {
            // remove from waitingList and add to exclusive
            await Promise.all([
              waitingListItem.ref.delete(),
              sourceMailingAddressDocRef.update({
                exclusive: [accountId],
                recipientPaths: recipientPathsWithVerifiedClaimsToAddress,
                recipients: recipientIdsFromPaths(
                  recipientPathsWithVerifiedClaimsToAddress
                ),
              }),
            ]);
            console.log(
              `Exclusivity array was empty! Move ${accountId} from Waiting List to Exclusive array.`
            );
            console.log(`REPROCESS recipientPaths ${accountId}`);

            await reprocessUserEnteredMailingAddresses(
              recipientPathsWithVerifiedClaimsToAddress
            );
          } else if (isAlreadyExclusive) {
            // remove account from waitingList it is already exclusive
            await Promise.all([
              waitingListItem.ref.delete(),
              sourceMailingAddressDocRef.update({
                recipientPaths: recipientPathsWithVerifiedClaimsToAddress,
                recipients: recipientIdsFromPaths(
                  recipientPathsWithVerifiedClaimsToAddress
                ),
              }),
            ]);
            await reprocessUserEnteredMailingAddresses(
              recipientPathsWithVerifiedClaimsToAddress
            );
          } else {
            console.log(
              "Reconciling Exclusivity mailingAddressId: ",
              sourceAddressId
            );
            // Exclusive array is NOT empty
            // if exclusive array is not empty we will verify that the account(s) in the array
            // should be there. If they shouldn't then they are removed, otherwise left alone.
            // if they are removed then the titan/functions/mailingAddresses/onUpdateMailingAddressSource.js
            // handles the next account in line for exclusivity when the exclusive array becomes empty and we do nothing here.
            // If exclusive id(s) are not removed then we also do nothing the waitinglist items stands verified
            // END
            await reconcileExclusivity({
              sourceMailingAddressDocRef,
              addressData,
            });
          }
        } else {
          console.log(
            `Removing accountId: ${accountId} from waitingList no claims on addressId: ${sourceAddressId}`
          );
          // Our claims array IS EMPTY (We found no recipients associated with the current account and address that are assigned to groups with exclusive mailing products)
          // remove from waiting list and REPROCESS userEnteredMailingAddresses
          await waitingListItem.ref.delete();
          // not  processing. no recipients with product to process
        }
        // last line of for loop
      }
    }

    /**
     * Clears an account's exclusive claim if it should not have it. Otherwise leaves it be.
     *
     * Asks:
     *  - Should the account(s) listed in the exclusive array remain? (checking source mailing adderess association with an account and exlusive product by querying for recipients associated with products that have a matching address)
     *
     * 1. For each accountId in the exclusive array get all recipient group ids.
     * 2. Filter out all groupIds that DONT have an exclusive mailing product - we're left groups with exclusive products or none at all.
     * 3. Query for any recipients that are assigned to the EXCLUSIVE product groupIds AND are using the current source address
     * 4. We recieve EITHER a list of all recipients that have a claim to exclusivity
     *    OR an empty list and the account has no claim to exclusivity
     * 5. If we have a list of recipients then we KEEP exclusivity AND REPROCESS their userEnteredMailingAddresses
     *    - if we don't have a list of recipients with exclusive claims then we REMOVE exclusivity AND REPROCESS their userEnteredMailingAddresses
     **/
    async function reconcileExclusivity({
      sourceMailingAddressDocRef,
      addressData,
    }) {
      const sourceAddressId = sourceMailingAddressDocRef.id;

      const { exclusive = [] } = addressData ?? {};
      for (const accountId of exclusive) {
        // get all groupIds under the exclusive account
        const groupIds = await getRecipientGroupIds(accountId);
        // do any groups have an exclusive product?
        const groupIdsWithExclusiveProduct =
          await getGroupsWithExclusiveProduct({
            accountId,
            groupIds,
          });

        // for groups with exclusive products check for recipient with matching source address using matching mailingaddress field or searchTags.formattedAddress
        const recipientPathsWithVerifiedExclusiveClaims =
          await hasAddressEnrolledInExclusiveProduct({
            accountId,
            addressData,
            groupIds: groupIdsWithExclusiveProduct,
            sourceAddressId,
          });
        console.log(
          `#### Recipients with verified exclusive claims for accountId: ${accountId} to addressId: ${sourceAddressId} ----> `,
          recipientPathsWithVerifiedExclusiveClaims
        );
        // if our claims array is NOT empty - account should remain exclusive
        // we reprocess even if the exclusive array is not changed to be certain the correct addressDeliverability is reflected
        if (!isEmpty(recipientPathsWithVerifiedExclusiveClaims)) {
          // reprocess recipients
          console.log(
            `Keep Exclusive Claim to source address: ${sourceAddressId} ---> exclusive to: ${accountId}`
          );
          console.log(`REPROCESS exclusive recipient paths - kept exclusivity`);
          await reprocessUserEnteredMailingAddresses(
            recipientPathsWithVerifiedExclusiveClaims
          );
        } else {
          console.log(
            `Remove Exclusive Claim to source address: ${sourceAddressId} ---> exclusive to: ${accountId}`
          );
          console.log(
            `REPROCESS exclusive recipient paths - removed exclusivity.`
          );
          // remove id from exclusive array
          // reprocess user entered mailing addressess
          await sourceMailingAddressDocRef.update({
            exclusive: FieldValue.arrayRemove(accountId),
            recipients: [],
            recipientPaths: [],
          });
          await reprocessUserEnteredMailingAddresses(
            recipientPathsWithVerifiedExclusiveClaims
          );
        }
      }
    }

    async function syncSourceAddressFields({
      validationResult,
      addressData,
      sourceMailingAddressDocRef,
    }) {
      const {
        address1: currentSourceAddress1 = "",
        address2: currentSourceAddress2 = "",
        city: currentSourceCity = "",
        state: currentSourceState = "",
        postalCode: currentSourcePostalCode = "",
      } = addressData ?? {};

      const {
        address1: validated1 = "",
        address2: validated2 = "",
        city: validatedCity = "",
        state: validatedState = "",
        postalCode: validatedPostal = "",
      } = validationResult ?? {};

      const currentSource = {
        address1: currentSourceAddress1,
        address2: currentSourceAddress2,
        city: currentSourceCity,
        state: currentSourceState,
        postalCode: currentSourcePostalCode,
      };

      const validatedAddr = {
        address1: validated1,
        address2: validated2,
        city: validatedCity,
        state: validatedState,
        postalCode: validatedPostal,
      };

      // check for all required components
      const hasCompleteValidatedComponents =
        isString(validated1) &&
        validated1.trim() !== "" &&
        isString(validatedCity) &&
        validatedCity.trim() !== "" &&
        isString(validatedState) &&
        validatedState.trim() !== "" &&
        isString(validatedPostal) &&
        validatedPostal.trim() !== "";

      const hasCompleteSourceComponents =
        isString(currentSourceAddress1) &&
        currentSourceAddress1.trim() !== "" &&
        isString(currentSourceCity) &&
        currentSourceCity.trim() !== "" &&
        isString(currentSourceState) &&
        currentSourceState.trim() !== "" &&
        isString(currentSourcePostalCode) &&
        currentSourcePostalCode.trim() !== "";

      // does the hash of the validated components match the hash of the source components
      const hashedValidatedAddress = hashString(
        validatedAddr,
        preProcessAddressString
      );

      const hashedCurrentSource = hashString(
        currentSource,
        preProcessAddressString
      );

      const validatedComponentsMatchCurrentSource =
        hashedValidatedAddress === hashedCurrentSource;
      const validatedHashMatchesId =
        hashedValidatedAddress === sourceMailingAddressDocRef.id;

      /**
       * Source address has a validatedResult property
       * The validated components are complete (e.g. has an address1, address2, city, state)
       * validated hash matches source id
       */
      console.log(`
    ***********************************************************
      START Cleanup AddressId: ${sourceMailingAddressDocRef.id}
      
      checking if address source sync is needed...
  
  
      has validationResult? ${!!validationResult}
      validatedHash matches Id ${validatedHashMatchesId}
      current source matches validated ${JSON.stringify(currentSource) === JSON.stringify(validatedAddr)}
      current source ${JSON.stringify(currentSource)}
      validated source ${JSON.stringify(validatedAddr)}
      source path: ${sourceMailingAddressDocRef.path}
  
    ************************************************************
    `);
      if (
        hasCompleteValidatedComponents &&
        validatedHashMatchesId &&
        JSON.stringify(currentSource) !== JSON.stringify(validatedAddr)
      ) {
        try {
          await sourceMailingAddressDocRef.update(validatedAddr);
          console.log(`
          
          sync source with validated components
          ${sourceMailingAddressDocRef.path}
          
          
          
          `);
        } catch (error) {
          console.error("problem in syncSourceAddressFields");
        }
      } else if (!hasCompleteSourceComponents) {
        await sourceMailingAddressDocRef.update({ delete: true });
      }
    }
    function recipientIdsFromPaths(paths = []) {
      return paths.map((path) => path.split("/")[3]);
    }
  }
);
