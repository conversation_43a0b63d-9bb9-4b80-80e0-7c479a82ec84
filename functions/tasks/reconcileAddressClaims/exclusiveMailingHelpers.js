const {
  Timestamp,
  FieldValue,
  getFirestore,
} = require("firebase-admin/firestore");
const {
  deliverabilityStatus,
} = require("../../constants/deliverabilityStatus");

exports.getRecipientsByAccountIdAndStatus =
  async function getRecipientsByAccountIdAndStatus({ accountId, code }) {
    const db = getFirestore();
    try {
      console.log("accountId: ", accountId);
      console.log("code: ", code);
      const recipientsCollectionRef = db
        .collection("Account")
        .doc(accountId)
        .collection("Recipients");

      let recipientsQuery;

      if (code === "ALL") {
        const allCodes = Object.values(deliverabilityStatus).map(
          ({ code }) => code
        );
        recipientsQuery = recipientsCollectionRef
          .where("addressDeliverability.code", "in", allCodes)
          .where("isActive", "==", true);
      } else {
        recipientsQuery = recipientsCollectionRef
          .where("addressDeliverability.code", "==", code)
          .where("isActive", "==", true);
      }

      const recipientsSnapshot = await recipientsQuery.get();
      if (!recipientsSnapshot.empty) {
        // console.log(recipientsSnapshot.docs.map((doc) => ({id: doc.id, ...doc.data() })))
        return recipientsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));
      }
      return [];
    } catch (error) {
      console.error(error);
    }
  };

exports.getRecipientAddressIds = function getRecipientAddressIds(
  recipientData = []
) {
  return recipientData.map((recipient) => {
    if (recipient.mailingAddress) {
      return recipient.mailingAddress;
    } else if (recipient?.mailingAddresses) {
      return recipient?.mailingAddresses?.[0]?.id;
    }
    return;
  });
};

exports.getRecipientGroupIds = async function getRecipientGroupIds(accountId) {
  try {
    const db = getFirestore();
    const recipientGroupCollectionRef = db
      .collection("Account")
      .doc(accountId)
      .collection("RecipientGroups");
    const recipientGroupCollectionSnapshot =
      await recipientGroupCollectionRef.get();
    if (!recipientGroupCollectionSnapshot.empty) {
      return recipientGroupCollectionSnapshot.docs.map((doc) => doc.id);
    }
    return [];
  } catch (error) {
    console.log(error);
    return [];
  }
};

exports.getGroupsWithExclusiveProduct =
  async function getGroupsWithExclusiveProduct({ accountId, groupIds }) {
    const db = getFirestore();
    try {
      const magazineProducts = ["1", "11", "12", "3", "7"];

      const groupChecks = await Promise.all(
        groupIds.map(async (groupId) => {
          const recipientGroupRef = db
            .collection("Account")
            .doc(accountId.toString())
            .collection("RecipientGroups")
            .doc(groupId);

          const recipientGroupSnapshot = await recipientGroupRef.get();
          if (!recipientGroupSnapshot.exists) {
            console.warn(`Recipient group ${groupId} not found`);
            return null;
          }

          const recipientGroupData = recipientGroupSnapshot.data();
          const hasExclusiveProduct =
            Array.isArray(recipientGroupData.productPlans) &&
            recipientGroupData.productPlans.some((plan) =>
              magazineProducts.includes(plan)
            );

          return hasExclusiveProduct ? groupId : null;
        })
      );

      return groupChecks.filter((id) => id !== null);
    } catch (error) {
      console.log(error);
      return [];
    }
  };

const DEFAULT_BATCH = 500;
exports.getMailingAddressDocsBatch =
  async function* getMailingAddressDocsBatch({
    batchSize = DEFAULT_BATCH,
    addressIds = [],
  }) {
    const db = getFirestore();
    let docRefsBatch = [];

    for (const id of addressIds) {
      docRefsBatch.push(db.collection("MailingAddresses").doc(id));

      if (docRefsBatch.length === batchSize) {
        const addressSnapshots = await db.getAll(...docRefsBatch);
        yield addressSnapshots;
        docRefsBatch = [];
      }
    }

    // fall through here and pick up remaining
    if (docRefsBatch.length > 0) {
      const addressSnapshots = await db.getAll(...docRefsBatch);
      yield addressSnapshots;
    }
  };

exports.hasAddressEnrolledInExclusiveProduct =
  async function hasAddressEnrolledInExclusiveProduct({
    accountId,
    addressData,
    sourceAddressId,
    groupIds = [],
  }) {
    const db = getFirestore();
    const { address1, address2, city, state, postalCode } = addressData ?? {};
    console.log(
      "hasAddressEnrolledInExclusiveProduct",
      sourceAddressId,
      groupIds
    );
    const formattedAddress = [
      address1?.trim(),
      address2?.trim(),
      city?.trim(),
      state?.trim(),
      postalCode?.trim(),
    ]
      .filter(Boolean)
      .join(" ")
      .toUpperCase();
    console.log("Formatted Address: ", formattedAddress);

    const addressPrefix = address1?.trim().toUpperCase();

    try {
      // Base query for active recipients at that address
      // favor sourceAddressId, if unavailable then search formattedAddress
      let baseQuery;
      if (sourceAddressId) {
        baseQuery = db
          .collection("Account")
          .doc(accountId.toString())
          .collection("Recipients")
          .where("isActive", "==", true)
          .where("mailingAddress", "==", sourceAddressId);
      } else {
        baseQuery = db
          .collection("Account")
          .doc(accountId.toString())
          .collection("Recipients")
          .where("isActive", "==", true)
          .where("searchTags.formattedMailingAddress", ">=", addressPrefix)
          .where(
            "searchTags.formattedMailingAddress",
            "<",
            addressPrefix + "\uf8ff"
          );
      }

      //  split an array into chunks of a set size
      function chunkArray(arr, size) {
        const chunks = [];
        for (let i = 0; i < arr.length; i += size) {
          chunks.push(arr.slice(i, i + size));
        }
        return chunks;
      }

      let matchingDocs = [];

      if (Array.isArray(groupIds) && groupIds.length > 0) {
        const MAX_ARRAY_CONTAINS = 10;

        if (groupIds.length <= MAX_ARRAY_CONTAINS) {
          const snap = await baseQuery
            .where("recipientGroupIds", "array-contains-any", groupIds)
            .get();
          matchingDocs = snap.docs;
        } else {
          // Multiple queries in chunks of 10
          const seen = new Map();
          for (const chunk of chunkArray(groupIds, MAX_ARRAY_CONTAINS)) {
            const snap = await baseQuery
              .where("recipientGroupIds", "array-contains-any", chunk)
              .get();
            for (const doc of snap.docs) {
              seen.set(doc.id, doc);
            }
          }
          matchingDocs = Array.from(seen.values());
        }
      }

      if (matchingDocs.length === 0) {
        console.log(
          `No active recipients with product at ${formattedAddress}. AccountId: ${accountId}` +
            (groupIds.length ? ` in groups [${groupIds.join(", ")}]` : "")
        );
        return [];
      }

      console.log(
        `Active Recipients with product at ${formattedAddress}. AccountId: ${accountId}`
      );
      return matchingDocs.map((doc) => doc.ref.path);
    } catch (error) {
      console.error("Problem in hasAddressEnrolledInExclusiveProduct", error);
      throw error;
    }
  };

exports.reprocessUserEnteredMailingAddresses =
  async function reprocessUserEnteredMailingAddresses(recipientPaths = []) {
    const db = getFirestore();
    if (!Array.isArray(recipientPaths) || recipientPaths.length === 0) {
      console.warn("No recipient paths provided; nothing to reprocess.");
      return;
    }

    try {
      const updates = recipientPaths.map((path) => {
        const addrRef = db
          .doc(path)
          .collection("UserEnteredMailingAddress")
          .doc("0");
        return addrRef.update({ processingStatus: "REPROCESS" });
      });

      await Promise.all([updates]);
    } catch (error) {
      console.error(error);
    }
  };
