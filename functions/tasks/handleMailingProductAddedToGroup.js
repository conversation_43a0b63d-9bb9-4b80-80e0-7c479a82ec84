const {
  Timestamp,
  FieldValue,
  getFirestore,
} = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const {
  onTaskDispatched,
  getFunctions,
} = require("firebase-functions/v2/tasks");
const { defineString } = require("firebase-functions/params");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const {
  addToTopicConfigured,
} = require("../utils/pubsub/addToTopicConfigured");
const { logToFirestore } = require("../utils/functionLogger");
const { collectionHistory } = require("../utils/collectionHistory");
const {
  mailingAddressStateCases,
} = require("../constants/mailingAddressStateCases");
const { REPROCESS, DONE } = mailingAddressStateCases;

const projectIdEnv = defineString("TITAN_PROJECT_ID");
const projectId = projectIdEnv.value();
const location = "us-central1";
const handleTask = `projects/${projectId}/locations/${location}/functions/handleMailingProductAddedToGroup`;

exports.handleMailingProductAddedToGroup = onTaskDispatched(
  {
    retryConfig: { maxAttempts: 5 },
    timeoutSeconds: 540,
    memory: "1GiB",
  },
  async (task) => {
    const { accountId, recipientGroupId, startAfterId = null } = task.data;
    try {
      await processBatch({ accountId, recipientGroupId, startAfterId });
    } catch (error) {
      logger.error("Error processing task batch:", error);
      throw error; // trigger retry
    }
  }
);

async function processBatch({ accountId, recipientGroupId, startAfterId }) {
  const db = getFirestore();
  const batchSize = 100;

  const recipientsCollectionRef = db
    .collection(collectionNames.account)
    .doc(accountId)
    .collection(subCollectionNames.contacts.recipients);

  let query = recipientsCollectionRef
    .where("recipientGroupIds", "array-contains", recipientGroupId)
    .orderBy("__name__")
    .limit(batchSize);

  if (startAfterId) {
    const lastSnap = await db.doc(startAfterId).get();
    if (lastSnap.exists) {
      query = query.startAfter(lastSnap);
    }
  }

  const snapshot = await query.get();
  if (snapshot.empty) {
    return;
  }

  for (const recipientDoc of snapshot.docs) {
    const userAddrRef = recipientDoc.ref
      .collection(subCollectionNames.contacts.userEnteredMailingAddress)
      .doc("0");

    const addrSnap = await userAddrRef.get();
    if (!addrSnap.exists) continue;

    const { processingStatus } = addrSnap.data();

    try {
      if (processingStatus === REPROCESS) {
        await userAddrRef.update({ processingStatus: DONE });
      }
      await userAddrRef.update({ processingStatus: REPROCESS });
    } catch (err) {
      await logToFirestore({
        functionName: "handleMailingProductAddedToGroup",
        type: "error",
        message: "Failed to update processingStatus to REPROCESS",
        data: { path: userAddrRef.path, error: err.message, stack: err.stack },
      });
    }

    try {
      const recipientId = recipientDoc.id;
      const message = JSON.stringify({
        accountId,
        recipientId,
        action: "productAdded",
        userId: "",
      });
      await addToTopicConfigured({
        topic: "remove-release-exclusivity",
        message,
      });
      await collectionHistory({
        collection: `${collectionNames.account}/${accountId}/${subCollectionNames.contacts.recipients}`,
        id: recipientId,
        context: {
          action: "productAdded",
          topic: "remove-release-exclusivity",
          notes: "Product added to group.",
        },
        action: "UPDATE",
      });
    } catch (err) {
      logger.error(`Error processing recipient ${recipientDoc.id}:`, err);
    }
  }

  // Schedule the next batch
  const lastDoc = snapshot.docs[snapshot.docs.length - 1];
  const nextPayload = {
    accountId,
    recipientGroupId,
    startAfterId: lastDoc.ref.path,
  };
  const functionsClient = getFunctions();
  const queue = functionsClient.taskQueue(handleTask);
  await queue.enqueue(nextPayload);
}
