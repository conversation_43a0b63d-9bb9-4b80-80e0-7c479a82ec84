const { defineString, defineSecret } = require("firebase-functions/params");
const { logger } = require("firebase-functions");
const axios = require("axios");
const { getAstridCreds } = require("./getAstridCreds");

async function astridConnection({ payload, url, method = "GET" }) {
  const creds = await getAstridCreds();
  const { token, baseUrl } = creds;
  const endpoint = `${baseUrl}/${url}`;
  console.log("🚀 Astrid Connection endpoint: ", endpoint);
  try {
    const response = await axios({
      method: method,
      url: endpoint,
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      data: payload,
    });
    return { data: response?.data };
  } catch (error) {
    if (error.response) {
      logger.error("HTTP error response:", {
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers,
      });
    } else if (error.request) {
      logger.error("No response received:", {
        request: error.request,
      });
    } else {
      logger.error("Error setting up the request:", error.message);
    }
    logger.error("Error astridConnection:", error.message, error.stack);
    // Optionally log the request config for debugging
    logger.error("Request config:", error.config);
    return { error: error.message };
  }
}

module.exports.astridConnection = astridConnection;
