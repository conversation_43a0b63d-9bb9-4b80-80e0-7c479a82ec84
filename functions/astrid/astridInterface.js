const { onCall } = require("firebase-functions/v2/https");
const { defineSecret, defineString, HttpsError } = require("firebase-functions/params");
const axios = require("axios");
const {getAstridCreds} = require("../utils/getAstridCreds");

exports.callAstrid = onCall(async (request) => {
  const { payload, url, method = "GET" } = request.data;
  const creds = await getAstridCreds();
  const { token, baseUrl } = creds;

  const astridUrl = `${baseUrl}/${url}`;

  try {
    // No need to call token.access(); Firebase manages secrets automatically
    const response = await axios({
      method: method,
      url: astridUrl,
      headers: {
        Authorization: `Bearer ${token}`, // Use the secret token directly
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      data: payload,
    });

    return { data: response.data };
  } catch (error) {
    console.error("Error making HTTP request:", error.message, error);
    return { error: error.message };
  }
});
