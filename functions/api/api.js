const { onRequest } = require("firebase-functions/v2/https");
const express = require("express");
const cors = require("cors");
const { validateApi<PERSON>ey } = require("./v1/middleware/validateApiKey");

const { v1_integration_rules_get } = require("./v1/integrationRules/v1_integration_rules_get");

const { v1_recipient_get } = require("./v1/recipients/v1_recipient_get");
const { v1_recipients_get } = require("./v1/recipients/v1_recipients_get");
const { v1_recipients_post } = require("./v1/recipients/v1_recipients_post");
const { v1_recipients_put } = require("./v1/recipients/v1_recipients_put");
const { v1_recipients_delete, } = require("./v1/recipients/v1_recipients_delete");

const { v1_recipient_groups_get, } = require("./v1/recipientGroups/v1_recipient_groups_get");
const { v1_recipient_groups_post, } = require("./v1/recipientGroups/v1_recipient_groups_post");
const { v1_recipient_groups_put, } = require("./v1/recipientGroups/v1_recipient_groups_put");
const { v1_recipient_groups_delete, } = require("./v1/recipientGroups/v1_recipient_groups_delete");

const { v1_custom_fields_get, } = require("./v1/customFields/v1_custom_fields_get");
const { v1_custom_fields_get_single, } = require("./v1/customFields/v1_custom_fields_get_single");
const { v1_custom_fields_post, } = require("./v1/customFields/v1_custom_fields_post");
const { v1_custom_fields_put, } = require("./v1/customFields/v1_custom_fields_put");
const { v1_custom_fields_delete, } = require("./v1/customFields/v1_custom_fields_delete");

const createApiApp = (db) => {
  // Initialize Express App
  const app = express();

  // Enable CORS
  app.use(cors({ origin: true }));

  // Middleware to parse JSON requests
  app.use(express.json());

  // *** ROUTES
  // Integration Config
  app.get("/v1/integration-rules/:integrationId", validateApiKey(db), async (req, res) => {
    const response = await v1_integration_rules_get(db, req);

    res.status(response.status).send(response);
  });

  // Recipients
  app.get("/v1/recipients/:recipientId", validateApiKey(db), async (req, res) => {
    const response = await v1_recipient_get(db, req);

    res.status(response.status).send(response);
  });
  app.get("/v1/recipients", validateApiKey(db), async (req, res) => {
    const response = await v1_recipients_get(db, req);

    res.status(response.status).send(response);
  });
  app.post("/v1/recipients", validateApiKey(db), async (req, res) => {
    const response = await v1_recipients_post(db, req);

    res.status(response.status).send(response);
  });
  app.put("/v1/recipients/:recipientId", validateApiKey(db), async (req, res) => {
      const response = await v1_recipients_put(db, req);

      res.status(response.status).send(response);
    }
  );
  app.delete("/v1/recipients/:recipientId", validateApiKey(db), async (req, res) => {
      const response = await v1_recipients_delete(db, req);

      res.status(response.status).send(response);
    }
  );

  // Recipient Groups
  app.get("/v1/recipient-groups", validateApiKey(db), async (req, res) => {
    const response = await v1_recipient_groups_get(db, req);

    res.status(response.status).send(response);
  });
  app.post("/v1/recipient-groups", validateApiKey(db), async (req, res) => {
    const response = await v1_recipient_groups_post(db, req);

    res.status(response.status).send(response);
  });
  app.put("/v1/recipient-groups/:groupId", validateApiKey(db), async (req, res) => {
      const response = await v1_recipient_groups_put(db, req);

      res.status(response.status).send(response);
    }
  );
  app.delete("/v1/recipient-groups/:groupId", validateApiKey(db), async (req, res) => {
      const response = await v1_recipient_groups_delete(db, req);

      res.status(response.status).send(response);
    }
  );

  // Custom Fields
  app.get("/v1/custom-fields", validateApiKey(db), async (req, res) => {
    const response = await v1_custom_fields_get(db, req);

    res.status(response.status).send(response);
  });
  app.get("/v1/custom-fields/:customFieldsId", validateApiKey(db), async (req, res) => {
    const response = await v1_custom_fields_get_single(db, req);

    res.status(response.status).send(response);
  });
  app.post("/v1/custom-fields", validateApiKey(db), async (req, res) => {
    const response = await v1_custom_fields_post(db, req);

    res.status(response.status).send(response);
  });
  app.put("/v1/custom-fields/:customFieldsId", validateApiKey(db), async (req, res) => {
      const response = await v1_custom_fields_put(db, req);

      res.status(response.status).send(response);
    }
  );
  app.delete("/v1/custom-fields/:customFieldsId", validateApiKey(db), async (req, res) => {
    const response = await v1_custom_fields_delete(db, req);

    res.status(response.status).send(response);
  }
);

  // Catch-all Route
  app.use((req, res) => {
    res.status(404).send({ error: "Route not found" });
  });

  return app;
};

exports.api = (db) => {  
  return onRequest({    
    // This allows unauthenticated access    
    invoker: 'public',  
    // Optional: specify region
    region: 'us-central1' 
  }, createApiApp(db));
};
