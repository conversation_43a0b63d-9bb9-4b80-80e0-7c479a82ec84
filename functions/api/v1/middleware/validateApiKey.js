/**
 * Middleware to validate API key
 *
 * @param {FirebaseFirestore.Firestore} db - Firestore database instance
 * @returns {Function} Express middleware function with accountId attached to request
 */
exports.validateApiKey = (db) => {
  return async (req, res, next) => {
    // Extract API key from request headers
    const apiKey = req.headers["x-api-key"];

    // Check if API key is present
    if (!apiKey) {
      return res.status(401).json({
        error: "No API key provided",
        message: "Authentication required",
      });
    }

    try {
      const apiKeyDoc = await db.collection("ApiKeys").doc(apiKey).get();

      // Check if the document exists and is active
      if (!apiKeyDoc.exists) {
        return res.status(403).json({
          error: "Invalid API key",
          message: "The provided API key is not valid",
        });
      }

      // Get document data
      const apiKeyData = apiKeyDoc.data();
      const { accountId } = apiKeyData || {};

      // Validate that accountId exists
      if (!accountId) {
        return res.status(403).json({
          error: "Invalid API key configuration",
          message: "No associated account found",
        });
      }

      // Explicitly pass accountId to the next middleware/route handler
      req.accountId = accountId;

      next();
    } catch (error) {
      console.error("API Key Validation Error:", error);
      res.status(500).json({
        error: "Internal server error",
        message: "Failed to validate API key",
      });
    }
  };
};
