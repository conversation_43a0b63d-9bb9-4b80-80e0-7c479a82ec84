const { logger } = require("firebase-functions");
const { FieldPath } = require("firebase-admin/firestore");
const { collectionNames, subCollectionNames } = require("../../../constants/collectionNames.js");
const { saveApiLog } = require("../../../utils/api-logger.js");

exports.v1_integration_rules_get = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;
    const integrationId = req.params.integrationId;
    const integrationName = integrationId.split("-").join(" ");

    if (!accountId) {
      throw new Error("No accountId found in request.");
    }

    if (!integrationId) {
      throw new Error("No integrationId found in request.");
    }

    // Get account
    const accountRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString());
    const accountDoc = await accountRef.get();
    // was the account found?
    if (!accountDoc.exists) {
      throw new Error("No Account was not found for the given accountId.");
    }
    // const accountData = accountDoc.data();

    const {
      page = 1,
      pageSize = 20,  // default to 20 records per page, max of 100
    } = req.query;

    // Validate and parse pagination parameters
    const pageNumber = Math.max(1, parseInt(page, 10));
    const pageSizeNumber = Math.min(100, Math.max(1, parseInt(pageSize, 10)));

    // collect account's integration rules
    let integrationRulesRef = await accountRef
      .collection(subCollectionNames.account.integrations)
      .where("connector", "==", integrationName)
      .where("methodType", "==", "Import Contacts");

    // execute query
    const integrationRulesSnapshot = await integrationRulesRef.get();

    if (integrationRulesSnapshot.empty || integrationRulesSnapshot.docs[0]?.data()?.importRules?.length === 0) {
      saveApiLog(200, req);

      return {
        status: 404,
        data: [],
        message: "No integration rules found.",
      };
    }

    // get the integration rules object array
    const integrationRulesObjectArray = integrationRulesSnapshot.docs[0]?.data()?.importRules;
    const recipientGroupIdDefault = integrationRulesSnapshot.docs[0]?.data()?.recipientGroupIdDefault;

    // Build the response object
    let integrationRulesResponseObject = [];
    if (integrationRulesObjectArray && integrationRulesObjectArray.length > 0) {
      integrationRulesResponseObject = integrationRulesObjectArray.map(obj => {
        const {recipientGroupId, ...theRest} = obj;
        return {
          recipientGroupId,
          recipientGroupIdDefault,
          rules: {
            ...theRest
          }
        }
      });
    }

    // Fetch total documents to calculate total pages
    const totalSnapshot = await integrationRulesRef.count().get();
    const totalRecords = totalSnapshot.data().count;
    const totalPages = Math.ceil(totalRecords / pageSizeNumber);

    saveApiLog(200, req);

    return {
      status: 200,
      pagination: {
        currentPage: pageNumber,
        totalPages,
        totalRecords,
        pageSize: pageSizeNumber,
      },
      data: integrationRulesResponseObject,
    };
  } catch (error) {
    logger.error("Error fetching integration rules.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};
