const {
  collectionNames,
  subCollectionNames,
} = require("../../../constants/collectionNames.js");
const {
  convertFirestoreTimestampToDateTime,
} = require("../../../utils/convertFirestoreTimestampToDateTime.js");
const { logger } = require("firebase-functions");
const { saveApiLog } = require("../../../utils/api-logger.js");
const { Timestamp } = require("firebase-admin/firestore");

exports.v1_custom_fields_post = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;

    if (!accountId) {
      throw new Error("Account ID was not found in request.");
    }

    // Extract only the required fields from the request body
    const { label, type, isActive, sortable } = req.body;

    // Validate required fields
    if (!label) {
      saveApiLog(400, req);

      return { status: 400, error: "Label is required." };
    }

    const name = 'custom' + label.trim()
      .replace(/[^0-9a-zA-Z ]/g, '').split(' ').filter(word => word.length > 0)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');

    // Create the custom field document
    const storeableDataObject = {
      name,
      label: label,
      type: type ?? 'text',        // Optional: Default to text
      sortable: sortable ?? false, // Optional: Default to false
      isActive: isActive ?? true,  // Optional: Default to true
      createdAt: Timestamp.now(),  // required
      updatedAt: Timestamp.now(),  // required
    };

    // Add document to Firestore
    const newDocRef = await db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.customFields)
      .add(storeableDataObject);

    // Return the created document with its ID
    const newData = {
      id: newDocRef.id,
      ...storeableDataObject,
    };

    // Build the response data
    const responseData = buildResponseData(newData);
    saveApiLog(201, req);

    return {
      status: 201,
      data: responseData,
    };
  } catch (error) {
    logger.error("Error creating custom field.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};

const buildResponseData = (data) => {
  return {
    id: data.id,
    name: data.name,
    label: data.label,
    type: data.type,
    isActive: data.isActive,
    sortable: data.sortable,
    createdAt: convertFirestoreTimestampToDateTime(data.createdAt),
    updatedAt: convertFirestoreTimestampToDateTime(data.updatedAt),
  };
};
