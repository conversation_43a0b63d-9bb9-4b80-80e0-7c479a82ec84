const { Timestamp, FieldValue } = require("firebase-admin/firestore");
const { collectionNames, subCollectionNames, } = require("../../../constants/collectionNames.js");
const { logger } = require("firebase-functions");
const { saveApiLog } = require("../../../utils/api-logger.js");

exports.v1_custom_fields_delete = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;
    const customFieldsId = req.params.customFieldsId;

    if (!accountId) {
      throw new Error("Account ID was not found in request.");
    }

    if (!customFieldsId) {
      saveApiLog(400, req);

      return { status: 400, error: "Custom Fields ID is required." };
    }

    // Get reference to the existing document
    const collectionRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.customFields)
      .doc(customFieldsId);

    // Check if document exists
    const doc = await collectionRef.get();
    if (!doc.exists) {
      saveApiLog(404, req);

      return { status: 404, error: "Custom field not found." };
    }

    // delete field key throughout recipients
    const oldFieldName = doc.data().name;
    const querySnapshot = await db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.recipients)
      .orderBy(oldFieldName, 'desc')
      .get();

    if (oldFieldName !== null) {
      // create new field key and delete old field key
      if (!querySnapshot.empty) {
        for (const recipientDoc of querySnapshot.docs) {
          await db.collection(collectionNames.account).doc(accountId.toString())
            .collection(subCollectionNames.contacts.recipients)
            .doc(recipientDoc.id)
            .update({
              [oldFieldName]: FieldValue.delete()
            });
        };
      }
    }

    // delete document
    await collectionRef.delete();
    saveApiLog(200, req);

    return {
      status: 200,
      message: "Custom field has been successfully deleted.",
    };
  } catch (error) {
    logger.error("Error deleting custom field.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};
