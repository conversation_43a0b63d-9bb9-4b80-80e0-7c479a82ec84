const { Timestamp, FieldValue } = require("firebase-admin/firestore");
const { collectionNames, subCollectionNames } = require("../../../constants/collectionNames.js");
const { convertFirestoreTimestampToDateTime } = require("../../../utils/convertFirestoreTimestampToDateTime.js");
const { logger } = require("firebase-functions");
const { saveApiLog } = require("../../../utils/api-logger.js");
const { isEmpty, isArray } = require("lodash");

function createCustomFieldName(label) {
  return 'custom' + label.trim()
    .replace(/[^0-9a-zA-Z ]/g, '').split(' ').filter(word => word.length > 0)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');
}


exports.v1_custom_fields_put = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;
    const customFieldsId = req.params.customFieldsId; // Assuming customFieldsId is passed as URL parameter

    if (!accountId) {
      throw new Error("Account ID was not found in request.");
    }

    if (!customFieldsId) {
      return { status: 400, error: "Custom Fields ID is required." };
    }

    if (req.body.type !== void(0)) {
      return { status: 400, error: "You are not allowed to update the type of a custom field." };
    }

    // Extract only the updatable fields from the request body
    const { label, isActive, sortable } = req.body;

    // Get reference to the existing document
    const collectionRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.customFields)
      .doc(customFieldsId);

    // Check if document exists
    const customFieldDoc = await collectionRef.get();
    if (!customFieldDoc.exists) {
      saveApiLog(404, req);

      return { status: 404, error: "The Custom Field not found." };
    }

    // Prepare update data
    const updateData = {
      updatedAt: Timestamp.now(),
    };

    if (!isEmpty(label)) {
      const newFieldName = createCustomFieldName(label);
  
      const oldFieldName = customFieldDoc.data().name;
      // Get the recipients, orderBy(oldFieldName, 'desc') selects only those with custom field's data defined.
      const querySnapshot = await db
        .collection(collectionNames.account)
        .doc(accountId.toString())
        .collection(subCollectionNames.contacts.recipients)
        .orderBy(oldFieldName, 'desc')
        .get();

      if (oldFieldName !== newFieldName) {
        // create new field key and delete old field key
        if (!querySnapshot.empty) {
          for (const recipientDoc of querySnapshot.docs) {
            await db.collection(collectionNames.account).doc(accountId.toString())
              .collection(subCollectionNames.contacts.recipients)
              .doc(recipientDoc.id)
              .update({
                [newFieldName]: recipientDoc.data()[oldFieldName],
                [oldFieldName]: FieldValue.delete()
              });
          };
        }
      }
      updateData.name = newFieldName;
      updateData.label = label;
    }

    if (!isEmpty(sortable)) {
      updateData.sortable = sortable;
    }

    if (!isEmpty(isActive)) {
      updateData.isActive = isActive;
    }

    // Update the document
    await collectionRef.update(updateData);

    // Get the updated document
    const updatedDoc = await collectionRef.get();
    const updatedData = {
      id: updatedDoc.id,
      ...updatedDoc.data(),
    };

    // build the response data
    const responseData = buildResponseData(updatedData);
    saveApiLog(200, req);

    return {
      status: 200,
      data: responseData,
    };
  } catch (error) {
    logger.error("Error updating recipient group.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};

const buildResponseData = (field) => {
  return {
    id: field.id,
    name: field.name,
    label: field.label,
    type: field.type,
    isActive: field.isActive,
    sortable: field.sortable,
    createdAt: convertFirestoreTimestampToDateTime(field.createdAt),
    updatedAt: convertFirestoreTimestampToDateTime(field.updatedAt),
  };
};
