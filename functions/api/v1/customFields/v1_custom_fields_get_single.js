const {
  collectionNames,
  subCollectionNames,
} = require("../../../constants/collectionNames.js");
const {
  convertFirestoreTimestampToDateTime,
} = require("../../../utils/convertFirestoreTimestampToDateTime");
const { logger } = require("firebase-functions");
const { saveApiLog } = require("../../../utils/api-logger.js");

exports.v1_custom_fields_get_single = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;
    const customFieldsId = req.params.customFieldsId; // Assuming customFieldsId is passed as URL parameter

    if (!accountId) {
      throw new Error("Account ID was not found in request.");
    }

    if (!customFieldsId) {
      return { status: 400, error: "Custom Fields ID is required." };
    }

    // Get reference to the existing document
    const collectionRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.customFields)
      .doc(customFieldsId);

    // Check if document exists
    const doc = await collectionRef.get();
    if (!doc.exists) {
      saveApiLog(404, req);

      return { status: 404, error: "The Custom Field not found." };
    }

    const data = buildResponseData({
      id: doc.id,
      ...doc.data(),
    });

    saveApiLog(200, req);

    return {
      status: 200,
      data,
    };
  } catch (error) {
    logger.error("Error fetching custom fields.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};

const buildResponseData = (data) => {
    return {
      id: data.id,
      name: data.name,
      label: data.label,
      type: data.type,
      isActive: data.isActive,
      sortable: data.sortable,
      createdAt: convertFirestoreTimestampToDateTime(data.createdAt),
      updatedAt: convertFirestoreTimestampToDateTime(data.updatedAt),
    };
};
