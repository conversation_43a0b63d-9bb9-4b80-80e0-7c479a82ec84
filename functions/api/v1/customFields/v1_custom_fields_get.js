const {
  collectionNames,
  subCollectionNames,
} = require("../../../constants/collectionNames.js");
const {
  convertFirestoreTimestampToDateTime,
} = require("../../../utils/convertFirestoreTimestampToDateTime");
const { logger } = require("firebase-functions");
const { saveApiLog } = require("../../../utils/api-logger.js");

exports.v1_custom_fields_get = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;

    if (!accountId) {
      throw new Error("Account ID was not found in request.");
    }

    const {
      page = 1,
      pageSize = 20,  // default to 20 records per page, max of 100
      sort_by = "updatedAt",
      sort_order = "desc",
    } = req.query;

    // Validate and parse pagination parameters
    const pageNumber = Math.max(1, parseInt(page, 10));
    const pageSizeNumber = Math.min(100, Math.max(1, parseInt(pageSize, 10)));

    // Reference to recipients sub-collection
    let collectionRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.customFields)
      .where("isActive", "==", true);

    // Apply sorting
    const validSortFields = ["createdAt", "updatedAt"];
    const validSortOrders = ["asc", "desc"];
    if (
      validSortFields.includes(sort_by) &&
      validSortOrders.includes(sort_order)
    ) {
      collectionRef = collectionRef.orderBy(sort_by, sort_order);
    } else {
      collectionRef = collectionRef.orderBy("updatedAt", "desc");
    }

    // Determine pagination strategy
    let paginatedRef = collectionRef;
    let firstPageSnapshot;

    // If not first page, we need to find the last document of the previous page
    if (pageNumber > 1) {
      // Fetch the documents to skip
      const documentsToSkip = (pageNumber - 1) * pageSizeNumber;

      // Get the last document of the previous page to use as a starting point
      firstPageSnapshot = await collectionRef.limit(documentsToSkip).get();

      // Use the last document of the previous page to start the next page
      const lastDocOfPreviousPage =
        firstPageSnapshot.docs[firstPageSnapshot.docs.length - 1];

      if (lastDocOfPreviousPage) {
        paginatedRef = collectionRef.startAfter(lastDocOfPreviousPage);
      }
    }

    // Apply limit for the current page
    paginatedRef = paginatedRef.limit(pageSizeNumber);

    // Execute query
    const snapshot = await paginatedRef.get();

    // Check if the snapshot has data
    if (snapshot.empty) {
      saveApiLog(200, req);

      return {
        status: 200,
        data: [],
        message: "No custom fields found for this account.",
      };
    }

    // Map the data into a response
    const mappedData = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    const reducedData = buildResponseData(mappedData);

    // Fetch total documents to calculate total pages
    const totalSnapshot = await collectionRef.get();
    const totalRecords = totalSnapshot.size;
    const totalPages = Math.ceil(totalRecords / pageSizeNumber);

    saveApiLog(200, req);

    return {
      status: 200,
      pagination: {
        currentPage: pageNumber,
        totalPages,
        totalRecords,
        pageSize: pageSizeNumber,
      },
      data: reducedData,
    };
  } catch (error) {
    logger.error("Error fetching custom fields.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};

const buildResponseData = (data) => {
  return data.map((rec) => {

    return {
      id: rec.id,
      name: rec.name,
      label: rec.label,
      type: rec.type,
      isActive: rec.isActive,
      sortable: rec.sortable,
      createdAt: convertFirestoreTimestampToDateTime(rec.createdAt),
      updatedAt: convertFirestoreTimestampToDateTime(rec.updatedAt),
    };
  });
};
