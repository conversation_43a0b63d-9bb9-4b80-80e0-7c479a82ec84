const { logger } = require("firebase-functions");
const { Timestamp } = require("firebase-admin/firestore");
const { collectionNames, subCollectionNames } = require("../../../constants/collectionNames.js");
const { dateStringToTimestamp } = require("../../../utils/dateStringToTimestamp");
const { delocalizeDate } = require("../../../utils/dateStringLocalization");
const { saveApiLog } = require("../../../utils/api-logger.js");
const { recursiveDocDelete } = require("../../../utils/recursiveDocDelete.js");
const { isNil, isEmpty } = require("lodash");
const { 
  recipientDataWithSubcollections, 
  buildRecipientResponseObject } = require("./components/recipientData");

exports.v1_recipients_put = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;
    const recipientId = req.params.recipientId;

    if (!accountId || !recipientId) {
      throw new Error("accountId and recipientId are required.");
    }

    // Get account
    const accountSnapshot = await db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .get();
    const accountData = accountSnapshot.data();

    // Get valid custom fields from the account
    const customFieldsSnapshot = await db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.customFields)
      .where("isActive", "==", true)
      .get();

    // map custom fields to an array
    const accountCustomFields = customFieldsSnapshot?.docs?.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    const {
      name,
      significantOther,
      salutation,
      recipientGroupIds,
      emailAddresses,
      phoneNumbers,
      notes,
      significantDates,
      socialMediaAccounts,
      userEnteredMailingAddresses,
      websites,
      externalIds,
      source,
      tags,
    } = req.body;

    // get custom fields names from the request body
    const customFieldsNames = Object.keys(req.body).filter(item => item.startsWith('custom'));


    // remove empty emailAddresses
    for (let i = emailAddresses?.length - 1; i >= 0; i--) {
      if (!emailAddresses[i].email || !emailAddresses[i].email.length) {
        emailAddresses.splice(i, 1);
      }
    }
    // remove empty phoneNumbers
    for (let i = phoneNumbers?.length - 1; i >= 0; i--) {
      if (!phoneNumbers[i].phoneNumber || !phoneNumbers[i].phoneNumber.length) {
        phoneNumbers.splice(i, 1);
      }
    }
    // remove empty significant dates
    for (let i = significantDates?.length - 1; i >= 0; i--) {
      if (!significantDates[i].date || !significantDates[i].date.length) {
        significantDates.splice(i, 1);
      }
    }
    // remove empty socialMediaAccounts
    for (let i = socialMediaAccounts?.length - 1; i >= 0; i--) {
      if (!socialMediaAccounts[i].value || !socialMediaAccounts[i].value.length) {
        socialMediaAccounts.splice(i, 1);
      }
    }
    // remove empty websites
    for (let i = websites?.length - 1; i >= 0; i--) {
      if (!websites[i].url || !websites[i].url.length) {
        websites.splice(i, 1);
      }
    }
    // remove empty external IDs
    for (let i = externalIds?.length - 1; i >= 0; i--) {
      if (!externalIds[i].externalId || !externalIds[i].externalId.length) {
        externalIds.splice(i, 1);
      }
    }

    // Get recipient reference
    const recipientRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.recipients)
      .doc(recipientId);

    // Check if recipient exists
    const recipientDoc = await recipientRef.get();
    if (!recipientDoc.exists) {
      saveApiLog(404, req);

      return {
        status: 404,
        error: "Not Found",
        message: "Recipient not found.",
      };
    }

    // Prepare updated recipient data - only include fields that were passed in
    const recipientData = {
      updatedAt: Timestamp.now(),
    };

    if (name) {
      recipientData.name = {
        ...recipientDoc.data().name, // Preserve existing values
        ...(name.firstName && { firstName: name.firstName }),
        ...(name.lastName && { lastName: name.lastName }),
        ...(name.middle !== undefined && { middle: name.middle }),
        ...(name.nickname !== undefined && { nickname: name.nickname }),
        ...(name.prefix !== undefined && { prefix: name.prefix }),
        ...(name.suffix !== undefined && { suffix: name.suffix }),
        ...(name.phoneticFirstName !== undefined && { phoneticFirstName: name.phoneticFirstName }),
        ...(name.phoneticLastName !== undefined && { phoneticLastName: name.phoneticLastName }),
      };
    }

    if (significantOther) {
      recipientData.significantOther = {
        ...recipientDoc.data().significantOther, // Preserve existing values
        ...(significantOther.firstName !== undefined && {
          firstName: significantOther.firstName,
        }),
        ...(significantOther.lastName !== undefined && {
          lastName: significantOther.lastName,
        }),
      };
    }

    if (salutation) {
      recipientData.salutation = {
        ...recipientDoc.data()?.salutation, // Preserve existing values
        ...(salutation.company !== undefined && { company: salutation.company }),
        ...(salutation.jobTitle !== undefined && { jobTitle: salutation.jobTitle }),
        ...(salutation.letterSalutation !== undefined && { letterSalutation: salutation.letterSalutation }),
        ...(salutation.mailingSalutation !== undefined && { mailingSalutation: salutation.mailingSalutation }),
      };
    } else if (recipientDoc.data()?.salutation) {
      recipientData.salutation = {
        ...recipientDoc.data().salutation // Preserve existing values
      };
    } else {
      recipientData.salutation = {};
    }

    // stolen from functions/storage/recipient-file-import/handleRecipientFileImport.js
    // Create default letter salutation if not provided
    if (!salutation?.letterSalutation) {
      // is significant other first name present?
      if (recipientData?.name?.firstName && recipientData?.significantOther?.firstName) {
        recipientData.salutation.letterSalutation = `${recipientData?.name?.firstName} & ${recipientData?.significantOther?.firstName}`;
      } else {
        recipientData.salutation.letterSalutation =
          recipientData?.name?.firstName ||
          "Current Resident";
      }
    }
    // Create default mailing salutation if not provided
    if (!salutation?.mailingSalutation) {
      // check for existence of significant other
      // verify last names match
      if (
        recipientData?.significantOther?.firstName &&
        recipientData?.significantOther?.lastName && 
        recipientData?.name?.firstName
      ) {
        // check for matching last names
        // if they match then use the matching last name in mailing salutation
        if (
          recipientData?.significantOther?.lastName?.trim()?.toLowerCase() ===
          recipientData?.name?.lastName?.trim()?.toLowerCase()
        ) {
          recipientData.salutation.mailingSalutation = `${recipientData?.name?.firstName} & ${recipientData.significantOther.firstName} ${recipientData.name.lastName}`;
        } else if (
          isNil(recipientData?.significantOther?.lastName) ||
          isEmpty(recipientData?.significantOther?.lastName)
        ) {
          // if the significant other last name is empty then use the primary recipient last name
          recipientData.salutation.mailingSalutation = `${recipientData?.name?.firstName} & ${recipientData?.significantOther?.firstName} ${recipientData?.name?.lastName}`;
        } else {
          // if the significant other last name is not empty and so and primary names dont match then use the primary recipient last name and significant other last name
          recipientData.salutation.mailingSalutation = `${recipientData?.name?.firstName} ${recipientData?.name?.lastName} & ${recipientData?.significantOther?.firstName} ${recipientData?.significantOther?.lastName}`;
        }
      } else if (!isEmpty(recipientData?.name?.firstName) && !isEmpty(recipientData?.name?.lastName)) {
        recipientData.salutation.mailingSalutation =
        ((recipientData.name.firstName || "") + " " +
        (recipientData.name.lastName || "")).trim();
      } else {
        recipientData.salutation.mailingSalutation =
          recipientData?.name?.firstName || "Current Resident";
      }
    }

    if (!isEmpty(source)) {
      recipientData.source = source;
    }

    if (!isEmpty(tags) && tags.length > 0) {
      recipientData.tags = tags;
    }

    // accept only one recipient group id
    if (!isEmpty(recipientGroupIds) && recipientGroupIds.length > 0) {
      recipientData.recipientGroupIds = [ recipientGroupIds[0] ];
    }

    // accept only one (complete-ish) user entered mailing address
    if (!isEmpty(userEnteredMailingAddresses) && userEnteredMailingAddresses.length > 1) {
      let useAddr = userEnteredMailingAddresses.find(addr => addr?.label?.toLowerCase() === 'mailing');
      if (!useAddr) useAddr = userEnteredMailingAddresses[0]; // if none labeled mailing, use first
      while(userEnteredMailingAddresses.length > 0) userEnteredMailingAddresses.pop(); // clear out any other addresses
      if ((useAddr?.address1 && useAddr.address1.trim() !== '') ||
          (useAddr?.city && useAddr.city.trim() !== '' && 
            useAddr?.state && useAddr.state.trim() !== '' && 
            useAddr?.postalCode && useAddr.postalCode.trim() !== '')) {
        userEnteredMailingAddresses.push(useAddr); // restore the one we are using if address line is present
      }
    }
    
    if (customFieldsNames?.length > 0) {
      for (const field of customFieldsNames) {
        const customField = accountCustomFields?.filter(item => item.name === field).shift();
        if (!isEmpty(customField)) {
          if (customField.type === "number") {
            recipientData[field] = Number(req.body[field]);
          } else if (customField.type === "date") {
            recipientData[field] = dateStringToTimestamp(delocalizeDate(req.body[field], accountData.timezone));
          } else  {
            recipientData[field] = req.body[field];
          }
        }
      }
    }

    // Start a batch write
    const batch = db.batch();

    // Update main recipient document only with provided fields
    if (Object.keys(recipientData).length > 1) {
      // Check if there's more than just updatedAt
      batch.update(recipientRef, recipientData);
    } else {
      batch.update(recipientRef, { updatedAt: recipientData.updatedAt });
    }

    // Helper function to replace subcollection items only if provided
    const updateSubcollection = async (items, subcollectionName, merge = false) => {
      if (!items) return; // Skip if no items provided for this subcollection

      if (!merge) {
        // if not merging, delete all existing documents in subcollection
        const docRefs = await recipientRef
          .collection(subcollectionName)
          .listDocuments();

        for (const docRef of docRefs) {
          await recursiveDocDelete(docRef, batch);
        }
      } else {
        // if merging, only delete documents with the same label
        // Delete all existing documents in subcollection
        const existingDocs = await recipientRef
          .collection(subcollectionName)
          .get();

        for (const doc of existingDocs.docs) {
          const found = items.find((item) => item.label === doc.data().label);
          if (found) {
            await recursiveDocDelete(doc.ref, batch);
          }
        };
      }

      // Add new documents
      if (subcollectionName === "UserEnteredMailingAddress") {
        items.forEach((item) => {
          const subDocRef = recipientRef.collection(subcollectionName).doc("0");
          batch.set(subDocRef, {
            ...item,
          });
        });
      } else {
        items.forEach((item) => {
          const subDocRef = recipientRef.collection(subcollectionName).doc();
          batch.set(subDocRef, {
            ...item,
          });
        });
      }
    };

    // Only update subcollections that were provided in the request
    await Promise.all(
      [
        emailAddresses &&
          updateSubcollection(
            emailAddresses.map(({ email, label, primary }) => ({
              email,
              label,
              isPrimary: primary ?? false,
            })),
            "EmailAddresses"
          ),
        phoneNumbers &&
          updateSubcollection(
            phoneNumbers.map(({ label, phoneExtension, phoneNumber, primary }) => ({
              label,
              phoneExtension: phoneExtension ?? "",
              phoneNumber,
              isPrimary: primary ?? false
            })),
            "PhoneNumbers"
          ),
        notes &&
          updateSubcollection(
            notes.map(({ value }) => ({ value })),
            "Notes"
          ),
        significantDates &&
          updateSubcollection(
          significantDates.map(({ label, date }) => {
            return { label, date: dateStringToTimestamp(delocalizeDate(date, accountData.timezone)) };
          }),
          "SignificantDates"
          ),
        socialMediaAccounts &&
          updateSubcollection(
            socialMediaAccounts.map(({ label, value }) => ({ label, value })),
            "SocialMediaAccounts"
          ),
        userEnteredMailingAddresses && userEnteredMailingAddresses.length > 0 &&
          updateSubcollection(
            userEnteredMailingAddresses.map(
              ({ address1, address2, city, state, postalCode }) => ({
                address1,
                address2: address2 ?? "",
                city,
                state,
                postalCode,
              })
            ),
            "UserEnteredMailingAddress"
          ),
        websites &&
          updateSubcollection(
            websites.map(({ label, url }) => ({ label, url })),
            "Websites"
          ),
        externalIds &&
          updateSubcollection(
            externalIds.map(({ label, externalId }) => ({
              label,
              externalId,
              updatedAt: Timestamp.now() })),
            "ExternalIds",
            true
          ),
      ].filter(Boolean)
    ); // Filter out undefined promises for unprovided subcollections

    // Commit the batch
    await batch.commit();

    // Get the complete updated recipient data
    const updatedRecipientDoc = await recipientRef.get();
    
    // Fetch sub-collections for recipient
    const updatedRecipientData = await recipientDataWithSubcollections(updatedRecipientDoc);
    delete updatedRecipientData.isActive;

    // Build the response object
    const recipientResponseObject = buildRecipientResponseObject(updatedRecipientData, accountData.timezone, accountCustomFields);

    saveApiLog(200, req);

    return {
      status: 200,
      message: "Recipient updated successfully.",
      data: recipientResponseObject,
    };
  } catch (error) {
    logger.error("Error updating recipient.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};
