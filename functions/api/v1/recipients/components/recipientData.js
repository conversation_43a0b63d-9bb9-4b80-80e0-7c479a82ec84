const { isEmpty } = require("lodash");
const { collectionNames, subCollectionNames } = require("../../../../constants/collectionNames.js");
const { convertFirestoreTimestampToDateTime } = require("../../../../utils/convertFirestoreTimestampToDateTime");
const { localizeDate } = require("../../../../utils/dateStringLocalization");

/**
 * @param {object} recipientDoc - firestore recipient document
 * @returns {object} Recipient data object with all its subcollections
 */
exports.recipientDataWithSubcollections = async function (recipientDoc) {
  const subcollectionsObjects = await getSubcollectionObjects(recipientDoc, [
    subCollectionNames.contacts.emailAddresses,
    subCollectionNames.contacts.notes,
    subCollectionNames.contacts.phoneNumbers,
    subCollectionNames.contacts.significantDates,
    subCollectionNames.contacts.socialMediaAccounts,
    subCollectionNames.contacts.userEnteredMailingAddress,
    subCollectionNames.contacts.websites,
    subCollectionNames.contacts.externalIds,
  ]);

  // Return recipient with all their subcollections
  return {
    id: recipientDoc.id,
    ...recipientDoc.data(),
    ...subcollectionsObjects,
  };
}

/**
 * @param {object} recipientDoc - firestore recipient document
 * @param {array} subcollections - Subcollctions to fetch
 * @returns {object}
 */
async function getSubcollectionObjects(recipientDoc, subcollections) {
  // Fetch all subcollections in parallel
  const subcollectionData = await Promise.all(
    subcollections.map(async (collectionName) => {
      const snapshot = await recipientDoc.ref.collection(collectionName).get();

      return {
        collectionName,
        data: snapshot.docs.map((subDoc) => ({
          id: subDoc.id,
          ...subDoc.data(),
        })),
      };
    })
  );

  // return final object with all subcollections
  return subcollectionData.reduce(
    (acc, { collectionName, data }) => {
      acc[collectionName] = data;
      return acc;
    },
    {}
  );
}

/**
 * @param {object} recipient - recipient data
 * @param {string} timezone - account's Time Zone ID/IANA Time Zone Name
 * @param {object[]} accountCustomFields - account's custom fields objects
 * @returns {object} Recipient response object
 */
exports.buildRecipientResponseObject = function (recipient, timezone, accountCustomFields) {
  const emailAddresses =
    recipient.EmailAddresses?.map(({ email, label, isPrimary }) => ({ 
      email, 
      label, 
      primary: isPrimary ?? false })) ||
    [];
  const phoneNumbers =
    recipient.PhoneNumbers?.map(({ label, phoneNumber, phoneExtension, isPrimary }) => ({
      label,
      phoneNumber,
      phoneExtension,
      primary: isPrimary ?? false,
    })) || [];
  const notes = recipient.Notes?.map(({ value }) => ({ value })) || [];
  const significantDates =
    recipient.SignificantDates?.map(({ label, date }) => {
      return { label, date: !isEmpty(date) ? localizeDate(date, timezone) : null };
    }) || [];
  const socialMediaAccounts =
    recipient.SocialMediaAccounts?.map(({ label, value }) => ({
      label,
      value,
    })) || [];
  const websites =
    recipient.Websites?.map(({ label, url }) => ({ label, url })) || [];
  const externalIds =
    recipient.ExternalIds?.map(({ label, externalId }) => ({ label, externalId })) || [];
  const userEnteredMailingAddresses =
    recipient.UserEnteredMailingAddress?.map((address) => {
      return {
        address1: address.address1,
        address2: address.address2,
        city: address.city,
        state: address.state,
        postalCode: address.postalCode,
      };
    }) || [];
  const customFields = {
    ...(accountCustomFields.length > 0 && 
      Object.fromEntries(
        accountCustomFields.map(field => 
          (!isEmpty(recipient[field.name]) ? 
          [field.name, (field.type === 'date' ? localizeDate(recipient[field.name], timezone) : recipient[field.name])] : 
          [])
        )
      ))
  };

  return {
    id: recipient.id,
    accountId: recipient.accountId,
    recipientGroupIds: recipient.recipientGroupIds,
    name: recipient.name,
    significantOther: recipient.significantOther,
    salutation: recipient.salutation,
    emailAddresses,
    phoneNumbers,
    notes,
    significantDates,
    socialMediaAccounts,
    userEnteredMailingAddresses,
    websites,
    tags: recipient.tags,
    externalIds,
    ...customFields,
    createdAt: recipient.createdAt ? convertFirestoreTimestampToDateTime(recipient.createdAt) : null,
    updatedAt: recipient.updatedAt ? convertFirestoreTimestampToDateTime(recipient.updatedAt) : null,
  };
}
