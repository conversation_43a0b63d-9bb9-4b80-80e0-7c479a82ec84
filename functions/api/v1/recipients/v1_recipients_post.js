const { logger } = require("firebase-functions");
const { Timestamp } = require("firebase-admin/firestore");
const { collectionNames, subCollectionNames } = require("../../../constants/collectionNames.js");
const { convertFirestoreTimestampToDateTime } = require("../../../utils/convertFirestoreTimestampToDateTime");
const { dateStringToTimestamp } = require("../../../utils/dateStringToTimestamp");
const { delocalizeDate } = require("../../../utils/dateStringLocalization");
const { saveApiLog } = require("../../../utils/api-logger.js");
const { createUUID } = require("../../../utils/createUUID");
const { isNil, isEmpty } = require("lodash");

exports.v1_recipients_post = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;
    if (!accountId) {
      throw new Error("No accountId found in request.");
    }

    // Get account
    const accountSnapshot = await db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .get();
    const accountData = accountSnapshot.data();

    // Get valid custom fields from the account
    const customFieldsSnapshot = await db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.customFields)
      .where("isActive", "==", true)
      .get();

    // map custom fields to an array
    const accountCustomFields = customFieldsSnapshot?.docs?.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    const theData = Array.isArray(req.body) ? req.body[0] : req.body;

    const {
      name,
      significantOther,
      salutation,
      recipientGroupIds = [],
      emailAddresses = [],
      phoneNumbers = [],
      notes = [],
      significantDates = [],
      socialMediaAccounts = [],
      userEnteredMailingAddresses = [],
      websites = [],
      externalIds = [],
      source,
      tags,
    } = theData;

    // get custom fields names from the request body
    const customFieldsNames = Object.keys(theData).filter((item) =>
      item.startsWith("custom")
    );

    // handle posting with no recipient group ids.
    if (isEmpty(recipientGroupIds)) {
      let groupName;
      // if we have external ids, use the first one as the group name
      if (!isEmpty(externalIds)) {
        // look up external id name in the integrations collection
        groupName = externalIds[0]?.label;
        const integrationsSnapshot = await db
          .collection(collectionNames.account)
          .doc(accountId.toString())
          .collection(subCollectionNames.account.integrations)
          .where("connector", "==", groupName)
          .where("methodType", "==", "Import Contacts")
          .limit(1)
          .get();

        // if we found an installed integration, look up the associated group id in the recipient groups collection
        const recipientGroupIdDefault = integrationsSnapshot?.docs[0]?.data()?.recipientGroupIdDefault || null;
        if (!integrationsSnapshot.empty && !isEmpty(recipientGroupIdDefault)) {
          const groupDoc = await db
            .collection(collectionNames.account)
            .doc(accountId.toString())
            .collection(subCollectionNames.contacts.recipientGroups)
            .doc(recipientGroupIdDefault)
            .get();

          if (groupDoc.exists) {
            recipientGroupIds.push(groupDoc.id);
          }
        }
      }
      // fallback to "Imported" group
      if (isEmpty(recipientGroupIds)) {
        groupName = "Imported";
        const groupsSnapshot = await db
          .collection(collectionNames.account)
          .doc(accountId.toString())
          .collection(subCollectionNames.contacts.recipientGroups)
          .where("name", "==", groupName)
          .where("isActive", "==", true)
          .limit(1)
          .get();

        if (groupsSnapshot.empty) {
          // Create the recipient group document
          const recipientGroupData = {
            name: groupName,
            description: "Imported from other sources.",
            isActive: true,
            createdAt: Timestamp.now(),
            updatedAt: Timestamp.now(),
          };

          // Add document to Firestore
          const recipientGroupsCollectionRef = db
            .collection(collectionNames.account)
            .doc(accountId.toString())
            .collection(subCollectionNames.contacts.recipientGroups);
          const uuid = createUUID();
          const docRef = recipientGroupsCollectionRef.doc(uuid);
          await docRef.set(recipientGroupData);

          recipientGroupIds.push(docRef.id);
        } else {
          recipientGroupIds.push(groupsSnapshot?.docs[0]?.id);
        }
      }
    }

    // ensure we only have one recipient group id
    if (!isEmpty(recipientGroupIds) && recipientGroupIds.length > 1) {
      recipientGroupIds = [ recipientGroupIds[0] ];
    }

    // Create recipient document
    const recipientCollectionRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.recipients);
    const uuid = createUUID();
    const recipientRef = recipientCollectionRef.doc(uuid);

    // Prepare main recipient data
    const recipientData = {
      accountId: parseInt(accountId),
      recipientGroupIds,
      name: {
        firstName: name?.firstName?.trim() || "",
        lastName: name?.lastName?.trim() || "",
        middle: name?.middle?.trim() || "",
        nickname: name?.nickname?.trim() || "",
        prefix: name?.prefix?.trim() || "",
        suffix: name?.suffix?.trim() || "",
        phoneticFirstName: name?.phoneticFirstName?.trim() || "",
        phoneticLastName: name?.phoneticLastName?.trim() || "",
      },
      significantOther: {
        firstName: significantOther?.firstName?.trim() || "",
        lastName: significantOther?.lastName?.trim() || "",
      },
      salutation: {
        company: salutation?.company?.trim() || "",
        jobTitle: salutation?.jobTitle?.trim() || "",
        letterSalutation: salutation?.letterSalutation ?? "",
        mailingSalutation: salutation?.mailingSalutation ?? "",
      },
      ...(tags?.length > 0 ? { tags: tags } : {}),
      mailingsPaused: false,
      isActive: true,
      source: source || null,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    };

    // stolen from functions/storage/recipient-file-import/handleRecipientFileImport.js
    // Create default letter salutation if not provided
    if (!recipientData?.salutation?.letterSalutation || recipientData?.salutation?.letterSalutation === "") {
      // is significant other first name present?
      if (recipientData?.name?.firstName && recipientData?.significantOther?.firstName) {
        recipientData.salutation.letterSalutation = `${recipientData?.name?.firstName} & ${recipientData?.significantOther?.firstName}`;
      } else {
        recipientData.salutation.letterSalutation =
          recipientData?.name?.firstName ||
          "Current Resident";
      }
    }
    // Create default mailing salutation if not provided
    if (!recipientData?.salutation?.mailingSalutation || recipientData?.salutation?.mailingSalutation === "") {
      // check for existence of significant other
      // verify last names match
      if (
        recipientData?.significantOther?.firstName &&
        recipientData?.significantOther?.lastName
      ) {
        // check for matching last names
        // if they match then use the matching last name in mailing salutation
        if (
          recipientData?.significantOther?.lastName?.trim()?.toLowerCase() ===
          recipientData?.name?.lastName?.trim()?.toLowerCase()
        ) {
          recipientData.salutation.mailingSalutation = `${recipientData.name.firstName} & ${recipientData.significantOther.firstName} ${recipientData.name.lastName}`;
        } else if (
          isNil(recipientData?.significantOther?.lastName) ||
          isEmpty(recipientData?.significantOther?.lastName)
        ) {
          // if the significant other last name is empty then use the primary recipient last name
          recipientData.salutation.mailingSalutation = `${recipientData?.name?.firstName} & ${recipientData?.significantOther?.firstName} ${recipientData?.name?.lastName}`;
        } else {
          // if the significant other last name is not empty and so and primary names dont match then use the primary recipient last name and significant other last name
          recipientData.salutation.mailingSalutation = `${recipientData?.name?.firstName} ${recipientData?.name?.lastName} & ${recipientData?.significantOther?.firstName} ${recipientData?.significantOther?.lastName}`;
        }
      } else if (!isEmpty(recipientData?.name?.firstName) && !isEmpty(recipientData?.name?.lastName)) {
        recipientData.salutation.mailingSalutation =
          ((recipientData.name.firstName || "") + " " +
          (recipientData.name.lastName || "")).trim();
      } else {
        recipientData.salutation.mailingSalutation =
          recipientData?.name?.firstName || "Current Resident";
      }
    }

    if (customFieldsNames?.length > 0) {
      for (const field of customFieldsNames) {
        const customField = accountCustomFields
          ?.filter((item) => item.name === field)
          .shift();
        if (!isEmpty(customField)) {
          if (customField.type === "number") {
            recipientData[field] = Number(theData[field]);
          } else if (customField.type === "date") {
            recipientData[field] = dateStringToTimestamp(
              delocalizeDate(theData[field], accountData.timezone)
            );
          } else {
            recipientData[field] = theData[field];
          }
        }
      }
    }

    // Start a batch write
    const batch = db.batch();

    // Add main recipient document to batch
    batch.set(recipientRef, recipientData);

    // Helper function to add subcollection items to batch
    const addSubcollectionToBatch = (items, subcollectionName) => {
      items.forEach((item) => {
        const subDocRef = recipientRef.collection(subcollectionName).doc();
        batch.set(subDocRef, {
          ...item,
        });
      });
    };

    const addAddressSubcollectionToBatch = (items, subcollectionName) => {
      items.forEach((item) => {
        const subDocRef = recipientRef.collection(subcollectionName).doc("0");
        batch.set(subDocRef, {
          ...item,
        });
      });
    };

    // remove empty emailAddresses
    for (let i = emailAddresses?.length - 1; i >= 0; i--) {
      if (!emailAddresses[i].email || !emailAddresses[i].email.length) {
        emailAddresses.splice(i, 1);
      }
    }

    // Add all EmailAddresses subcollections to batch
    addSubcollectionToBatch(
      emailAddresses.map(({ email, label, primary }) => ({
        email,
        label,
        isPrimary: primary ?? false,
      })),
      "EmailAddresses"
    );

    // remove empty phoneNumbers
    for (let i = phoneNumbers?.length - 1; i >= 0; i--) {
      if (!phoneNumbers[i].phoneNumber || !phoneNumbers[i].phoneNumber.length) {
        phoneNumbers.splice(i, 1);
      }
    }

    // Add all PhoneNumbers subcollections to batch
    addSubcollectionToBatch(
      phoneNumbers.map(({ label, phoneExtension, phoneNumber, primary }) => ({
        label,
        phoneExtension: phoneExtension ?? "",
        phoneNumber,
        isPrimary: primary ?? false,
      })),
      "PhoneNumbers"
    );

    // Add all Notes subcollections to batch
    addSubcollectionToBatch(
      notes.map(({ value }) => ({ value })),
      "Notes"
    );

    // remove empty significant dates
    for (let i = significantDates?.length - 1; i >= 0; i--) {
      if (!significantDates[i].date || !significantDates[i].date.length) {
        significantDates.splice(i, 1);
      }
    }

    // Add all SignificantDates subcollections to batch
    addSubcollectionToBatch(
      significantDates.map(({ label, date }) => {
        return {
          label,
          date: dateStringToTimestamp(
            delocalizeDate(date, accountData.timezone)
          ),
        };
      }),
      "SignificantDates"
    );

    // remove empty socialMediaAccounts
    for (let i = socialMediaAccounts?.length - 1; i >= 0; i--) {
      if (
        !socialMediaAccounts[i].value ||
        !socialMediaAccounts[i].value.length
      ) {
        socialMediaAccounts.splice(i, 1);
      }
    }

    // Add all SocialMediaAccounts subcollections to batch
    addSubcollectionToBatch(
      socialMediaAccounts.map(({ label, value }) => ({ label, value })),
      "SocialMediaAccounts"
    );

    // accept only one (complete-ish) user entered mailing address
    if (!isEmpty(userEnteredMailingAddresses) && userEnteredMailingAddresses.length > 0) {
      let useAddr = userEnteredMailingAddresses.find(addr => addr?.label?.toLowerCase() === 'mailing');
      if (!useAddr) useAddr = userEnteredMailingAddresses[0]; // if none labeled mailing, use first
      while(userEnteredMailingAddresses.length > 0) userEnteredMailingAddresses.pop(); // clear out any other addresses
      if ((useAddr?.address1 && useAddr.address1.trim() !== '') ||
          (useAddr?.city && useAddr.city.trim() !== '' && 
           useAddr?.state && useAddr.state.trim() !== '' && 
           useAddr?.postalCode && useAddr.postalCode.trim() !== '')) {
        userEnteredMailingAddresses.push(useAddr); // restore the one we are using if address line is present
      }
    }

    // Add all userEnteredMailingAddresses subcollections to batch
    addAddressSubcollectionToBatch(
      userEnteredMailingAddresses.map(
        ({ address1, address2, city, state, postalCode }) => ({
          address1,
          address2: address2 ?? "",
          city,
          state,
          postalCode,
        })
      ),
      "UserEnteredMailingAddress"
    );

    // remove empty websites
    for (let i = websites?.length - 1; i >= 0; i--) {
      if (!websites[i].url || !websites[i].url.length) {
        websites.splice(i, 1);
      }
    }

    // Add all Websites subcollections to batch
    addSubcollectionToBatch(
      websites.map(({ label, url }) => ({ label, url })),
      "Websites"
    );

    // remove empty externalIds
    for (let i = externalIds?.length - 1; i >= 0; i--) {
      if (!externalIds[i].externalId || !externalIds[i].externalId.length) {
        externalIds.splice(i, 1);
      }
    }

    // Add all ExternalIds subcollections to batch
    addSubcollectionToBatch(
      externalIds.map(({ label, externalId }) => ({
        label,
        externalId,
        updatedAt: Timestamp.now(),
      })),
      "ExternalIds"
    );

    // Commit the batch
    await batch.commit();
    saveApiLog(201, req);

    return {
      status: 201,
      message: "Recipient created successfully.",
      data: {
        id: recipientRef.id,
        ...recipientData,
        ...(emailAddresses?.length && { emailAddresses }),
        ...(phoneNumbers?.length && { phoneNumbers }),
        ...(notes?.length && { notes }),
        ...(significantDates?.length && { significantDates }),
        ...(socialMediaAccounts?.length && {  socialMediaAccounts }),
        ...(userEnteredMailingAddresses?.length && { userEnteredMailingAddresses }),
        ...(websites?.length && { websites }),
        ...(externalIds?.length && { externalIds }),
        ...(tags && { tags }),
        source,
        createdAt: convertFirestoreTimestampToDateTime(recipientData.createdAt),
        updatedAt: convertFirestoreTimestampToDateTime(recipientData.updatedAt),
      },
    };
  } catch (error) {
    logger.error("Error creating recipient.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};
