const { logger } = require("firebase-functions");
const { FieldPath } = require("firebase-admin/firestore");
const { collectionNames, subCollectionNames } = require("../../../constants/collectionNames.js");
const { saveApiLog } = require("../../../utils/api-logger.js");
const { 
  recipientDataWithSubcollections, 
  buildRecipientResponseObject } = require("./components/recipientData");

exports.v1_recipients_get = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;

    if (!accountId) {
      throw new Error("No accountId found in request.");
    }

    // Get account
    const accountRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString());
    const accountDoc = await accountRef.get();
    const accountData = accountDoc.data();

    // collect account's custom fields
    const accountCustomFieldsSnapshot = await accountRef
      .collection(subCollectionNames.contacts.customFields)
      .where("isActive", "==", true)
      .get();
    // build custom fields object array
    let accountCustomFields = [];
    if (!accountCustomFieldsSnapshot.empty) {
      accountCustomFields = accountCustomFieldsSnapshot.docs.map(doc => ({
        name: doc.data().name,
        type: doc.data().type,
      }));
    }

    // Reference to recipients sub-collection with where clause
    let recipientsRef = accountRef
      .collection(subCollectionNames.contacts.recipients)
      .where("isActive", "==", true);

    const {
      page = 1,
      pageSize = 20,  // default to 20 records per page, max of 100
      sort_by = null,
      sort_order = null,
      updatedAfter = null,
      createdAfter = null,
      recipientGroupId = null,
    } = req.query;

    // Validate and parse pagination parameters, max of 100
    const pageNumber = Math.max(1, parseInt(page, 10));
    const pageSizeNumber = Math.min(100, Math.max(1, parseInt(pageSize, 10)));

    // Apply updatedAt filter if provided
    if (updatedAfter) {
      const updatedAfterDate = new Date(updatedAfter);
      recipientsRef = recipientsRef.where("updatedAt", ">", updatedAfterDate);
    }

    // Apply createdAt filter if provided
    if (createdAfter) {
      const createdAfterDate = new Date(createdAfter);
      recipientsRef = recipientsRef.where("createdAt", ">", createdAfterDate);
    }

    // Apply recipientGroupId filter if provided
    if (recipientGroupId) {
      recipientsRef = recipientsRef.where("recipientGroupIds", "array-contains", recipientGroupId);
    }

    // Apply filtering conditions
    // if (first_name) {
    //   recipientsRef = recipientsRef
    //     .where("first_name", ">=", first_name)
    //     .where("first_name", "<=", first_name + "\uf8ff");
    // }
    // if (last_name) {
    //   recipientsRef = recipientsRef
    //     .where("last_name", ">=", last_name)
    //     .where("last_name", "<=", last_name + "\uf8ff");
    // }

    // Apply sorting
    const validSortFields = ["createdAt", "updatedAt"];
    const validSortOrders = ["asc", "desc"];
    if (
      validSortFields.includes(sort_by) &&
      validSortOrders.includes(sort_order)
    ) {
      recipientsRef = recipientsRef.orderBy(sort_by, sort_order);
    } else {
      recipientsRef = recipientsRef.orderBy(FieldPath.documentId());
    }

    // This method of offset and limit can cause extra billing as Firestore will 
    // read through all documents to find the offset. Although this is a standard billing
    // pattern, it is recommended to use the startAt cursor method as it doesn't cause that
    // elevated billing. unfortunately since each call to this External API is a stand alone
    // query, we can't use the startAt cursor method. 
    let paginatedRef = recipientsRef;
    if (pageNumber > 1) {
      const documentsToSkip = (pageNumber - 1) * pageSizeNumber;
      paginatedRef = recipientsRef.offset(documentsToSkip);
    }
    paginatedRef = paginatedRef.limit(pageSizeNumber);

    const recipientsSnapshot = await paginatedRef.get();

    // Check if the snapshot has data
    if (recipientsSnapshot.empty) {
      saveApiLog(200, req);

      return {
        status: 200,
        data: [],
        message: "No recipients found for the given account and criteria.",
      };
    }

    // Fetch sub-collections for each recipient
    const recipientsData = await Promise.all(
      recipientsSnapshot.docs.map(async (recipientDoc) => {
        return await recipientDataWithSubcollections(recipientDoc);
      })
    );

    // Build the response object
    const recipientResponseObject = recipientsData.map(
      recipient => buildRecipientResponseObject(recipient, accountData.timezone, accountCustomFields)
    );

    // Fetch total documents to calculate total pages
    const totalSnapshot = await recipientsRef.count().get();
    const totalRecords = totalSnapshot.data().count;
    const totalPages = Math.ceil(totalRecords / pageSizeNumber);

    saveApiLog(200, req);

    return {
      status: 200,
      pagination: {
        currentPage: pageNumber,
        totalPages,
        totalRecords,
        pageSize: pageSizeNumber,
      },
      data: recipientResponseObject,
    };
  } catch (error) {
    logger.error("Error fetching recipients.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};
