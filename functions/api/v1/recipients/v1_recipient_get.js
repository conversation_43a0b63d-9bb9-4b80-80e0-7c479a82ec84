const { logger } = require("firebase-functions");
const { collectionNames, subCollectionNames } = require("../../../constants/collectionNames.js");
const { saveApiLog } = require("../../../utils/api-logger.js");
const { 
  recipientDataWithSubcollections, 
  buildRecipientResponseObject } = require("./components/recipientData");

exports.v1_recipient_get = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;
    const recipientId = req.params.recipientId;

    if (!accountId) {
      throw new Error("No accountId found in request.");
    }

    // Get account
    const accountRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString());
    const accountDoc = await accountRef.get();
    const accountData = accountDoc.data();

    // collect account's custom fields
    const accountCustomFieldsSnapshot = await accountRef
      .collection(subCollectionNames.contacts.customFields)
      .where("isActive", "==", true)
      .get();
    // build custom fields object array
    let accountCustomFields = [];
    if (!accountCustomFieldsSnapshot.empty) {
      accountCustomFields = accountCustomFieldsSnapshot.docs.map(doc => ({
        name: doc.data().name,
        type: doc.data().type,
      }));
    }

    // Reference to recipient
    let recipientRef = accountRef
      .collection(subCollectionNames.contacts.recipients)
      .doc(recipientId);
    const recipientDoc = await recipientRef.get();

    // Check if the snapshot has data
    if (!recipientDoc.exists || recipientDoc.data().isActive !== true) {
      saveApiLog(404, req);

      return {
        status: 404,
        data: {},
        message: "Recipient was not found for the given account.",
      };
    }

    saveApiLog(200, req);

    // Fetch sub-collections for recipient
    const recipientData = await recipientDataWithSubcollections(recipientDoc);

    // Build the response object
    const recipientResponseObject = buildRecipientResponseObject(recipientData, accountData.timezone, accountCustomFields);

    return {
      status: 200,
      data: recipientResponseObject,
    };
  } catch (error) {
    logger.error("Error fetching recipient.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};
