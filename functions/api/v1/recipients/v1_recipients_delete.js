const {
  collectionNames,
  subCollectionNames,
} = require("../../../constants/collectionNames.js");
const { logger } = require("firebase-functions");
const { saveApiLog } = require("../../../utils/api-logger.js");
const { Timestamp } = require("firebase-admin/firestore");

exports.v1_recipients_delete = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;
    const recipientId = req.params.recipientId;

    if (!accountId) {
      throw new Error("No accountId found in request.");
    }

    if (!recipientId) {
      saveApiLog(400, req);

      return { status: 400, error: "Recipient ID is required." };
    }

    // Get reference to the existing document
    const recipientRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.recipients)
      .doc(recipientId);

    // Check if document exists
    const doc = await recipientRef.get();
    if (!doc.exists) {
      saveApiLog(404, req);

      return { status: 404, error: "Recipient not found." };
    }

    // Prepare soft delete data
    const deleteData = {
      isActive: false,
      updatedAt: Timestamp.now(),
    };

    // Update the document to mark it as inactive
    await recipientRef.update(deleteData);
    saveApiLog(200, req);

    return {
      status: 200,
      message: "Recipient has been successfully deleted.",
    };
  } catch (error) {
    logger.error("Error deleting recipient.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};
