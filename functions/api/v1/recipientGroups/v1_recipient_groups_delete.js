const {
  collectionNames,
  subCollectionNames,
} = require("../../../constants/collectionNames.js");
const { logger } = require("firebase-functions");
const { saveApiLog } = require("../../../utils/api-logger.js");
const { Timestamp } = require("firebase-admin/firestore");

exports.v1_recipient_groups_delete = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;
    const groupId = req.params.groupId;

    if (!accountId) {
      throw new Error("No accountId found in request.");
    }

    if (!groupId) {
      saveApiLog(400, req);

      return { status: 400, error: "Group ID is required." };
    }

    // Get reference to the existing document
    const groupRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.recipientGroups)
      .doc(groupId);

    // Check if document exists
    const doc = await groupRef.get();
    if (!doc.exists) {
      saveApiLog(404, req);

      return { status: 404, error: "Recipient group not found." };
    }

    // Prepare soft delete data
    const deleteData = {
      isActive: false,
      updatedAt: Timestamp.now(),
    };

    // Update the document to mark it as inactive
    await groupRef.update(deleteData);
    saveApiLog(200, req);

    return {
      status: 200,
      message: "Recipient Group has been successfully deleted.",
    };
  } catch (error) {
    logger.error("Error deleting recipient group.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};
