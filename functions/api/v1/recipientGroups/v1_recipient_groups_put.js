const {
  collectionNames,
  subCollectionNames,
} = require("../../../constants/collectionNames.js");
const {
  convertFirestoreTimestampToDateTime,
} = require("../../../utils/convertFirestoreTimestampToDateTime.js");
const { logger } = require("firebase-functions");
const { saveApiLog } = require("../../../utils/api-logger.js");
const { Timestamp } = require("firebase-admin/firestore");

exports.v1_recipient_groups_put = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;
    const groupId = req.params.groupId; // Assuming groupId is passed as URL parameter

    if (!accountId) {
      throw new Error("No accountId found in request.");
    }

    if (!groupId) {
      saveApiLog(400, req);

      return { status: 400, error: "Group ID is required" };
    }

    // Extract only the updatable fields from the request body
    const { name, description } = req.body;

    // Validate required fields
    if (!name) {
      saveApiLog(400, req);

      return { status: 400, error: "Name is required." };
    }

    // Get reference to the existing document
    const groupRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.recipientGroups)
      .doc(groupId);

    // Check if document exists
    const doc = await groupRef.get();
    if (!doc.exists) {
      saveApiLog(404, req);

      return { status: 404, error: "Recipient group not found." };
    }

    // Prepare update data
    const updateData = {
      name,
      description: description || "",
      updatedAt: Timestamp.now(),
    };

    // Update the document
    await groupRef.update(updateData);

    // Get the updated document
    const updatedDoc = await groupRef.get();
    const updatedGroupData = {
      id: updatedDoc.id,
      ...updatedDoc.data(),
    };

    const reducedGroupData = reduceGroupData(updatedGroupData);
    saveApiLog(200, req);

    return {
      status: 200,
      data: reducedGroupData,
    };
  } catch (error) {
    logger.error("Error updating recipient group.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};

const reduceGroupData = (group) => {
  return {
    id: group.id,
    name: group.name,
    description: group.description,
    createdAt: convertFirestoreTimestampToDateTime(group.createdAt),
    updatedAt: convertFirestoreTimestampToDateTime(group.updatedAt),
  };
};
