const { Timestamp } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const { collectionNames, subCollectionNames } = require("../../../constants/collectionNames.js");
const { convertFirestoreTimestampToDateTime } = require("../../../utils/convertFirestoreTimestampToDateTime.js");
const { saveApiLog } = require("../../../utils/api-logger.js");
const { createUUID } = require("../../../utils/createUUID");

exports.v1_recipient_groups_post = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;

    if (!accountId) {
      throw new Error("No accountId found in request.");
    }

    // Extract only the required fields from the request body
    const { name, description } = req.body;

    // Validate required fields
    if (!name) {
      saveApiLog(400, req);

      return { status: 400, error: "Name is required." };
    }

    // Create the recipient group document
    const recipientGroupData = {
      name,
      description: description || "", // Make description optional
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    };

    // Add document to Firestore
    const recipientGroupsCollectionRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.recipientGroups);
    const uuid = createUUID();
    const recipientGroupRef = recipientGroupsCollectionRef.doc(uuid);
    await recipientGroupRef.set(recipientGroupData);

    // Return the created document with its ID
    const newGroupData = {
      id: recipientGroupRef.id,
      ...recipientGroupData,
    };

    const reducedNewGroupData = reduceGroupData(newGroupData);
    saveApiLog(201, req);

    return {
      status: 201,
      data: reducedNewGroupData,
    };
  } catch (error) {
    logger.error("Error creating recipient group.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};

const reduceGroupData = (group) => {
  return {
    id: group.id,
    name: group.name,
    description: group.description,
    createdAt: convertFirestoreTimestampToDateTime(group.createdAt),
    updatedAt: convertFirestoreTimestampToDateTime(group.updatedAt),
  };
};
