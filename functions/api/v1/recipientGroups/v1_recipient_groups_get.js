const {
  collectionNames,
  subCollectionNames,
} = require("../../../constants/collectionNames.js");
const {
  convertFirestoreTimestampToDateTime,
} = require("../../../utils/convertFirestoreTimestampToDateTime");
const { saveApiLog } = require("../../../utils/api-logger.js");
const { logger } = require("firebase-functions");

exports.v1_recipient_groups_get = async (db, req) => {
  try {
    if (!db) {
      throw new Error("Database reference not found.");
    }

    const accountId = req.accountId;

    if (!accountId) {
      throw new Error("No accountId found in request.");
    }

    const {
      page = 1,
      pageSize = 20,
      sort_by = "updatedAt",
      sort_order = "desc",
    } = req.query;

    // Validate and parse pagination parameters
    const pageNumber = Math.max(1, parseInt(page, 10));
    const pageSizeNumber = Math.min(100, Math.max(1, parseInt(pageSize, 10)));

    // Reference to recipients sub-collection
    let groupsRef = db
      .collection(collectionNames.account)
      .doc(accountId.toString())
      .collection(subCollectionNames.contacts.recipientGroups)
      .where("isActive", "==", true);

    // Apply filtering conditions
    // if (first_name) {
    //   recipientsRef = recipientsRef
    //     .where("first_name", ">=", first_name)
    //     .where("first_name", "<=", first_name + "\uf8ff");
    // }

    // Apply sorting
    const validSortFields = ["createdAt", "updatedAt"];
    const validSortOrders = ["asc", "desc"];
    if (
      validSortFields.includes(sort_by) &&
      validSortOrders.includes(sort_order)
    ) {
      groupsRef = groupsRef.orderBy(sort_by, sort_order);
    } else {
      groupsRef = groupsRef.orderBy("updatedAt", "desc");
    }

    // Determine pagination strategy
    let paginatedRef = groupsRef;
    let firstPageSnapshot;

    // If not first page, we need to find the last document of the previous page
    if (pageNumber > 1) {
      // Fetch the documents to skip
      const documentsToSkip = (pageNumber - 1) * pageSizeNumber;

      // Get the last document of the previous page to use as a starting point
      firstPageSnapshot = await groupsRef.limit(documentsToSkip).get();

      // Use the last document of the previous page to start the next page
      const lastDocOfPreviousPage =
        firstPageSnapshot.docs[firstPageSnapshot.docs.length - 1];

      if (lastDocOfPreviousPage) {
        paginatedRef = groupsRef.startAfter(lastDocOfPreviousPage);
      }
    }

    // Apply limit for the current page
    paginatedRef = paginatedRef.limit(pageSizeNumber);

    // Execute query
    const groupsSnapshot = await paginatedRef.get();

    // Check if the snapshot has data
    if (groupsSnapshot.empty) {
      saveApiLog(200, req);

      return {
        status: 200,
        message: "No recipient groups found for the given account.",
      };
    }

    // Map the data into a response
    const groupsData = groupsSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    const reducedGroupsData = reduceGroupsData(groupsData);

    // Fetch total documents to calculate total pages
    const totalSnapshot = await groupsRef.get();
    const totalRecords = totalSnapshot.size;
    const totalPages = Math.ceil(totalRecords / pageSizeNumber);

    saveApiLog(200, req);

    return {
      status: 200,
      pagination: {
        currentPage: pageNumber,
        totalPages,
        totalRecords,
        pageSize: pageSizeNumber,
      },
      data: reducedGroupsData,
    };
  } catch (error) {
    logger.error("Error fetching recipient groups.", error);
    saveApiLog(500, req);

    return {
      status: 500,
      error: "Internal Server Error",
      details: error.message,
    };
  }
};

const reduceGroupsData = (groupsData) => {
  return groupsData.map((group) => {
    const assignments = [];

    if (group.assignments) {
      Object.values(group.assignments).forEach((group) => {
        Object.values(group).forEach((item) => {
          if (item.productType) {
            assignments.push(item.productType);
          }
        });
      });
    }

    return {
      id: group.id,
      name: group.name,
      description: group.description,
      assignments,
      productPlans: group.productPlans,
      createdAt: convertFirestoreTimestampToDateTime(group.createdAt),
      updatedAt: convertFirestoreTimestampToDateTime(group.updatedAt),
    };
  });
};
