const { onRequest } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const {
  collectionNames,
  subCollectionNames,
} = require("../../constants/collectionNames");
const { FieldValue, Timestamp } = require("firebase-admin/firestore");
const { isNil, isEmpty } = require("lodash");

exports.products = (db) =>
  onRequest(async (req, res) => {
    if (req.method === "POST") {
      await handlePostRequest({ req, res, db });
      return;
    }

    if (req.method === "GET") {
      res.status(200).json({
        message: "Nothing here.",
      });
    }
  });

