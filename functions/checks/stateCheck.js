const { onRequest } = require("firebase-functions/v2/https");
const logger = require("firebase-functions/logger");

exports.check = (db) => {
  return onRequest(async (req, res) => {
    const { authenticate = false } = req.query;

    try {
      // is the database reference available?
      if (!db) {
        throw new Error("Database reference not found.");
      }

      // Can we query the database?
      const apiLogsRef = db.collection("ApiLogs");
      const logSnapshot = await apiLogsRef.limit(1).get();
      if (logSnapshot.empty) {
        throw new Error("Database read error.");
      }

      // Is user authenticated?
      if (authenticate === "true" && (!req.auth || !req.auth.uid)) {
        // logger.info("Unauthenticated health check endpoint hit");
        res.status(401).json({
          status: "unauthorized",
          message: "Unauthenticated health check endpoint hit",
        });
        return;
      }

      // Health check successful
      // logger.info("Health check endpoint hit");
      res.status(200).json({
        status: "ok",
        message: "Service is operational.",
        timestamp: Date.now(),
      });
    } catch (error) {
      logger.error("Error in health check endpoint.", error);
      res.status(500).json({ status: "error", message: error });
    }
  });
};
