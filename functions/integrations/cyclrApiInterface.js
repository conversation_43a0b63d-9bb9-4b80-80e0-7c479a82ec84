const { onCall } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const { defineString } = require("firebase-functions/params");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const axios = require("axios");
const { initCyclrToken } = require("./components/cyclr");
const { isEmpty, isArray } = require("lodash");
const https = require("https"); // temp: for getting the outbound IP
const { astridConnection } = require("../astrid/astridConnection");
// const titanProjectId = defineString("TITAN_PROJECT_ID");

// Retrieve the token and base URL from Secrets Manager
const titanPublicDomain = defineString("CYCLR_TITAN_PUBLIC_DOMAIN");
const cyclrMarketplaceId = defineString("CYCLR_MARKETPLACE_ID");

// Retrieve the base URL from Secrets Manager
const cyclrBaseUrl = defineString("CYCLR_API_URL");

class CyclrApiInterface {
  constructor(db, accountId) {
    this.db = db;
    this.accountId = accountId;
    this.accountRef = null;
    this.accountData = null;
    this.cyclrAccountId = null;
    this.cyclrToken = null;
  }

  getAccountRef(force = false) {
    if (this.accountRef && !force) {
      return this.accountRef;
    }

    this.accountRef = this.db
      .collection(collectionNames.account)
      .doc(this.accountId);

    return this.accountRef;
  }

  async getAccountData(force = false) {
    if (this.accountData && !force) {
      return this.accountData;
    }

    try {
      const accountRef = this.getAccountRef();
      const accountDoc = await accountRef.get();
      this.accountData = accountDoc.data();

      return this.accountData;
    } catch (error) {
      throw new Error("Error retreiving RM Account data. " + error.message);
    }
  }

  async getCyclrAccountId(force = false) {
    if (this.cyclrAccountId && !force) {
      return this.cyclrAccountId;
    }

    try {
      const accountData = await this.getAccountData();

      this.cyclrAccountId = accountData?.cyclrAccountId ?? null;

      return this.cyclrAccountId;
    } catch (error) {
      throw new Error(
        "Error retreiving Cyclr Account ID from RM Account data. " +
          error.message
      );
    }
  }

  async getCyclrToken(force = false) {
    if (this.cyclrToken && !force) {
      return this.cyclrToken;
    }

    this.cyclrToken = await initCyclrToken();

    return this.cyclrToken;
  }
}

exports.cyclrApiInterface = (db) =>
  onCall(async (request) => {
    const {
      accountId,
      action,
      cycleId,
      method = "",
      path = "",
      data = {},
    } = request.data;

    try {
      let state = new CyclrApiInterface(db, accountId);
      const cyclrToken = await state.getCyclrToken(true);

      if (cyclrToken && action) {
        switch (action) {
          case "setupAccount":
            return await setupCyclrAccount(state);
          case "validateRmApiKeyAtCyclr":
            return await validateRmApiKeyAtCyclr(state);
          case "getMarketplaceLink":
            return await getMarketplaceLink(state);
          case "getCycles":
            return await getCycles(state);
          case "runCycle":
            return await runCycle(state, cycleId);
          case "fubRulesOptions":
            return await fubRulesOptions(state);
          case "pushRecipientUpdate":
            return await pushRecipientUpdate(state, data);
          default:
            return await cyclrApiFetch(state, method, path, data);
        }
      } else {
        throw new Error("Cyclr token was null or error returned.");
      }
    } catch (error) {
      logger.error("cyclrApiInterface() Error.", error);
      return { status: 400, message: error.message };
    }
  });

/**
 * @param {object} state - CyclrApiInterface state data
 * @returns {object} Status
 */
async function setupCyclrAccount(state) {
  const accountRef = state.getAccountRef();
  const accountData = await state.getAccountData();
  const name = accountData?.name;

  try {
    const response = await createCyclrAccount(name);
    const data = await response.json();

    if (response.status === 200 && data.Id) {
      await accountRef.update({
        cyclrAccountId: data.Id,
      });
    } else {
      throw new Error(
        `Cyclr returned unexpected status while setting up account: ${response.status}`
      );
    }

    return {
      status: response.status,
      message: "Cyclr account setup successfully.",
    };
  } catch (error) {
    const msg = "Cyclr account setup error.";
    logger.error(msg, error);
    return { status: 400, message: `${msg} ${error.message}` };
  }
}

/**
 * @param {object} state - CyclrApiInterface state data
 * @returns {object} Status
 */
async function validateRmApiKeyAtCyclr(state) {
  const apiKey = await getRMAccountApiKey(state);

  try {
    // get the connectors
    const connectorsResponse = await cyclrApiFetch(
      state,
      "GET",
      `/v1.0/account/connectors`
    );
    const responseData = await connectorsResponse.json();
    const rmConnector = responseData.find(
      (connector) => connector.Name === "ReminderMedia"
    );

    if (!rmConnector) {
      throw new Error(
        "Unable to find ReminderMedia connector in Cyclr account."
      );
    }

    // if the API key is different, update it to current
    if (rmConnector.AuthValue !== apiKey) {
      const response = await cyclrApiFetch(
        state,
        "PUT",
        `/v1.0/account/connectors/${rmConnector.Id}`,
        {
          Name: "ReminderMedia",
          authValue: apiKey,
        }
      );

      if (response.status !== 200) {
        throw new Error(
          `Cyclr returned unexpected status while setting up account: ${response.status}`
        );
      }

      return {
        status: response.status,
        message: "Cyclr account updated successfully.",
      };
    }

    return { status: 200, message: "Cyclr account validated successfully." };
  } catch (error) {
    const msg = "Cyclr account validation error.";
    logger.error(msg, error);
    return { status: 400, message: `${msg} ${error.message}` };
  }
}

/**
 * @param {object} state - CyclrApiInterface state data
 * @returns {object} Cyclr response data or error status
 */
async function getMarketplaceLink(state) {
  try {
    const accountData = await state.getAccountData();
    const storedCyclrAccountId = await state.getCyclrAccountId();
    const cyclrAccountId =
      storedCyclrAccountId ??
      createCyclrAccountId(accountData?.name, state.accountId);
    const cyclrToken = await state.getCyclrToken();
    const apiKey = await getRMAccountApiKey(state);

    if (!apiKey || !accountData || !cyclrAccountId || !cyclrToken) {
      throw new Error(
        "Unable to collect prerequisites in getMarketplaceLink()."
      );
    }

    const response = await cyclrApiFetch(
      state,
      "POST",
      `/v1.0/accounts/${cyclrAccountId}/marketplace`,
      {
        MarketplaceId: cyclrMarketplaceId.value(),
        AccountName: accountData?.name + " (" + state.accountId + ")",
        ConnectorAuthentications: [
          {
            name: "ReminderMedia",
            authValue: apiKey,
            Properties: [
              {
                Name: "ServerDomain",
                Value: titanPublicDomain.value(),
              },
            ],
          },
        ],
      }
    );

    if (response.status === 200) {
      if (!storedCyclrAccountId) {
        const accountRef = state.getAccountRef();
        await accountRef.update({
          cyclrAccountId,
        });
        state.accountData.cyclrAccountId = cyclrAccountId;
      }

      console.log("+++ getMarketplaceLink() - validateRmApiKeyAtCyclr");

      await validateRmApiKeyAtCyclr(state);

      return await response.json(); // w/Axios:  return response.data;
    } else {
      throw new Error(
        `Cyclr returned unexpected status while fetching marketplace link: ${response.status}`
      );
    }
  } catch (error) {
    const msg = "Error retreiving marketplace link. ";
    logger.error(msg, error);
    return { status: 400, message: `${msg} ${error.message}` };
  }
}

/**
 * @param {object} state - CyclrApiInterface state data
 * @returns {object} Cyclr data or error status
 */
async function getCycles(state) {
  try {
    const response = await cyclrApiFetch(state, "GET", `/v1.0/cycles`);

    if (response && response?.status === 200) {
      return await response.json(); // w/Axios:  return response.data;
    } else {
      throw new Error(
        `Cyclr returned unexpected status while fetching cycles: ${response.status}`
      );
    }
  } catch (error) {
    const msg = "Error retreiving account cycles.";
    logger.error(msg, error);
    return { status: 400, message: `${msg} ${error.message}` };
  }
}

/**
 * @param {object} state - CyclrApiInterface state data
 * @param {string} cycleId - cycleId to run
 * @returns {object} Cyclr response or error status
 */
async function runCycle(state, cycleId) {
  try {
    const date = new Date();
    date.setSeconds(date.getSeconds() + 15);

    const response = await cyclrApiFetch(
      state,
      "PUT",
      `/v1.0/cycles/${cycleId}/activate`,
      {
        StartTime: date.toISOString(),
        Interval: 60 * 24, // one day
        RunOnce: false,
      }
    );

    if (response.status === 200) {
      return await response.json(); // Axios:  return response.data;
    } else {
      throw new Error(
        `Cyclr returned unexpected status while running cycle: ${response.status}`
      );
    }
  } catch (error) {
    const msg = "Error running cycle.";
    logger.error(msg, error);
    return { status: 400, message: `${msg} ${error.message}` };
  }
}

/**
 * Collects all available FUB \options to
 * populate the FUB settings rules form
 *
 * @param {object} state - CyclrApiInterface state data
 * @returns {object} Data or error status
 */
async function fubRulesOptions(state) {
  try {
    const accountData = await state.getAccountData();
    const storedCyclrAccountId = await state.getCyclrAccountId();
    const cyclrAccountId =
      storedCyclrAccountId ??
      createCyclrAccountId(accountData?.name, state.accountId);
    const cyclrToken = await state.getCyclrToken();

    if (isEmpty(cyclrToken)) {
      throw new Error("Unable to collect Cyclr token in cyclrApiFetch().");
    }

    const returnData = await astridConnection({
      url: "cyclr/fub",
      method: "POST",
      payload: {
        baseUrl: cyclrBaseUrl.value(),
        cyclrToken: cyclrToken,
        cyclrAccountId: cyclrAccountId,
      },
    });

    console.log(
      "Promises finished: object count ",
      Object.keys(returnData?.data).length
    );
    console.log("Promises finished: returnData ", returnData?.data);

    return returnData?.data ?? {};
  } catch (error) {
    const msg = "Error requesting FUB Form Data.";
    logger.error(msg, error);
    return { status: 400, message: `${msg} ${error.message}` };
  }
}

/**
 * @param {object} state - CyclrApiInterface state data
 * @param {object} data - { recipientId, externalIdsId }
 * @returns {object} status
 */
async function pushRecipientUpdate(state, data) {
  const { recipientId, externalIdsId } = data;
  const accountRef = state.getAccountRef();

  try {
    const recipientRef = accountRef
      .collection(subCollectionNames.contacts.recipients)
      .doc(recipientId);

    const externalIdRef = recipientRef
      .collection(subCollectionNames.contacts.externalIds)
      .doc(externalIdsId);
    const externalIdDoc = await externalIdRef.get();
    const externalId = externalIdDoc.data();

    const integrationRef = accountRef
      .collection(subCollectionNames.account.integrations)
      .where("connector", "==", externalId.label)
      .where("methodType", "==", "Push Contact");
    const integrationSnapshot = await integrationRef.get();
    const integration = integrationSnapshot.docs[0]?.data();

    for (const webhook of integration.webhooks) {
      const success = cyclrWebhookFetch(state, webhook.Url, {
        id: recipientId,
      });
      if (!success) {
        throw new Error("Error posting recipient update to Cyclr.");
      }
    }

    return {
      status: 200,
      message: "Successfully posted recipient update to Cyclr.",
    };
  } catch (error) {
    const msg = "Error in pushRecipientUpdate().";
    logger.error(msg, error);
    return { status: 400, message: `${msg} ${error.message}` };
  }
}

// ******************
// Utilities
// ******************

/**
 * @param {string} name
 * @param {string} accountId
 * @returns {string} Generated Cyclr Account ID
 */
function createCyclrAccountId(name, accountId) {
  return name.trim().toLowerCase().replace(/ /g, "-") + "-" + accountId;
}

/**
 * @param {object} state - CyclrApiInterface state data
 * @returns {string} API Key
 */
async function getRMAccountApiKey(state) {
  const db = state.db;
  const accountId = state.accountId;

  try {
    const apiKeycollectionRef = db
      .collection(collectionNames.apiKeys)
      .where("accountId", "==", accountId);
    const apiKeySnapShot = await apiKeycollectionRef.get();

    if (apiKeySnapShot.size === 0) {
      throw new Error();
    }

    const apiKeyData = apiKeySnapShot.docs[0];

    return apiKeyData?.id;
  } catch (error) {
    throw new Error("Error retreiving ReminderMedia API key. " + error.message);
  }
}

// ******************
// Requesters
// ******************

/**
 * Make Cyclr Account based API calls using Axios
 *
 * @param {object} state - CyclrApiInterface state data
 * @param {string} method - HTTP method
 * @param {string} path - Cyclr API path
 * @param {object} data - Request body
 * @returns {Promise<object>} Axios response
 */
async function cyclrAccountApiCall(state, method, path, data) {
  try {
    const cyclrAccountId = await state.getCyclrAccountId();
    const cyclrToken = await state.getCyclrToken();

    if (isEmpty(cyclrAccountId) || isEmpty(cyclrToken)) {
      throw new Error(
        "Unable to collect prerequisites in cyclrAccountApiCall()."
      );
    }

    const request = {
      method,
      url: `${cyclrBaseUrl.value()}${path}`,
      headers: {
        Authorization: `Bearer ${cyclrToken}`,
        "X-Cyclr-Account": cyclrAccountId,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      timeout: 7000, // 7s timeout
    };

    if (data) {
      request.data = data;
    }

    return await axios(request);
  } catch (error) {
    logger.error("Error calling Cyclr's API.", error);
    return { status: 400, message: error.message };
  }
}

/**
 * Make Cyclr API calls using fetch
 *
 * @param {object} state - CyclrApiInterface state data
 * @param {string} method - HTTP method
 * @param {string} path - Cyclr API path
 * @param {object} data - Request body
 * @returns {Promise<object>} fetch response
 */
async function cyclrApiFetch(state, method, path, data) {
  try {
    const cyclrAccountId = await state.getCyclrAccountId();
    const cyclrToken = await state.getCyclrToken();

    if (isEmpty(cyclrToken)) {
      throw new Error("Unable to collect Cyclr token in cyclrApiFetch().");
    }

    const request = {
      method,
      headers: {
        Authorization: `Bearer ${cyclrToken}`,
        ...(cyclrAccountId && { "X-Cyclr-Account": cyclrAccountId }),
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      signal: AbortSignal.timeout(7000), // 7s timeout
    };

    if (data) {
      request.body = JSON.stringify(data);
    }

    return await fetch(`${cyclrBaseUrl.value()}${path}`, request);
  } catch (error) {
    logger.error(`Error fetching from Cyclr's API. Path: ${path}`, error);
    return { status: 400, message: error.message, json: () => ({}) };
  }
}

/**
 * POST to Cyclr webhook
 *
 * @param {object} state - CyclrApiInterface state data
 * @param {string} url - Cyclr's API URL
 * @param {object} data - Lead data
 * @returns {boolean} Returns true if the lead was posted successfully
 */
async function cyclrWebhookFetch(state, url, data) {
  const cyclrToken = await state.getCyclrToken();

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${cyclrToken}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify(data),
    });

    return response.status === 200;
  } catch (error) {
    throw new Error(`Error calling Cyclr Webhoook. ${error.message}`);
  }
}

/**
 * @param {object} state - CyclrApiInterface state data
 * @param {string} name - RM Account display name
 * @returns {Promise<object>} Axios response
 */
async function createCyclrAccount(state, name) {
  const cyclrToken = await state.getCyclrToken();
  const accountId = state.accountId;

  try {
    return fetch(`${cyclrBaseUrl.value()}/v1.0/accounts`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${cyclrToken}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify({
        name: `${name} (${accountId})`,
        Timezone: "America/New_York",
      }),
      signal: AbortSignal.timeout(7000), // 7s timeout
    });
  } catch (error) {
    throw new Error(`Error creating Cyclr account. ${error.message}`);
  }
}
