const axios = require("axios");
const { logger } = require("firebase-functions");
const { SecretManagerServiceClient } = require('@google-cloud/secret-manager');
const { defineString } = require("firebase-functions/params");

// Initialize Secret Manager client
const client = new SecretManagerServiceClient();

// Retrieve the token and base URL from Secrets Manager
const cyclrClientId = defineString("CYCLR_CLIENT_ID");
const cyclrClientSecret = defineString("CYCLR_CLIENT_SECRET");
const cyclrBaseUrl = defineString("CYCLR_API_URL");

const projectMaster = defineString("SECRETS_MASTER")
const secretParent = `projects/${projectMaster.value()}/secrets/CYCLR_PARTNER_TOKEN`

/**
 * @returns {string|null}
 */
exports.initCyclrToken = async function () {
  let cyclrToken = null;

  cyclrToken = await retrieveTokenFromSecrets();

  if (!cyclrToken || cyclrToken === "") {
    logger.error("Cyclr token was not found in secrets. Retrieving new token.");
    cyclrToken = await getNewTokenFromCyclr();
  }

  const validationResult = await validateCyclrToken(cyclrToken);

  if (validationResult === 401 || validationResult === 403) {
    logger.error("Cyclr token is invalid. Retrieving new token.");
    cyclrToken = await getNewTokenFromCyclr();
  }

  return cyclrToken;
}

/**
 * @returns {string|null}
 */
async function retrieveTokenFromSecrets() {
  let cyclrToken = null;

  try {
    const [version] = await client.accessSecretVersion({
      name: secretParent + '/versions/latest',
    });
    cyclrToken = version?.payload?.data.toString();
  } catch (error) {
    logger.error("Error getting Cyclr secret from Google.", error);
  }

  return cyclrToken;
}

/**
 * @returns {string|null}
 */
async function getNewTokenFromCyclr() {
  let cyclrToken = null;

  try {
    const response = await axios({
      method: 'GET',
      url: `${cyclrBaseUrl.value()}/oauth/token`,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json",
      },
      data: {
        grant_type: "client_credentials",
        client_id: cyclrClientId.value(),
        client_secret: cyclrClientSecret.value(),
      },
    });

    if (response.status === 200 && response.data.access_token) {
      cyclrToken = response.data.access_token;

      if (cyclrToken) {
        const version = await updateTokenInSecrets(cyclrToken);
        logger.log("Added new Cyclr token secret version.", version.name);
        const versionNum = version.name.split('/').slice(-1)[0];
        if (!isNaN(versionNum) && versionNum > 0) {
          const destroyedVersion = await destroySecretVersion(versionNum - 1);
          logger.log("Previous Cyclr token secret version was recycled.", destroyedVersion.name);
        }
      }
    } else {
      throw new Error("Cyclr returned unexpected status. " + response.statusText);
    }
  } catch (error) {
    logger.error("Error getting new token from Cyclr.", error);
  }

  return cyclrToken;
}

/**
 * @param {string} cyclrToken - Cyclr's 14 day token
 * @returns {boolean} Returns true if token is updated
 */
async function updateTokenInSecrets(cyclrToken) {
  try {
    const [version] = await client.addSecretVersion({
      parent: secretParent,
      payload: {
        data: Buffer.from(cyclrToken, 'utf8'),
      },
    });

    return version;
  } catch (error) {
    logger.error("Error updating Cyclr secret at Google.", error);
  }

  return false;
}

async function destroySecretVersion(destroyVersion) {
  const [version] = await client.destroySecretVersion({
    name: secretParent + `/versions/${destroyVersion}`,
  });

  return version;
}

// /**
//  * @param {string} cyclrToken - Cyclr's 14 day token
//  * @returns {boolean} Returns true if token is valid
//  */
// async function validateCyclrToken(cyclrToken) {
//   try {
//     const response = await axios({
//       method: "GET",
//       url: `${cyclrBaseUrl.value()}/v1.0/templates`,
//       headers: {
//         Authorization: `Bearer ${cyclrToken}`,
//         "Content-Type": "application/json",
//         Accept: "application/json",
//       },
//       timeout: 7000, // 7s timeout
//     });

//     return (response.status === 200);
//   } catch (error) {
//     logger.error("Error validating Cyclr token.", error);
//   }

//   return false;
// }

/**
 * @param {string} cyclrToken - Cyclr's 14 day token
 * @returns {number} Returns status code
 */
async function validateCyclrToken(cyclrToken) {
  try {
    const response = await fetch(
      `${cyclrBaseUrl.value()}/v1.0/templates`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${cyclrToken}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      signal: AbortSignal.timeout(7000), // 7s timeout
    });

    return response.status;
  } catch (error) {
    logger.error("Error validating Cyclr token.", error);
    return 400;
  }
}
