const { onCall } = require("firebase-functions/v2/https");
const { logger } = require("firebase-functions");
const { FieldPath } = require("firebase-admin/firestore");
const {
  collectionNames,
  subCollectionNames,
} = require("../constants/collectionNames");
const { recursiveDocDelete } = require("../utils/recursiveDocDelete.js");

class State {
  constructor(db, accountId) {
    this.db = db;
    this.accountId = accountId;
    this.accountRef = null;
    this.accountData = null;
  }

  getAccountRef(force = false) {
    if (this.accountRef && !force) {
      return this.accountRef;
    }

    this.accountRef = this.db
      .collection(collectionNames.account)
      .doc(this.accountId);

    return this.accountRef;
  }

  async getAccountData(force = false) {
    if (this.accountData && !force) {
      return this.accountData;
    }

    try {
      const accountRef = this.getAccountRef();
      const accountDoc = await accountRef.get();
      this.accountData = accountDoc.data();

      return this.accountData;
    } catch (error) {
      throw new Error("Error retreiving RM Account data. " + error.message);
    }
  }
}

exports.integrationsUtils = (db) =>
  onCall(async (request) => {
    const { accountId, action, integration } = request.data;

    try {
      let state = new State(db, accountId);

      switch (action) {
        case "cleanUpExternalIds":
          return await cleanUpExternalIds(state, integration);
        case "softDeleteRecipientsPerIntegration":
          return await softDeleteRecipientsPerIntegration(state, integration);
        case "deleteRecipientsPerIntegration":
          return await deleteRecipientsPerIntegration(state, integration);

        default:
          throw new Error("Action not supported.");
      }
    } catch (error) {
      logger.error(error);
      return { status: 400, message: error.message };
    }
  });

/**
 * @param {object} state - function state data
 * @param {string} integration - The name of the integration
 * @returns {object} Status
 */
async function cleanUpExternalIds(state, integration) {
  const db = state.db;
  const queryLimit = 100;
  const batchLimit = 400;

  try {
    const accountRef = state.getAccountRef();
    const accountData = await state.getAccountData();

    // use cursor-based pagination to loop through all
    // recipients and delete external ids
    let nextDoc = null;
    let batch = null;
    let batchCounter = 0;
    let deletedCount = 0;

    do {
      let recipientsRef = accountRef
        .collection(subCollectionNames.contacts.recipients)
        .orderBy(FieldPath.documentId(), 'asc')
        .limit(queryLimit);

      if (nextDoc) {
        recipientsRef = recipientsRef.startAfter(nextDoc.id)
      }
      const recipientsSnapshot = await recipientsRef.get();
      nextDoc = recipientsSnapshot.docs[recipientsSnapshot.docs.length - 1];

      for (const recipientDoc of recipientsSnapshot.docs) {
        if (batch === null) {
          batch = db.batch();
        }

        const externalIdsSnapshot = await recipientDoc.ref
          .collection(subCollectionNames.contacts.externalIds)
          .get();

        for (const externalIdDoc of externalIdsSnapshot.docs) {
          const externalIdData = externalIdDoc.data();
          if (externalIdData.label === integration) {
            batch.delete(externalIdDoc.ref);
            batchCounter++;
          }
        };

        if (batchCounter >= batchLimit) {
          await batch.commit();
          batch = null;
          deletedCount += batchCounter;
          batchCounter = 0;
        }
      };
    } while (nextDoc);

    if (batchCounter > 0) {
      deletedCount += batchCounter;
      await batch.commit();
    }

    console.log(`${accountData.name} (${state.accountId}) - Cleaned up ${deletedCount} ${integration} external IDs.`);

    return {
      status: 200,
      message: `Successfully cleaned up ${deletedCount} ${integration} External ID associations`,
    };
  } catch (error) {
    const msg = "Error cleaning up External ID associations";
    logger.error(msg, error);
    return { status: 400, message: `${msg}` };
  }
}

/**
 * @param {object} state - function state data
 * @param {string} integration - The name of the integration
 * @returns {object} Status
 */
async function softDeleteRecipientsPerIntegration(state, integration) {
  const db = state.db;
  const queryLimit = 100;
  const batchLimit = 500;

  try {
    const accountRef = state.getAccountRef();
    const accountData = await state.getAccountData();

    // use cursor-based pagination to loop through all
    // recipients and delete external ids
    let nextDoc = null;
    let batch = null;
    let batchCounter = 0;
    let deletedCount = 0;

    do {
      let recipientsRef = accountRef
        .collection(subCollectionNames.contacts.recipients)
        .orderBy(FieldPath.documentId(), 'asc')
        .where("isActive", "==", true)
        .limit(queryLimit);

      if (nextDoc) {
        recipientsRef = recipientsRef.startAfter(nextDoc.id)
      }
      const recipientsSnapshot = await recipientsRef.get();
      nextDoc = recipientsSnapshot.docs[recipientsSnapshot.docs.length - 1];

      for (const recipientDoc of recipientsSnapshot.docs) {
        if (batch === null) {
          batch = db.batch();
        }

        const externalIdsSnapshot = await recipientDoc.ref
          .collection(subCollectionNames.contacts.externalIds)
          .get();

        for (const externalIdDoc of externalIdsSnapshot.docs) {
          const externalIdData = externalIdDoc.data();
          if (externalIdData.label === integration) {
            batch.update(recipientDoc.ref, {
              isActive: false,
            });
            batchCounter++;
          }
        };

        if (batchCounter >= batchLimit) {
          await batch.commit();
          batch = null;
          deletedCount += batchCounter;
          batchCounter = 0;
        }
      };
    } while (nextDoc);

    if (batchCounter > 0) {
      deletedCount += batchCounter;
      await batch.commit();
    }

    console.log(`${accountData.name} (${state.accountId}) - Deleted ${deletedCount} ${integration} recipients.`);

    return {
      status: 200,
      message: `Successfully deleted ${deletedCount} ${integration} recipients`,
    };
  } catch (error) {
    const msg = "Error deleting recipients";
    logger.error(msg, error);
    return { status: 400, message: `${msg}` };
  }
}

/**
 * FYI, works, but later an email validation or address
 * validation will create an incomplete recipient later.
 * 
 * @param {object} state - function state data
 * @param {string} integration - The name of the integration
 * @returns {object} Status
 */
async function deleteRecipientsPerIntegration(state, integration) {
  const db = state.db;
  const queryLimit = 100;
  const batchLimit = 5;

  try {
    const accountRef = state.getAccountRef();
    const accountData = await state.getAccountData();

    // use cursor-based pagination to loop through all
    // recipients and delete external ids
    let nextDoc = null;
    let batch = null;
    let batchCounter = 0;
    let deletedCount = 0;
    let recipientIds = [];

    do {
      let recipientsRef = accountRef
        .collection(subCollectionNames.contacts.recipients)
        .orderBy(FieldPath.documentId(), 'asc')
        .limit(queryLimit);

      if (nextDoc) {
        recipientsRef = recipientsRef.startAfter(nextDoc.id)
      }
      const recipientsSnapshot = await recipientsRef.get();
      nextDoc = recipientsSnapshot.docs[recipientsSnapshot.docs.length - 1];

      for (const recipientDoc of recipientsSnapshot.docs) {
        recipientIds.push(recipientDoc.id);
        if (batch === null) {
          batch = db.batch();
        }

        const externalIdsSnapshot = await recipientDoc.ref
          .collection(subCollectionNames.contacts.externalIds)
          .get();

        for (const externalIdDoc of externalIdsSnapshot.docs) {
          const externalIdData = externalIdDoc.data();
          if (externalIdData.label === integration) {
            await recursiveDocDelete(recipientDoc.ref, batch);
            batchCounter++;
          }
        };

        if (batchCounter >= batchLimit) {
          await batch.commit();
          batch = null;
          deletedCount += batchCounter;
          batchCounter = 0;

          // clean up after email validation
          await deleteRecipientsCleanUp(state, recipientIds);
          recipientIds = [];
        }
      };
    } while (nextDoc);

    if (batchCounter > 0) {
      deletedCount += batchCounter;
      await batch.commit();

      // clean up after email validation
      await deleteRecipientsCleanUp(state, recipientIds);
      recipientIds = [];
    }

    console.log(`${accountData.name} (${state.accountId}) - Deleted ${deletedCount} ${integration} recipients.`);

    return {
      status: 200,
      message: `Successfully deleted ${deletedCount} ${integration} recipients`,
    };
  } catch (error) {
    const msg = "Error deleting recipients";
    logger.error(msg, error);
    return { status: 400, message: `${msg}` };
  }
}

/**
 * @param {object} state - function state data
 * @param {array} recipientIds - array of recipient IDs to delete
 * @returns {object} Status
 */
async function deleteRecipientsCleanUp(state, recipientIds) {
  const db = state.db;

  try {
    const accountRef = state.getAccountRef();

    let batch = db.batch();

    for (const recipientId of recipientIds) {
      console.log("reprocessing recipientId: ", recipientId);

      const recipientDoc = await accountRef
        .collection(subCollectionNames.contacts.recipients)
        .doc(recipientId)
        .get();

      if (recipientDoc.exists) {
        await recursiveDocDelete(recipientDoc.ref, batch);
      }
    }

    await batch.commit();

    return;
  } catch (error) {
    const msg = "Error deleting recipients";
    logger.error(msg, error);
    return { status: 400, message: `${msg}` };
  }
}
