const { Timestamp, getFirestore } = require("firebase-admin/firestore");
const { logger } = require("firebase-functions");
const { onRequest } = require("firebase-functions/v2/https");
const { collectionNames, subCollectionNames } = require("../constants/collectionNames");
const { createUUID } = require("../utils/createUUID");
const { isEmpty } = require("lodash");

exports.cyclrWebhook = onRequest(
  //{ cors: [/cyclr\.com$/, "cyclr.com"] },
  async (req, res) => {
    const db = getFirestore();
    const { method, body } = req;

    if (method !== "POST") {
      res.status(405).send("Method Not Allowed");
      return;
    }

    try {
      if (isEmpty(body) || !body.Status) {
        throw new Error("Message body is empty or missing Status.");
      }

      let accountSnapshot = await db
        .collection(collectionNames.account)
        .where("cyclrAccountId", "==", body.AccountApiId)
        .where("isActive", "==", true)
        .limit(1)
        .get();

      if (accountSnapshot.empty) {
        throw new Error(`Unable to find RM Account with Cyclr AccountApiId: ${body.AccountApiId}`);
      }

      const accountId = accountSnapshot.docs[0].id;

      switch (body.Status) {
        case "installed":
          for (const cycle of body.MarketplaceInstallDetails) {
            // Assumes TemplateTags[0] format is "{two word method type} {single preposition} {connector name}"
            // Examples: "Import Contacts from Connector Name", "Export Contacts to Connector Name", or "Push Lead to Connector Name", etc.
            const methodType = cycle?.TemplateTags[0]?.split(" ")?.splice(0,2)?.join(" ");
            const connectorName = cycle?.TemplateTags[0]?.split(" ")?.splice(3)?.join(" ");
            const importFolderName = !isEmpty(connectorName) ? connectorName : "Imported";

            // Create a group for the connector if it's an import
            let groupsSnapshot = null;
            let recipientGroupIdDefault = null;
            if (methodType === "Import Contacts") {
              // look for existing group with the connector name
              groupsSnapshot = await db
                .collection(collectionNames.account)
                .doc(accountId)
                .collection(subCollectionNames.contacts.recipientGroups)
                .where("name", "==", importFolderName)
                .where("isActive", "==", true)
                .limit(1)
                .get();

              // if not found, create a new group
              if (isEmpty(groupsSnapshot) || groupsSnapshot.empty) {
                const recipientGroupsCollectionRef = db
                  .collection(collectionNames.account)
                  .doc(accountId)
                  .collection(subCollectionNames.contacts.recipientGroups);
                const uuid = createUUID();
                const newDocRef = recipientGroupsCollectionRef.doc(uuid);
                await newDocRef.set({
                  name: connectorName,
                  description: `Imported from ${connectorName}.`,
                  isActive: true,
                  createdAt: Timestamp.now(),
                  updatedAt: Timestamp.now(),
                });

                recipientGroupIdDefault = newDocRef?.id;
              } else if (!groupsSnapshot.empty) {
                recipientGroupIdDefault = groupsSnapshot?.docs[0]?.id;
              }
            }

            const cycleDetails = {
              templateId: cycle?.TemplateId || null,
              cycleName: cycle?.TemplateTags[0] || body.MarketplaceIntegrationName || null,
              connector: connectorName || null,
              methodType: methodType,
              webhooks: cycle?.Webhooks,
              status: cycle?.CycleStatus,
              createdAt: Timestamp.now(),
              updatedAt: Timestamp.now(),
            }

            if (!isEmpty(recipientGroupIdDefault)) {
              cycleDetails.recipientGroupIdDefault = recipientGroupIdDefault;
            }

            // store integration details in integrations collection
            await db
              .collection(collectionNames.account)
              .doc(accountId)
              .collection(subCollectionNames.account.integrations)
              .add(cycleDetails);
          }
          break;

        case "uninstalled":
          for (const cycle of body.MarketplaceInstallDetails) {
            const cycleSnapshot = await db
              .collection(collectionNames.account)
              .doc(accountId)
              .collection(subCollectionNames.account.integrations)
              .where("templateId", "==", cycle?.TemplateId)
              .get();

            if (!cycleSnapshot.empty) {
              cycleSnapshot.docs.map(async (doc) => {
                await doc.ref.delete();
              });
            }
          }
          break;

        case "stopped":
        case "started":
          for (const cycle of body.MarketplaceInstallDetails) {
            const cycleUpdSnapshot = await db
              .collection(collectionNames.account)
              .doc(accountId)
              .collection(subCollectionNames.account.integrations)
              .where("templateId", "==", cycle?.TemplateId)
              .get();

            if (!cycleUpdSnapshot.empty) {
              cycleUpdSnapshot.docs.map(async (doc) => {
                await doc.ref.update({
                  status: body.Status,
                  updatedAt: Timestamp.now(),
                });
              });
            }
          }
          break;

        default:
      }     

    } catch (error) {
      logger.error("Error in cyclrWebhook.", error);
      res.status(400).json({status: 400, message: `Error in cyclrWebhook. ${error.message}`});
    }

    res.status(200).send();
  }
);
