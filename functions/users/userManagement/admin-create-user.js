const { onCall, HttpsError } = require("firebase-functions/v2/https");
const { getAuth } = require("firebase-admin/auth");
const { error } = require("firebase-functions/logger");

exports.adminCreateUser = (db) =>
  onCall(async (request) => {
    const { data } = request;
    const { email, password, name } = data;

    const usersRef = db.collection("Users");

    return getAuth()
      .createUser({
        email,
        emailVerified: false,
        password,
        name,
        disabled: false,
      })
      .then((user) => {
        return usersRef
          .add({ name, email })
          .then(() => {
            return {
              message: `Successfully created auth and user record for ${name}, ${email}`,
              data: {
                user,
              },
            };
          })
          .catch((err) => {
            error("error", err);
            // https://github.com/grpc/grpc/blob/master/doc/statuscodes.md
            // https://firebase.google.com/docs/functions/callable?gen=2nd
            throw new HttpsError("", err);
          });
      })
      .catch((err) => {
        error("error", err);
        throw new HttpsError("", err);
      });
  });
