const {
  beforeUserCreated,
  beforeUserSignedIn,
  onCreate,
} = require("firebase-functions/v2/identity");

const { onDocumentCreated } = require("firebase-functions/v2/firestore");

exports.beforeCreateUser = (admin) => {
  return beforeUserCreated(async (event) => {
    const { data, additionalUserInfo } = event;

    const { uid, email = "", displayName } = data;
    const { providerId } = additionalUserInfo;

    const isInternalUser = providerId === "microsoft.com";

    if (isInternalUser) {
      // Update email verification status and add custom claims
      const adminEmail = additionalUserInfo.profile.mail;

      const customClaims = { role: "admin", type: "internal" };
      await admin.firestore().collection("Users").doc(uid).set(
        {
          email: adminEmail,
          name: displayName,
          claims: customClaims,
        },
        { merge: true }
      );
      return { customClaims };
    }

    return {}; // No custom claims for non-internal users
  });
};

exports.verifyInternalUserEmail = (admin) => {
  return onDocumentCreated("Users/{userId}", async (event) => {
    const snapshot = event.data;
    if (!snapshot) {
      return;
    }

    const userData = snapshot.data();
    const userId = event.params.userId;

    if (
      userData.claims &&
      userData.claims.type === "internal" &&
      !userData.emailVerified
    ) {
      try {
        await admin.auth().updateUser(userId, { emailVerified: true });
      } catch (error) {
        console.error(`Error verifying email for user ${userId}:`, error);
      }
    }
  });
};
