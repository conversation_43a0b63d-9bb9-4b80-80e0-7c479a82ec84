const fs = require("fs");
const { SecretManagerServiceClient } = require("@google-cloud/secret-manager");

const client = new SecretManagerServiceClient();

const versionNumber = "latest";
const hostingEnvFilePath = "hosting.env";
const functionsEnvFilePath = "functions.env";
const ssoHostingEnvFilePath = "sso.hosting.env";
const ssoFunctionsEnvFilePath = "sso.functions.env";

const STAGE_ENV_LABEL = "stage";
const PROD_ENV_LABEL = "prod";
const QA_ENV_LABEL = "qa";
const DEMO_ENV_LABEL = "demo";

const params = {
  stage: {
    projectId: "",
    hostingEnvSecretName: "StageFirebaseHostingEnv",
    functionsEnvSecretName: "StageFirebaseFunctionsEnv",
    ssoHostingEnvSecretName: "StageSSOFirebaseHostingEnv",
    ssoFunctionsEnvSecretName: "StageSSOFirebaseFunctionsEnv",
  },
  demo: {
    projectId: "",
    hostingEnvSecretName: "DemoFirebaseHostingEnv",
    functionsEnvSecretName: "DemoFirebaseFunctionsEnv",
    ssoHostingEnvSecretName: "DemoSSOFirebaseHostingEnv",
    ssoFunctionsEnvSecretName: "DemoSSOFirebaseFunctionsEnv",
  },
  qa: {
    projectId: "",
    hostingEnvSecretName: "QaFirebaseHostingEnv",
    functionsEnvSecretName: "QaFirebaseFunctionsEnv",
    ssoHostingEnvSecretName: "QaSSOFirebaseHostingEnv",
    ssoFunctionsEnvSecretName: "QaSSOFirebaseFunctionsEnv",
  },
  prod: {
    projectId: "",
    hostingEnvSecretName: "ProdFirebaseHostingEnv",
    functionsEnvSecretName: "ProdFirebaseFunctionsEnv",
    ssoHostingEnvSecretName: "ProdSSOFirebaseHostingEnv",
    ssoFunctionsEnvSecretName: "ProdSSOFirebaseFunctionsEnv",
  },
};

const getSecret = async (name) => {
  const [version] = await client.accessSecretVersion({
    name: name,
  });

  return version.payload.data.toString();
};

const isHostingHttpsEnabled = () => {
  if (process.argv[2] && process.argv[2] === "HOSTING_HTTPS=TRUE") {
    return true;
  } else {
    return false;
  }
};

const getEnv = () => {
  let env = undefined;
  if (process.argv[3]) {
    env = process.argv[3];
  } else {
    throw new Error(
      "Missing specified env. Acceptable values are: prod, demo, qa, stage",
    );
  }

  switch (env) {
    case STAGE_ENV_LABEL:
      return env;
    case QA_ENV_LABEL:
      return env;
    case DEMO_ENV_LABEL:
      return env;
    case PROD_ENV_LABEL:
      return env;
    default:
      throw new Error(
        "Invalid env value provided. Acceptable values are: prod, qa, stage",
      );
  }
};

const formatSecretName = (projectId, secretName, versionNumber) => {
  return `projects/${projectId}/secrets/${secretName}/versions/${versionNumber}`;
};

const getSecretByEnv = async (projectId, secretName, versionNumber) => {
  try {
    return await getSecret(
      formatSecretName(projectId, secretName, versionNumber),
    );
  } catch (error) {
    throw new Error(error.message);
  }
};

const writeFiles = (
  hostingHttpsEnabled,
  rmCampaignsFirebaseHostingEnv,
  firebaseFunctionsEnv,
  ssoFirebaseHostingEnv,
  ssoFirebaseFunctionsEnv,
) => {
  // Clear out previous .env files
  try {
    fs.truncateSync(hostingEnvFilePath, 0, () => {});
    fs.truncateSync(functionsEnvFilePath, 0, () => {});
    fs.truncateSync(ssoHostingEnvFilePath, 0, () => {});
  } catch (error) {
    console.log(error.message);
  }

  // Write .env files
  fs.appendFileSync(
    hostingEnvFilePath,
    hostingHttpsEnabled
      ? rmCampaignsFirebaseHostingEnv.replace("HTTPS=false", "HTTPS=true")
      : rmCampaignsFirebaseHostingEnv,
  );
  fs.appendFileSync(functionsEnvFilePath, firebaseFunctionsEnv);
  fs.appendFileSync(ssoHostingEnvFilePath, ssoFirebaseHostingEnv);
  fs.appendFileSync(ssoFunctionsEnvFilePath, ssoFirebaseFunctionsEnv);
};

(async () => {
  const hostingHttpsEnabled = isHostingHttpsEnabled();
  const env = getEnv();
  const projectId = params[env]["projectId"];
  const hostingEnvSecretName = params[env]["hostingEnvSecretName"];
  const functionsEnvSecretName = params[env]["functionsEnvSecretName"];
  const ssoHostingEnvSecretName = params[env]["ssoHostingEnvSecretName"];
  const ssoFunctionsEnvSecretName = params[env]["ssoFunctionsEnvSecretName"];

  const rmCampaignsFirebaseHostingEnv = await getSecretByEnv(
    projectId,
    hostingEnvSecretName,
    versionNumber,
  );
  const firebaseFunctionsEnv = await getSecretByEnv(
    projectId,
    functionsEnvSecretName,
    versionNumber,
  );
  const ssoFirebaseHostingEnv = await getSecretByEnv(
    projectId,
    ssoHostingEnvSecretName,
    versionNumber,
  );
  const ssoFirebaseFunctionsEnv = await getSecretByEnv(
    projectId,
    ssoFunctionsEnvSecretName,
    versionNumber,
  );

  writeFiles(
    hostingHttpsEnabled,
    rmCampaignsFirebaseHostingEnv,
    firebaseFunctionsEnv,
    ssoFirebaseHostingEnv,
    ssoFirebaseFunctionsEnv,
  );
})();
