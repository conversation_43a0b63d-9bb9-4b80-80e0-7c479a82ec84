const firebase_admin = require("firebase-admin");
const serviceAccount = require("C:/Users/<USER>/Downloads/qa-firebase-project-2ad4f-firebase-adminsdk-vmw13-bc125732cd.json");
const admin = firebase_admin.initializeApp({
  credential: firebase_admin.credential.cert(serviceAccount),
});

const deleteCollections = async (collectionNames) => {
  for (const collectionName of collectionNames) {
    // const ref = admin.firestore().doc("my_document");
    const collectionRef = admin.firestore().collection(collectionName);
    try {
      await admin.firestore().recursiveDelete(collectionRef);
      console.log(`deleted collection: ${collectionName}`);
    } catch (error) {
      console.log(error);
    }
  }
};

const main = async () => {
  const collectionNames = ["Deletion Test"];
  await deleteCollections(collectionNames);
};

(async () => {
  await main();
})();
