@import url(https://fonts.googleapis.com/css?family=Nunito+Sans:300,300i,400,400i,700,700i,800,800i|Lato:400,700,900|Montserrat:500);
@charset "UTF-8";
/*!
  Ionicons, v2.0.0
  Created by <PERSON> for the Ionic Framework, http://ionicons.com/
  https://twitter.com/benjsperry  https://twitter.com/ionicframework
  MIT License: https://github.com/driftyco/ionicons

  Android-style icons originally built by Google’s
  Material Design Icons: https://github.com/google/material-design-icons
  used under CC BY http://creativecommons.org/licenses/by/4.0/
  Modified icons to fit ionicon’s grid from original.
*/
@font-face {
  font-family: "Ionicons";
  src: url(/fonts/ionicons.eot?2c2ae068be3b089e0a5b59abb1831550);
  src:
    url(/fonts/ionicons.eot?2c2ae068be3b089e0a5b59abb1831550#iefix)
      format("embedded-opentype"),
    url(/fonts/ionicons.ttf?24712f6c47821394fba7942fbb52c3b2) format("truetype"),
    url(/fonts/ionicons.woff?05acfdb568b3df49ad31355b19495d4a) format("woff"),
    url(/fonts/ionicons.svg?621bd386841f74e0053cb8e67f8a0604#Ionicons)
      format("svg");
  font-weight: normal;
  font-style: normal;
}

.ion,
.ionicons,
.ion-alert:before,
.ion-alert-circled:before,
.ion-android-add:before,
.ion-android-add-circle:before,
.ion-android-alarm-clock:before,
.ion-android-alert:before,
.ion-android-apps:before,
.ion-android-archive:before,
.ion-android-arrow-back:before,
.ion-android-arrow-down:before,
.ion-android-arrow-dropdown:before,
.ion-android-arrow-dropdown-circle:before,
.ion-android-arrow-dropleft:before,
.ion-android-arrow-dropleft-circle:before,
.ion-android-arrow-dropright:before,
.ion-android-arrow-dropright-circle:before,
.ion-android-arrow-dropup:before,
.ion-android-arrow-dropup-circle:before,
.ion-android-arrow-forward:before,
.ion-android-arrow-up:before,
.ion-android-attach:before,
.ion-android-bar:before,
.ion-android-bicycle:before,
.ion-android-boat:before,
.ion-android-bookmark:before,
.ion-android-bulb:before,
.ion-android-bus:before,
.ion-android-calendar:before,
.ion-android-call:before,
.ion-android-camera:before,
.ion-android-cancel:before,
.ion-android-car:before,
.ion-android-cart:before,
.ion-android-chat:before,
.ion-android-checkbox:before,
.ion-android-checkbox-blank:before,
.ion-android-checkbox-outline:before,
.ion-android-checkbox-outline-blank:before,
.ion-android-checkmark-circle:before,
.ion-android-clipboard:before,
.ion-android-close:before,
.ion-android-cloud:before,
.ion-android-cloud-circle:before,
.ion-android-cloud-done:before,
.ion-android-cloud-outline:before,
.ion-android-color-palette:before,
.ion-android-compass:before,
.ion-android-contact:before,
.ion-android-contacts:before,
.ion-android-contract:before,
.ion-android-create:before,
.ion-android-delete:before,
.ion-android-desktop:before,
.ion-android-document:before,
.ion-android-done:before,
.ion-android-done-all:before,
.ion-android-download:before,
.ion-android-drafts:before,
.ion-android-exit:before,
.ion-android-expand:before,
.ion-android-favorite:before,
.ion-android-favorite-outline:before,
.ion-android-film:before,
.ion-android-folder:before,
.ion-android-folder-open:before,
.ion-android-funnel:before,
.ion-android-globe:before,
.ion-android-hand:before,
.ion-android-hangout:before,
.ion-android-happy:before,
.ion-android-home:before,
.ion-android-image:before,
.ion-android-laptop:before,
.ion-android-list:before,
.ion-android-locate:before,
.ion-android-lock:before,
.ion-android-mail:before,
.ion-android-map:before,
.ion-android-menu:before,
.ion-android-microphone:before,
.ion-android-microphone-off:before,
.ion-android-more-horizontal:before,
.ion-android-more-vertical:before,
.ion-android-navigate:before,
.ion-android-notifications:before,
.ion-android-notifications-none:before,
.ion-android-notifications-off:before,
.ion-android-open:before,
.ion-android-options:before,
.ion-android-people:before,
.ion-android-person:before,
.ion-android-person-add:before,
.ion-android-phone-landscape:before,
.ion-android-phone-portrait:before,
.ion-android-pin:before,
.ion-android-plane:before,
.ion-android-playstore:before,
.ion-android-print:before,
.ion-android-radio-button-off:before,
.ion-android-radio-button-on:before,
.ion-android-refresh:before,
.ion-android-remove:before,
.ion-android-remove-circle:before,
.ion-android-restaurant:before,
.ion-android-sad:before,
.ion-android-search:before,
.ion-android-send:before,
.ion-android-settings:before,
.ion-android-share:before,
.ion-android-share-alt:before,
.ion-android-star:before,
.ion-android-star-half:before,
.ion-android-star-outline:before,
.ion-android-stopwatch:before,
.ion-android-subway:before,
.ion-android-sunny:before,
.ion-android-sync:before,
.ion-android-textsms:before,
.ion-android-time:before,
.ion-android-train:before,
.ion-android-unlock:before,
.ion-android-upload:before,
.ion-android-volume-down:before,
.ion-android-volume-mute:before,
.ion-android-volume-off:before,
.ion-android-volume-up:before,
.ion-android-walk:before,
.ion-android-warning:before,
.ion-android-watch:before,
.ion-android-wifi:before,
.ion-aperture:before,
.ion-archive:before,
.ion-arrow-down-a:before,
.ion-arrow-down-b:before,
.ion-arrow-down-c:before,
.ion-arrow-expand:before,
.ion-arrow-graph-down-left:before,
.ion-arrow-graph-down-right:before,
.ion-arrow-graph-up-left:before,
.ion-arrow-graph-up-right:before,
.ion-arrow-left-a:before,
.ion-arrow-left-b:before,
.ion-arrow-left-c:before,
.ion-arrow-move:before,
.ion-arrow-resize:before,
.ion-arrow-return-left:before,
.ion-arrow-return-right:before,
.ion-arrow-right-a:before,
.ion-arrow-right-b:before,
.ion-arrow-right-c:before,
.ion-arrow-shrink:before,
.ion-arrow-swap:before,
.ion-arrow-up-a:before,
.ion-arrow-up-b:before,
.ion-arrow-up-c:before,
.ion-asterisk:before,
.ion-at:before,
.ion-backspace:before,
.ion-backspace-outline:before,
.ion-bag:before,
.ion-battery-charging:before,
.ion-battery-empty:before,
.ion-battery-full:before,
.ion-battery-half:before,
.ion-battery-low:before,
.ion-beaker:before,
.ion-beer:before,
.ion-bluetooth:before,
.ion-bonfire:before,
.ion-bookmark:before,
.ion-bowtie:before,
.ion-briefcase:before,
.ion-bug:before,
.ion-calculator:before,
.ion-calendar:before,
.ion-camera:before,
.ion-card:before,
.ion-cash:before,
.ion-chatbox:before,
.ion-chatbox-working:before,
.ion-chatboxes:before,
.ion-chatbubble:before,
.ion-chatbubble-working:before,
.ion-chatbubbles:before,
.ion-checkmark:before,
.ion-checkmark-circled:before,
.ion-checkmark-round:before,
.ion-chevron-down:before,
.ion-chevron-left:before,
.ion-chevron-right:before,
.ion-chevron-up:before,
.ion-clipboard:before,
.ion-clock:before,
.ion-close:before,
.ion-close-circled:before,
.ion-close-round:before,
.ion-closed-captioning:before,
.ion-cloud:before,
.ion-code:before,
.ion-code-download:before,
.ion-code-working:before,
.ion-coffee:before,
.ion-compass:before,
.ion-compose:before,
.ion-connection-bars:before,
.ion-contrast:before,
.ion-crop:before,
.ion-cube:before,
.ion-disc:before,
.ion-document:before,
.ion-document-text:before,
.ion-drag:before,
.ion-earth:before,
.ion-easel:before,
.ion-edit:before,
.ion-egg:before,
.ion-eject:before,
.ion-email:before,
.ion-email-unread:before,
.ion-erlenmeyer-flask:before,
.ion-erlenmeyer-flask-bubbles:before,
.ion-eye:before,
.ion-eye-disabled:before,
.ion-female:before,
.ion-filing:before,
.ion-film-marker:before,
.ion-fireball:before,
.ion-flag:before,
.ion-flame:before,
.ion-flash:before,
.ion-flash-off:before,
.ion-folder:before,
.ion-fork:before,
.ion-fork-repo:before,
.ion-forward:before,
.ion-funnel:before,
.ion-gear-a:before,
.ion-gear-b:before,
.ion-grid:before,
.ion-hammer:before,
.ion-happy:before,
.ion-happy-outline:before,
.ion-headphone:before,
.ion-heart:before,
.ion-heart-broken:before,
.ion-help:before,
.ion-help-buoy:before,
.ion-help-circled:before,
.ion-home:before,
.ion-icecream:before,
.ion-image:before,
.ion-images:before,
.ion-information:before,
.ion-information-circled:before,
.ion-ionic:before,
.ion-ios-alarm:before,
.ion-ios-alarm-outline:before,
.ion-ios-albums:before,
.ion-ios-albums-outline:before,
.ion-ios-americanfootball:before,
.ion-ios-americanfootball-outline:before,
.ion-ios-analytics:before,
.ion-ios-analytics-outline:before,
.ion-ios-arrow-back:before,
.ion-ios-arrow-down:before,
.ion-ios-arrow-forward:before,
.ion-ios-arrow-left:before,
.ion-ios-arrow-right:before,
.ion-ios-arrow-thin-down:before,
.ion-ios-arrow-thin-left:before,
.ion-ios-arrow-thin-right:before,
.ion-ios-arrow-thin-up:before,
.ion-ios-arrow-up:before,
.ion-ios-at:before,
.ion-ios-at-outline:before,
.ion-ios-barcode:before,
.ion-ios-barcode-outline:before,
.ion-ios-baseball:before,
.ion-ios-baseball-outline:before,
.ion-ios-basketball:before,
.ion-ios-basketball-outline:before,
.ion-ios-bell:before,
.ion-ios-bell-outline:before,
.ion-ios-body:before,
.ion-ios-body-outline:before,
.ion-ios-bolt:before,
.ion-ios-bolt-outline:before,
.ion-ios-book:before,
.ion-ios-book-outline:before,
.ion-ios-bookmarks:before,
.ion-ios-bookmarks-outline:before,
.ion-ios-box:before,
.ion-ios-box-outline:before,
.ion-ios-briefcase:before,
.ion-ios-briefcase-outline:before,
.ion-ios-browsers:before,
.ion-ios-browsers-outline:before,
.ion-ios-calculator:before,
.ion-ios-calculator-outline:before,
.ion-ios-calendar:before,
.ion-ios-calendar-outline:before,
.ion-ios-camera:before,
.ion-ios-camera-outline:before,
.ion-ios-cart:before,
.ion-ios-cart-outline:before,
.ion-ios-chatboxes:before,
.ion-ios-chatboxes-outline:before,
.ion-ios-chatbubble:before,
.ion-ios-chatbubble-outline:before,
.ion-ios-checkmark:before,
.ion-ios-checkmark-empty:before,
.ion-ios-checkmark-outline:before,
.ion-ios-circle-filled:before,
.ion-ios-circle-outline:before,
.ion-ios-clock:before,
.ion-ios-clock-outline:before,
.ion-ios-close:before,
.ion-ios-close-empty:before,
.ion-ios-close-outline:before,
.ion-ios-cloud:before,
.ion-ios-cloud-download:before,
.ion-ios-cloud-download-outline:before,
.ion-ios-cloud-outline:before,
.ion-ios-cloud-upload:before,
.ion-ios-cloud-upload-outline:before,
.ion-ios-cloudy:before,
.ion-ios-cloudy-night:before,
.ion-ios-cloudy-night-outline:before,
.ion-ios-cloudy-outline:before,
.ion-ios-cog:before,
.ion-ios-cog-outline:before,
.ion-ios-color-filter:before,
.ion-ios-color-filter-outline:before,
.ion-ios-color-wand:before,
.ion-ios-color-wand-outline:before,
.ion-ios-compose:before,
.ion-ios-compose-outline:before,
.ion-ios-contact:before,
.ion-ios-contact-outline:before,
.ion-ios-copy:before,
.ion-ios-copy-outline:before,
.ion-ios-crop:before,
.ion-ios-crop-strong:before,
.ion-ios-download:before,
.ion-ios-download-outline:before,
.ion-ios-drag:before,
.ion-ios-email:before,
.ion-ios-email-outline:before,
.ion-ios-eye:before,
.ion-ios-eye-outline:before,
.ion-ios-fastforward:before,
.ion-ios-fastforward-outline:before,
.ion-ios-filing:before,
.ion-ios-filing-outline:before,
.ion-ios-film:before,
.ion-ios-film-outline:before,
.ion-ios-flag:before,
.ion-ios-flag-outline:before,
.ion-ios-flame:before,
.ion-ios-flame-outline:before,
.ion-ios-flask:before,
.ion-ios-flask-outline:before,
.ion-ios-flower:before,
.ion-ios-flower-outline:before,
.ion-ios-folder:before,
.ion-ios-folder-outline:before,
.ion-ios-football:before,
.ion-ios-football-outline:before,
.ion-ios-game-controller-a:before,
.ion-ios-game-controller-a-outline:before,
.ion-ios-game-controller-b:before,
.ion-ios-game-controller-b-outline:before,
.ion-ios-gear:before,
.ion-ios-gear-outline:before,
.ion-ios-glasses:before,
.ion-ios-glasses-outline:before,
.ion-ios-grid-view:before,
.ion-ios-grid-view-outline:before,
.ion-ios-heart:before,
.ion-ios-heart-outline:before,
.ion-ios-help:before,
.ion-ios-help-empty:before,
.ion-ios-help-outline:before,
.ion-ios-home:before,
.ion-ios-home-outline:before,
.ion-ios-infinite:before,
.ion-ios-infinite-outline:before,
.ion-ios-information:before,
.ion-ios-information-empty:before,
.ion-ios-information-outline:before,
.ion-ios-ionic-outline:before,
.ion-ios-keypad:before,
.ion-ios-keypad-outline:before,
.ion-ios-lightbulb:before,
.ion-ios-lightbulb-outline:before,
.ion-ios-list:before,
.ion-ios-list-outline:before,
.ion-ios-location:before,
.ion-ios-location-outline:before,
.ion-ios-locked:before,
.ion-ios-locked-outline:before,
.ion-ios-loop:before,
.ion-ios-loop-strong:before,
.ion-ios-medical:before,
.ion-ios-medical-outline:before,
.ion-ios-medkit:before,
.ion-ios-medkit-outline:before,
.ion-ios-mic:before,
.ion-ios-mic-off:before,
.ion-ios-mic-outline:before,
.ion-ios-minus:before,
.ion-ios-minus-empty:before,
.ion-ios-minus-outline:before,
.ion-ios-monitor:before,
.ion-ios-monitor-outline:before,
.ion-ios-moon:before,
.ion-ios-moon-outline:before,
.ion-ios-more:before,
.ion-ios-more-outline:before,
.ion-ios-musical-note:before,
.ion-ios-musical-notes:before,
.ion-ios-navigate:before,
.ion-ios-navigate-outline:before,
.ion-ios-nutrition:before,
.ion-ios-nutrition-outline:before,
.ion-ios-paper:before,
.ion-ios-paper-outline:before,
.ion-ios-paperplane:before,
.ion-ios-paperplane-outline:before,
.ion-ios-partlysunny:before,
.ion-ios-partlysunny-outline:before,
.ion-ios-pause:before,
.ion-ios-pause-outline:before,
.ion-ios-paw:before,
.ion-ios-paw-outline:before,
.ion-ios-people:before,
.ion-ios-people-outline:before,
.ion-ios-person:before,
.ion-ios-person-outline:before,
.ion-ios-personadd:before,
.ion-ios-personadd-outline:before,
.ion-ios-photos:before,
.ion-ios-photos-outline:before,
.ion-ios-pie:before,
.ion-ios-pie-outline:before,
.ion-ios-pint:before,
.ion-ios-pint-outline:before,
.ion-ios-play:before,
.ion-ios-play-outline:before,
.ion-ios-plus:before,
.ion-ios-plus-empty:before,
.ion-ios-plus-outline:before,
.ion-ios-pricetag:before,
.ion-ios-pricetag-outline:before,
.ion-ios-pricetags:before,
.ion-ios-pricetags-outline:before,
.ion-ios-printer:before,
.ion-ios-printer-outline:before,
.ion-ios-pulse:before,
.ion-ios-pulse-strong:before,
.ion-ios-rainy:before,
.ion-ios-rainy-outline:before,
.ion-ios-recording:before,
.ion-ios-recording-outline:before,
.ion-ios-redo:before,
.ion-ios-redo-outline:before,
.ion-ios-refresh:before,
.ion-ios-refresh-empty:before,
.ion-ios-refresh-outline:before,
.ion-ios-reload:before,
.ion-ios-reverse-camera:before,
.ion-ios-reverse-camera-outline:before,
.ion-ios-rewind:before,
.ion-ios-rewind-outline:before,
.ion-ios-rose:before,
.ion-ios-rose-outline:before,
.ion-ios-search:before,
.ion-ios-search-strong:before,
.ion-ios-settings:before,
.ion-ios-settings-strong:before,
.ion-ios-shuffle:before,
.ion-ios-shuffle-strong:before,
.ion-ios-skipbackward:before,
.ion-ios-skipbackward-outline:before,
.ion-ios-skipforward:before,
.ion-ios-skipforward-outline:before,
.ion-ios-snowy:before,
.ion-ios-speedometer:before,
.ion-ios-speedometer-outline:before,
.ion-ios-star:before,
.ion-ios-star-half:before,
.ion-ios-star-outline:before,
.ion-ios-stopwatch:before,
.ion-ios-stopwatch-outline:before,
.ion-ios-sunny:before,
.ion-ios-sunny-outline:before,
.ion-ios-telephone:before,
.ion-ios-telephone-outline:before,
.ion-ios-tennisball:before,
.ion-ios-tennisball-outline:before,
.ion-ios-thunderstorm:before,
.ion-ios-thunderstorm-outline:before,
.ion-ios-time:before,
.ion-ios-time-outline:before,
.ion-ios-timer:before,
.ion-ios-timer-outline:before,
.ion-ios-toggle:before,
.ion-ios-toggle-outline:before,
.ion-ios-trash:before,
.ion-ios-trash-outline:before,
.ion-ios-undo:before,
.ion-ios-undo-outline:before,
.ion-ios-unlocked:before,
.ion-ios-unlocked-outline:before,
.ion-ios-upload:before,
.ion-ios-upload-outline:before,
.ion-ios-videocam:before,
.ion-ios-videocam-outline:before,
.ion-ios-volume-high:before,
.ion-ios-volume-low:before,
.ion-ios-wineglass:before,
.ion-ios-wineglass-outline:before,
.ion-ios-world:before,
.ion-ios-world-outline:before,
.ion-ipad:before,
.ion-iphone:before,
.ion-ipod:before,
.ion-jet:before,
.ion-key:before,
.ion-knife:before,
.ion-laptop:before,
.ion-leaf:before,
.ion-levels:before,
.ion-lightbulb:before,
.ion-link:before,
.ion-load-a:before,
.ion-load-b:before,
.ion-load-c:before,
.ion-load-d:before,
.ion-location:before,
.ion-lock-combination:before,
.ion-locked:before,
.ion-log-in:before,
.ion-log-out:before,
.ion-loop:before,
.ion-magnet:before,
.ion-male:before,
.ion-man:before,
.ion-map:before,
.ion-medkit:before,
.ion-merge:before,
.ion-mic-a:before,
.ion-mic-b:before,
.ion-mic-c:before,
.ion-minus:before,
.ion-minus-circled:before,
.ion-minus-round:before,
.ion-model-s:before,
.ion-monitor:before,
.ion-more:before,
.ion-mouse:before,
.ion-music-note:before,
.ion-navicon:before,
.ion-navicon-round:before,
.ion-navigate:before,
.ion-network:before,
.ion-no-smoking:before,
.ion-nuclear:before,
.ion-outlet:before,
.ion-paintbrush:before,
.ion-paintbucket:before,
.ion-paper-airplane:before,
.ion-paperclip:before,
.ion-pause:before,
.ion-person:before,
.ion-person-add:before,
.ion-person-stalker:before,
.ion-pie-graph:before,
.ion-pin:before,
.ion-pinpoint:before,
.ion-pizza:before,
.ion-plane:before,
.ion-planet:before,
.ion-play:before,
.ion-playstation:before,
.ion-plus:before,
.ion-plus-circled:before,
.ion-plus-round:before,
.ion-podium:before,
.ion-pound:before,
.ion-power:before,
.ion-pricetag:before,
.ion-pricetags:before,
.ion-printer:before,
.ion-pull-request:before,
.ion-qr-scanner:before,
.ion-quote:before,
.ion-radio-waves:before,
.ion-record:before,
.ion-refresh:before,
.ion-reply:before,
.ion-reply-all:before,
.ion-ribbon-a:before,
.ion-ribbon-b:before,
.ion-sad:before,
.ion-sad-outline:before,
.ion-scissors:before,
.ion-search:before,
.ion-settings:before,
.ion-share:before,
.ion-shuffle:before,
.ion-skip-backward:before,
.ion-skip-forward:before,
.ion-social-android:before,
.ion-social-android-outline:before,
.ion-social-angular:before,
.ion-social-angular-outline:before,
.ion-social-apple:before,
.ion-social-apple-outline:before,
.ion-social-bitcoin:before,
.ion-social-bitcoin-outline:before,
.ion-social-buffer:before,
.ion-social-buffer-outline:before,
.ion-social-chrome:before,
.ion-social-chrome-outline:before,
.ion-social-codepen:before,
.ion-social-codepen-outline:before,
.ion-social-css3:before,
.ion-social-css3-outline:before,
.ion-social-designernews:before,
.ion-social-designernews-outline:before,
.ion-social-dribbble:before,
.ion-social-dribbble-outline:before,
.ion-social-dropbox:before,
.ion-social-dropbox-outline:before,
.ion-social-euro:before,
.ion-social-euro-outline:before,
.ion-social-facebook:before,
.ion-social-facebook-outline:before,
.ion-social-foursquare:before,
.ion-social-foursquare-outline:before,
.ion-social-freebsd-devil:before,
.ion-social-github:before,
.ion-social-github-outline:before,
.ion-social-google:before,
.ion-social-google-outline:before,
.ion-social-googleplus:before,
.ion-social-googleplus-outline:before,
.ion-social-hackernews:before,
.ion-social-hackernews-outline:before,
.ion-social-html5:before,
.ion-social-html5-outline:before,
.ion-social-instagram:before,
.ion-social-instagram-outline:before,
.ion-social-javascript:before,
.ion-social-javascript-outline:before,
.ion-social-linkedin:before,
.ion-social-linkedin-outline:before,
.ion-social-markdown:before,
.ion-social-nodejs:before,
.ion-social-octocat:before,
.ion-social-pinterest:before,
.ion-social-pinterest-outline:before,
.ion-social-python:before,
.ion-social-reddit:before,
.ion-social-reddit-outline:before,
.ion-social-rss:before,
.ion-social-rss-outline:before,
.ion-social-sass:before,
.ion-social-skype:before,
.ion-social-skype-outline:before,
.ion-social-snapchat:before,
.ion-social-snapchat-outline:before,
.ion-social-tumblr:before,
.ion-social-tumblr-outline:before,
.ion-social-tux:before,
.ion-social-twitch:before,
.ion-social-twitch-outline:before,
.ion-social-twitter:before,
.ion-social-twitter-outline:before,
.ion-social-usd:before,
.ion-social-usd-outline:before,
.ion-social-vimeo:before,
.ion-social-vimeo-outline:before,
.ion-social-whatsapp:before,
.ion-social-whatsapp-outline:before,
.ion-social-windows:before,
.ion-social-windows-outline:before,
.ion-social-wordpress:before,
.ion-social-wordpress-outline:before,
.ion-social-yahoo:before,
.ion-social-yahoo-outline:before,
.ion-social-yen:before,
.ion-social-yen-outline:before,
.ion-social-youtube:before,
.ion-social-youtube-outline:before,
.ion-soup-can:before,
.ion-soup-can-outline:before,
.ion-speakerphone:before,
.ion-speedometer:before,
.ion-spoon:before,
.ion-star:before,
.ion-stats-bars:before,
.ion-steam:before,
.ion-stop:before,
.ion-thermometer:before,
.ion-thumbsdown:before,
.ion-thumbsup:before,
.ion-toggle:before,
.ion-toggle-filled:before,
.ion-transgender:before,
.ion-trash-a:before,
.ion-trash-b:before,
.ion-trophy:before,
.ion-tshirt:before,
.ion-tshirt-outline:before,
.ion-umbrella:before,
.ion-university:before,
.ion-unlocked:before,
.ion-upload:before,
.ion-usb:before,
.ion-videocamera:before,
.ion-volume-high:before,
.ion-volume-low:before,
.ion-volume-medium:before,
.ion-volume-mute:before,
.ion-wand:before,
.ion-waterdrop:before,
.ion-wifi:before,
.ion-wineglass:before,
.ion-woman:before,
.ion-wrench:before,
.ion-xbox:before {
  display: inline-block;
  font-family: "Ionicons";
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  text-rendering: auto;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ion-alert:before {
  content: "\F101";
}

.ion-alert-circled:before {
  content: "\F100";
}

.ion-android-add:before {
  content: "\F2C7";
}

.ion-android-add-circle:before {
  content: "\F359";
}

.ion-android-alarm-clock:before {
  content: "\F35A";
}

.ion-android-alert:before {
  content: "\F35B";
}

.ion-android-apps:before {
  content: "\F35C";
}

.ion-android-archive:before {
  content: "\F2C9";
}

.ion-android-arrow-back:before {
  content: "\F2CA";
}

.ion-android-arrow-down:before {
  content: "\F35D";
}

.ion-android-arrow-dropdown:before {
  content: "\F35F";
}

.ion-android-arrow-dropdown-circle:before {
  content: "\F35E";
}

.ion-android-arrow-dropleft:before {
  content: "\F361";
}

.ion-android-arrow-dropleft-circle:before {
  content: "\F360";
}

.ion-android-arrow-dropright:before {
  content: "\F363";
}

.ion-android-arrow-dropright-circle:before {
  content: "\F362";
}

.ion-android-arrow-dropup:before {
  content: "\F365";
}

.ion-android-arrow-dropup-circle:before {
  content: "\F364";
}

.ion-android-arrow-forward:before {
  content: "\F30F";
}

.ion-android-arrow-up:before {
  content: "\F366";
}

.ion-android-attach:before {
  content: "\F367";
}

.ion-android-bar:before {
  content: "\F368";
}

.ion-android-bicycle:before {
  content: "\F369";
}

.ion-android-boat:before {
  content: "\F36A";
}

.ion-android-bookmark:before {
  content: "\F36B";
}

.ion-android-bulb:before {
  content: "\F36C";
}

.ion-android-bus:before {
  content: "\F36D";
}

.ion-android-calendar:before {
  content: "\F2D1";
}

.ion-android-call:before {
  content: "\F2D2";
}

.ion-android-camera:before {
  content: "\F2D3";
}

.ion-android-cancel:before {
  content: "\F36E";
}

.ion-android-car:before {
  content: "\F36F";
}

.ion-android-cart:before {
  content: "\F370";
}

.ion-android-chat:before {
  content: "\F2D4";
}

.ion-android-checkbox:before {
  content: "\F374";
}

.ion-android-checkbox-blank:before {
  content: "\F371";
}

.ion-android-checkbox-outline:before {
  content: "\F373";
}

.ion-android-checkbox-outline-blank:before {
  content: "\F372";
}

.ion-android-checkmark-circle:before {
  content: "\F375";
}

.ion-android-clipboard:before {
  content: "\F376";
}

.ion-android-close:before {
  content: "\F2D7";
}

.ion-android-cloud:before {
  content: "\F37A";
}

.ion-android-cloud-circle:before {
  content: "\F377";
}

.ion-android-cloud-done:before {
  content: "\F378";
}

.ion-android-cloud-outline:before {
  content: "\F379";
}

.ion-android-color-palette:before {
  content: "\F37B";
}

.ion-android-compass:before {
  content: "\F37C";
}

.ion-android-contact:before {
  content: "\F2D8";
}

.ion-android-contacts:before {
  content: "\F2D9";
}

.ion-android-contract:before {
  content: "\F37D";
}

.ion-android-create:before {
  content: "\F37E";
}

.ion-android-delete:before {
  content: "\F37F";
}

.ion-android-desktop:before {
  content: "\F380";
}

.ion-android-document:before {
  content: "\F381";
}

.ion-android-done:before {
  content: "\F383";
}

.ion-android-done-all:before {
  content: "\F382";
}

.ion-android-download:before {
  content: "\F2DD";
}

.ion-android-drafts:before {
  content: "\F384";
}

.ion-android-exit:before {
  content: "\F385";
}

.ion-android-expand:before {
  content: "\F386";
}

.ion-android-favorite:before {
  content: "\F388";
}

.ion-android-favorite-outline:before {
  content: "\F387";
}

.ion-android-film:before {
  content: "\F389";
}

.ion-android-folder:before {
  content: "\F2E0";
}

.ion-android-folder-open:before {
  content: "\F38A";
}

.ion-android-funnel:before {
  content: "\F38B";
}

.ion-android-globe:before {
  content: "\F38C";
}

.ion-android-hand:before {
  content: "\F2E3";
}

.ion-android-hangout:before {
  content: "\F38D";
}

.ion-android-happy:before {
  content: "\F38E";
}

.ion-android-home:before {
  content: "\F38F";
}

.ion-android-image:before {
  content: "\F2E4";
}

.ion-android-laptop:before {
  content: "\F390";
}

.ion-android-list:before {
  content: "\F391";
}

.ion-android-locate:before {
  content: "\F2E9";
}

.ion-android-lock:before {
  content: "\F392";
}

.ion-android-mail:before {
  content: "\F2EB";
}

.ion-android-map:before {
  content: "\F393";
}

.ion-android-menu:before {
  content: "\F394";
}

.ion-android-microphone:before {
  content: "\F2EC";
}

.ion-android-microphone-off:before {
  content: "\F395";
}

.ion-android-more-horizontal:before {
  content: "\F396";
}

.ion-android-more-vertical:before {
  content: "\F397";
}

.ion-android-navigate:before {
  content: "\F398";
}

.ion-android-notifications:before {
  content: "\F39B";
}

.ion-android-notifications-none:before {
  content: "\F399";
}

.ion-android-notifications-off:before {
  content: "\F39A";
}

.ion-android-open:before {
  content: "\F39C";
}

.ion-android-options:before {
  content: "\F39D";
}

.ion-android-people:before {
  content: "\F39E";
}

.ion-android-person:before {
  content: "\F3A0";
}

.ion-android-person-add:before {
  content: "\F39F";
}

.ion-android-phone-landscape:before {
  content: "\F3A1";
}

.ion-android-phone-portrait:before {
  content: "\F3A2";
}

.ion-android-pin:before {
  content: "\F3A3";
}

.ion-android-plane:before {
  content: "\F3A4";
}

.ion-android-playstore:before {
  content: "\F2F0";
}

.ion-android-print:before {
  content: "\F3A5";
}

.ion-android-radio-button-off:before {
  content: "\F3A6";
}

.ion-android-radio-button-on:before {
  content: "\F3A7";
}

.ion-android-refresh:before {
  content: "\F3A8";
}

.ion-android-remove:before {
  content: "\F2F4";
}

.ion-android-remove-circle:before {
  content: "\F3A9";
}

.ion-android-restaurant:before {
  content: "\F3AA";
}

.ion-android-sad:before {
  content: "\F3AB";
}

.ion-android-search:before {
  content: "\F2F5";
}

.ion-android-send:before {
  content: "\F2F6";
}

.ion-android-settings:before {
  content: "\F2F7";
}

.ion-android-share:before {
  content: "\F2F8";
}

.ion-android-share-alt:before {
  content: "\F3AC";
}

.ion-android-star:before {
  content: "\F2FC";
}

.ion-android-star-half:before {
  content: "\F3AD";
}

.ion-android-star-outline:before {
  content: "\F3AE";
}

.ion-android-stopwatch:before {
  content: "\F2FD";
}

.ion-android-subway:before {
  content: "\F3AF";
}

.ion-android-sunny:before {
  content: "\F3B0";
}

.ion-android-sync:before {
  content: "\F3B1";
}

.ion-android-textsms:before {
  content: "\F3B2";
}

.ion-android-time:before {
  content: "\F3B3";
}

.ion-android-train:before {
  content: "\F3B4";
}

.ion-android-unlock:before {
  content: "\F3B5";
}

.ion-android-upload:before {
  content: "\F3B6";
}

.ion-android-volume-down:before {
  content: "\F3B7";
}

.ion-android-volume-mute:before {
  content: "\F3B8";
}

.ion-android-volume-off:before {
  content: "\F3B9";
}

.ion-android-volume-up:before {
  content: "\F3BA";
}

.ion-android-walk:before {
  content: "\F3BB";
}

.ion-android-warning:before {
  content: "\F3BC";
}

.ion-android-watch:before {
  content: "\F3BD";
}

.ion-android-wifi:before {
  content: "\F305";
}

.ion-aperture:before {
  content: "\F313";
}

.ion-archive:before {
  content: "\F102";
}

.ion-arrow-down-a:before {
  content: "\F103";
}

.ion-arrow-down-b:before {
  content: "\F104";
}

.ion-arrow-down-c:before {
  content: "\F105";
}

.ion-arrow-expand:before {
  content: "\F25E";
}

.ion-arrow-graph-down-left:before {
  content: "\F25F";
}

.ion-arrow-graph-down-right:before {
  content: "\F260";
}

.ion-arrow-graph-up-left:before {
  content: "\F261";
}

.ion-arrow-graph-up-right:before {
  content: "\F262";
}

.ion-arrow-left-a:before {
  content: "\F106";
}

.ion-arrow-left-b:before {
  content: "\F107";
}

.ion-arrow-left-c:before {
  content: "\F108";
}

.ion-arrow-move:before {
  content: "\F263";
}

.ion-arrow-resize:before {
  content: "\F264";
}

.ion-arrow-return-left:before {
  content: "\F265";
}

.ion-arrow-return-right:before {
  content: "\F266";
}

.ion-arrow-right-a:before {
  content: "\F109";
}

.ion-arrow-right-b:before {
  content: "\F10A";
}

.ion-arrow-right-c:before {
  content: "\F10B";
}

.ion-arrow-shrink:before {
  content: "\F267";
}

.ion-arrow-swap:before {
  content: "\F268";
}

.ion-arrow-up-a:before {
  content: "\F10C";
}

.ion-arrow-up-b:before {
  content: "\F10D";
}

.ion-arrow-up-c:before {
  content: "\F10E";
}

.ion-asterisk:before {
  content: "\F314";
}

.ion-at:before {
  content: "\F10F";
}

.ion-backspace:before {
  content: "\F3BF";
}

.ion-backspace-outline:before {
  content: "\F3BE";
}

.ion-bag:before {
  content: "\F110";
}

.ion-battery-charging:before {
  content: "\F111";
}

.ion-battery-empty:before {
  content: "\F112";
}

.ion-battery-full:before {
  content: "\F113";
}

.ion-battery-half:before {
  content: "\F114";
}

.ion-battery-low:before {
  content: "\F115";
}

.ion-beaker:before {
  content: "\F269";
}

.ion-beer:before {
  content: "\F26A";
}

.ion-bluetooth:before {
  content: "\F116";
}

.ion-bonfire:before {
  content: "\F315";
}

.ion-bookmark:before {
  content: "\F26B";
}

.ion-bowtie:before {
  content: "\F3C0";
}

.ion-briefcase:before {
  content: "\F26C";
}

.ion-bug:before {
  content: "\F2BE";
}

.ion-calculator:before {
  content: "\F26D";
}

.ion-calendar:before {
  content: "\F117";
}

.ion-camera:before {
  content: "\F118";
}

.ion-card:before {
  content: "\F119";
}

.ion-cash:before {
  content: "\F316";
}

.ion-chatbox:before {
  content: "\F11B";
}

.ion-chatbox-working:before {
  content: "\F11A";
}

.ion-chatboxes:before {
  content: "\F11C";
}

.ion-chatbubble:before {
  content: "\F11E";
}

.ion-chatbubble-working:before {
  content: "\F11D";
}

.ion-chatbubbles:before {
  content: "\F11F";
}

.ion-checkmark:before {
  content: "\F122";
}

.ion-checkmark-circled:before {
  content: "\F120";
}

.ion-checkmark-round:before {
  content: "\F121";
}

.ion-chevron-down:before {
  content: "\F123";
}

.ion-chevron-left:before {
  content: "\F124";
}

.ion-chevron-right:before {
  content: "\F125";
}

.ion-chevron-up:before {
  content: "\F126";
}

.ion-clipboard:before {
  content: "\F127";
}

.ion-clock:before {
  content: "\F26E";
}

.ion-close:before {
  content: "\F12A";
}

.ion-close-circled:before {
  content: "\F128";
}

.ion-close-round:before {
  content: "\F129";
}

.ion-closed-captioning:before {
  content: "\F317";
}

.ion-cloud:before {
  content: "\F12B";
}

.ion-code:before {
  content: "\F271";
}

.ion-code-download:before {
  content: "\F26F";
}

.ion-code-working:before {
  content: "\F270";
}

.ion-coffee:before {
  content: "\F272";
}

.ion-compass:before {
  content: "\F273";
}

.ion-compose:before {
  content: "\F12C";
}

.ion-connection-bars:before {
  content: "\F274";
}

.ion-contrast:before {
  content: "\F275";
}

.ion-crop:before {
  content: "\F3C1";
}

.ion-cube:before {
  content: "\F318";
}

.ion-disc:before {
  content: "\F12D";
}

.ion-document:before {
  content: "\F12F";
}

.ion-document-text:before {
  content: "\F12E";
}

.ion-drag:before {
  content: "\F130";
}

.ion-earth:before {
  content: "\F276";
}

.ion-easel:before {
  content: "\F3C2";
}

.ion-edit:before {
  content: "\F2BF";
}

.ion-egg:before {
  content: "\F277";
}

.ion-eject:before {
  content: "\F131";
}

.ion-email:before {
  content: "\F132";
}

.ion-email-unread:before {
  content: "\F3C3";
}

.ion-erlenmeyer-flask:before {
  content: "\F3C5";
}

.ion-erlenmeyer-flask-bubbles:before {
  content: "\F3C4";
}

.ion-eye:before {
  content: "\F133";
}

.ion-eye-disabled:before {
  content: "\F306";
}

.ion-female:before {
  content: "\F278";
}

.ion-filing:before {
  content: "\F134";
}

.ion-film-marker:before {
  content: "\F135";
}

.ion-fireball:before {
  content: "\F319";
}

.ion-flag:before {
  content: "\F279";
}

.ion-flame:before {
  content: "\F31A";
}

.ion-flash:before {
  content: "\F137";
}

.ion-flash-off:before {
  content: "\F136";
}

.ion-folder:before {
  content: "\F139";
}

.ion-fork:before {
  content: "\F27A";
}

.ion-fork-repo:before {
  content: "\F2C0";
}

.ion-forward:before {
  content: "\F13A";
}

.ion-funnel:before {
  content: "\F31B";
}

.ion-gear-a:before {
  content: "\F13D";
}

.ion-gear-b:before {
  content: "\F13E";
}

.ion-grid:before {
  content: "\F13F";
}

.ion-hammer:before {
  content: "\F27B";
}

.ion-happy:before {
  content: "\F31C";
}

.ion-happy-outline:before {
  content: "\F3C6";
}

.ion-headphone:before {
  content: "\F140";
}

.ion-heart:before {
  content: "\F141";
}

.ion-heart-broken:before {
  content: "\F31D";
}

.ion-help:before {
  content: "\F143";
}

.ion-help-buoy:before {
  content: "\F27C";
}

.ion-help-circled:before {
  content: "\F142";
}

.ion-home:before {
  content: "\F144";
}

.ion-icecream:before {
  content: "\F27D";
}

.ion-image:before {
  content: "\F147";
}

.ion-images:before {
  content: "\F148";
}

.ion-information:before {
  content: "\F14A";
}

.ion-information-circled:before {
  content: "\F149";
}

.ion-ionic:before {
  content: "\F14B";
}

.ion-ios-alarm:before {
  content: "\F3C8";
}

.ion-ios-alarm-outline:before {
  content: "\F3C7";
}

.ion-ios-albums:before {
  content: "\F3CA";
}

.ion-ios-albums-outline:before {
  content: "\F3C9";
}

.ion-ios-americanfootball:before {
  content: "\F3CC";
}

.ion-ios-americanfootball-outline:before {
  content: "\F3CB";
}

.ion-ios-analytics:before {
  content: "\F3CE";
}

.ion-ios-analytics-outline:before {
  content: "\F3CD";
}

.ion-ios-arrow-back:before {
  content: "\F3CF";
}

.ion-ios-arrow-down:before {
  content: "\F3D0";
}

.ion-ios-arrow-forward:before {
  content: "\F3D1";
}

.ion-ios-arrow-left:before {
  content: "\F3D2";
}

.ion-ios-arrow-right:before {
  content: "\F3D3";
}

.ion-ios-arrow-thin-down:before {
  content: "\F3D4";
}

.ion-ios-arrow-thin-left:before {
  content: "\F3D5";
}

.ion-ios-arrow-thin-right:before {
  content: "\F3D6";
}

.ion-ios-arrow-thin-up:before {
  content: "\F3D7";
}

.ion-ios-arrow-up:before {
  content: "\F3D8";
}

.ion-ios-at:before {
  content: "\F3DA";
}

.ion-ios-at-outline:before {
  content: "\F3D9";
}

.ion-ios-barcode:before {
  content: "\F3DC";
}

.ion-ios-barcode-outline:before {
  content: "\F3DB";
}

.ion-ios-baseball:before {
  content: "\F3DE";
}

.ion-ios-baseball-outline:before {
  content: "\F3DD";
}

.ion-ios-basketball:before {
  content: "\F3E0";
}

.ion-ios-basketball-outline:before {
  content: "\F3DF";
}

.ion-ios-bell:before {
  content: "\F3E2";
}

.ion-ios-bell-outline:before {
  content: "\F3E1";
}

.ion-ios-body:before {
  content: "\F3E4";
}

.ion-ios-body-outline:before {
  content: "\F3E3";
}

.ion-ios-bolt:before {
  content: "\F3E6";
}

.ion-ios-bolt-outline:before {
  content: "\F3E5";
}

.ion-ios-book:before {
  content: "\F3E8";
}

.ion-ios-book-outline:before {
  content: "\F3E7";
}

.ion-ios-bookmarks:before {
  content: "\F3EA";
}

.ion-ios-bookmarks-outline:before {
  content: "\F3E9";
}

.ion-ios-box:before {
  content: "\F3EC";
}

.ion-ios-box-outline:before {
  content: "\F3EB";
}

.ion-ios-briefcase:before {
  content: "\F3EE";
}

.ion-ios-briefcase-outline:before {
  content: "\F3ED";
}

.ion-ios-browsers:before {
  content: "\F3F0";
}

.ion-ios-browsers-outline:before {
  content: "\F3EF";
}

.ion-ios-calculator:before {
  content: "\F3F2";
}

.ion-ios-calculator-outline:before {
  content: "\F3F1";
}

.ion-ios-calendar:before {
  content: "\F3F4";
}

.ion-ios-calendar-outline:before {
  content: "\F3F3";
}

.ion-ios-camera:before {
  content: "\F3F6";
}

.ion-ios-camera-outline:before {
  content: "\F3F5";
}

.ion-ios-cart:before {
  content: "\F3F8";
}

.ion-ios-cart-outline:before {
  content: "\F3F7";
}

.ion-ios-chatboxes:before {
  content: "\F3FA";
}

.ion-ios-chatboxes-outline:before {
  content: "\F3F9";
}

.ion-ios-chatbubble:before {
  content: "\F3FC";
}

.ion-ios-chatbubble-outline:before {
  content: "\F3FB";
}

.ion-ios-checkmark:before {
  content: "\F3FF";
}

.ion-ios-checkmark-empty:before {
  content: "\F3FD";
}

.ion-ios-checkmark-outline:before {
  content: "\F3FE";
}

.ion-ios-circle-filled:before {
  content: "\F400";
}

.ion-ios-circle-outline:before {
  content: "\F401";
}

.ion-ios-clock:before {
  content: "\F403";
}

.ion-ios-clock-outline:before {
  content: "\F402";
}

.ion-ios-close:before {
  content: "\F406";
}

.ion-ios-close-empty:before {
  content: "\F404";
}

.ion-ios-close-outline:before {
  content: "\F405";
}

.ion-ios-cloud:before {
  content: "\F40C";
}

.ion-ios-cloud-download:before {
  content: "\F408";
}

.ion-ios-cloud-download-outline:before {
  content: "\F407";
}

.ion-ios-cloud-outline:before {
  content: "\F409";
}

.ion-ios-cloud-upload:before {
  content: "\F40B";
}

.ion-ios-cloud-upload-outline:before {
  content: "\F40A";
}

.ion-ios-cloudy:before {
  content: "\F410";
}

.ion-ios-cloudy-night:before {
  content: "\F40E";
}

.ion-ios-cloudy-night-outline:before {
  content: "\F40D";
}

.ion-ios-cloudy-outline:before {
  content: "\F40F";
}

.ion-ios-cog:before {
  content: "\F412";
}

.ion-ios-cog-outline:before {
  content: "\F411";
}

.ion-ios-color-filter:before {
  content: "\F414";
}

.ion-ios-color-filter-outline:before {
  content: "\F413";
}

.ion-ios-color-wand:before {
  content: "\F416";
}

.ion-ios-color-wand-outline:before {
  content: "\F415";
}

.ion-ios-compose:before {
  content: "\F418";
}

.ion-ios-compose-outline:before {
  content: "\F417";
}

.ion-ios-contact:before {
  content: "\F41A";
}

.ion-ios-contact-outline:before {
  content: "\F419";
}

.ion-ios-copy:before {
  content: "\F41C";
}

.ion-ios-copy-outline:before {
  content: "\F41B";
}

.ion-ios-crop:before {
  content: "\F41E";
}

.ion-ios-crop-strong:before {
  content: "\F41D";
}

.ion-ios-download:before {
  content: "\F420";
}

.ion-ios-download-outline:before {
  content: "\F41F";
}

.ion-ios-drag:before {
  content: "\F421";
}

.ion-ios-email:before {
  content: "\F423";
}

.ion-ios-email-outline:before {
  content: "\F422";
}

.ion-ios-eye:before {
  content: "\F425";
}

.ion-ios-eye-outline:before {
  content: "\F424";
}

.ion-ios-fastforward:before {
  content: "\F427";
}

.ion-ios-fastforward-outline:before {
  content: "\F426";
}

.ion-ios-filing:before {
  content: "\F429";
}

.ion-ios-filing-outline:before {
  content: "\F428";
}

.ion-ios-film:before {
  content: "\F42B";
}

.ion-ios-film-outline:before {
  content: "\F42A";
}

.ion-ios-flag:before {
  content: "\F42D";
}

.ion-ios-flag-outline:before {
  content: "\F42C";
}

.ion-ios-flame:before {
  content: "\F42F";
}

.ion-ios-flame-outline:before {
  content: "\F42E";
}

.ion-ios-flask:before {
  content: "\F431";
}

.ion-ios-flask-outline:before {
  content: "\F430";
}

.ion-ios-flower:before {
  content: "\F433";
}

.ion-ios-flower-outline:before {
  content: "\F432";
}

.ion-ios-folder:before {
  content: "\F435";
}

.ion-ios-folder-outline:before {
  content: "\F434";
}

.ion-ios-football:before {
  content: "\F437";
}

.ion-ios-football-outline:before {
  content: "\F436";
}

.ion-ios-game-controller-a:before {
  content: "\F439";
}

.ion-ios-game-controller-a-outline:before {
  content: "\F438";
}

.ion-ios-game-controller-b:before {
  content: "\F43B";
}

.ion-ios-game-controller-b-outline:before {
  content: "\F43A";
}

.ion-ios-gear:before {
  content: "\F43D";
}

.ion-ios-gear-outline:before {
  content: "\F43C";
}

.ion-ios-glasses:before {
  content: "\F43F";
}

.ion-ios-glasses-outline:before {
  content: "\F43E";
}

.ion-ios-grid-view:before {
  content: "\F441";
}

.ion-ios-grid-view-outline:before {
  content: "\F440";
}

.ion-ios-heart:before {
  content: "\F443";
}

.ion-ios-heart-outline:before {
  content: "\F442";
}

.ion-ios-help:before {
  content: "\F446";
}

.ion-ios-help-empty:before {
  content: "\F444";
}

.ion-ios-help-outline:before {
  content: "\F445";
}

.ion-ios-home:before {
  content: "\F448";
}

.ion-ios-home-outline:before {
  content: "\F447";
}

.ion-ios-infinite:before {
  content: "\F44A";
}

.ion-ios-infinite-outline:before {
  content: "\F449";
}

.ion-ios-information:before {
  content: "\F44D";
}

.ion-ios-information-empty:before {
  content: "\F44B";
}

.ion-ios-information-outline:before {
  content: "\F44C";
}

.ion-ios-ionic-outline:before {
  content: "\F44E";
}

.ion-ios-keypad:before {
  content: "\F450";
}

.ion-ios-keypad-outline:before {
  content: "\F44F";
}

.ion-ios-lightbulb:before {
  content: "\F452";
}

.ion-ios-lightbulb-outline:before {
  content: "\F451";
}

.ion-ios-list:before {
  content: "\F454";
}

.ion-ios-list-outline:before {
  content: "\F453";
}

.ion-ios-location:before {
  content: "\F456";
}

.ion-ios-location-outline:before {
  content: "\F455";
}

.ion-ios-locked:before {
  content: "\F458";
}

.ion-ios-locked-outline:before {
  content: "\F457";
}

.ion-ios-loop:before {
  content: "\F45A";
}

.ion-ios-loop-strong:before {
  content: "\F459";
}

.ion-ios-medical:before {
  content: "\F45C";
}

.ion-ios-medical-outline:before {
  content: "\F45B";
}

.ion-ios-medkit:before {
  content: "\F45E";
}

.ion-ios-medkit-outline:before {
  content: "\F45D";
}

.ion-ios-mic:before {
  content: "\F461";
}

.ion-ios-mic-off:before {
  content: "\F45F";
}

.ion-ios-mic-outline:before {
  content: "\F460";
}

.ion-ios-minus:before {
  content: "\F464";
}

.ion-ios-minus-empty:before {
  content: "\F462";
}

.ion-ios-minus-outline:before {
  content: "\F463";
}

.ion-ios-monitor:before {
  content: "\F466";
}

.ion-ios-monitor-outline:before {
  content: "\F465";
}

.ion-ios-moon:before {
  content: "\F468";
}

.ion-ios-moon-outline:before {
  content: "\F467";
}

.ion-ios-more:before {
  content: "\F46A";
}

.ion-ios-more-outline:before {
  content: "\F469";
}

.ion-ios-musical-note:before {
  content: "\F46B";
}

.ion-ios-musical-notes:before {
  content: "\F46C";
}

.ion-ios-navigate:before {
  content: "\F46E";
}

.ion-ios-navigate-outline:before {
  content: "\F46D";
}

.ion-ios-nutrition:before {
  content: "\F470";
}

.ion-ios-nutrition-outline:before {
  content: "\F46F";
}

.ion-ios-paper:before {
  content: "\F472";
}

.ion-ios-paper-outline:before {
  content: "\F471";
}

.ion-ios-paperplane:before {
  content: "\F474";
}

.ion-ios-paperplane-outline:before {
  content: "\F473";
}

.ion-ios-partlysunny:before {
  content: "\F476";
}

.ion-ios-partlysunny-outline:before {
  content: "\F475";
}

.ion-ios-pause:before {
  content: "\F478";
}

.ion-ios-pause-outline:before {
  content: "\F477";
}

.ion-ios-paw:before {
  content: "\F47A";
}

.ion-ios-paw-outline:before {
  content: "\F479";
}

.ion-ios-people:before {
  content: "\F47C";
}

.ion-ios-people-outline:before {
  content: "\F47B";
}

.ion-ios-person:before {
  content: "\F47E";
}

.ion-ios-person-outline:before {
  content: "\F47D";
}

.ion-ios-personadd:before {
  content: "\F480";
}

.ion-ios-personadd-outline:before {
  content: "\F47F";
}

.ion-ios-photos:before {
  content: "\F482";
}

.ion-ios-photos-outline:before {
  content: "\F481";
}

.ion-ios-pie:before {
  content: "\F484";
}

.ion-ios-pie-outline:before {
  content: "\F483";
}

.ion-ios-pint:before {
  content: "\F486";
}

.ion-ios-pint-outline:before {
  content: "\F485";
}

.ion-ios-play:before {
  content: "\F488";
}

.ion-ios-play-outline:before {
  content: "\F487";
}

.ion-ios-plus:before {
  content: "\F48B";
}

.ion-ios-plus-empty:before {
  content: "\F489";
}

.ion-ios-plus-outline:before {
  content: "\F48A";
}

.ion-ios-pricetag:before {
  content: "\F48D";
}

.ion-ios-pricetag-outline:before {
  content: "\F48C";
}

.ion-ios-pricetags:before {
  content: "\F48F";
}

.ion-ios-pricetags-outline:before {
  content: "\F48E";
}

.ion-ios-printer:before {
  content: "\F491";
}

.ion-ios-printer-outline:before {
  content: "\F490";
}

.ion-ios-pulse:before {
  content: "\F493";
}

.ion-ios-pulse-strong:before {
  content: "\F492";
}

.ion-ios-rainy:before {
  content: "\F495";
}

.ion-ios-rainy-outline:before {
  content: "\F494";
}

.ion-ios-recording:before {
  content: "\F497";
}

.ion-ios-recording-outline:before {
  content: "\F496";
}

.ion-ios-redo:before {
  content: "\F499";
}

.ion-ios-redo-outline:before {
  content: "\F498";
}

.ion-ios-refresh:before {
  content: "\F49C";
}

.ion-ios-refresh-empty:before {
  content: "\F49A";
}

.ion-ios-refresh-outline:before {
  content: "\F49B";
}

.ion-ios-reload:before {
  content: "\F49D";
}

.ion-ios-reverse-camera:before {
  content: "\F49F";
}

.ion-ios-reverse-camera-outline:before {
  content: "\F49E";
}

.ion-ios-rewind:before {
  content: "\F4A1";
}

.ion-ios-rewind-outline:before {
  content: "\F4A0";
}

.ion-ios-rose:before {
  content: "\F4A3";
}

.ion-ios-rose-outline:before {
  content: "\F4A2";
}

.ion-ios-search:before {
  content: "\F4A5";
}

.ion-ios-search-strong:before {
  content: "\F4A4";
}

.ion-ios-settings:before {
  content: "\F4A7";
}

.ion-ios-settings-strong:before {
  content: "\F4A6";
}

.ion-ios-shuffle:before {
  content: "\F4A9";
}

.ion-ios-shuffle-strong:before {
  content: "\F4A8";
}

.ion-ios-skipbackward:before {
  content: "\F4AB";
}

.ion-ios-skipbackward-outline:before {
  content: "\F4AA";
}

.ion-ios-skipforward:before {
  content: "\F4AD";
}

.ion-ios-skipforward-outline:before {
  content: "\F4AC";
}

.ion-ios-snowy:before {
  content: "\F4AE";
}

.ion-ios-speedometer:before {
  content: "\F4B0";
}

.ion-ios-speedometer-outline:before {
  content: "\F4AF";
}

.ion-ios-star:before {
  content: "\F4B3";
}

.ion-ios-star-half:before {
  content: "\F4B1";
}

.ion-ios-star-outline:before {
  content: "\F4B2";
}

.ion-ios-stopwatch:before {
  content: "\F4B5";
}

.ion-ios-stopwatch-outline:before {
  content: "\F4B4";
}

.ion-ios-sunny:before {
  content: "\F4B7";
}

.ion-ios-sunny-outline:before {
  content: "\F4B6";
}

.ion-ios-telephone:before {
  content: "\F4B9";
}

.ion-ios-telephone-outline:before {
  content: "\F4B8";
}

.ion-ios-tennisball:before {
  content: "\F4BB";
}

.ion-ios-tennisball-outline:before {
  content: "\F4BA";
}

.ion-ios-thunderstorm:before {
  content: "\F4BD";
}

.ion-ios-thunderstorm-outline:before {
  content: "\F4BC";
}

.ion-ios-time:before {
  content: "\F4BF";
}

.ion-ios-time-outline:before {
  content: "\F4BE";
}

.ion-ios-timer:before {
  content: "\F4C1";
}

.ion-ios-timer-outline:before {
  content: "\F4C0";
}

.ion-ios-toggle:before {
  content: "\F4C3";
}

.ion-ios-toggle-outline:before {
  content: "\F4C2";
}

.ion-ios-trash:before {
  content: "\F4C5";
}

.ion-ios-trash-outline:before {
  content: "\F4C4";
}

.ion-ios-undo:before {
  content: "\F4C7";
}

.ion-ios-undo-outline:before {
  content: "\F4C6";
}

.ion-ios-unlocked:before {
  content: "\F4C9";
}

.ion-ios-unlocked-outline:before {
  content: "\F4C8";
}

.ion-ios-upload:before {
  content: "\F4CB";
}

.ion-ios-upload-outline:before {
  content: "\F4CA";
}

.ion-ios-videocam:before {
  content: "\F4CD";
}

.ion-ios-videocam-outline:before {
  content: "\F4CC";
}

.ion-ios-volume-high:before {
  content: "\F4CE";
}

.ion-ios-volume-low:before {
  content: "\F4CF";
}

.ion-ios-wineglass:before {
  content: "\F4D1";
}

.ion-ios-wineglass-outline:before {
  content: "\F4D0";
}

.ion-ios-world:before {
  content: "\F4D3";
}

.ion-ios-world-outline:before {
  content: "\F4D2";
}

.ion-ipad:before {
  content: "\F1F9";
}

.ion-iphone:before {
  content: "\F1FA";
}

.ion-ipod:before {
  content: "\F1FB";
}

.ion-jet:before {
  content: "\F295";
}

.ion-key:before {
  content: "\F296";
}

.ion-knife:before {
  content: "\F297";
}

.ion-laptop:before {
  content: "\F1FC";
}

.ion-leaf:before {
  content: "\F1FD";
}

.ion-levels:before {
  content: "\F298";
}

.ion-lightbulb:before {
  content: "\F299";
}

.ion-link:before {
  content: "\F1FE";
}

.ion-load-a:before {
  content: "\F29A";
}

.ion-load-b:before {
  content: "\F29B";
}

.ion-load-c:before {
  content: "\F29C";
}

.ion-load-d:before {
  content: "\F29D";
}

.ion-location:before {
  content: "\F1FF";
}

.ion-lock-combination:before {
  content: "\F4D4";
}

.ion-locked:before {
  content: "\F200";
}

.ion-log-in:before {
  content: "\F29E";
}

.ion-log-out:before {
  content: "\F29F";
}

.ion-loop:before {
  content: "\F201";
}

.ion-magnet:before {
  content: "\F2A0";
}

.ion-male:before {
  content: "\F2A1";
}

.ion-man:before {
  content: "\F202";
}

.ion-map:before {
  content: "\F203";
}

.ion-medkit:before {
  content: "\F2A2";
}

.ion-merge:before {
  content: "\F33F";
}

.ion-mic-a:before {
  content: "\F204";
}

.ion-mic-b:before {
  content: "\F205";
}

.ion-mic-c:before {
  content: "\F206";
}

.ion-minus:before {
  content: "\F209";
}

.ion-minus-circled:before {
  content: "\F207";
}

.ion-minus-round:before {
  content: "\F208";
}

.ion-model-s:before {
  content: "\F2C1";
}

.ion-monitor:before {
  content: "\F20A";
}

.ion-more:before {
  content: "\F20B";
}

.ion-mouse:before {
  content: "\F340";
}

.ion-music-note:before {
  content: "\F20C";
}

.ion-navicon:before {
  content: "\F20E";
}

.ion-navicon-round:before {
  content: "\F20D";
}

.ion-navigate:before {
  content: "\F2A3";
}

.ion-network:before {
  content: "\F341";
}

.ion-no-smoking:before {
  content: "\F2C2";
}

.ion-nuclear:before {
  content: "\F2A4";
}

.ion-outlet:before {
  content: "\F342";
}

.ion-paintbrush:before {
  content: "\F4D5";
}

.ion-paintbucket:before {
  content: "\F4D6";
}

.ion-paper-airplane:before {
  content: "\F2C3";
}

.ion-paperclip:before {
  content: "\F20F";
}

.ion-pause:before {
  content: "\F210";
}

.ion-person:before {
  content: "\F213";
}

.ion-person-add:before {
  content: "\F211";
}

.ion-person-stalker:before {
  content: "\F212";
}

.ion-pie-graph:before {
  content: "\F2A5";
}

.ion-pin:before {
  content: "\F2A6";
}

.ion-pinpoint:before {
  content: "\F2A7";
}

.ion-pizza:before {
  content: "\F2A8";
}

.ion-plane:before {
  content: "\F214";
}

.ion-planet:before {
  content: "\F343";
}

.ion-play:before {
  content: "\F215";
}

.ion-playstation:before {
  content: "\F30A";
}

.ion-plus:before {
  content: "\F218";
}

.ion-plus-circled:before {
  content: "\F216";
}

.ion-plus-round:before {
  content: "\F217";
}

.ion-podium:before {
  content: "\F344";
}

.ion-pound:before {
  content: "\F219";
}

.ion-power:before {
  content: "\F2A9";
}

.ion-pricetag:before {
  content: "\F2AA";
}

.ion-pricetags:before {
  content: "\F2AB";
}

.ion-printer:before {
  content: "\F21A";
}

.ion-pull-request:before {
  content: "\F345";
}

.ion-qr-scanner:before {
  content: "\F346";
}

.ion-quote:before {
  content: "\F347";
}

.ion-radio-waves:before {
  content: "\F2AC";
}

.ion-record:before {
  content: "\F21B";
}

.ion-refresh:before {
  content: "\F21C";
}

.ion-reply:before {
  content: "\F21E";
}

.ion-reply-all:before {
  content: "\F21D";
}

.ion-ribbon-a:before {
  content: "\F348";
}

.ion-ribbon-b:before {
  content: "\F349";
}

.ion-sad:before {
  content: "\F34A";
}

.ion-sad-outline:before {
  content: "\F4D7";
}

.ion-scissors:before {
  content: "\F34B";
}

.ion-search:before {
  content: "\F21F";
}

.ion-settings:before {
  content: "\F2AD";
}

.ion-share:before {
  content: "\F220";
}

.ion-shuffle:before {
  content: "\F221";
}

.ion-skip-backward:before {
  content: "\F222";
}

.ion-skip-forward:before {
  content: "\F223";
}

.ion-social-android:before {
  content: "\F225";
}

.ion-social-android-outline:before {
  content: "\F224";
}

.ion-social-angular:before {
  content: "\F4D9";
}

.ion-social-angular-outline:before {
  content: "\F4D8";
}

.ion-social-apple:before {
  content: "\F227";
}

.ion-social-apple-outline:before {
  content: "\F226";
}

.ion-social-bitcoin:before {
  content: "\F2AF";
}

.ion-social-bitcoin-outline:before {
  content: "\F2AE";
}

.ion-social-buffer:before {
  content: "\F229";
}

.ion-social-buffer-outline:before {
  content: "\F228";
}

.ion-social-chrome:before {
  content: "\F4DB";
}

.ion-social-chrome-outline:before {
  content: "\F4DA";
}

.ion-social-codepen:before {
  content: "\F4DD";
}

.ion-social-codepen-outline:before {
  content: "\F4DC";
}

.ion-social-css3:before {
  content: "\F4DF";
}

.ion-social-css3-outline:before {
  content: "\F4DE";
}

.ion-social-designernews:before {
  content: "\F22B";
}

.ion-social-designernews-outline:before {
  content: "\F22A";
}

.ion-social-dribbble:before {
  content: "\F22D";
}

.ion-social-dribbble-outline:before {
  content: "\F22C";
}

.ion-social-dropbox:before {
  content: "\F22F";
}

.ion-social-dropbox-outline:before {
  content: "\F22E";
}

.ion-social-euro:before {
  content: "\F4E1";
}

.ion-social-euro-outline:before {
  content: "\F4E0";
}

.ion-social-facebook:before {
  content: "\F231";
}

.ion-social-facebook-outline:before {
  content: "\F230";
}

.ion-social-foursquare:before {
  content: "\F34D";
}

.ion-social-foursquare-outline:before {
  content: "\F34C";
}

.ion-social-freebsd-devil:before {
  content: "\F2C4";
}

.ion-social-github:before {
  content: "\F233";
}

.ion-social-github-outline:before {
  content: "\F232";
}

.ion-social-google:before {
  content: "\F34F";
}

.ion-social-google-outline:before {
  content: "\F34E";
}

.ion-social-googleplus:before {
  content: "\F235";
}

.ion-social-googleplus-outline:before {
  content: "\F234";
}

.ion-social-hackernews:before {
  content: "\F237";
}

.ion-social-hackernews-outline:before {
  content: "\F236";
}

.ion-social-html5:before {
  content: "\F4E3";
}

.ion-social-html5-outline:before {
  content: "\F4E2";
}

.ion-social-instagram:before {
  content: "\F351";
}

.ion-social-instagram-outline:before {
  content: "\F350";
}

.ion-social-javascript:before {
  content: "\F4E5";
}

.ion-social-javascript-outline:before {
  content: "\F4E4";
}

.ion-social-linkedin:before {
  content: "\F239";
}

.ion-social-linkedin-outline:before {
  content: "\F238";
}

.ion-social-markdown:before {
  content: "\F4E6";
}

.ion-social-nodejs:before {
  content: "\F4E7";
}

.ion-social-octocat:before {
  content: "\F4E8";
}

.ion-social-pinterest:before {
  content: "\F2B1";
}

.ion-social-pinterest-outline:before {
  content: "\F2B0";
}

.ion-social-python:before {
  content: "\F4E9";
}

.ion-social-reddit:before {
  content: "\F23B";
}

.ion-social-reddit-outline:before {
  content: "\F23A";
}

.ion-social-rss:before {
  content: "\F23D";
}

.ion-social-rss-outline:before {
  content: "\F23C";
}

.ion-social-sass:before {
  content: "\F4EA";
}

.ion-social-skype:before {
  content: "\F23F";
}

.ion-social-skype-outline:before {
  content: "\F23E";
}

.ion-social-snapchat:before {
  content: "\F4EC";
}

.ion-social-snapchat-outline:before {
  content: "\F4EB";
}

.ion-social-tumblr:before {
  content: "\F241";
}

.ion-social-tumblr-outline:before {
  content: "\F240";
}

.ion-social-tux:before {
  content: "\F2C5";
}

.ion-social-twitch:before {
  content: "\F4EE";
}

.ion-social-twitch-outline:before {
  content: "\F4ED";
}

.ion-social-twitter:before {
  content: "\F243";
}

.ion-social-twitter-outline:before {
  content: "\F242";
}

.ion-social-usd:before {
  content: "\F353";
}

.ion-social-usd-outline:before {
  content: "\F352";
}

.ion-social-vimeo:before {
  content: "\F245";
}

.ion-social-vimeo-outline:before {
  content: "\F244";
}

.ion-social-whatsapp:before {
  content: "\F4F0";
}

.ion-social-whatsapp-outline:before {
  content: "\F4EF";
}

.ion-social-windows:before {
  content: "\F247";
}

.ion-social-windows-outline:before {
  content: "\F246";
}

.ion-social-wordpress:before {
  content: "\F249";
}

.ion-social-wordpress-outline:before {
  content: "\F248";
}

.ion-social-yahoo:before {
  content: "\F24B";
}

.ion-social-yahoo-outline:before {
  content: "\F24A";
}

.ion-social-yen:before {
  content: "\F4F2";
}

.ion-social-yen-outline:before {
  content: "\F4F1";
}

.ion-social-youtube:before {
  content: "\F24D";
}

.ion-social-youtube-outline:before {
  content: "\F24C";
}

.ion-soup-can:before {
  content: "\F4F4";
}

.ion-soup-can-outline:before {
  content: "\F4F3";
}

.ion-speakerphone:before {
  content: "\F2B2";
}

.ion-speedometer:before {
  content: "\F2B3";
}

.ion-spoon:before {
  content: "\F2B4";
}

.ion-star:before {
  content: "\F24E";
}

.ion-stats-bars:before {
  content: "\F2B5";
}

.ion-steam:before {
  content: "\F30B";
}

.ion-stop:before {
  content: "\F24F";
}

.ion-thermometer:before {
  content: "\F2B6";
}

.ion-thumbsdown:before {
  content: "\F250";
}

.ion-thumbsup:before {
  content: "\F251";
}

.ion-toggle:before {
  content: "\F355";
}

.ion-toggle-filled:before {
  content: "\F354";
}

.ion-transgender:before {
  content: "\F4F5";
}

.ion-trash-a:before {
  content: "\F252";
}

.ion-trash-b:before {
  content: "\F253";
}

.ion-trophy:before {
  content: "\F356";
}

.ion-tshirt:before {
  content: "\F4F7";
}

.ion-tshirt-outline:before {
  content: "\F4F6";
}

.ion-umbrella:before {
  content: "\F2B7";
}

.ion-university:before {
  content: "\F357";
}

.ion-unlocked:before {
  content: "\F254";
}

.ion-upload:before {
  content: "\F255";
}

.ion-usb:before {
  content: "\F2B8";
}

.ion-videocamera:before {
  content: "\F256";
}

.ion-volume-high:before {
  content: "\F257";
}

.ion-volume-low:before {
  content: "\F258";
}

.ion-volume-medium:before {
  content: "\F259";
}

.ion-volume-mute:before {
  content: "\F25A";
}

.ion-wand:before {
  content: "\F358";
}

.ion-waterdrop:before {
  content: "\F25B";
}

.ion-wifi:before {
  content: "\F25C";
}

.ion-wineglass:before {
  content: "\F2B9";
}

.ion-woman:before {
  content: "\F25D";
}

.ion-wrench:before {
  content: "\F2BA";
}

.ion-xbox:before {
  content: "\F30C";
}
@font-face {
  font-family: "Icomoon";
  src: url(/fonts/icomoon.eot?7c8b60c9ec70bb2f28ff8b7211386da8);
  src:
    url(/fonts/icomoon.eot?7c8b60c9ec70bb2f28ff8b7211386da8?#iefix)
      format("embedded-opentype"),
    url(/fonts/icomoon.ttf?19d7a91b28ec220a4364a722a35ad49b) format("truetype"),
    url(/fonts/icomoon.woff?693ff7e20949df86da05491dac9b58b6) format("woff"),
    url(/fonts/icomoon.svg?12bbe3076a0d409f31cd575d83cfcc57#icomoon)
      format("svg");
  font-weight: normal;
  font-style: normal;
}

/* Products */
.icon-product-automation:before,
.icon-auto_awesome:before {
  content: "\E933";
}

.icon-product-digital:before,
.icon-laptop:before {
  content: "\F011";
}

.icon-product-local:before,
.icon-location:before {
  content: "\F010";
}

.icon-product-social:before,
.icon-paper-plane1:before {
  content: "\F012";
}

.icon-product-postcards:before,
.icon-mailbox:before {
  content: "\F014";
}

.icon-product-pages:before,
.icon-list-alt:before {
  content: "\F022";
}

/* Table */
.icon-sort:before,
.icon-unsorted:before {
  content: "\F0DC";
}

.icon-sort-desc:before,
.icon-sort-down:before {
  content: "\F0DD";
}

.icon-sort-asc:before,
.icon-sort-up:before {
  content: "\F0DE";
}

/* Social */
.icon-instagram:before {
  content: "\E903";
}

.icon-podcast:before {
  content: "\E905";
  font-size: 93.4%;
}

/* Various */
.icon-truck:before {
  content: "\E9B0";
}

.icon-envelope:before {
  content: "\F013";
}

.icon-windows:before {
  content: "\E900";
}

.icon-home:before {
  content: "\E901";
}

.icon-graph:before {
  content: "\E902";
}

.icon-calendar:before,
.icon-insert_invitation:before {
  content: "\E932";
}

.icon-calendar-2:before,
.icon-calendar_today:before {
  content: "\E930";
}

.icon-calendar-3:before,
.icon-date_range:before {
  content: "\E934";
}

.icon-list:before,
.icon-view_list:before {
  content: "\E931";
}

.icon-agenda:before,
.icon-view_agenda:before {
  content: "\E92F";
}

.icon-library:before,
.icon-books:before {
  content: "\E904";
}

.icon-library-2:before,
.icon-books-2:before,
.icon-library_books:before {
  content: "\E938";
}

.icon-book:before,
.icon-menu_book:before {
  content: "\E957";
}

.icon-auto-fix-high:before,
.icon-auto_fix_high:before {
  content: "\E935";
}

.icon-pin:before,
.icon-push_pin:before {
  content: "\E937";
}

.icon-eye:before {
  content: "\E93A";
}

.icon-group:before {
  content: "\E939";
}

.icon-group-add:before,
.icon-group_add:before {
  content: "\E943";
}

.icon-person-add:before,
.icon-person_add:before {
  content: "\E945";
}

.icon-folder:before {
  content: "\E936";
}

.icon-folder-open:before,
.icon-folder_open:before {
  content: "\E93B";
}

.icon-folder-add:before,
.icon-folder-new:before,
.icon-create_new_folder:before {
  content: "\E93C";
}

.icon-folder-move:before,
.icon-drive_file_move:before {
  content: "\E942";
}

.icon-folder-move-2:before,
.icon-drive_file_move_outline:before {
  content: "\E941";
}

.icon-check:before {
  content: "\E973";
}

.icon-checkbox:before,
.icon-check_box_outline_blank:before {
  content: "\E93F";
}

.icon-checkbox-checked:before,
.icon-check_box:before {
  content: "\E940";
}

.icon-checkbox-2:before,
.icon-panorama_fisheye:before {
  content: "\E93D";
}

.icon-checkbox-2-checked:before,
.icon-check_circle:before {
  content: "\E93E";
}

.icon-page-add:before,
.icon-note_add:before {
  content: "\E944";
}

.icon-drag:before,
.icon-drag_handle:before {
  content: "\E946";
}

.icon-drag-2:before,
.icon-drag_indicator:before {
  content: "\E947";
}

.icon-more:before,
.icon-keyboard_control:before {
  content: "\E948";
}

.icon-more-2:before,
.icon-more_vert:before {
  content: "\E949";
}

.icon-contacts:before {
  content: "\E94C";
}

.icon-devices:before,
.icon-important_devices:before {
  content: "\E94A";
}

.icon-fact-check:before,
.icon-fact_check:before {
  content: "\E94B";
}

.icon-list-check:before,
.icon-playlist_add_check:before {
  content: "\E956";
}

.icon-list-add:before,
.icon-playlist_add:before {
  content: "\E96B";
}

.icon-archive:before {
  content: "\E94D";
}

.icon-unarchive:before {
  content: "\E94E";
}

.icon-pause:before {
  content: "\E94F";
}

.icon-history:before {
  content: "\E952";
}

.icon-show:before,
.icon-remove_red_eye:before {
  content: "\E950";
}

.icon-hide:before,
.icon-visibility_off:before {
  content: "\E951";
}

.icon-play-circle:before,
.icon-play_circle_filled:before {
  content: "\E953";
}

.icon-play-circle-2:before,
.icon-play_circle_outline:before {
  content: "\E954";
}

.icon-file-xls:before,
.icon-document-file-xls:before {
  content: "\E959";
}

.icon-file-upload:before,
.icon-upload_file:before {
  content: "\E958";
}

.icon-chart-bar:before,
.icon-bar_chart:before {
  content: "\E95C";
}

.icon-chart-pie:before,
.icon-pie_chart:before {
  content: "\E95D";
}

.icon-chart-line:before,
.icon-show_chart:before {
  content: "\E95B";
}

.icon-chart-line-2:before,
.icon-stacked_line_chart:before {
  content: "\E95A";
}

.icon-send:before {
  content: "\E95F";
}

.icon-emoji:before,
.icon-insert_emoticon:before {
  content: "\E960";
}

.icon-emoji-2:before,
.icon-emoji_emotions:before {
  content: "\E961";
}

.icon-message:before {
  content: "\E962";
}

.icon-message-2:before,
.icon-textsms:before {
  content: "\E963";
}

.icon-phone:before,
.icon-call:before {
  content: "\E964";
}

.icon-phone-end:before,
.icon-call_end:before {
  content: "\E965";
}

.icon-phone-call:before,
.icon-phone_in_talk:before {
  content: "\E96A";
}

.icon-mic:before {
  content: "\E967";
}

.icon-mute:before,
.icon-mic-off:before,
.icon-mic_off:before {
  content: "\E966";
}

.icon-speaker:before,
.icon-volume_up:before {
  content: "\E968";
}

.icon-filter:before,
.icon-filter_list:before {
  content: "\E969";
}

.icon-add:before {
  content: "\E970";
}

.icon-add-2:before,
.icon-add_circle:before {
  content: "\E971";
}

.icon-add-3:before,
.icon-add_circle_outline:before {
  content: "\E972";
}

.icon-remove:before {
  content: "\E96D";
}

.icon-remove-2:before,
.icon-remove_circle:before {
  content: "\E96E";
}

.icon-remove-3:before,
.icon-remove_circle_outline:before {
  content: "\E96F";
}

.icon-link:before {
  content: "\E976";
}

.icon-text:before,
.icon-text_snippet:before {
  content: "\E975";
}

/* WYSIWYG */
.icon-layout:before,
.icon-web:before {
  content: "\E91A";
}

.icon-layout-2:before {
  content: "\E922";
}

.icon-box:before {
  content: "\E92E";
}

.icon-profile:before {
  content: "\E955";
}

.icon-upload:before,
.icon-cloud_upload:before {
  content: "\E91B";
}

.icon-upload-2:before,
.icon-upload-cloud:before {
  content: "\E923";
}

.icon-image:before,
.icon-insert_photo:before {
  content: "\E921";
}

.icon-image-2:before {
  content: "\E924";
}

.icon-type:before,
.icon-format_size:before {
  content: "\E91F";
}

.icon-type-2:before {
  content: "\E920";
}

.icon-undo:before {
  content: "\E907";
}

.icon-redo:before {
  content: "\E906";
}

.icon-copy:before,
.icon-content_copy:before {
  content: "\E912";
}

.icon-unlocked:before,
.icon-lock_open:before {
  content: "\E91D";
}

.icon-locked:before,
.icon-lock_outline:before {
  content: "\E91E";
}

.icon-edit:before,
.icon-create:before {
  content: "\E92C";
}

.icon-edit-2:before {
  content: "\E96C";
}

.icon-delete:before {
  content: "\E925";
}

.icon-rotate:before,
.icon-loop:before {
  content: "\E926";
}

.icon-move:before {
  content: "\E92D";
}

.icon-crop:before {
  content: "\E927";
}

.icon-settings:before,
.icon-tune:before {
  content: "\E928";
}

.icon-settings-2:before,
.icon-sliders:before {
  content: "\E95E";
}

.icon-settings-3:before,
.icon-gear:before,
.icon-settings_X:before {
  content: "\E974";
}

.icon-fit:before,
.icon-fit_screen:before {
  content: "\E92B";
}

.icon-expand:before,
.icon-fullscreen:before {
  content: "\E929";
}

.icon-collapse:before,
.icon-fullscreen_exit:before {
  content: "\E92A";
}

.icon-bold:before,
.icon-format_bold:before {
  content: "\E90F";
}

.icon-italic:before,
.icon-format_italic:before {
  content: "\E910";
}

.icon-underline:before,
.icon-format_underlined:before {
  content: "\E911";
}

.icon-strikethrough:before,
.icon-strikethrough_s:before {
  content: "\E913";
}

.icon-caps:before,
.icon-font_download:before {
  content: "\E91C";
}

.icon-text-left:before,
.icon-format_align_left:before {
  content: "\E918";
}

.icon-text-center:before,
.icon-format_align_center:before {
  content: "\E916";
}

.icon-text-right:before,
.icon-format_align_right:before {
  content: "\E919";
}

.icon-text-justify:before,
.icon-format_align_justify:before {
  content: "\E917";
}

.icon-align-vertical-top:before,
.icon-align_vertical_top:before {
  content: "\E90D";
}

.icon-align-vertical-center:before,
.icon-align_vertical_center:before {
  content: "\E90C";
}

.icon-align-vertical-bottom:before,
.icon-align_vertical_bottom:before {
  content: "\E909";
}

.icon-align-horizontal-left:before,
.icon-align_horizontal_left:before {
  content: "\E90A";
}

.icon-align-horizontal-center:before,
.icon-align_horizontal_center:before {
  content: "\E908";
}

.icon-align-horizontal-right:before,
.icon-align_horizontal_right:before {
  content: "\E90B";
}

.icon-vertical-align-top:before,
.icon-vertical_align_top:before {
  content: "\E915";
}

.icon-vertical-align-center:before,
.icon-vertical_align_center:before {
  content: "\E914";
}

.icon-vertical-align-bottom:before,
.icon-vertical_align_bottom:before {
  content: "\E90E";
}
@font-face {
  font-family: "TeX-Gyre-Adventor";
  src: url(/fonts/texgyreadventor-regular-webfont.eot?ffb34d8174f1d32f399b4dcee193154c);
  src:
    url(/fonts/texgyreadventor-regular-webfont.eot?ffb34d8174f1d32f399b4dcee193154c?#iefix)
      format("embedded-opentype"),
    url(/fonts/texgyreadventor-regular-webfont.woff?b4a835de6f8a03ec3cec27063fe24459)
      format("woff"),
    url(/fonts/texgyreadventor-regular-webfont.ttf?15e713b92b8e8cbba8865c3731e31b24)
      format("truetype"),
    url(/fonts/texgyreadventor-regular-webfont.svg?94e11d479a3846e9ca2c8721fb44bcbd#texgyreadventorregular)
      format("svg");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "TeX-Gyre-Adventor";
  src: url(/fonts/texgyreadventor-bold-webfont.eot?a4b357b377b2d0ab2346a29349212022);
  src:
    url(/fonts/texgyreadventor-bold-webfont.eot?a4b357b377b2d0ab2346a29349212022?#iefix)
      format("embedded-opentype"),
    url(/fonts/texgyreadventor-bold-webfont.woff?756caa3a3a4349e0cf0c71b9608404eb)
      format("woff"),
    url(/fonts/texgyreadventor-bold-webfont.ttf?871ac5c67ae0a0a2c77fe8881dc797aa)
      format("truetype"),
    url(/fonts/texgyreadventor-bold-webfont.svg?c329964ed54189639771a7ff92fa50ec#texgyreadventorbold)
      format("svg");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: "TeX-Gyre-Adventor";
  src: url(/fonts/texgyreadventor-italic-webfont.eot?4152634615df807699915454b3e377c5);
  src:
    url(/fonts/texgyreadventor-italic-webfont.eot?4152634615df807699915454b3e377c5?#iefix)
      format("embedded-opentype"),
    url(/fonts/texgyreadventor-italic-webfont.woff?c62deebe0a99b671e01d09b78e2b4590)
      format("woff"),
    url(/fonts/texgyreadventor-italic-webfont.ttf?86575bcad5ffde28b5ebc925b579194d)
      format("truetype"),
    url(/fonts/texgyreadventor-italic-webfont.svg?0cf01356fad28facd1b6aa1052cd3864#texgyreadventoritalic)
      format("svg");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: "TeX-Gyre-Adventor";
  src: url(/fonts/texgyreadventor-bolditalic-webfont.eot?07fb6c888fac5c93d9b65df87bbcfeb9);
  src:
    url(/fonts/texgyreadventor-bolditalic-webfont.eot?07fb6c888fac5c93d9b65df87bbcfeb9?#iefix)
      format("embedded-opentype"),
    url(/fonts/texgyreadventor-bolditalic-webfont.woff?f17394ded21fbfac54ab81a1ea032d73)
      format("woff"),
    url(/fonts/texgyreadventor-bolditalic-webfont.ttf?b3440f4c43708ebc62c91506d765b002)
      format("truetype"),
    url(/fonts/texgyreadventor-bolditalic-webfont.svg?add5de7ba411679b7a73860b3b79a485#texgyreadventorbold_italic)
      format("svg");
  font-weight: bold;
  font-style: italic;
}
@charset "UTF-8";
/**
 * Foundation for Sites by ZURB
 * Version 6.3.1
 * foundation.zurb.com
 * Licensed under MIT Open Source
 */
/*! normalize-scss | MIT/GPLv2 License | bit.ly/normalize-scss */
/* Document
       ========================================================================== */
/**
     * 1. Change the default font family in all browsers (opinionated).
     * 2. Correct the line height in all browsers.
     * 3. Prevent adjustments of font size after orientation changes in
     *    IE on Windows Phone and in iOS.
     */
html {
  font-family: sans-serif;
  /* 1 */
  line-height: 1.15;
  /* 2 */
  -ms-text-size-adjust: 100%;
  /* 3 */
  -webkit-text-size-adjust: 100%;
  /* 3 */
}

/* Sections
       ========================================================================== */
/**
     * Remove the margin in all browsers (opinionated).
     */
body {
  margin: 0;
}

/**
     * Add the correct display in IE 9-.
     */
article,
aside,
footer,
header,
nav,
section {
  display: block;
}

/**
     * Correct the font size and margin on `h1` elements within `section` and
     * `article` contexts in Chrome, Firefox, and Safari.
     */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
       ========================================================================== */
/**
     * Add the correct display in IE 9-.
     */
figcaption,
figure {
  display: block;
}

/**
     * Add the correct margin in IE 8.
     */
figure {
  margin: 1em 40px;
}

/**
     * 1. Add the correct box sizing in Firefox.
     * 2. Show the overflow in Edge and IE.
     */
hr {
  box-sizing: content-box;
  /* 1 */
  height: 0;
  /* 1 */
  overflow: visible;
  /* 2 */
}

/**
     * Add the correct display in IE.
     */
main {
  display: block;
}

/**
     * 1. Correct the inheritance and scaling of font size in all browsers.
     * 2. Correct the odd `em` font sizing in all browsers.
     */
pre {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/* Links
       ========================================================================== */
/**
     * 1. Remove the gray background on active links in IE 10.
     * 2. Remove gaps in links underline in iOS 8+ and Safari 8+.
     */
a {
  background-color: transparent;
  /* 1 */
  -webkit-text-decoration-skip: objects;
  /* 2 */
}

/**
     * Remove the outline on focused links when they are also active or hovered
     * in all browsers (opinionated).
     */
a:active,
a:hover {
  outline-width: 0;
}

/* Text-level semantics
       ========================================================================== */
/**
     * 1. Remove the bottom border in Firefox 39-.
     * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
     */
abbr[title] {
  border-bottom: none;
  /* 1 */
  text-decoration: underline;
  /* 2 */
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  /* 2 */
}

/**
     * Prevent the duplicate application of `bolder` by the next rule in Safari 6.
     */
b,
strong {
  font-weight: inherit;
}

/**
     * Add the correct font weight in Chrome, Edge, and Safari.
     */
b,
strong {
  font-weight: bolder;
}

/**
     * 1. Correct the inheritance and scaling of font size in all browsers.
     * 2. Correct the odd `em` font sizing in all browsers.
     */
code,
kbd,
samp {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/**
     * Add the correct font style in Android 4.3-.
     */
dfn {
  font-style: italic;
}

/**
     * Add the correct background and color in IE 9-.
     */
mark {
  background-color: #ff0;
  color: #000;
}

/**
     * Add the correct font size in all browsers.
     */
small {
  font-size: 80%;
}

/**
     * Prevent `sub` and `sup` elements from affecting the line height in
     * all browsers.
     */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
       ========================================================================== */
/**
     * Add the correct display in IE 9-.
     */
audio,
video {
  display: inline-block;
}

/**
     * Add the correct display in iOS 4-7.
     */
audio:not([controls]) {
  display: none;
  height: 0;
}

/**
     * Remove the border on images inside links in IE 10-.
     */
img {
  border-style: none;
}

/**
     * Hide the overflow in IE.
     */
svg:not(:root) {
  overflow: hidden;
}

/* Forms
       ========================================================================== */
/**
     * 1. Change the font styles in all browsers (opinionated).
     * 2. Remove the margin in Firefox and Safari.
     */
button,
input,
optgroup,
select,
textarea {
  font-family: sans-serif;
  /* 1 */
  font-size: 100%;
  /* 1 */
  line-height: 1.15;
  /* 1 */
  margin: 0;
  /* 2 */
}

/**
     * Show the overflow in IE.
     */
button {
  overflow: visible;
}

/**
     * Remove the inheritance of text transform in Edge, Firefox, and IE.
     * 1. Remove the inheritance of text transform in Firefox.
     */
button,
select {
  /* 1 */
  text-transform: none;
}

/**
     * 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`
     *    controls in Android 4.
     * 2. Correct the inability to style clickable types in iOS and Safari.
     */
button,
html [type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  /**
         * Remove the inner border and padding in Firefox.
         */
  /**
         * Restore the focus styles unset by the previous rule.
         */
}
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
     * Show the overflow in Edge.
     */
input {
  overflow: visible;
}

/**
     * 1. Add the correct box sizing in IE 10-.
     * 2. Remove the padding in IE 10-.
     */
[type="checkbox"],
[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}

/**
     * Correct the cursor style of increment and decrement buttons in Chrome.
     */
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
     * 1. Correct the odd appearance in Chrome and Safari.
     * 2. Correct the outline style in Safari.
     */
[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
  /**
         * Remove the inner padding and cancel buttons in Chrome and Safari on macOS.
         */
}
[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
     * 1. Correct the inability to style clickable types in iOS and Safari.
     * 2. Change font properties to `inherit` in Safari.
     */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/**
     * Change the border, margin, and padding in all browsers (opinionated).
     */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

/**
     * 1. Correct the text wrapping in Edge and IE.
     * 2. Correct the color inheritance from `fieldset` elements in IE.
     * 3. Remove the padding so developers are not caught out when they zero out
     *    `fieldset` elements in all browsers.
     */
legend {
  box-sizing: border-box;
  /* 1 */
  display: table;
  /* 1 */
  max-width: 100%;
  /* 1 */
  padding: 0;
  /* 3 */
  color: inherit;
  /* 2 */
  white-space: normal;
  /* 1 */
}

/**
     * 1. Add the correct display in IE 9-.
     * 2. Add the correct vertical alignment in Chrome, Firefox, and Opera.
     */
progress {
  display: inline-block;
  /* 1 */
  vertical-align: baseline;
  /* 2 */
}

/**
     * Remove the default vertical scrollbar in IE.
     */
textarea {
  overflow: auto;
}

/* Interactive
       ========================================================================== */
/*
     * Add the correct display in Edge, IE, and Firefox.
     */
details {
  display: block;
}

/*
     * Add the correct display in all browsers.
     */
summary {
  display: list-item;
}

/*
     * Add the correct display in IE 9-.
     */
menu {
  display: block;
}

/* Scripting
       ========================================================================== */
/**
     * Add the correct display in IE 9-.
     */
canvas {
  display: inline-block;
}

/**
     * Add the correct display in IE.
     */
template {
  display: none;
}

/* Hidden
       ========================================================================== */
/**
     * Add the correct display in IE 10-.
     */
[hidden] {
  display: none;
}

.foundation-mq {
  font-family: "small=0em&medium=40em&large=64em&xlarge=75em&xxlarge=90em";
}

html {
  box-sizing: border-box;
  font-size: 100%;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

body {
  margin: 0;
  padding: 0;
  background: #fefefe;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-weight: normal;
  line-height: 1.5;
  color: #0a0a0a;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img {
  display: inline-block;
  vertical-align: middle;
  max-width: 100%;
  height: auto;
  -ms-interpolation-mode: bicubic;
}

textarea {
  height: auto;
  min-height: 50px;
  border-radius: 0;
}

select {
  box-sizing: border-box;
  width: 100%;
  border-radius: 0;
}

.map_canvas img,
.map_canvas embed,
.map_canvas object,
.mqa-display img,
.mqa-display embed,
.mqa-display object {
  max-width: none !important;
}

button {
  padding: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  border-radius: 0;
  background: transparent;
  line-height: 1;
}
[data-whatinput="mouse"] button {
  outline: 0;
}

pre {
  overflow: auto;
}

.is-visible {
  display: block !important;
}

.is-hidden {
  display: none !important;
}

.row {
  max-width: 75rem;
  margin-right: auto;
  margin-left: auto;
}
.row::before,
.row::after {
  display: table;
  content: " ";
}
.row::after {
  clear: both;
}
.row.collapse > .column,
.row.collapse > .columns {
  padding-right: 0;
  padding-left: 0;
}
.row .row {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}
@media print, screen and (min-width: 40em) {
  .row .row {
    margin-right: -0.9375rem;
    margin-left: -0.9375rem;
  }
}
@media print, screen and (min-width: 64em) {
  .row .row {
    margin-right: -0.9375rem;
    margin-left: -0.9375rem;
  }
}
.row .row.collapse {
  margin-right: 0;
  margin-left: 0;
}
.row.expanded {
  max-width: none;
}
.row.expanded .row {
  margin-right: auto;
  margin-left: auto;
}
.row:not(.expanded) .row {
  max-width: none;
}
.row.gutter-small > .column,
.row.gutter-small > .columns {
  padding-right: 0.625rem;
  padding-left: 0.625rem;
}
.row.gutter-medium > .column,
.row.gutter-medium > .columns {
  padding-right: 0.9375rem;
  padding-left: 0.9375rem;
}

.column,
.columns {
  width: 100%;
  float: left;
  padding-right: 0.625rem;
  padding-left: 0.625rem;
}
@media print, screen and (min-width: 40em) {
  .column,
  .columns {
    padding-right: 0.9375rem;
    padding-left: 0.9375rem;
  }
}
.column:last-child:not(:first-child),
.columns:last-child:not(:first-child) {
  float: right;
}
.column.end:last-child:last-child,
.end.columns:last-child:last-child {
  float: left;
}

.column.row.row,
.row.row.columns {
  float: none;
}

.row .column.row.row,
.row .row.row.columns {
  margin-right: 0;
  margin-left: 0;
  padding-right: 0;
  padding-left: 0;
}

.small-1 {
  width: 8.33333%;
}

.small-push-1 {
  position: relative;
  left: 8.33333%;
}

.small-pull-1 {
  position: relative;
  left: -8.33333%;
}

.small-offset-0 {
  margin-left: 0%;
}

.small-2 {
  width: 16.66667%;
}

.small-push-2 {
  position: relative;
  left: 16.66667%;
}

.small-pull-2 {
  position: relative;
  left: -16.66667%;
}

.small-offset-1 {
  margin-left: 8.33333%;
}

.small-3 {
  width: 25%;
}

.small-push-3 {
  position: relative;
  left: 25%;
}

.small-pull-3 {
  position: relative;
  left: -25%;
}

.small-offset-2 {
  margin-left: 16.66667%;
}

.small-4 {
  width: 33.33333%;
}

.small-push-4 {
  position: relative;
  left: 33.33333%;
}

.small-pull-4 {
  position: relative;
  left: -33.33333%;
}

.small-offset-3 {
  margin-left: 25%;
}

.small-5 {
  width: 41.66667%;
}

.small-push-5 {
  position: relative;
  left: 41.66667%;
}

.small-pull-5 {
  position: relative;
  left: -41.66667%;
}

.small-offset-4 {
  margin-left: 33.33333%;
}

.small-6 {
  width: 50%;
}

.small-push-6 {
  position: relative;
  left: 50%;
}

.small-pull-6 {
  position: relative;
  left: -50%;
}

.small-offset-5 {
  margin-left: 41.66667%;
}

.small-7 {
  width: 58.33333%;
}

.small-push-7 {
  position: relative;
  left: 58.33333%;
}

.small-pull-7 {
  position: relative;
  left: -58.33333%;
}

.small-offset-6 {
  margin-left: 50%;
}

.small-8 {
  width: 66.66667%;
}

.small-push-8 {
  position: relative;
  left: 66.66667%;
}

.small-pull-8 {
  position: relative;
  left: -66.66667%;
}

.small-offset-7 {
  margin-left: 58.33333%;
}

.small-9 {
  width: 75%;
}

.small-push-9 {
  position: relative;
  left: 75%;
}

.small-pull-9 {
  position: relative;
  left: -75%;
}

.small-offset-8 {
  margin-left: 66.66667%;
}

.small-10 {
  width: 83.33333%;
}

.small-push-10 {
  position: relative;
  left: 83.33333%;
}

.small-pull-10 {
  position: relative;
  left: -83.33333%;
}

.small-offset-9 {
  margin-left: 75%;
}

.small-11 {
  width: 91.66667%;
}

.small-push-11 {
  position: relative;
  left: 91.66667%;
}

.small-pull-11 {
  position: relative;
  left: -91.66667%;
}

.small-offset-10 {
  margin-left: 83.33333%;
}

.small-12 {
  width: 100%;
}

.small-offset-11 {
  margin-left: 91.66667%;
}

.small-up-1 > .column,
.small-up-1 > .columns {
  float: left;
  width: 100%;
}
.small-up-1 > .column:nth-of-type(1n),
.small-up-1 > .columns:nth-of-type(1n) {
  clear: none;
}
.small-up-1 > .column:nth-of-type(1n + 1),
.small-up-1 > .columns:nth-of-type(1n + 1) {
  clear: both;
}
.small-up-1 > .column:last-child,
.small-up-1 > .columns:last-child {
  float: left;
}

.small-up-2 > .column,
.small-up-2 > .columns {
  float: left;
  width: 50%;
}
.small-up-2 > .column:nth-of-type(1n),
.small-up-2 > .columns:nth-of-type(1n) {
  clear: none;
}
.small-up-2 > .column:nth-of-type(2n + 1),
.small-up-2 > .columns:nth-of-type(2n + 1) {
  clear: both;
}
.small-up-2 > .column:last-child,
.small-up-2 > .columns:last-child {
  float: left;
}

.small-up-3 > .column,
.small-up-3 > .columns {
  float: left;
  width: 33.33333%;
}
.small-up-3 > .column:nth-of-type(1n),
.small-up-3 > .columns:nth-of-type(1n) {
  clear: none;
}
.small-up-3 > .column:nth-of-type(3n + 1),
.small-up-3 > .columns:nth-of-type(3n + 1) {
  clear: both;
}
.small-up-3 > .column:last-child,
.small-up-3 > .columns:last-child {
  float: left;
}

.small-up-4 > .column,
.small-up-4 > .columns {
  float: left;
  width: 25%;
}
.small-up-4 > .column:nth-of-type(1n),
.small-up-4 > .columns:nth-of-type(1n) {
  clear: none;
}
.small-up-4 > .column:nth-of-type(4n + 1),
.small-up-4 > .columns:nth-of-type(4n + 1) {
  clear: both;
}
.small-up-4 > .column:last-child,
.small-up-4 > .columns:last-child {
  float: left;
}

.small-up-5 > .column,
.small-up-5 > .columns {
  float: left;
  width: 20%;
}
.small-up-5 > .column:nth-of-type(1n),
.small-up-5 > .columns:nth-of-type(1n) {
  clear: none;
}
.small-up-5 > .column:nth-of-type(5n + 1),
.small-up-5 > .columns:nth-of-type(5n + 1) {
  clear: both;
}
.small-up-5 > .column:last-child,
.small-up-5 > .columns:last-child {
  float: left;
}

.small-up-6 > .column,
.small-up-6 > .columns {
  float: left;
  width: 16.66667%;
}
.small-up-6 > .column:nth-of-type(1n),
.small-up-6 > .columns:nth-of-type(1n) {
  clear: none;
}
.small-up-6 > .column:nth-of-type(6n + 1),
.small-up-6 > .columns:nth-of-type(6n + 1) {
  clear: both;
}
.small-up-6 > .column:last-child,
.small-up-6 > .columns:last-child {
  float: left;
}

.small-up-7 > .column,
.small-up-7 > .columns {
  float: left;
  width: 14.28571%;
}
.small-up-7 > .column:nth-of-type(1n),
.small-up-7 > .columns:nth-of-type(1n) {
  clear: none;
}
.small-up-7 > .column:nth-of-type(7n + 1),
.small-up-7 > .columns:nth-of-type(7n + 1) {
  clear: both;
}
.small-up-7 > .column:last-child,
.small-up-7 > .columns:last-child {
  float: left;
}

.small-up-8 > .column,
.small-up-8 > .columns {
  float: left;
  width: 12.5%;
}
.small-up-8 > .column:nth-of-type(1n),
.small-up-8 > .columns:nth-of-type(1n) {
  clear: none;
}
.small-up-8 > .column:nth-of-type(8n + 1),
.small-up-8 > .columns:nth-of-type(8n + 1) {
  clear: both;
}
.small-up-8 > .column:last-child,
.small-up-8 > .columns:last-child {
  float: left;
}

.small-collapse > .column,
.small-collapse > .columns {
  padding-right: 0;
  padding-left: 0;
}

.small-collapse .row {
  margin-right: 0;
  margin-left: 0;
}

.expanded.row .small-collapse.row {
  margin-right: 0;
  margin-left: 0;
}

.small-uncollapse > .column,
.small-uncollapse > .columns {
  padding-right: 0.625rem;
  padding-left: 0.625rem;
}

.small-centered {
  margin-right: auto;
  margin-left: auto;
}
.small-centered,
.small-centered:last-child:not(:first-child) {
  float: none;
  clear: both;
}

.small-uncentered,
.small-push-0,
.small-pull-0 {
  position: static;
  float: left;
  margin-right: 0;
  margin-left: 0;
}

@media print, screen and (min-width: 40em) {
  .medium-1 {
    width: 8.33333%;
  }
  .medium-push-1 {
    position: relative;
    left: 8.33333%;
  }
  .medium-pull-1 {
    position: relative;
    left: -8.33333%;
  }
  .medium-offset-0 {
    margin-left: 0%;
  }
  .medium-2 {
    width: 16.66667%;
  }
  .medium-push-2 {
    position: relative;
    left: 16.66667%;
  }
  .medium-pull-2 {
    position: relative;
    left: -16.66667%;
  }
  .medium-offset-1 {
    margin-left: 8.33333%;
  }
  .medium-3 {
    width: 25%;
  }
  .medium-push-3 {
    position: relative;
    left: 25%;
  }
  .medium-pull-3 {
    position: relative;
    left: -25%;
  }
  .medium-offset-2 {
    margin-left: 16.66667%;
  }
  .medium-4 {
    width: 33.33333%;
  }
  .medium-push-4 {
    position: relative;
    left: 33.33333%;
  }
  .medium-pull-4 {
    position: relative;
    left: -33.33333%;
  }
  .medium-offset-3 {
    margin-left: 25%;
  }
  .medium-5 {
    width: 41.66667%;
  }
  .medium-push-5 {
    position: relative;
    left: 41.66667%;
  }
  .medium-pull-5 {
    position: relative;
    left: -41.66667%;
  }
  .medium-offset-4 {
    margin-left: 33.33333%;
  }
  .medium-6 {
    width: 50%;
  }
  .medium-push-6 {
    position: relative;
    left: 50%;
  }
  .medium-pull-6 {
    position: relative;
    left: -50%;
  }
  .medium-offset-5 {
    margin-left: 41.66667%;
  }
  .medium-7 {
    width: 58.33333%;
  }
  .medium-push-7 {
    position: relative;
    left: 58.33333%;
  }
  .medium-pull-7 {
    position: relative;
    left: -58.33333%;
  }
  .medium-offset-6 {
    margin-left: 50%;
  }
  .medium-8 {
    width: 66.66667%;
  }
  .medium-push-8 {
    position: relative;
    left: 66.66667%;
  }
  .medium-pull-8 {
    position: relative;
    left: -66.66667%;
  }
  .medium-offset-7 {
    margin-left: 58.33333%;
  }
  .medium-9 {
    width: 75%;
  }
  .medium-push-9 {
    position: relative;
    left: 75%;
  }
  .medium-pull-9 {
    position: relative;
    left: -75%;
  }
  .medium-offset-8 {
    margin-left: 66.66667%;
  }
  .medium-10 {
    width: 83.33333%;
  }
  .medium-push-10 {
    position: relative;
    left: 83.33333%;
  }
  .medium-pull-10 {
    position: relative;
    left: -83.33333%;
  }
  .medium-offset-9 {
    margin-left: 75%;
  }
  .medium-11 {
    width: 91.66667%;
  }
  .medium-push-11 {
    position: relative;
    left: 91.66667%;
  }
  .medium-pull-11 {
    position: relative;
    left: -91.66667%;
  }
  .medium-offset-10 {
    margin-left: 83.33333%;
  }
  .medium-12 {
    width: 100%;
  }
  .medium-offset-11 {
    margin-left: 91.66667%;
  }
  .medium-up-1 > .column,
  .medium-up-1 > .columns {
    float: left;
    width: 100%;
  }
  .medium-up-1 > .column:nth-of-type(1n),
  .medium-up-1 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .medium-up-1 > .column:nth-of-type(1n + 1),
  .medium-up-1 > .columns:nth-of-type(1n + 1) {
    clear: both;
  }
  .medium-up-1 > .column:last-child,
  .medium-up-1 > .columns:last-child {
    float: left;
  }
  .medium-up-2 > .column,
  .medium-up-2 > .columns {
    float: left;
    width: 50%;
  }
  .medium-up-2 > .column:nth-of-type(1n),
  .medium-up-2 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .medium-up-2 > .column:nth-of-type(2n + 1),
  .medium-up-2 > .columns:nth-of-type(2n + 1) {
    clear: both;
  }
  .medium-up-2 > .column:last-child,
  .medium-up-2 > .columns:last-child {
    float: left;
  }
  .medium-up-3 > .column,
  .medium-up-3 > .columns {
    float: left;
    width: 33.33333%;
  }
  .medium-up-3 > .column:nth-of-type(1n),
  .medium-up-3 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .medium-up-3 > .column:nth-of-type(3n + 1),
  .medium-up-3 > .columns:nth-of-type(3n + 1) {
    clear: both;
  }
  .medium-up-3 > .column:last-child,
  .medium-up-3 > .columns:last-child {
    float: left;
  }
  .medium-up-4 > .column,
  .medium-up-4 > .columns {
    float: left;
    width: 25%;
  }
  .medium-up-4 > .column:nth-of-type(1n),
  .medium-up-4 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .medium-up-4 > .column:nth-of-type(4n + 1),
  .medium-up-4 > .columns:nth-of-type(4n + 1) {
    clear: both;
  }
  .medium-up-4 > .column:last-child,
  .medium-up-4 > .columns:last-child {
    float: left;
  }
  .medium-up-5 > .column,
  .medium-up-5 > .columns {
    float: left;
    width: 20%;
  }
  .medium-up-5 > .column:nth-of-type(1n),
  .medium-up-5 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .medium-up-5 > .column:nth-of-type(5n + 1),
  .medium-up-5 > .columns:nth-of-type(5n + 1) {
    clear: both;
  }
  .medium-up-5 > .column:last-child,
  .medium-up-5 > .columns:last-child {
    float: left;
  }
  .medium-up-6 > .column,
  .medium-up-6 > .columns {
    float: left;
    width: 16.66667%;
  }
  .medium-up-6 > .column:nth-of-type(1n),
  .medium-up-6 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .medium-up-6 > .column:nth-of-type(6n + 1),
  .medium-up-6 > .columns:nth-of-type(6n + 1) {
    clear: both;
  }
  .medium-up-6 > .column:last-child,
  .medium-up-6 > .columns:last-child {
    float: left;
  }
  .medium-up-7 > .column,
  .medium-up-7 > .columns {
    float: left;
    width: 14.28571%;
  }
  .medium-up-7 > .column:nth-of-type(1n),
  .medium-up-7 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .medium-up-7 > .column:nth-of-type(7n + 1),
  .medium-up-7 > .columns:nth-of-type(7n + 1) {
    clear: both;
  }
  .medium-up-7 > .column:last-child,
  .medium-up-7 > .columns:last-child {
    float: left;
  }
  .medium-up-8 > .column,
  .medium-up-8 > .columns {
    float: left;
    width: 12.5%;
  }
  .medium-up-8 > .column:nth-of-type(1n),
  .medium-up-8 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .medium-up-8 > .column:nth-of-type(8n + 1),
  .medium-up-8 > .columns:nth-of-type(8n + 1) {
    clear: both;
  }
  .medium-up-8 > .column:last-child,
  .medium-up-8 > .columns:last-child {
    float: left;
  }
  .medium-collapse > .column,
  .medium-collapse > .columns {
    padding-right: 0;
    padding-left: 0;
  }
  .medium-collapse .row {
    margin-right: 0;
    margin-left: 0;
  }
  .expanded.row .medium-collapse.row {
    margin-right: 0;
    margin-left: 0;
  }
  .medium-uncollapse > .column,
  .medium-uncollapse > .columns {
    padding-right: 0.9375rem;
    padding-left: 0.9375rem;
  }
  .medium-centered {
    margin-right: auto;
    margin-left: auto;
  }
  .medium-centered,
  .medium-centered:last-child:not(:first-child) {
    float: none;
    clear: both;
  }
  .medium-uncentered,
  .medium-push-0,
  .medium-pull-0 {
    position: static;
    float: left;
    margin-right: 0;
    margin-left: 0;
  }
}

@media print, screen and (min-width: 64em) {
  .large-1 {
    width: 8.33333%;
  }
  .large-push-1 {
    position: relative;
    left: 8.33333%;
  }
  .large-pull-1 {
    position: relative;
    left: -8.33333%;
  }
  .large-offset-0 {
    margin-left: 0%;
  }
  .large-2 {
    width: 16.66667%;
  }
  .large-push-2 {
    position: relative;
    left: 16.66667%;
  }
  .large-pull-2 {
    position: relative;
    left: -16.66667%;
  }
  .large-offset-1 {
    margin-left: 8.33333%;
  }
  .large-3 {
    width: 25%;
  }
  .large-push-3 {
    position: relative;
    left: 25%;
  }
  .large-pull-3 {
    position: relative;
    left: -25%;
  }
  .large-offset-2 {
    margin-left: 16.66667%;
  }
  .large-4 {
    width: 33.33333%;
  }
  .large-push-4 {
    position: relative;
    left: 33.33333%;
  }
  .large-pull-4 {
    position: relative;
    left: -33.33333%;
  }
  .large-offset-3 {
    margin-left: 25%;
  }
  .large-5 {
    width: 41.66667%;
  }
  .large-push-5 {
    position: relative;
    left: 41.66667%;
  }
  .large-pull-5 {
    position: relative;
    left: -41.66667%;
  }
  .large-offset-4 {
    margin-left: 33.33333%;
  }
  .large-6 {
    width: 50%;
  }
  .large-push-6 {
    position: relative;
    left: 50%;
  }
  .large-pull-6 {
    position: relative;
    left: -50%;
  }
  .large-offset-5 {
    margin-left: 41.66667%;
  }
  .large-7 {
    width: 58.33333%;
  }
  .large-push-7 {
    position: relative;
    left: 58.33333%;
  }
  .large-pull-7 {
    position: relative;
    left: -58.33333%;
  }
  .large-offset-6 {
    margin-left: 50%;
  }
  .large-8 {
    width: 66.66667%;
  }
  .large-push-8 {
    position: relative;
    left: 66.66667%;
  }
  .large-pull-8 {
    position: relative;
    left: -66.66667%;
  }
  .large-offset-7 {
    margin-left: 58.33333%;
  }
  .large-9 {
    width: 75%;
  }
  .large-push-9 {
    position: relative;
    left: 75%;
  }
  .large-pull-9 {
    position: relative;
    left: -75%;
  }
  .large-offset-8 {
    margin-left: 66.66667%;
  }
  .large-10 {
    width: 83.33333%;
  }
  .large-push-10 {
    position: relative;
    left: 83.33333%;
  }
  .large-pull-10 {
    position: relative;
    left: -83.33333%;
  }
  .large-offset-9 {
    margin-left: 75%;
  }
  .large-11 {
    width: 91.66667%;
  }
  .large-push-11 {
    position: relative;
    left: 91.66667%;
  }
  .large-pull-11 {
    position: relative;
    left: -91.66667%;
  }
  .large-offset-10 {
    margin-left: 83.33333%;
  }
  .large-12 {
    width: 100%;
  }
  .large-offset-11 {
    margin-left: 91.66667%;
  }
  .large-up-1 > .column,
  .large-up-1 > .columns {
    float: left;
    width: 100%;
  }
  .large-up-1 > .column:nth-of-type(1n),
  .large-up-1 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .large-up-1 > .column:nth-of-type(1n + 1),
  .large-up-1 > .columns:nth-of-type(1n + 1) {
    clear: both;
  }
  .large-up-1 > .column:last-child,
  .large-up-1 > .columns:last-child {
    float: left;
  }
  .large-up-2 > .column,
  .large-up-2 > .columns {
    float: left;
    width: 50%;
  }
  .large-up-2 > .column:nth-of-type(1n),
  .large-up-2 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .large-up-2 > .column:nth-of-type(2n + 1),
  .large-up-2 > .columns:nth-of-type(2n + 1) {
    clear: both;
  }
  .large-up-2 > .column:last-child,
  .large-up-2 > .columns:last-child {
    float: left;
  }
  .large-up-3 > .column,
  .large-up-3 > .columns {
    float: left;
    width: 33.33333%;
  }
  .large-up-3 > .column:nth-of-type(1n),
  .large-up-3 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .large-up-3 > .column:nth-of-type(3n + 1),
  .large-up-3 > .columns:nth-of-type(3n + 1) {
    clear: both;
  }
  .large-up-3 > .column:last-child,
  .large-up-3 > .columns:last-child {
    float: left;
  }
  .large-up-4 > .column,
  .large-up-4 > .columns {
    float: left;
    width: 25%;
  }
  .large-up-4 > .column:nth-of-type(1n),
  .large-up-4 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .large-up-4 > .column:nth-of-type(4n + 1),
  .large-up-4 > .columns:nth-of-type(4n + 1) {
    clear: both;
  }
  .large-up-4 > .column:last-child,
  .large-up-4 > .columns:last-child {
    float: left;
  }
  .large-up-5 > .column,
  .large-up-5 > .columns {
    float: left;
    width: 20%;
  }
  .large-up-5 > .column:nth-of-type(1n),
  .large-up-5 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .large-up-5 > .column:nth-of-type(5n + 1),
  .large-up-5 > .columns:nth-of-type(5n + 1) {
    clear: both;
  }
  .large-up-5 > .column:last-child,
  .large-up-5 > .columns:last-child {
    float: left;
  }
  .large-up-6 > .column,
  .large-up-6 > .columns {
    float: left;
    width: 16.66667%;
  }
  .large-up-6 > .column:nth-of-type(1n),
  .large-up-6 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .large-up-6 > .column:nth-of-type(6n + 1),
  .large-up-6 > .columns:nth-of-type(6n + 1) {
    clear: both;
  }
  .large-up-6 > .column:last-child,
  .large-up-6 > .columns:last-child {
    float: left;
  }
  .large-up-7 > .column,
  .large-up-7 > .columns {
    float: left;
    width: 14.28571%;
  }
  .large-up-7 > .column:nth-of-type(1n),
  .large-up-7 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .large-up-7 > .column:nth-of-type(7n + 1),
  .large-up-7 > .columns:nth-of-type(7n + 1) {
    clear: both;
  }
  .large-up-7 > .column:last-child,
  .large-up-7 > .columns:last-child {
    float: left;
  }
  .large-up-8 > .column,
  .large-up-8 > .columns {
    float: left;
    width: 12.5%;
  }
  .large-up-8 > .column:nth-of-type(1n),
  .large-up-8 > .columns:nth-of-type(1n) {
    clear: none;
  }
  .large-up-8 > .column:nth-of-type(8n + 1),
  .large-up-8 > .columns:nth-of-type(8n + 1) {
    clear: both;
  }
  .large-up-8 > .column:last-child,
  .large-up-8 > .columns:last-child {
    float: left;
  }
  .large-collapse > .column,
  .large-collapse > .columns {
    padding-right: 0;
    padding-left: 0;
  }
  .large-collapse .row {
    margin-right: 0;
    margin-left: 0;
  }
  .expanded.row .large-collapse.row {
    margin-right: 0;
    margin-left: 0;
  }
  .large-uncollapse > .column,
  .large-uncollapse > .columns {
    padding-right: 0.9375rem;
    padding-left: 0.9375rem;
  }
  .large-centered {
    margin-right: auto;
    margin-left: auto;
  }
  .large-centered,
  .large-centered:last-child:not(:first-child) {
    float: none;
    clear: both;
  }
  .large-uncentered,
  .large-push-0,
  .large-pull-0 {
    position: static;
    float: left;
    margin-right: 0;
    margin-left: 0;
  }
}

.column-block {
  margin-bottom: 1.25rem;
}
.column-block > :last-child {
  margin-bottom: 0;
}
@media print, screen and (min-width: 40em) {
  .column-block {
    margin-bottom: 1.875rem;
  }
  .column-block > :last-child {
    margin-bottom: 0;
  }
}

div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
p,
blockquote,
th,
td {
  margin: 0;
  padding: 0;
}

p {
  margin-bottom: 1rem;
  font-size: inherit;
  line-height: 1.6;
  text-rendering: optimizeLegibility;
}

em,
i {
  font-style: italic;
  line-height: inherit;
}

strong,
b {
  font-weight: bold;
  line-height: inherit;
}

small {
  font-size: 80%;
  line-height: inherit;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-style: normal;
  font-weight: normal;
  color: inherit;
  text-rendering: optimizeLegibility;
}
h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small {
  line-height: 0;
  color: #cacaca;
}

h1 {
  font-size: 1.5rem;
  line-height: 1.4;
  margin-top: 0;
  margin-bottom: 0.5rem;
}

h2 {
  font-size: 1.25rem;
  line-height: 1.4;
  margin-top: 0;
  margin-bottom: 0.5rem;
}

h3 {
  font-size: 1.1875rem;
  line-height: 1.4;
  margin-top: 0;
  margin-bottom: 0.5rem;
}

h4 {
  font-size: 1.125rem;
  line-height: 1.4;
  margin-top: 0;
  margin-bottom: 0.5rem;
}

h5 {
  font-size: 1.0625rem;
  line-height: 1.4;
  margin-top: 0;
  margin-bottom: 0.5rem;
}

h6 {
  font-size: 1rem;
  line-height: 1.4;
  margin-top: 0;
  margin-bottom: 0.5rem;
}

@media print, screen and (min-width: 40em) {
  h1 {
    font-size: 3rem;
  }
  h2 {
    font-size: 2.5rem;
  }
  h3 {
    font-size: 1.9375rem;
  }
  h4 {
    font-size: 1.5625rem;
  }
  h5 {
    font-size: 1.25rem;
  }
  h6 {
    font-size: 1rem;
  }
}

a {
  line-height: inherit;
  color: #1779ba;
  text-decoration: none;
  cursor: pointer;
}
a:hover,
a:focus {
  color: #1468a0;
}
a img {
  border: 0;
}

hr {
  clear: both;
  max-width: 75rem;
  height: 0;
  margin: 1.25rem auto;
  border-top: 0;
  border-right: 0;
  border-bottom: 1px solid #cacaca;
  border-left: 0;
}

ul,
ol,
dl {
  margin-bottom: 1rem;
  list-style-position: outside;
  line-height: 1.6;
}

li {
  font-size: inherit;
}

ul {
  margin-left: 1.25rem;
  list-style-type: disc;
}

ol {
  margin-left: 1.25rem;
}

ul ul,
ol ul,
ul ol,
ol ol {
  margin-left: 1.25rem;
  margin-bottom: 0;
}

dl {
  margin-bottom: 1rem;
}
dl dt {
  margin-bottom: 0.3rem;
  font-weight: bold;
}

blockquote {
  margin: 0 0 1rem;
  padding: 0.5625rem 1.25rem 0 1.1875rem;
  border-left: 1px solid #cacaca;
}
blockquote,
blockquote p {
  line-height: 1.6;
  color: #8a8a8a;
}

cite {
  display: block;
  font-size: 0.8125rem;
  color: #8a8a8a;
}
cite:before {
  content: "\2014   ";
}

abbr {
  border-bottom: 1px dotted #0a0a0a;
  color: #0a0a0a;
  cursor: help;
}

figure {
  margin: 0;
}

code {
  padding: 0.125rem 0.3125rem 0.0625rem;
  border: 1px solid #cacaca;
  background-color: #e6e6e6;
  font-family: Consolas, "Liberation Mono", Courier, monospace;
  font-weight: normal;
  color: #0a0a0a;
}

kbd {
  margin: 0;
  padding: 0.125rem 0.25rem 0;
  background-color: #e6e6e6;
  font-family: Consolas, "Liberation Mono", Courier, monospace;
  color: #0a0a0a;
}

.subheader {
  margin-top: 0.2rem;
  margin-bottom: 0.5rem;
  font-weight: normal;
  line-height: 1.4;
  color: #8a8a8a;
}

.lead {
  font-size: 125%;
  line-height: 1.6;
}

.stat {
  font-size: 2.5rem;
  line-height: 1;
}
p + .stat {
  margin-top: -1rem;
}

.no-bullet {
  margin-left: 0;
  list-style: none;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-justify {
  text-align: justify;
}

@media print, screen and (min-width: 40em) {
  .medium-text-left {
    text-align: left;
  }
  .medium-text-right {
    text-align: right;
  }
  .medium-text-center {
    text-align: center;
  }
  .medium-text-justify {
    text-align: justify;
  }
}

@media print, screen and (min-width: 64em) {
  .large-text-left {
    text-align: left;
  }
  .large-text-right {
    text-align: right;
  }
  .large-text-center {
    text-align: center;
  }
  .large-text-justify {
    text-align: justify;
  }
}

.show-for-print {
  display: none !important;
}

@media print {
  * {
    background: transparent !important;
    box-shadow: none !important;
    color: black !important;
    text-shadow: none !important;
  }
  .show-for-print {
    display: block !important;
  }
  .hide-for-print {
    display: none !important;
  }
  table.show-for-print {
    display: table !important;
  }
  thead.show-for-print {
    display: table-header-group !important;
  }
  tbody.show-for-print {
    display: table-row-group !important;
  }
  tr.show-for-print {
    display: table-row !important;
  }
  td.show-for-print {
    display: table-cell !important;
  }
  th.show-for-print {
    display: table-cell !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  .ir a:after,
  a[href^="javascript:"]:after,
  a[href^="#"]:after {
    content: "";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  pre,
  blockquote {
    border: 1px solid #8a8a8a;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  @page {
    margin: 0.5cm;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
}

[type="text"],
[type="password"],
[type="date"],
[type="datetime"],
[type="datetime-local"],
[type="month"],
[type="week"],
[type="email"],
[type="number"],
[type="search"],
[type="tel"],
[type="time"],
[type="url"],
[type="color"],
textarea {
  display: block;
  box-sizing: border-box;
  width: 100%;
  height: 2.4375rem;
  margin: 0 0 1rem;
  padding: 0.5rem;
  border: 1px solid #cacaca;
  border-radius: 0;
  background-color: #fefefe;
  box-shadow: inset 0 1px 2px rgba(10, 10, 10, 0.1);
  font-family: inherit;
  font-size: 1rem;
  font-weight: normal;
  color: #0a0a0a;
  transition:
    box-shadow 0.5s,
    border-color 0.25s ease-in-out;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
[type="text"]:focus,
[type="password"]:focus,
[type="date"]:focus,
[type="datetime"]:focus,
[type="datetime-local"]:focus,
[type="month"]:focus,
[type="week"]:focus,
[type="email"]:focus,
[type="number"]:focus,
[type="search"]:focus,
[type="tel"]:focus,
[type="time"]:focus,
[type="url"]:focus,
[type="color"]:focus,
textarea:focus {
  outline: none;
  border: 1px solid #8a8a8a;
  background-color: #fefefe;
  box-shadow: 0 0 5px #cacaca;
  transition:
    box-shadow 0.5s,
    border-color 0.25s ease-in-out;
}

textarea {
  max-width: 100%;
}
textarea[rows] {
  height: auto;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #cacaca;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  color: #cacaca;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #cacaca;
}

input::-ms-input-placeholder,
textarea::-ms-input-placeholder {
  color: #cacaca;
}

input::placeholder,
textarea::placeholder {
  color: #cacaca;
}

input:disabled,
input[readonly],
textarea:disabled,
textarea[readonly] {
  background-color: #e6e6e6;
  cursor: not-allowed;
}

[type="submit"],
[type="button"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
}

input[type="search"] {
  box-sizing: border-box;
}

[type="file"],
[type="checkbox"],
[type="radio"] {
  margin: 0 0 1rem;
}

[type="checkbox"] + label,
[type="radio"] + label {
  display: inline-block;
  vertical-align: baseline;
  margin-left: 0.5rem;
  margin-right: 1rem;
  margin-bottom: 0;
}
[type="checkbox"] + label[for],
[type="radio"] + label[for] {
  cursor: pointer;
}

label > [type="checkbox"],
label > [type="radio"] {
  margin-right: 0.5rem;
}

[type="file"] {
  width: 100%;
}

label {
  display: block;
  margin: 0;
  font-size: 0.875rem;
  font-weight: normal;
  line-height: 1.8;
  color: #0a0a0a;
}
label.middle {
  margin: 0 0 1rem;
  padding: 0.5625rem 0;
}

.help-text {
  margin-top: -0.5rem;
  font-size: 0.8125rem;
  font-style: italic;
  color: #0a0a0a;
}

.input-group {
  display: table;
  width: 100%;
  margin-bottom: 1rem;
}
.input-group > :first-child {
  border-radius: 0 0 0 0;
}
.input-group > :last-child > * {
  border-radius: 0 0 0 0;
}

.input-group-label,
.input-group-field,
.input-group-button,
.input-group-button a,
.input-group-button input,
.input-group-button button,
.input-group-button label {
  margin: 0;
  white-space: nowrap;
  display: table-cell;
  vertical-align: middle;
}

.input-group-label {
  padding: 0 1rem;
  border: 1px solid #cacaca;
  background: #e6e6e6;
  color: #0a0a0a;
  text-align: center;
  white-space: nowrap;
  width: 1%;
  height: 100%;
}
.input-group-label:first-child {
  border-right: 0;
}
.input-group-label:last-child {
  border-left: 0;
}

.input-group-field {
  border-radius: 0;
  height: 2.5rem;
}

.input-group-button {
  padding-top: 0;
  padding-bottom: 0;
  text-align: center;
  width: 1%;
  height: 100%;
}
.input-group-button a,
.input-group-button input,
.input-group-button button,
.input-group-button label {
  height: 2.5rem;
  padding-top: 0;
  padding-bottom: 0;
  font-size: 1rem;
}

.input-group .input-group-button {
  display: table-cell;
}

fieldset {
  margin: 0;
  padding: 0;
  border: 0;
}

legend {
  max-width: 100%;
  margin-bottom: 0.5rem;
}

.fieldset {
  margin: 1.125rem 0;
  padding: 1.25rem;
  border: 1px solid #cacaca;
}
.fieldset legend {
  margin: 0;
  margin-left: -0.1875rem;
  padding: 0 0.1875rem;
  background: #fefefe;
}

select {
  height: 2.4375rem;
  margin: 0 0 1rem;
  padding: 0.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 1px solid #cacaca;
  border-radius: 0;
  background-color: #fefefe;
  font-family: inherit;
  font-size: 1rem;
  line-height: normal;
  color: #0a0a0a;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' width='32' height='24' viewBox='0 0 32 24'><polygon points='0,0 32,0 16,24' style='fill: rgb%28138, 138, 138%29'></polygon></svg>");
  background-origin: content-box;
  background-position: right -1rem center;
  background-repeat: no-repeat;
  background-size: 9px 6px;
  padding-right: 1.5rem;
  transition:
    box-shadow 0.5s,
    border-color 0.25s ease-in-out;
}
@media screen and (min-width: 0\0) {
  select {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAYCAYAAACbU/80AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAIpJREFUeNrEkckNgDAMBBfRkEt0ObRBBdsGXUDgmQfK4XhH2m8czQAAy27R3tsw4Qfe2x8uOO6oYLb6GlOor3GF+swURAOmUJ+RwtEJs9WvTGEYxBXqI1MQAZhCfUQKRzDMVj+TwrAIV6jvSUEkYAr1LSkcyTBb/V+KYfX7xAeusq3sLDtGH3kEGACPWIflNZfhRQAAAABJRU5ErkJggg==");
  }
}
select:focus {
  outline: none;
  border: 1px solid #8a8a8a;
  background-color: #fefefe;
  box-shadow: 0 0 5px #cacaca;
  transition:
    box-shadow 0.5s,
    border-color 0.25s ease-in-out;
}
select:disabled {
  background-color: #e6e6e6;
  cursor: not-allowed;
}
select::-ms-expand {
  display: none;
}
select[multiple] {
  height: auto;
  background-image: none;
}

.is-invalid-input:not(:focus) {
  border-color: #cc4b37;
  background-color: #f9ecea;
}
.is-invalid-input:not(:focus)::-webkit-input-placeholder {
  color: #cc4b37;
}
.is-invalid-input:not(:focus)::-moz-placeholder {
  color: #cc4b37;
}
.is-invalid-input:not(:focus):-ms-input-placeholder {
  color: #cc4b37;
}
.is-invalid-input:not(:focus)::-ms-input-placeholder {
  color: #cc4b37;
}
.is-invalid-input:not(:focus)::placeholder {
  color: #cc4b37;
}

.is-invalid-label {
  color: #cc4b37;
}

.form-error {
  display: none;
  margin-top: -0.5rem;
  margin-bottom: 1rem;
  font-size: 0.75rem;
  font-weight: bold;
  color: #cc4b37;
}
.form-error.is-visible {
  display: block;
}

.button {
  display: inline-block;
  vertical-align: middle;
  margin: 0 0 1rem 0;
  padding: 0.85em 1em;
  -webkit-appearance: none;
  border: 1px solid transparent;
  border-radius: 0;
  transition:
    background-color 0.25s ease-out,
    color 0.25s ease-out;
  font-size: 0.9rem;
  line-height: 1;
  text-align: center;
  cursor: pointer;
  background-color: #1779ba;
  color: #fefefe;
}
[data-whatinput="mouse"] .button {
  outline: 0;
}
.button:hover,
.button:focus {
  background-color: #14679e;
  color: #fefefe;
}
.button.tiny {
  font-size: 0.6rem;
}
.button.small {
  font-size: 0.75rem;
}
.button.large {
  font-size: 1.25rem;
}
.button.expanded {
  display: block;
  width: 100%;
  margin-right: 0;
  margin-left: 0;
}
.button.primary {
  background-color: #1779ba;
  color: #fefefe;
}
.button.primary:hover,
.button.primary:focus {
  background-color: #126195;
  color: #fefefe;
}
.button.secondary {
  background-color: #767676;
  color: #fefefe;
}
.button.secondary:hover,
.button.secondary:focus {
  background-color: #5e5e5e;
  color: #fefefe;
}
.button.success {
  background-color: #3adb76;
  color: #0a0a0a;
}
.button.success:hover,
.button.success:focus {
  background-color: #22bb5b;
  color: #0a0a0a;
}
.button.warning {
  background-color: #ffae00;
  color: #0a0a0a;
}
.button.warning:hover,
.button.warning:focus {
  background-color: #cc8b00;
  color: #0a0a0a;
}
.button.alert {
  background-color: #cc4b37;
  color: #fefefe;
}
.button.alert:hover,
.button.alert:focus {
  background-color: #a53b2a;
  color: #fefefe;
}
.button.hollow {
  border: 1px solid #1779ba;
  color: #1779ba;
}
.button.hollow,
.button.hollow:hover,
.button.hollow:focus {
  background-color: transparent;
}
.button.hollow:hover,
.button.hollow:focus {
  border-color: #0c3d5d;
  color: #0c3d5d;
}
.button.hollow.primary {
  border: 1px solid #1779ba;
  color: #1779ba;
}
.button.hollow.primary:hover,
.button.hollow.primary:focus {
  border-color: #0c3d5d;
  color: #0c3d5d;
}
.button.hollow.secondary {
  border: 1px solid #767676;
  color: #767676;
}
.button.hollow.secondary:hover,
.button.hollow.secondary:focus {
  border-color: #3b3b3b;
  color: #3b3b3b;
}
.button.hollow.success {
  border: 1px solid #3adb76;
  color: #3adb76;
}
.button.hollow.success:hover,
.button.hollow.success:focus {
  border-color: #157539;
  color: #157539;
}
.button.hollow.warning {
  border: 1px solid #ffae00;
  color: #ffae00;
}
.button.hollow.warning:hover,
.button.hollow.warning:focus {
  border-color: #805700;
  color: #805700;
}
.button.hollow.alert {
  border: 1px solid #cc4b37;
  color: #cc4b37;
}
.button.hollow.alert:hover,
.button.hollow.alert:focus {
  border-color: #67251a;
  color: #67251a;
}
.button.disabled,
.button[disabled] {
  opacity: 0.25;
  cursor: not-allowed;
}
.button.disabled,
.button.disabled:hover,
.button.disabled:focus,
.button[disabled],
.button[disabled]:hover,
.button[disabled]:focus {
  background-color: #1779ba;
  color: #fefefe;
}
.button.disabled.primary,
.button[disabled].primary {
  opacity: 0.25;
  cursor: not-allowed;
}
.button.disabled.primary,
.button.disabled.primary:hover,
.button.disabled.primary:focus,
.button[disabled].primary,
.button[disabled].primary:hover,
.button[disabled].primary:focus {
  background-color: #1779ba;
  color: #fefefe;
}
.button.disabled.secondary,
.button[disabled].secondary {
  opacity: 0.25;
  cursor: not-allowed;
}
.button.disabled.secondary,
.button.disabled.secondary:hover,
.button.disabled.secondary:focus,
.button[disabled].secondary,
.button[disabled].secondary:hover,
.button[disabled].secondary:focus {
  background-color: #767676;
  color: #fefefe;
}
.button.disabled.success,
.button[disabled].success {
  opacity: 0.25;
  cursor: not-allowed;
}
.button.disabled.success,
.button.disabled.success:hover,
.button.disabled.success:focus,
.button[disabled].success,
.button[disabled].success:hover,
.button[disabled].success:focus {
  background-color: #3adb76;
  color: #0a0a0a;
}
.button.disabled.warning,
.button[disabled].warning {
  opacity: 0.25;
  cursor: not-allowed;
}
.button.disabled.warning,
.button.disabled.warning:hover,
.button.disabled.warning:focus,
.button[disabled].warning,
.button[disabled].warning:hover,
.button[disabled].warning:focus {
  background-color: #ffae00;
  color: #0a0a0a;
}
.button.disabled.alert,
.button[disabled].alert {
  opacity: 0.25;
  cursor: not-allowed;
}
.button.disabled.alert,
.button.disabled.alert:hover,
.button.disabled.alert:focus,
.button[disabled].alert,
.button[disabled].alert:hover,
.button[disabled].alert:focus {
  background-color: #cc4b37;
  color: #fefefe;
}
.button.dropdown::after {
  display: block;
  width: 0;
  height: 0;
  border: inset 0.4em;
  content: "";
  border-bottom-width: 0;
  border-top-style: solid;
  border-color: #fefefe transparent transparent;
  position: relative;
  top: 0.4em;
  display: inline-block;
  float: right;
  margin-left: 1em;
}
.button.arrow-only::after {
  top: -0.1em;
  float: none;
  margin-left: 0;
}

.accordion {
  margin-left: 0;
  background: #fefefe;
  list-style-type: none;
}

.accordion-item:first-child > :first-child {
  border-radius: 0 0 0 0;
}

.accordion-item:last-child > :last-child {
  border-radius: 0 0 0 0;
}

.accordion-title {
  position: relative;
  display: block;
  padding: 1.25rem 1rem;
  border: 1px solid #e6e6e6;
  border-bottom: 0;
  font-size: 0.75rem;
  line-height: 1;
  color: #1779ba;
}
:last-child:not(.is-active) > .accordion-title {
  border-bottom: 1px solid #e6e6e6;
  border-radius: 0 0 0 0;
}
.accordion-title:hover,
.accordion-title:focus {
  background-color: #e6e6e6;
}
.accordion-title::before {
  position: absolute;
  top: 50%;
  right: 1rem;
  margin-top: -0.5rem;
  content: "+";
}
.is-active > .accordion-title::before {
  content: "\2013";
}

.accordion-content {
  display: none;
  padding: 1rem;
  border: 1px solid #e6e6e6;
  border-bottom: 0;
  background-color: #fefefe;
  color: #0a0a0a;
}
:last-child > .accordion-content:last-child {
  border-bottom: 1px solid #e6e6e6;
}

.is-accordion-submenu-parent > a {
  position: relative;
}
.is-accordion-submenu-parent > a::after {
  display: block;
  width: 0;
  height: 0;
  border: inset 6px;
  content: "";
  border-bottom-width: 0;
  border-top-style: solid;
  border-color: #1779ba transparent transparent;
  position: absolute;
  top: 50%;
  margin-top: -3px;
  right: 1rem;
}

.is-accordion-submenu-parent[aria-expanded="true"] > a::after {
  transform: rotate(180deg);
  transform-origin: 50% 50%;
}

.badge {
  display: inline-block;
  min-width: 2.1em;
  padding: 0.3em;
  border-radius: 50%;
  font-size: 0.6rem;
  text-align: center;
  background: #1779ba;
  color: #fefefe;
}
.badge.primary {
  background: #1779ba;
  color: #fefefe;
}
.badge.secondary {
  background: #767676;
  color: #fefefe;
}
.badge.success {
  background: #3adb76;
  color: #0a0a0a;
}
.badge.warning {
  background: #ffae00;
  color: #0a0a0a;
}
.badge.alert {
  background: #cc4b37;
  color: #fefefe;
}

.breadcrumbs {
  margin: 0 0 1rem 0;
  list-style: none;
}
.breadcrumbs::before,
.breadcrumbs::after {
  display: table;
  content: " ";
}
.breadcrumbs::after {
  clear: both;
}
.breadcrumbs li {
  float: left;
  font-size: 0.6875rem;
  color: #0a0a0a;
  cursor: default;
  text-transform: uppercase;
}
.breadcrumbs li:not(:last-child)::after {
  position: relative;
  top: 1px;
  margin: 0 0.75rem;
  opacity: 1;
  content: "/";
  color: #cacaca;
}
.breadcrumbs a {
  color: #1779ba;
}
.breadcrumbs a:hover {
  text-decoration: underline;
}
.breadcrumbs .disabled {
  color: #cacaca;
  cursor: not-allowed;
}

.button-group {
  margin-bottom: 1rem;
  font-size: 0;
}
.button-group::before,
.button-group::after {
  display: table;
  content: " ";
}
.button-group::after {
  clear: both;
}
.button-group .button {
  margin: 0;
  margin-right: 1px;
  margin-bottom: 1px;
  font-size: 0.9rem;
}
.button-group .button:last-child {
  margin-right: 0;
}
.button-group.tiny .button {
  font-size: 0.6rem;
}
.button-group.small .button {
  font-size: 0.75rem;
}
.button-group.large .button {
  font-size: 1.25rem;
}
.button-group.expanded {
  margin-right: -1px;
}
.button-group.expanded::before,
.button-group.expanded::after {
  display: none;
}
.button-group.expanded .button:first-child:last-child {
  width: 100%;
}
.button-group.expanded .button:first-child:nth-last-child(2),
.button-group.expanded
  .button:first-child:nth-last-child(2):first-child:nth-last-child(2)
  ~ .button {
  display: inline-block;
  width: calc(50% - 1px);
  margin-right: 1px;
}
.button-group.expanded .button:first-child:nth-last-child(2):last-child,
.button-group.expanded
  .button:first-child:nth-last-child(2):first-child:nth-last-child(2)
  ~ .button:last-child {
  margin-right: -6px;
}
.button-group.expanded .button:first-child:nth-last-child(3),
.button-group.expanded
  .button:first-child:nth-last-child(3):first-child:nth-last-child(3)
  ~ .button {
  display: inline-block;
  width: calc(33.33333% - 1px);
  margin-right: 1px;
}
.button-group.expanded .button:first-child:nth-last-child(3):last-child,
.button-group.expanded
  .button:first-child:nth-last-child(3):first-child:nth-last-child(3)
  ~ .button:last-child {
  margin-right: -6px;
}
.button-group.expanded .button:first-child:nth-last-child(4),
.button-group.expanded
  .button:first-child:nth-last-child(4):first-child:nth-last-child(4)
  ~ .button {
  display: inline-block;
  width: calc(25% - 1px);
  margin-right: 1px;
}
.button-group.expanded .button:first-child:nth-last-child(4):last-child,
.button-group.expanded
  .button:first-child:nth-last-child(4):first-child:nth-last-child(4)
  ~ .button:last-child {
  margin-right: -6px;
}
.button-group.expanded .button:first-child:nth-last-child(5),
.button-group.expanded
  .button:first-child:nth-last-child(5):first-child:nth-last-child(5)
  ~ .button {
  display: inline-block;
  width: calc(20% - 1px);
  margin-right: 1px;
}
.button-group.expanded .button:first-child:nth-last-child(5):last-child,
.button-group.expanded
  .button:first-child:nth-last-child(5):first-child:nth-last-child(5)
  ~ .button:last-child {
  margin-right: -6px;
}
.button-group.expanded .button:first-child:nth-last-child(6),
.button-group.expanded
  .button:first-child:nth-last-child(6):first-child:nth-last-child(6)
  ~ .button {
  display: inline-block;
  width: calc(16.66667% - 1px);
  margin-right: 1px;
}
.button-group.expanded .button:first-child:nth-last-child(6):last-child,
.button-group.expanded
  .button:first-child:nth-last-child(6):first-child:nth-last-child(6)
  ~ .button:last-child {
  margin-right: -6px;
}
.button-group.primary .button {
  background-color: #1779ba;
  color: #fefefe;
}
.button-group.primary .button:hover,
.button-group.primary .button:focus {
  background-color: #126195;
  color: #fefefe;
}
.button-group.secondary .button {
  background-color: #767676;
  color: #fefefe;
}
.button-group.secondary .button:hover,
.button-group.secondary .button:focus {
  background-color: #5e5e5e;
  color: #fefefe;
}
.button-group.success .button {
  background-color: #3adb76;
  color: #0a0a0a;
}
.button-group.success .button:hover,
.button-group.success .button:focus {
  background-color: #22bb5b;
  color: #0a0a0a;
}
.button-group.warning .button {
  background-color: #ffae00;
  color: #0a0a0a;
}
.button-group.warning .button:hover,
.button-group.warning .button:focus {
  background-color: #cc8b00;
  color: #0a0a0a;
}
.button-group.alert .button {
  background-color: #cc4b37;
  color: #fefefe;
}
.button-group.alert .button:hover,
.button-group.alert .button:focus {
  background-color: #a53b2a;
  color: #fefefe;
}
.button-group.stacked .button,
.button-group.stacked-for-small .button,
.button-group.stacked-for-medium .button {
  width: 100%;
}
.button-group.stacked .button:last-child,
.button-group.stacked-for-small .button:last-child,
.button-group.stacked-for-medium .button:last-child {
  margin-bottom: 0;
}
@media print, screen and (min-width: 40em) {
  .button-group.stacked-for-small .button {
    width: auto;
    margin-bottom: 0;
  }
}
@media print, screen and (min-width: 64em) {
  .button-group.stacked-for-medium .button {
    width: auto;
    margin-bottom: 0;
  }
}
@media screen and (max-width: 39.9375em) {
  .button-group.stacked-for-small.expanded {
    display: block;
  }
  .button-group.stacked-for-small.expanded .button {
    display: block;
    margin-right: 0;
  }
}

.callout {
  position: relative;
  margin: 0 0 1rem 0;
  padding: 1rem;
  border: 1px solid rgba(10, 10, 10, 0.25);
  border-radius: 0;
  background-color: white;
  color: #0a0a0a;
}
.callout > :first-child {
  margin-top: 0;
}
.callout > :last-child {
  margin-bottom: 0;
}
.callout.primary {
  background-color: #d7ecfa;
  color: #0a0a0a;
}
.callout.secondary {
  background-color: #eaeaea;
  color: #0a0a0a;
}
.callout.success {
  background-color: #e1faea;
  color: #0a0a0a;
}
.callout.warning {
  background-color: #fff3d9;
  color: #0a0a0a;
}
.callout.alert {
  background-color: #f7e4e1;
  color: #0a0a0a;
}
.callout.small {
  padding-top: 0.5rem;
  padding-right: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.5rem;
}
.callout.large {
  padding-top: 3rem;
  padding-right: 3rem;
  padding-bottom: 3rem;
  padding-left: 3rem;
}

.card {
  margin-bottom: 1rem;
  border: 1px solid #e6e6e6;
  border-radius: 0;
  background: #fefefe;
  box-shadow: none;
  overflow: hidden;
  color: #0a0a0a;
}
.card > :last-child {
  margin-bottom: 0;
}

.card-divider {
  padding: 1rem;
  background: #e6e6e6;
}
.card-divider > :last-child {
  margin-bottom: 0;
}

.card-section {
  padding: 1rem;
}
.card-section > :last-child {
  margin-bottom: 0;
}

.close-button {
  position: absolute;
  color: #8a8a8a;
  cursor: pointer;
}
[data-whatinput="mouse"] .close-button {
  outline: 0;
}
.close-button:hover,
.close-button:focus {
  color: #0a0a0a;
}
.close-button.small {
  right: 0.66rem;
  top: 0.33em;
  font-size: 1.5em;
  line-height: 1;
}
.close-button,
.close-button.medium {
  right: 1rem;
  top: 0.5rem;
  font-size: 2em;
  line-height: 1;
}

.menu {
  margin: 0;
  list-style-type: none;
}
.menu > li {
  display: table-cell;
  vertical-align: middle;
}
[data-whatinput="mouse"] .menu > li {
  outline: 0;
}
.menu > li > a {
  display: block;
  padding: 0.7rem 1rem;
  line-height: 1;
}
.menu input,
.menu select,
.menu a,
.menu button {
  margin-bottom: 0;
}
.menu > li > a img,
.menu > li > a i,
.menu > li > a svg {
  vertical-align: middle;
}
.menu > li > a img + span,
.menu > li > a i + span,
.menu > li > a svg + span {
  vertical-align: middle;
}
.menu > li > a img,
.menu > li > a i,
.menu > li > a svg {
  margin-right: 0.25rem;
  display: inline-block;
}
.menu > li,
.menu.horizontal > li {
  display: table-cell;
}
.menu.expanded {
  display: table;
  width: 100%;
  table-layout: fixed;
}
.menu.expanded > li:first-child:last-child {
  width: 100%;
}
.menu.vertical > li {
  display: block;
}
@media print, screen and (min-width: 40em) {
  .menu.medium-horizontal > li {
    display: table-cell;
  }
  .menu.medium-expanded {
    display: table;
    width: 100%;
    table-layout: fixed;
  }
  .menu.medium-expanded > li:first-child:last-child {
    width: 100%;
  }
  .menu.medium-vertical > li {
    display: block;
  }
}
@media print, screen and (min-width: 64em) {
  .menu.large-horizontal > li {
    display: table-cell;
  }
  .menu.large-expanded {
    display: table;
    width: 100%;
    table-layout: fixed;
  }
  .menu.large-expanded > li:first-child:last-child {
    width: 100%;
  }
  .menu.large-vertical > li {
    display: block;
  }
}
.menu.simple li {
  display: inline-block;
  vertical-align: top;
  line-height: 1;
}
.menu.simple a {
  padding: 0;
}
.menu.simple li {
  margin-left: 0;
  margin-right: 1rem;
}
.menu.simple.align-right li {
  margin-right: 0;
  margin-left: 1rem;
}
.menu.align-right::before,
.menu.align-right::after {
  display: table;
  content: " ";
}
.menu.align-right::after {
  clear: both;
}
.menu.align-right > li {
  float: right;
}
.menu.icon-top > li > a {
  text-align: center;
}
.menu.icon-top > li > a img,
.menu.icon-top > li > a i,
.menu.icon-top > li > a svg {
  display: block;
  margin: 0 auto 0.25rem;
}
.menu.icon-top.vertical a > span {
  margin: auto;
}
.menu.nested {
  margin-left: 1rem;
}
.menu .active > a {
  background: #1779ba;
  color: #fefefe;
}
.menu.menu-bordered li {
  border: 1px solid #e6e6e6;
}
.menu.menu-bordered li:not(:first-child) {
  border-top: 0;
}
.menu.menu-hover li:hover {
  background-color: #e6e6e6;
}

.menu-text {
  padding-top: 0;
  padding-bottom: 0;
  padding: 0.7rem 1rem;
  font-weight: bold;
  line-height: 1;
  color: inherit;
}

.menu-centered {
  text-align: center;
}
.menu-centered > .menu {
  display: inline-block;
  vertical-align: top;
}

.no-js [data-responsive-menu] ul {
  display: none;
}

.menu-icon {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 16px;
  cursor: pointer;
}
.menu-icon::after {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 2px;
  background: #fefefe;
  box-shadow:
    0 7px 0 #fefefe,
    0 14px 0 #fefefe;
  content: "";
}
.menu-icon:hover::after {
  background: #cacaca;
  box-shadow:
    0 7px 0 #cacaca,
    0 14px 0 #cacaca;
}

.menu-icon.dark {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 16px;
  cursor: pointer;
}
.menu-icon.dark::after {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 2px;
  background: #0a0a0a;
  box-shadow:
    0 7px 0 #0a0a0a,
    0 14px 0 #0a0a0a;
  content: "";
}
.menu-icon.dark:hover::after {
  background: #8a8a8a;
  box-shadow:
    0 7px 0 #8a8a8a,
    0 14px 0 #8a8a8a;
}

.is-drilldown {
  position: relative;
  overflow: hidden;
}
.is-drilldown li {
  display: block;
}
.is-drilldown.animate-height {
  transition: height 0.5s;
}

.is-drilldown-submenu {
  position: absolute;
  top: 0;
  left: 100%;
  z-index: -1;
  width: 100%;
  background: #fefefe;
  transition: transform 0.15s linear;
}
.is-drilldown-submenu.is-active {
  z-index: 1;
  display: block;
  transform: translateX(-100%);
}
.is-drilldown-submenu.is-closing {
  transform: translateX(100%);
}

.drilldown-submenu-cover-previous {
  min-height: 100%;
}

.is-drilldown-submenu-parent > a {
  position: relative;
}
.is-drilldown-submenu-parent > a::after {
  display: block;
  width: 0;
  height: 0;
  border: inset 6px;
  content: "";
  border-right-width: 0;
  border-left-style: solid;
  border-color: transparent transparent transparent #1779ba;
  position: absolute;
  top: 50%;
  margin-top: -6px;
  right: 1rem;
}

.js-drilldown-back > a::before {
  display: block;
  width: 0;
  height: 0;
  border: inset 6px;
  content: "";
  border-left-width: 0;
  border-right-style: solid;
  border-color: transparent #1779ba transparent transparent;
  border-left-width: 0;
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.75rem;
  border-left-width: 0;
}

.dropdown-pane {
  position: absolute;
  z-index: 10;
  display: block;
  width: 300px;
  padding: 1rem;
  visibility: hidden;
  border: 1px solid #cacaca;
  border-radius: 0;
  background-color: #fefefe;
  font-size: 1rem;
}
.dropdown-pane.is-open {
  visibility: visible;
}

.dropdown-pane.tiny {
  width: 100px;
}

.dropdown-pane.small {
  width: 200px;
}

.dropdown-pane.large {
  width: 400px;
}

.dropdown.menu > li.opens-left > .is-dropdown-submenu {
  top: 100%;
  right: 0;
  left: auto;
}

.dropdown.menu > li.opens-right > .is-dropdown-submenu {
  top: 100%;
  right: auto;
  left: 0;
}

.dropdown.menu > li.is-dropdown-submenu-parent > a {
  position: relative;
  padding-right: 1.5rem;
}

.dropdown.menu > li.is-dropdown-submenu-parent > a::after {
  display: block;
  width: 0;
  height: 0;
  border: inset 6px;
  content: "";
  border-bottom-width: 0;
  border-top-style: solid;
  border-color: #1779ba transparent transparent;
  right: 5px;
  margin-top: -3px;
}

[data-whatinput="mouse"] .dropdown.menu a {
  outline: 0;
}

.no-js .dropdown.menu ul {
  display: none;
}

.dropdown.menu.vertical > li .is-dropdown-submenu {
  top: 0;
}

.dropdown.menu.vertical > li.opens-left > .is-dropdown-submenu {
  right: 100%;
  left: auto;
}

.dropdown.menu.vertical > li.opens-right > .is-dropdown-submenu {
  right: auto;
  left: 100%;
}

.dropdown.menu.vertical > li > a::after {
  right: 14px;
}

.dropdown.menu.vertical > li.opens-left > a::after {
  display: block;
  width: 0;
  height: 0;
  border: inset 6px;
  content: "";
  border-left-width: 0;
  border-right-style: solid;
  border-color: transparent #1779ba transparent transparent;
}

.dropdown.menu.vertical > li.opens-right > a::after {
  display: block;
  width: 0;
  height: 0;
  border: inset 6px;
  content: "";
  border-right-width: 0;
  border-left-style: solid;
  border-color: transparent transparent transparent #1779ba;
}

@media print, screen and (min-width: 40em) {
  .dropdown.menu.medium-horizontal > li.opens-left > .is-dropdown-submenu {
    top: 100%;
    right: 0;
    left: auto;
  }
  .dropdown.menu.medium-horizontal > li.opens-right > .is-dropdown-submenu {
    top: 100%;
    right: auto;
    left: 0;
  }
  .dropdown.menu.medium-horizontal > li.is-dropdown-submenu-parent > a {
    position: relative;
    padding-right: 1.5rem;
  }
  .dropdown.menu.medium-horizontal > li.is-dropdown-submenu-parent > a::after {
    display: block;
    width: 0;
    height: 0;
    border: inset 6px;
    content: "";
    border-bottom-width: 0;
    border-top-style: solid;
    border-color: #1779ba transparent transparent;
    right: 5px;
    margin-top: -3px;
  }
  .dropdown.menu.medium-vertical > li .is-dropdown-submenu {
    top: 0;
  }
  .dropdown.menu.medium-vertical > li.opens-left > .is-dropdown-submenu {
    right: 100%;
    left: auto;
  }
  .dropdown.menu.medium-vertical > li.opens-right > .is-dropdown-submenu {
    right: auto;
    left: 100%;
  }
  .dropdown.menu.medium-vertical > li > a::after {
    right: 14px;
  }
  .dropdown.menu.medium-vertical > li.opens-left > a::after {
    display: block;
    width: 0;
    height: 0;
    border: inset 6px;
    content: "";
    border-left-width: 0;
    border-right-style: solid;
    border-color: transparent #1779ba transparent transparent;
  }
  .dropdown.menu.medium-vertical > li.opens-right > a::after {
    display: block;
    width: 0;
    height: 0;
    border: inset 6px;
    content: "";
    border-right-width: 0;
    border-left-style: solid;
    border-color: transparent transparent transparent #1779ba;
  }
}

@media print, screen and (min-width: 64em) {
  .dropdown.menu.large-horizontal > li.opens-left > .is-dropdown-submenu {
    top: 100%;
    right: 0;
    left: auto;
  }
  .dropdown.menu.large-horizontal > li.opens-right > .is-dropdown-submenu {
    top: 100%;
    right: auto;
    left: 0;
  }
  .dropdown.menu.large-horizontal > li.is-dropdown-submenu-parent > a {
    position: relative;
    padding-right: 1.5rem;
  }
  .dropdown.menu.large-horizontal > li.is-dropdown-submenu-parent > a::after {
    display: block;
    width: 0;
    height: 0;
    border: inset 6px;
    content: "";
    border-bottom-width: 0;
    border-top-style: solid;
    border-color: #1779ba transparent transparent;
    right: 5px;
    margin-top: -3px;
  }
  .dropdown.menu.large-vertical > li .is-dropdown-submenu {
    top: 0;
  }
  .dropdown.menu.large-vertical > li.opens-left > .is-dropdown-submenu {
    right: 100%;
    left: auto;
  }
  .dropdown.menu.large-vertical > li.opens-right > .is-dropdown-submenu {
    right: auto;
    left: 100%;
  }
  .dropdown.menu.large-vertical > li > a::after {
    right: 14px;
  }
  .dropdown.menu.large-vertical > li.opens-left > a::after {
    display: block;
    width: 0;
    height: 0;
    border: inset 6px;
    content: "";
    border-left-width: 0;
    border-right-style: solid;
    border-color: transparent #1779ba transparent transparent;
  }
  .dropdown.menu.large-vertical > li.opens-right > a::after {
    display: block;
    width: 0;
    height: 0;
    border: inset 6px;
    content: "";
    border-right-width: 0;
    border-left-style: solid;
    border-color: transparent transparent transparent #1779ba;
  }
}

.dropdown.menu.align-right .is-dropdown-submenu.first-sub {
  top: 100%;
  right: 0;
  left: auto;
}

.is-dropdown-menu.vertical {
  width: 100px;
}
.is-dropdown-menu.vertical.align-right {
  float: right;
}

.is-dropdown-submenu-parent {
  position: relative;
}
.is-dropdown-submenu-parent a::after {
  position: absolute;
  top: 50%;
  right: 5px;
  margin-top: -6px;
}
.is-dropdown-submenu-parent.opens-inner > .is-dropdown-submenu {
  top: 100%;
  left: auto;
}
.is-dropdown-submenu-parent.opens-left > .is-dropdown-submenu {
  right: 100%;
  left: auto;
}
.is-dropdown-submenu-parent.opens-right > .is-dropdown-submenu {
  right: auto;
  left: 100%;
}

.is-dropdown-submenu {
  position: absolute;
  top: 0;
  left: 100%;
  z-index: 1;
  display: none;
  min-width: 200px;
  border: 1px solid #cacaca;
  background: #fefefe;
}
.is-dropdown-submenu .is-dropdown-submenu-parent > a::after {
  right: 14px;
}
.is-dropdown-submenu .is-dropdown-submenu-parent.opens-left > a::after {
  display: block;
  width: 0;
  height: 0;
  border: inset 6px;
  content: "";
  border-left-width: 0;
  border-right-style: solid;
  border-color: transparent #1779ba transparent transparent;
}
.is-dropdown-submenu .is-dropdown-submenu-parent.opens-right > a::after {
  display: block;
  width: 0;
  height: 0;
  border: inset 6px;
  content: "";
  border-right-width: 0;
  border-left-style: solid;
  border-color: transparent transparent transparent #1779ba;
}
.is-dropdown-submenu .is-dropdown-submenu {
  margin-top: -1px;
}
.is-dropdown-submenu > li {
  width: 100%;
}
.is-dropdown-submenu.js-dropdown-active {
  display: block;
}

.responsive-embed,
.flex-video {
  position: relative;
  height: 0;
  margin-bottom: 1rem;
  padding-bottom: 75%;
  overflow: hidden;
}
.responsive-embed iframe,
.responsive-embed object,
.responsive-embed embed,
.responsive-embed video,
.flex-video iframe,
.flex-video object,
.flex-video embed,
.flex-video video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.responsive-embed.widescreen,
.flex-video.widescreen {
  padding-bottom: 56.25%;
}

.label {
  display: inline-block;
  padding: 0.33333rem 0.5rem;
  border-radius: 0;
  font-size: 0.8rem;
  line-height: 1;
  white-space: nowrap;
  cursor: default;
  background: #1779ba;
  color: #fefefe;
}
.label.primary {
  background: #1779ba;
  color: #fefefe;
}
.label.secondary {
  background: #767676;
  color: #fefefe;
}
.label.success {
  background: #3adb76;
  color: #0a0a0a;
}
.label.warning {
  background: #ffae00;
  color: #0a0a0a;
}
.label.alert {
  background: #cc4b37;
  color: #fefefe;
}

.media-object {
  display: block;
  margin-bottom: 1rem;
}
.media-object img {
  max-width: none;
}
@media screen and (max-width: 39.9375em) {
  .media-object.stack-for-small .media-object-section {
    padding: 0;
    padding-bottom: 1rem;
    display: block;
  }
  .media-object.stack-for-small .media-object-section img {
    width: 100%;
  }
}

.media-object-section {
  display: table-cell;
  vertical-align: top;
}
.media-object-section:first-child {
  padding-right: 1rem;
}
.media-object-section:last-child:not(:nth-child(2)) {
  padding-left: 1rem;
}
.media-object-section > :last-child {
  margin-bottom: 0;
}
.media-object-section.middle {
  vertical-align: middle;
}
.media-object-section.bottom {
  vertical-align: bottom;
}

.is-off-canvas-open {
  overflow: hidden;
}

.js-off-canvas-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition:
    opacity 0.5s ease,
    visibility 0.5s ease;
  background: rgba(254, 254, 254, 0.25);
  opacity: 0;
  visibility: hidden;
  overflow: hidden;
}
.js-off-canvas-overlay.is-visible {
  opacity: 1;
  visibility: visible;
}
.js-off-canvas-overlay.is-closable {
  cursor: pointer;
}
.js-off-canvas-overlay.is-overlay-absolute {
  position: absolute;
}
.js-off-canvas-overlay.is-overlay-fixed {
  position: fixed;
}

.off-canvas-wrapper {
  position: relative;
  overflow: hidden;
}

.off-canvas {
  position: fixed;
  z-index: 1;
  transition: transform 0.5s ease;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  background: #e6e6e6;
}
[data-whatinput="mouse"] .off-canvas {
  outline: 0;
}
.off-canvas.is-transition-overlap {
  z-index: 10;
}
.off-canvas.is-transition-overlap.is-open {
  box-shadow: 0 0 10px rgba(10, 10, 10, 0.7);
}
.off-canvas.is-open {
  transform: translate(0, 0);
}

.off-canvas-absolute {
  position: absolute;
  z-index: 1;
  transition: transform 0.5s ease;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  background: #e6e6e6;
}
[data-whatinput="mouse"] .off-canvas-absolute {
  outline: 0;
}
.off-canvas-absolute.is-transition-overlap {
  z-index: 10;
}
.off-canvas-absolute.is-transition-overlap.is-open {
  box-shadow: 0 0 10px rgba(10, 10, 10, 0.7);
}
.off-canvas-absolute.is-open {
  transform: translate(0, 0);
}

.position-left {
  top: 0;
  left: 0;
  width: 250px;
  height: 100%;
  transform: translateX(-250px);
  overflow-y: auto;
}
.position-left.is-open ~ .off-canvas-content {
  transform: translateX(250px);
}
.position-left.is-transition-push::after {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 1px;
  box-shadow: 0 0 10px rgba(10, 10, 10, 0.7);
  content: " ";
}
.position-left.is-transition-overlap.is-open ~ .off-canvas-content {
  transform: none;
}

.position-right {
  top: 0;
  right: 0;
  width: 250px;
  height: 100%;
  transform: translateX(250px);
  overflow-y: auto;
}
.position-right.is-open ~ .off-canvas-content {
  transform: translateX(-250px);
}
.position-right.is-transition-push::after {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 1px;
  box-shadow: 0 0 10px rgba(10, 10, 10, 0.7);
  content: " ";
}
.position-right.is-transition-overlap.is-open ~ .off-canvas-content {
  transform: none;
}

.position-top {
  top: 0;
  left: 0;
  width: 100%;
  height: 250px;
  transform: translateY(-250px);
  overflow-x: auto;
}
.position-top.is-open ~ .off-canvas-content {
  transform: translateY(250px);
}
.position-top.is-transition-push::after {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 1px;
  width: 100%;
  box-shadow: 0 0 10px rgba(10, 10, 10, 0.7);
  content: " ";
}
.position-top.is-transition-overlap.is-open ~ .off-canvas-content {
  transform: none;
}

.position-bottom {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 250px;
  transform: translateY(250px);
  overflow-x: auto;
}
.position-bottom.is-open ~ .off-canvas-content {
  transform: translateY(-250px);
}
.position-bottom.is-transition-push::after {
  position: absolute;
  top: 0;
  left: 0;
  height: 1px;
  width: 100%;
  box-shadow: 0 0 10px rgba(10, 10, 10, 0.7);
  content: " ";
}
.position-bottom.is-transition-overlap.is-open ~ .off-canvas-content {
  transform: none;
}

.off-canvas-content {
  transition: transform 0.5s ease;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

@media print, screen and (min-width: 40em) {
  .position-left.reveal-for-medium {
    transform: none;
    z-index: 1;
  }
  .position-left.reveal-for-medium ~ .off-canvas-content {
    margin-left: 250px;
  }
  .position-right.reveal-for-medium {
    transform: none;
    z-index: 1;
  }
  .position-right.reveal-for-medium ~ .off-canvas-content {
    margin-right: 250px;
  }
  .position-top.reveal-for-medium {
    transform: none;
    z-index: 1;
  }
  .position-top.reveal-for-medium ~ .off-canvas-content {
    margin-top: 250px;
  }
  .position-bottom.reveal-for-medium {
    transform: none;
    z-index: 1;
  }
  .position-bottom.reveal-for-medium ~ .off-canvas-content {
    margin-bottom: 250px;
  }
}

@media print, screen and (min-width: 64em) {
  .position-left.reveal-for-large {
    transform: none;
    z-index: 1;
  }
  .position-left.reveal-for-large ~ .off-canvas-content {
    margin-left: 250px;
  }
  .position-right.reveal-for-large {
    transform: none;
    z-index: 1;
  }
  .position-right.reveal-for-large ~ .off-canvas-content {
    margin-right: 250px;
  }
  .position-top.reveal-for-large {
    transform: none;
    z-index: 1;
  }
  .position-top.reveal-for-large ~ .off-canvas-content {
    margin-top: 250px;
  }
  .position-bottom.reveal-for-large {
    transform: none;
    z-index: 1;
  }
  .position-bottom.reveal-for-large ~ .off-canvas-content {
    margin-bottom: 250px;
  }
}

.orbit {
  position: relative;
}

.orbit-container {
  position: relative;
  height: 0;
  margin: 0;
  list-style: none;
  overflow: hidden;
}

.orbit-slide {
  width: 100%;
}
.orbit-slide.no-motionui.is-active {
  top: 0;
  left: 0;
}

.orbit-figure {
  margin: 0;
}

.orbit-image {
  width: 100%;
  max-width: 100%;
  margin: 0;
}

.orbit-caption {
  position: absolute;
  bottom: 0;
  width: 100%;
  margin-bottom: 0;
  padding: 1rem;
  background-color: rgba(10, 10, 10, 0.5);
  color: #fefefe;
}

.orbit-previous,
.orbit-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  padding: 1rem;
  color: #fefefe;
}
[data-whatinput="mouse"] .orbit-previous,
[data-whatinput="mouse"] .orbit-next {
  outline: 0;
}
.orbit-previous:hover,
.orbit-next:hover,
.orbit-previous:active,
.orbit-next:active,
.orbit-previous:focus,
.orbit-next:focus {
  background-color: rgba(10, 10, 10, 0.5);
}

.orbit-previous {
  left: 0;
}

.orbit-next {
  left: auto;
  right: 0;
}

.orbit-bullets {
  position: relative;
  margin-top: 0.8rem;
  margin-bottom: 0.8rem;
  text-align: center;
}
[data-whatinput="mouse"] .orbit-bullets {
  outline: 0;
}
.orbit-bullets button {
  width: 1.2rem;
  height: 1.2rem;
  margin: 0.1rem;
  border-radius: 50%;
  background-color: #cacaca;
}
.orbit-bullets button:hover {
  background-color: #8a8a8a;
}
.orbit-bullets button.is-active {
  background-color: #8a8a8a;
}

.pagination {
  margin-left: 0;
  margin-bottom: 1rem;
}
.pagination::before,
.pagination::after {
  display: table;
  content: " ";
}
.pagination::after {
  clear: both;
}
.pagination li {
  margin-right: 0.0625rem;
  border-radius: 0;
  font-size: 0.875rem;
  display: none;
}
.pagination li:last-child,
.pagination li:first-child {
  display: inline-block;
}
@media print, screen and (min-width: 40em) {
  .pagination li {
    display: inline-block;
  }
}
.pagination a,
.pagination button {
  display: block;
  padding: 0.1875rem 0.625rem;
  border-radius: 0;
  color: #0a0a0a;
}
.pagination a:hover,
.pagination button:hover {
  background: #e6e6e6;
}
.pagination .current {
  padding: 0.1875rem 0.625rem;
  background: #1779ba;
  color: #fefefe;
  cursor: default;
}
.pagination .disabled {
  padding: 0.1875rem 0.625rem;
  color: #cacaca;
  cursor: not-allowed;
}
.pagination .disabled:hover {
  background: transparent;
}
.pagination .ellipsis::after {
  padding: 0.1875rem 0.625rem;
  content: "\2026";
  color: #0a0a0a;
}

.pagination-previous a::before,
.pagination-previous.disabled::before {
  display: inline-block;
  margin-right: 0.5rem;
  content: "\AB";
}

.pagination-next a::after,
.pagination-next.disabled::after {
  display: inline-block;
  margin-left: 0.5rem;
  content: "\BB";
}

.progress {
  height: 1rem;
  margin-bottom: 1rem;
  border-radius: 0;
  background-color: #cacaca;
}
.progress.primary .progress-meter {
  background-color: #1779ba;
}
.progress.secondary .progress-meter {
  background-color: #767676;
}
.progress.success .progress-meter {
  background-color: #3adb76;
}
.progress.warning .progress-meter {
  background-color: #ffae00;
}
.progress.alert .progress-meter {
  background-color: #cc4b37;
}

.progress-meter {
  position: relative;
  display: block;
  width: 0%;
  height: 100%;
  background-color: #1779ba;
}

.progress-meter-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: absolute;
  margin: 0;
  font-size: 0.75rem;
  font-weight: bold;
  color: #fefefe;
  white-space: nowrap;
}

.slider {
  position: relative;
  height: 0.5rem;
  margin-top: 1.25rem;
  margin-bottom: 2.25rem;
  background-color: #e6e6e6;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  touch-action: none;
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  display: inline-block;
  max-width: 100%;
  height: 0.5rem;
  background-color: #cacaca;
  transition: all 0.2s ease-in-out;
}
.slider-fill.is-dragging {
  transition: all 0s linear;
}

.slider-handle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  position: absolute;
  left: 0;
  z-index: 1;
  display: inline-block;
  width: 1.4rem;
  height: 1.4rem;
  border-radius: 0;
  background-color: #1779ba;
  transition: all 0.2s ease-in-out;
  touch-action: manipulation;
}
[data-whatinput="mouse"] .slider-handle {
  outline: 0;
}
.slider-handle:hover {
  background-color: #14679e;
}
.slider-handle.is-dragging {
  transition: all 0s linear;
}

.slider.disabled,
.slider[disabled] {
  opacity: 0.25;
  cursor: not-allowed;
}

.slider.vertical {
  display: inline-block;
  width: 0.5rem;
  height: 12.5rem;
  margin: 0 1.25rem;
  transform: scale(1, -1);
}
.slider.vertical .slider-fill {
  top: 0;
  width: 0.5rem;
  max-height: 100%;
}
.slider.vertical .slider-handle {
  position: absolute;
  top: 0;
  left: 50%;
  width: 1.4rem;
  height: 1.4rem;
  transform: translateX(-50%);
}

.sticky-container {
  position: relative;
}

.sticky {
  position: relative;
  z-index: 0;
  transform: translate3d(0, 0, 0);
}

.sticky.is-stuck {
  position: fixed;
  z-index: 5;
}
.sticky.is-stuck.is-at-top {
  top: 0;
}
.sticky.is-stuck.is-at-bottom {
  bottom: 0;
}

.sticky.is-anchored {
  position: relative;
  right: auto;
  left: auto;
}
.sticky.is-anchored.is-at-bottom {
  bottom: 0;
}

body.is-reveal-open {
  overflow: hidden;
}

html.is-reveal-open,
html.is-reveal-open body {
  min-height: 100%;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.reveal-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1005;
  display: none;
  background-color: rgba(10, 10, 10, 0.45);
  overflow-y: scroll;
}

.reveal {
  z-index: 1006;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  display: none;
  padding: 1rem;
  border: 1px solid #cacaca;
  border-radius: 0;
  background-color: #fefefe;
  position: relative;
  top: 100px;
  margin-right: auto;
  margin-left: auto;
  overflow-y: auto;
}
[data-whatinput="mouse"] .reveal {
  outline: 0;
}
@media print, screen and (min-width: 40em) {
  .reveal {
    min-height: 0;
  }
}
.reveal .column,
.reveal .columns,
.reveal .columns {
  min-width: 0;
}
.reveal > :last-child {
  margin-bottom: 0;
}
@media print, screen and (min-width: 40em) {
  .reveal {
    width: 600px;
    max-width: 75rem;
  }
}
@media print, screen and (min-width: 40em) {
  .reveal .reveal {
    right: auto;
    left: auto;
    margin: 0 auto;
  }
}
.reveal.collapse {
  padding: 0;
}
@media print, screen and (min-width: 40em) {
  .reveal.tiny {
    width: 30%;
    max-width: 75rem;
  }
}
@media print, screen and (min-width: 40em) {
  .reveal.small {
    width: 50%;
    max-width: 75rem;
  }
}
@media print, screen and (min-width: 40em) {
  .reveal.large {
    width: 90%;
    max-width: 75rem;
  }
}
.reveal.full {
  top: 0;
  left: 0;
  width: 100%;
  max-width: none;
  height: 100%;
  height: 100vh;
  min-height: 100vh;
  margin-left: 0;
  border: 0;
  border-radius: 0;
}
@media screen and (max-width: 39.9375em) {
  .reveal {
    top: 0;
    left: 0;
    width: 100%;
    max-width: none;
    height: 100%;
    height: 100vh;
    min-height: 100vh;
    margin-left: 0;
    border: 0;
    border-radius: 0;
  }
}
.reveal.without-overlay {
  position: fixed;
}

.switch {
  height: 2rem;
  position: relative;
  margin-bottom: 1rem;
  outline: 0;
  font-size: 0.875rem;
  font-weight: bold;
  color: #fefefe;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.switch-input {
  position: absolute;
  margin-bottom: 0;
  opacity: 0;
}

.switch-paddle {
  position: relative;
  display: block;
  width: 4rem;
  height: 2rem;
  border-radius: 0;
  background: #cacaca;
  transition: all 0.25s ease-out;
  font-weight: inherit;
  color: inherit;
  cursor: pointer;
}
input + .switch-paddle {
  margin: 0;
}
.switch-paddle::after {
  position: absolute;
  top: 0.25rem;
  left: 0.25rem;
  display: block;
  width: 1.5rem;
  height: 1.5rem;
  transform: translate3d(0, 0, 0);
  border-radius: 0;
  background: #fefefe;
  transition: all 0.25s ease-out;
  content: "";
}
input:checked ~ .switch-paddle {
  background: #1779ba;
}
input:checked ~ .switch-paddle::after {
  left: 2.25rem;
}
[data-whatinput="mouse"] input:focus ~ .switch-paddle {
  outline: 0;
}

.switch-active,
.switch-inactive {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

/*.switch-active {*/
/*    left: 8%;*/
/*    display: none; }*/
/*input:checked + label > .switch-active {*/
/*    display: block; }*/

.switch-inactive {
  right: 15%;
}
input:checked + label > .switch-inactive {
  display: none;
}

.switch.tiny {
  height: 1.5rem;
}
.switch.tiny .switch-paddle {
  width: 3rem;
  height: 1.5rem;
  font-size: 0.625rem;
}
.switch.tiny .switch-paddle::after {
  top: 0.25rem;
  left: 0.25rem;
  width: 1rem;
  height: 1rem;
}
.switch.tiny input:checked ~ .switch-paddle::after {
  left: 1.75rem;
}

.switch.small {
  height: 1.75rem;
}
.switch.small .switch-paddle {
  width: 3.5rem;
  height: 1.75rem;
  font-size: 0.75rem;
}
.switch.small .switch-paddle::after {
  top: 0.25rem;
  left: 0.25rem;
  width: 1.25rem;
  height: 1.25rem;
}
.switch.small input:checked ~ .switch-paddle::after {
  left: 2rem;
}

.switch.large {
  height: 2.5rem;
}
.switch.large .switch-paddle {
  width: 5rem;
  height: 2.5rem;
  font-size: 1rem;
}
.switch.large .switch-paddle::after {
  top: 0.25rem;
  left: 0.25rem;
  width: 2rem;
  height: 2rem;
}
.switch.large input:checked ~ .switch-paddle::after {
  left: 2.75rem;
}

table {
  width: 100%;
  margin-bottom: 1rem;
  border-radius: 0;
}
table thead,
table tbody,
table tfoot {
  border: 1px solid #f1f1f1;
  background-color: #fefefe;
}
table caption {
  padding: 0.5rem 0.625rem 0.625rem;
  font-weight: bold;
}
table thead {
  background: #f8f8f8;
  color: #0a0a0a;
}
table tfoot {
  background: #f1f1f1;
  color: #0a0a0a;
}
table thead tr,
table tfoot tr {
  background: transparent;
}
table thead th,
table thead td,
table tfoot th,
table tfoot td {
  padding: 0.5rem 0.625rem 0.625rem;
  font-weight: bold;
  text-align: left;
}
table tbody th,
table tbody td {
  padding: 0.5rem 0.625rem 0.625rem;
}
table tbody tr:nth-child(even) {
  border-bottom: 0;
  background-color: #f1f1f1;
}
table.unstriped tbody {
  background-color: #fefefe;
}
table.unstriped tbody tr {
  border-bottom: 0;
  border-bottom: 1px solid #f1f1f1;
  background-color: #fefefe;
}

@media screen and (max-width: 63.9375em) {
  table.stack thead {
    display: none;
  }
  table.stack tfoot {
    display: none;
  }
  table.stack tr,
  table.stack th,
  table.stack td {
    display: block;
  }
  table.stack td {
    border-top: 0;
  }
}

table.scroll {
  display: block;
  width: 100%;
  overflow-x: auto;
}

table.hover thead tr:hover {
  background-color: #f3f3f3;
}

table.hover tfoot tr:hover {
  background-color: #ececec;
}

table.hover tbody tr:hover {
  background-color: #f9f9f9;
}

table.hover:not(.unstriped) tr:nth-of-type(even):hover {
  background-color: #ececec;
}

.table-scroll {
  overflow-x: auto;
}
.table-scroll table {
  width: auto;
}

.tabs {
  margin: 0;
  border: 1px solid #e6e6e6;
  background: #fefefe;
  list-style-type: none;
}
.tabs::before,
.tabs::after {
  display: table;
  content: " ";
}
.tabs::after {
  clear: both;
}

.tabs.vertical > li {
  display: block;
  float: none;
  width: auto;
}

.tabs.simple > li > a {
  padding: 0;
}
.tabs.simple > li > a:hover {
  background: transparent;
}

.tabs.primary {
  background: #1779ba;
}
.tabs.primary > li > a {
  color: #fefefe;
}
.tabs.primary > li > a:hover,
.tabs.primary > li > a:focus {
  background: #1673b1;
}

.tabs-title {
  float: left;
}
.tabs-title > a {
  display: block;
  padding: 1.25rem 1.5rem;
  font-size: 0.75rem;
  line-height: 1;
  color: #1779ba;
}
.tabs-title > a:hover {
  background: #fefefe;
  color: #1468a0;
}
.tabs-title > a:focus,
.tabs-title > a[aria-selected="true"] {
  background: #e6e6e6;
  color: #1779ba;
}

.tabs-content {
  border: 1px solid #e6e6e6;
  border-top: 0;
  background: #fefefe;
  color: #0a0a0a;
  transition: all 0.5s ease;
}

.tabs-content.vertical {
  border: 1px solid #e6e6e6;
  border-left: 0;
}

.tabs-panel {
  display: none;
  padding: 1rem;
}
.tabs-panel[aria-hidden="false"] {
  display: block;
}

.thumbnail {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 1rem;
  border: solid 4px #fefefe;
  border-radius: 0;
  box-shadow: 0 0 0 1px rgba(10, 10, 10, 0.2);
  line-height: 0;
}

a.thumbnail {
  transition: box-shadow 200ms ease-out;
}
a.thumbnail:hover,
a.thumbnail:focus {
  box-shadow: 0 0 6px 1px rgba(23, 121, 186, 0.5);
}
a.thumbnail image {
  box-shadow: none;
}

.title-bar {
  padding: 0.5rem;
  background: #0a0a0a;
  color: #fefefe;
}
.title-bar::before,
.title-bar::after {
  display: table;
  content: " ";
}
.title-bar::after {
  clear: both;
}
.title-bar .menu-icon {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.title-bar-left {
  float: left;
}

.title-bar-right {
  float: right;
  text-align: right;
}

.title-bar-title {
  display: inline-block;
  vertical-align: middle;
  font-weight: bold;
}

.has-tip {
  position: relative;
  display: inline-block;
  border-bottom: dotted 1px #8a8a8a;
  font-weight: bold;
  cursor: help;
}

.tooltip {
  position: absolute;
  top: calc(100% + 0.6495rem);
  z-index: 1200;
  max-width: 10rem;
  padding: 0.75rem;
  border-radius: 0;
  background-color: #0a0a0a;
  font-size: 80%;
  color: #fefefe;
}
.tooltip::before {
  display: block;
  width: 0;
  height: 0;
  border: inset 0.75rem;
  content: "";
  border-top-width: 0;
  border-bottom-style: solid;
  border-color: transparent transparent #0a0a0a;
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
}
.tooltip.top::before {
  display: block;
  width: 0;
  height: 0;
  border: inset 0.75rem;
  content: "";
  border-bottom-width: 0;
  border-top-style: solid;
  border-color: #0a0a0a transparent transparent;
  top: 100%;
  bottom: auto;
}
.tooltip.left::before {
  display: block;
  width: 0;
  height: 0;
  border: inset 0.75rem;
  content: "";
  border-right-width: 0;
  border-left-style: solid;
  border-color: transparent transparent transparent #0a0a0a;
  top: 50%;
  bottom: auto;
  left: 100%;
  transform: translateY(-50%);
}
.tooltip.right::before {
  display: block;
  width: 0;
  height: 0;
  border: inset 0.75rem;
  content: "";
  border-left-width: 0;
  border-right-style: solid;
  border-color: transparent #0a0a0a transparent transparent;
  top: 50%;
  right: 100%;
  bottom: auto;
  left: auto;
  transform: translateY(-50%);
}

.top-bar {
  padding: 0.5rem;
}
.top-bar::before,
.top-bar::after {
  display: table;
  content: " ";
}
.top-bar::after {
  clear: both;
}
.top-bar,
.top-bar ul {
  background-color: #e6e6e6;
}
.top-bar input {
  max-width: 200px;
  margin-right: 1rem;
}
.top-bar .input-group-field {
  width: 100%;
  margin-right: 0;
}
.top-bar input.button {
  width: auto;
}
.top-bar .top-bar-left,
.top-bar .top-bar-right {
  width: 100%;
}
@media print, screen and (min-width: 40em) {
  .top-bar .top-bar-left,
  .top-bar .top-bar-right {
    width: auto;
  }
}
@media screen and (max-width: 63.9375em) {
  .top-bar.stacked-for-medium .top-bar-left,
  .top-bar.stacked-for-medium .top-bar-right {
    width: 100%;
  }
}
@media screen and (max-width: 74.9375em) {
  .top-bar.stacked-for-large .top-bar-left,
  .top-bar.stacked-for-large .top-bar-right {
    width: 100%;
  }
}

.top-bar-title {
  display: inline-block;
  float: left;
  padding: 0.5rem 1rem 0.5rem 0;
}
.top-bar-title .menu-icon {
  bottom: 2px;
}

.top-bar-left {
  float: left;
}

.top-bar-right {
  float: right;
}

.hide {
  display: none !important;
}

.invisible {
  visibility: hidden;
}

@media screen and (max-width: 39.9375em) {
  .hide-for-small-only {
    display: none !important;
  }
}

@media screen and (max-width: 0em), screen and (min-width: 40em) {
  .show-for-small-only {
    display: none !important;
  }
}

@media print, screen and (min-width: 40em) {
  .hide-for-medium {
    display: none !important;
  }
}

@media screen and (max-width: 39.9375em) {
  .show-for-medium {
    display: none !important;
  }
}

@media screen and (min-width: 40em) and (max-width: 63.9375em) {
  .hide-for-medium-only {
    display: none !important;
  }
}

@media screen and (max-width: 39.9375em), screen and (min-width: 64em) {
  .show-for-medium-only {
    display: none !important;
  }
}

@media print, screen and (min-width: 64em) {
  .hide-for-large {
    display: none !important;
  }
}

@media screen and (max-width: 63.9375em) {
  .show-for-large {
    display: none !important;
  }
}

@media screen and (min-width: 64em) and (max-width: 74.9375em) {
  .hide-for-large-only {
    display: none !important;
  }
}

@media screen and (max-width: 63.9375em), screen and (min-width: 75em) {
  .show-for-large-only {
    display: none !important;
  }
}

.show-for-sr,
.show-on-focus {
  position: absolute !important;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
}

.show-on-focus:active,
.show-on-focus:focus {
  position: static !important;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
}

.show-for-landscape,
.hide-for-portrait {
  display: block !important;
}
@media screen and (orientation: landscape) {
  .show-for-landscape,
  .hide-for-portrait {
    display: block !important;
  }
}
@media screen and (orientation: portrait) {
  .show-for-landscape,
  .hide-for-portrait {
    display: none !important;
  }
}

.hide-for-landscape,
.show-for-portrait {
  display: none !important;
}
@media screen and (orientation: landscape) {
  .hide-for-landscape,
  .show-for-portrait {
    display: none !important;
  }
}
@media screen and (orientation: portrait) {
  .hide-for-landscape,
  .show-for-portrait {
    display: block !important;
  }
}

.float-left {
  float: left !important;
}

.float-right {
  float: right !important;
}

.float-center {
  display: block;
  margin-right: auto;
  margin-left: auto;
}

.clearfix::before,
.clearfix::after {
  display: table;
  content: " ";
}

.clearfix::after {
  clear: both;
}

.billboard {
  background: rgba(0, 0, 0, 0)
    url("https://remindermedia.com/wp-content/themes/JointsWP-CSS-master/library/img/login-page-bg.jpg")
    repeat scroll center center / cover;
}
@charset "UTF-8";

.dropup,
.dropright,
.dropdown,
.dropleft {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  text-align: left;
  list-style: none;
  background-clip: padding-box;
  border-radius: 0.25rem;
}

.dropdown-menu-right {
  right: 0;
  left: auto;
}

.dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}

.dropright .dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}

.dropright .dropdown-toggle::after {
  vertical-align: 0;
}

.dropleft .dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}

.dropleft .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-menu[x-placement^="top"],
.dropdown-menu[x-placement^="right"],
.dropdown-menu[x-placement^="bottom"],
.dropdown-menu[x-placement^="left"] {
  right: auto;
  bottom: auto;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}

.dropdown-item.active,
.dropdown-item:active {
  text-decoration: none;
}

.dropdown-item.disabled,
.dropdown-item:disabled {
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: 0.5rem 1.5rem;
  margin-bottom: 0;
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: 0.25rem 1.5rem;
}

/* ============================================================================
   Base
   ============================================================================ */

html {
  font-size: 100%;
}

body {
  background: #f1f0f5;
  color: #51606d;
  font-family: "Nunito Sans", Arial, Helvetica, sans-serif;
}

button {
  cursor: pointer;
}

/* ============================================================================
   Grid
   ============================================================================ */

.row {
  position: relative;
}

.row.expanded .row {
  margin-left: -0.625rem;
  margin-right: -0.625rem;
}

.column,
.columns {
  position: relative;
}

@media screen and (min-width: 40em) {
  .row.expanded .row {
    margin-left: -0.9375rem;
    margin-right: -0.9375rem;
  }
}

/* Extra Large */

@media print, screen and (min-width: 100em) {
  .xlarge-1 {
    width: 8.33333%;
  }

  .xlarge-push-1 {
    position: relative;
    left: 8.33333%;
  }

  .xlarge-pull-1 {
    position: relative;
    left: -8.33333%;
  }

  .xlarge-offset-0 {
    margin-left: 0%;
  }

  .xlarge-2 {
    width: 16.66667%;
  }

  .xlarge-push-2 {
    position: relative;
    left: 16.66667%;
  }

  .xlarge-pull-2 {
    position: relative;
    left: -16.66667%;
  }

  .xlarge-offset-1 {
    margin-left: 8.33333%;
  }

  .xlarge-3 {
    width: 25%;
  }

  .xlarge-push-3 {
    position: relative;
    left: 25%;
  }

  .xlarge-pull-3 {
    position: relative;
    left: -25%;
  }

  .xlarge-offset-2 {
    margin-left: 16.66667%;
  }

  .xlarge-4 {
    width: 33.33333%;
  }

  .xlarge-push-4 {
    position: relative;
    left: 33.33333%;
  }

  .xlarge-pull-4 {
    position: relative;
    left: -33.33333%;
  }

  .xlarge-offset-3 {
    margin-left: 25%;
  }

  .xlarge-5 {
    width: 41.66667%;
  }

  .xlarge-push-5 {
    position: relative;
    left: 41.66667%;
  }

  .xlarge-pull-5 {
    position: relative;
    left: -41.66667%;
  }

  .xlarge-offset-4 {
    margin-left: 33.33333%;
  }

  .xlarge-6 {
    width: 50%;
  }

  .xlarge-push-6 {
    position: relative;
    left: 50%;
  }

  .xlarge-pull-6 {
    position: relative;
    left: -50%;
  }

  .xlarge-offset-5 {
    margin-left: 41.66667%;
  }

  .xlarge-7 {
    width: 58.33333%;
  }

  .xlarge-push-7 {
    position: relative;
    left: 58.33333%;
  }

  .xlarge-pull-7 {
    position: relative;
    left: -58.33333%;
  }

  .xlarge-offset-6 {
    margin-left: 50%;
  }

  .xlarge-8 {
    width: 66.66667%;
  }

  .xlarge-push-8 {
    position: relative;
    left: 66.66667%;
  }

  .xlarge-pull-8 {
    position: relative;
    left: -66.66667%;
  }

  .xlarge-offset-7 {
    margin-left: 58.33333%;
  }

  .xlarge-9 {
    width: 75%;
  }

  .xlarge-push-9 {
    position: relative;
    left: 75%;
  }

  .xlarge-pull-9 {
    position: relative;
    left: -75%;
  }

  .xlarge-offset-8 {
    margin-left: 66.66667%;
  }

  .xlarge-10 {
    width: 83.33333%;
  }

  .xlarge-push-10 {
    position: relative;
    left: 83.33333%;
  }

  .xlarge-pull-10 {
    position: relative;
    left: -83.33333%;
  }

  .xlarge-offset-9 {
    margin-left: 75%;
  }

  .xlarge-11 {
    width: 91.66667%;
  }

  .xlarge-push-11 {
    position: relative;
    left: 91.66667%;
  }

  .xlarge-pull-11 {
    position: relative;
    left: -91.66667%;
  }

  .xlarge-offset-10 {
    margin-left: 83.33333%;
  }

  .xlarge-12 {
    width: 100%;
  }

  .xlarge-offset-11 {
    margin-left: 91.66667%;
  }

  .xlarge-up-1 > .column,
  .xlarge-up-1 > .columns {
    float: left;
    width: 100%;
  }

  .xlarge-up-1 > .column:nth-of-type(1n),
  .xlarge-up-1 > .columns:nth-of-type(1n) {
    clear: none;
  }

  .xlarge-up-1 > .column:nth-of-type(1n + 1),
  .xlarge-up-1 > .columns:nth-of-type(1n + 1) {
    clear: both;
  }

  .xlarge-up-1 > .column:last-child,
  .xlarge-up-1 > .columns:last-child {
    float: left;
  }

  .xlarge-up-2 > .column,
  .xlarge-up-2 > .columns {
    float: left;
    width: 50%;
  }

  .xlarge-up-2 > .column:nth-of-type(1n),
  .xlarge-up-2 > .columns:nth-of-type(1n) {
    clear: none;
  }

  .xlarge-up-2 > .column:nth-of-type(2n + 1),
  .xlarge-up-2 > .columns:nth-of-type(2n + 1) {
    clear: both;
  }

  .xlarge-up-2 > .column:last-child,
  .xlarge-up-2 > .columns:last-child {
    float: left;
  }

  .xlarge-up-3 > .column,
  .xlarge-up-3 > .columns {
    float: left;
    width: 33.33333%;
  }

  .xlarge-up-3 > .column:nth-of-type(1n),
  .xlarge-up-3 > .columns:nth-of-type(1n) {
    clear: none;
  }

  .xlarge-up-3 > .column:nth-of-type(3n + 1),
  .xlarge-up-3 > .columns:nth-of-type(3n + 1) {
    clear: both;
  }

  .xlarge-up-3 > .column:last-child,
  .xlarge-up-3 > .columns:last-child {
    float: left;
  }

  .xlarge-up-4 > .column,
  .xlarge-up-4 > .columns {
    float: left;
    width: 25%;
  }

  .xlarge-up-4 > .column:nth-of-type(1n),
  .xlarge-up-4 > .columns:nth-of-type(1n) {
    clear: none;
  }

  .xlarge-up-4 > .column:nth-of-type(4n + 1),
  .xlarge-up-4 > .columns:nth-of-type(4n + 1) {
    clear: both;
  }

  .xlarge-up-4 > .column:last-child,
  .xlarge-up-4 > .columns:last-child {
    float: left;
  }

  .xlarge-up-5 > .column,
  .xlarge-up-5 > .columns {
    float: left;
    width: 20%;
  }

  .xlarge-up-5 > .column:nth-of-type(1n),
  .xlarge-up-5 > .columns:nth-of-type(1n) {
    clear: none;
  }

  .xlarge-up-5 > .column:nth-of-type(5n + 1),
  .xlarge-up-5 > .columns:nth-of-type(5n + 1) {
    clear: both;
  }

  .xlarge-up-5 > .column:last-child,
  .xlarge-up-5 > .columns:last-child {
    float: left;
  }

  .xlarge-up-6 > .column,
  .xlarge-up-6 > .columns {
    float: left;
    width: 16.66667%;
  }

  .xlarge-up-6 > .column:nth-of-type(1n),
  .xlarge-up-6 > .columns:nth-of-type(1n) {
    clear: none;
  }

  .xlarge-up-6 > .column:nth-of-type(6n + 1),
  .xlarge-up-6 > .columns:nth-of-type(6n + 1) {
    clear: both;
  }

  .xlarge-up-6 > .column:last-child,
  .xlarge-up-6 > .columns:last-child {
    float: left;
  }

  .xlarge-up-7 > .column,
  .xlarge-up-7 > .columns {
    float: left;
    width: 14.28571%;
  }

  .xlarge-up-7 > .column:nth-of-type(1n),
  .xlarge-up-7 > .columns:nth-of-type(1n) {
    clear: none;
  }

  .xlarge-up-7 > .column:nth-of-type(7n + 1),
  .xlarge-up-7 > .columns:nth-of-type(7n + 1) {
    clear: both;
  }

  .xlarge-up-7 > .column:last-child,
  .xlarge-up-7 > .columns:last-child {
    float: left;
  }

  .xlarge-up-8 > .column,
  .xlarge-up-8 > .columns {
    float: left;
    width: 12.5%;
  }

  .xlarge-up-8 > .column:nth-of-type(1n),
  .xlarge-up-8 > .columns:nth-of-type(1n) {
    clear: none;
  }

  .xlarge-up-8 > .column:nth-of-type(8n + 1),
  .xlarge-up-8 > .columns:nth-of-type(8n + 1) {
    clear: both;
  }

  .xlarge-up-8 > .column:last-child,
  .xlarge-up-8 > .columns:last-child {
    float: left;
  }

  .xlarge-collapse > .column,
  .xlarge-collapse > .columns {
    padding-right: 0;
    padding-left: 0;
  }

  .xlarge-collapse .row {
    margin-right: 0;
    margin-left: 0;
  }

  .expanded.row .xlarge-collapse.row {
    margin-right: 0;
    margin-left: 0;
  }

  .xlarge-uncollapse > .column,
  .xlarge-uncollapse > .columns {
    padding-right: 0.9375rem;
    padding-left: 0.9375rem;
  }

  .xlarge-centered {
    margin-right: auto;
    margin-left: auto;
  }

  .xlarge-centered,
  .xlarge-centered:last-child:not(:first-child) {
    float: none;
    clear: both;
  }

  .xlarge-uncentered,
  .xlarge-push-0,
  .xlarge-pull-0 {
    position: static;
    float: left;
    margin-right: 0;
    margin-left: 0;
  }
}

/* Grid padding
   ============================================================================ */

/* Extra Small */

.grid-padding-xsmall.expanded .row {
  margin-left: -0.3125rem;
  margin-right: -0.3125rem;
}

.grid-padding-xsmall .column {
  padding-left: 0.3125rem;
  padding-right: 0.3125rem;
}

.grid-padding-xsmall .column-block {
  margin-bottom: 0.625rem;
}

/* Small */

.grid-padding-small.expanded .row {
  margin-left: -0.46875rem;
  margin-right: -0.46875rem;
}

.grid-padding-small .column {
  padding-left: 0.46875rem;
  padding-right: 0.46875rem;
}

.grid-padding-small .column-block {
  margin-bottom: 0.9375rem;
}

/* ============================================================================
   Visibility
   ============================================================================ */

/* Extra Large */

@media print, screen and (min-width: 100em) {
  .hide-for-xlarge {
    display: none !important;
  }
}

@media screen and (max-width: 99.9375em) {
  .show-for-xlarge {
    display: none !important;
  }
}

/* ============================================================================
   Text Alignment
   ============================================================================ */

@media screen and (max-width: 39.9375em) {
  .small-text-left {
    text-align: left;
  }

  .small-text-right {
    text-align: right;
  }

  .small-text-center {
    text-align: center;
  }

  .small-text-justify {
    text-align: justify;
  }
}

/* ============================================================================
   Disabled
   ============================================================================ */

.disabled,
[disabled] {
  opacity: 0.31 !important;
  cursor: not-allowed;
}

.badge.disabled,
.badge[disabled] {
  opacity: 0.25 !important;
  cursor: not-allowed;
}

/* ============================================================================
   Resets
   ============================================================================ */

nav ul,
nav li {
  list-style: none;
  margin: 0;
  padding: 0;
}

nav a,
nav a:hover,
nav a:focus {
  border: 0;
}

#darkroom-icons {
  display: none;
}

/* Foundation */

.top-bar ul {
  background: none;
}

.has-tip {
  border: 0;
}

.menu > li > a i {
  margin-right: 0;
}

.is-active {
  display: block;
}

/* ============================================================================
   Typography
   ============================================================================ */

p,
li,
dt,
dd,
pre {
  margin: 0 0 1.5rem;
  font-size: 0.875em;
  line-height: 1.5;
}

li p {
  font-size: 1em;
}

h1,
h2,
h3,
h4,
h5,
h6,
legend,
.h1 {
  margin: 0;
  color: #51606d;
  font-family: "Nunito Sans", Arial, Helvetica, sans-serif;
  line-height: 1.25;
  font-weight: 300;
}

/* Heading 1
   ============================================================================ */

h1,
.h1 {
  padding: 0 0 0.46em;
  font-size: 1.75em;
}

/* Heading 2
   ============================================================================ */

h2,
legend {
  padding: 1em 0;
  font-size: 1.375em;
}

/* Heading 3
   ============================================================================ */

h3,
fieldset fieldset legend {
  padding: 1.375em 0;
  font-size: 1.375em;
  font-weight: 400;
}

/* Heading 4
   ============================================================================ */

h4 {
  padding: 0.8125em 0;
  font-size: 1.125em;
  font-weight: 400;
}

/* Heading 5
   ============================================================================ */

h5 {
  padding: 0.875em 0;
  font-size: 1em;
  font-weight: 700;
  text-transform: uppercase;
}

/* Heading 6
   ============================================================================ */

h6 {
  padding: 0.5em 0;
  font-size: 0.875em;
  font-weight: 700;
}

/* Subtitles
   ============================================================================ */

.subtitle {
  display: block;
  padding-top: 0 !important;
}

h1 + .subtitle {
  margin-top: -1em;
}

h2 + .subtitle {
  margin-top: -1em;
}

h3 + .subtitle {
  margin-top: -1em;
}

h4 + .subtitle {
  margin-top: -0.8125em;
}

/* Supertitles
   ============================================================================ */

.suptitle {
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
}

.suptitle + h1,
.suptitle + h2,
.suptitle + h3,
.suptitle + h4,
.suptitle + h5,
.suptitle + h6 {
  padding-top: 0;
}

/* Text Styles
   ============================================================================ */

/* Rule */

.text-hr {
  background-image: linear-gradient(
    rgba(0, 0, 0, 0) 0px,
    rgba(0, 0, 0, 0) 23px,
    #d7d7d7 23px,
    #d7d7d7 24px,
    rgba(0, 0, 0, 0) 24px,
    rgba(0, 0, 0, 0) 23px,
    #d7d7d7 23px,
    #d7d7d7 24px,
    rgba(0, 0, 0, 0) 24px
  );
}

h1.text-hr {
  background-position: 0 5px;
}

h2.text-hr {
  background-position: 0 10px;
}

h5.text-hr {
  background-position: 0 2px;
}

p.text-hr {
  background-position: 0 0.75rem;
}

.text-hr-inner {
  background: #fff;
  padding: 0 0.5em;
}

/* Text Sizes */

.text-xsmall,
.text-xsmall > li {
  font-size: 0.625rem !important;
}

.text-small,
.text-small > li {
  font-size: 0.75rem !important;
}

.text-medium,
.text-medium > li {
  font-size: 0.875rem !important;
}

.text-large,
.text-large > li {
  font-size: 1rem !important;
}

.text-xlarge,
.text-xlarge > li {
  font-size: 1.25rem !important;
}

.text-xxlarge,
.text-xxlarge > li {
  font-size: 1.75rem !important;
}

.text-xxxlarge,
.text-xxxlarge > li {
  font-size: 2.75rem !important;
}

.text-xxxxlarge,
.text-xxxxlarge > li {
  font-size: 3.75rem !important;
}

/* Text Colors */

.text-color1,
.icon-color1::before,
.icon-color1.list-icon li::before {
  color: #71acd1 !important;
}

.text-color2,
.icon-color2::before,
.icon-color2.list-icon li::before {
  color: #ff4649 !important;
}

.text-color3,
.icon-color3::before,
.icon-color3.list-icon li::before {
  color: #76bb76 !important;
}

.text-color4,
.icon-color4::before,
.icon-color4.list-icon li::before {
  color: #51606d !important;
}

.text-light,
.text-color5,
.icon-color5::before,
.icon-color5.list-icon li::before {
  color: #b1b0b5 !important;
}

.text-color6,
.icon-color6::before,
.icon-color6.list-icon li::before {
  color: #edb15f !important;
}

.text-white,
.text-color7,
.icon-color7::before,
.icon-color7.list-icon li::before {
  color: #fff !important;
}

.text-color8,
.icon-color8::before,
.icon-color8.list-icon li::before {
  color: #f1f0f5 !important;
}

.text-color9,
.icon-color9::before,
.icon-color9.list-icon li::before {
  color: #234 !important;
}

.text-color10,
.icon-color10::before,
.icon-color10.list-icon li::before {
  color: #f9f9f9 !important;
}

.text-color11,
.icon-color11::before,
.icon-color11.list-icon li::before {
  color: #ca82e6 !important;
}

.text-color12,
.icon-color12::before,
.icon-color12.list-icon li::before {
  color: #e3e2e6 !important;
}

.text-color13,
.icon-color13::before,
.icon-color13.list-icon li::before {
  color: #f9ff50 !important;
}

.text-black,
.text-color14,
.icon-color14::before,
.icon-color14.list-icon li::before {
  color: #202020 !important;
}

.text-color15,
.icon-color15::before,
.icon-color15.list-icon li::before {
  color: #2d2d2d !important;
}

.text-color16,
.icon-color16::before,
.icon-color16.list-icon li::before {
  color: #484848 !important;
}

.text-dark,
.text-color17,
.icon-color17::before,
.icon-color17.list-icon li::before {
  color: gray !important;
}

.text-color18,
.icon-color18::before,
.icon-color18.list-icon li::before {
  color: #ff74af !important;
}

/* Text Weights */

.text-weight-light,
.font-weight-light {
  font-weight: 300 !important;
}

.text-weight-normal,
.font-weight-normal {
  font-weight: 400 !important;
}

.text-bold,
.text-weight-bold,
.font-weight-bold {
  font-weight: 700 !important;
}

.text-xbold,
.text-weight-xbold,
.font-weight-xbold {
  font-weight: 800 !important;
}

/* Text Transform */

.text-caps,
.text-transform-uppercase {
  text-transform: uppercase !important;
}

.text-transform-none {
  text-transform: none !important;
}

/* Text Style */

.text-italic,
.text-style-italic {
  font-style: italic;
}

/* Text Decoration */

.text-strikethrough,
.text-decoration-line-through {
  text-decoration: line-through;
}

.text-underline,
.text-decoration-underline {
  text-decoration: underline;
}

.text-underline:hover,
.text-underline:focus,
.text-decoration-underline:hover,
.text-decoration-underline:focus {
  text-decoration: none;
}

.text-underline-hover:hover,
.text-underline-hover:focus {
  text-decoration: underline;
}

/* Text Shadow */

.text-shadow-light {
  text-shadow:
    0 0 20px #fff,
    0 0 20px #fff,
    0 0 20px #fff,
    0 0 20px #fff,
    0 0 20px #fff,
    0 0 20px #fff,
    0 0 20px #fff;
}

.text-shadow-dark {
  text-shadow:
    0 0 20px #000,
    0 0 20px #000,
    0 0 20px #000,
    0 0 20px #000,
    0 0 20px #000,
    0 0 20px #000,
    0 0 20px #000;
}

/* Line Height */

.line-height-xsmall {
  line-height: 1;
}

.line-height-small {
  line-height: 1.25;
}

.line-height-medium,
.line-height-normal {
  line-height: 1.5;
}

.line-height-large {
  line-height: 2;
}

/* Font Family */

.text-montserrat {
  font-family: "Montserrat", Arial, Helvetica, sans-serif;
  font-weight: 500;
}

/* ============================================================================
   Rule
   ============================================================================ */

hr {
  border: 0;
  border-bottom: 1px solid #f1f0f5;
  clear: both;
  height: 0;
  max-width: none;
  margin: 0 0 1.5rem;
  padding: 0;
}

/* ============================================================================
   Links
   ============================================================================ */

a {
  color: #71acd1;
  text-decoration: none;
  outline: none;
}

a:hover,
a:focus {
  color: #51606d;
  cursor: pointer;
}

a:active {
  transition: 0s !important;
}

/* ============================================================================
   Selected
   ============================================================================ */

/* Selected Styles
   ============================================================================ */

.selected-style-1 {
  background-color: #f9f9f9;
  box-shadow: 0 0 0 8px #76bb76 inset;
}

.selected-style-2 {
  box-shadow: 0 0 0 8px #76bb76;
}

.selected-style-3 {
  box-shadow: 0 0 0 8px #71acd1;
}

/* ============================================================================
   Form
   ============================================================================ */

label {
  display: block;
  cursor: pointer;
  color: inherit;
  font-size: 0.875rem;
  font-weight: 700;
  line-height: 2;
  text-align: left;
}

label.radio {
  font-weight: 400;
}

[type="checkbox"] + label,
[type="radio"] + label {
  overflow: hidden;
  display: block;
  margin: 0 0 0.8rem;
  line-height: 1.5;
  text-align: left;
}

[type="checkbox"],
[type="radio"] {
  float: left;
  margin: 0.3rem 0.5rem 0 0;
}

label > [type="checkbox"],
label > [type="radio"] {
  margin-top: 0.5rem;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.form input,
.form select,
.form textarea {
  background-color: #fff;
  border: 1px solid #d4d3d8;
  border-radius: 5px;
  box-shadow: none;
  margin-bottom: 1.5rem;
  width: 100%;
  height: auto;
  padding: 1em;
  color: #51606d;
  font:
    0.875rem/1.5 "Nunito Sans",
    Arial,
    Helvetica,
    sans-serif;
  transition: none;
}

.form input:focus,
.form select:focus,
.form textarea:focus {
  border-color: #f1f0f5;
  transition: none;
  box-shadow: 0 0 5px #71acd1;
}

.form input::-webkit-input-placeholder,
.form textarea::-webkit-input-placeholder {
  color: #b1b0b5;
  opacity: 1;
}

.form input::-moz-placeholder,
.form textarea::-moz-placeholder {
  color: #b1b0b5;
  opacity: 1;
}

.form input:-ms-input-placeholder,
.form textarea:-ms-input-placeholder {
  color: #b1b0b5;
  opacity: 1;
}

.form input::-ms-input-placeholder,
.form textarea::-ms-input-placeholder {
  color: #b1b0b5;
  opacity: 1;
}

.form input::placeholder,
.form textarea::placeholder {
  color: #b1b0b5;
  opacity: 1;
}

.form select {
  background-position: right -1em center;
  padding-right: 2em !important;
}

.form textarea {
  min-height: 10rem;
  resize: vertical;
}

.form [type="checkbox"],
.form [type="radio"] {
  width: auto;
  margin-bottom: 0;
}

.form [type="file"] {
  border: 0;
  padding: 0;
}

.form input.radius-full {
  padding-left: 1.5em;
  padding-right: 1.5em;
}

/* ============================================================================
   Form Input Icon

   <div class="input-icon-container input-icon-container-left input-icon-container-right">
       <div class="icon input-icon-left ion-search"></div>
       <button class="icon input-icon-right ion-android-close"></button>
       <label>
           <span class="show-for-sr">Search</span>
           <input
               type="text"
               placeholder="Search..."
           >
       </label>
   </div>
   ============================================================================ */

.input-icon-container {
  position: relative;
}

.input-icon-container .icon {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}

.input-icon-container .icon:before {
  opacity: 1;
  margin: 0;
}

/* Left */

.form .input-icon-container-left input,
.form .input-icon-container-left textarea {
  padding-left: 3.5em;
}

.input-icon-left {
  left: 1em;
}

/* Right */

.form .input-icon-container-right input,
.form .input-icon-container-right textarea {
  padding-right: 3.5em;
}

.input-icon-right {
  right: 1em;
}

/* ============================================================================
   Form Input Image

   <div class="input-image-container input-image-container-left input-image-container-right">
       <img class="input-image-left" src="/path/image.png" alt="">
       <img class="input-image-right" src="/path/image.png" alt="">
       <label>
           Card Number
           <input type="text">
       </label>
   </div>
   ============================================================================ */

.input-image-container {
  position: relative;
}

.input-image-container img {
  width: 3.375rem;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}

/* Left */

.form .input-image-container-left input {
  padding-left: 5em;
}

.input-image-left {
  left: 0.625rem;
}

/* Right */

.form .input-image-container-right input {
  padding-right: 5em;
}

.input-image-right {
  right: 0.625rem;
}

/* Form Input Color
   ============================================================================ */

.input-color-panel {
  border-radius: 5px;
  width: 1.5rem;
  height: 1.5rem;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}

/* Left */

.input-color-left input {
  padding-left: 3em;
}

.input-color-left .input-color-panel {
  left: 1em;
}

/* Right */

.input-color-right input {
  padding-right: 3em;
}

.input-color-right .input-color-panel {
  right: 1em;
}

@media screen and (min-width: 64em) and (max-width: 79.9375em) {
  .input-color-left input,
  .input-color-right input {
    padding: 1em;
  }

  .input-color-panel {
    display: none;
  }
}

@media screen and (min-width: 80em) and (max-width: 99.9375em) {
  .input-color-panel {
    width: 1.25rem;
    height: 1.25rem;
  }

  /* Left */

  .input-color-left input {
    padding-left: 2.6em;
  }

  .input-color-left .input-color-panel {
    left: 0.8em;
  }

  /* Right */

  .input-color-right input {
    padding-right: 2.6em;
  }

  .input-color-right .input-color-panel {
    right: 0.8em;
  }
}

/* Form Button Inline
   ============================================================================ */

.form-button-inline {
  position: relative;
}

.form-button-inline input {
  padding-left: 3em;
}

.form-button-inline button {
  position: absolute;
  left: 1em;
  top: 50%;
  cursor: pointer;
  transform: translate(0, -50%);
}

/* Form Row Icon
   ============================================================================ */

.form .form-icon {
  position: absolute;
  left: -4rem;
  top: 0.4375rem;
}

@media screen and (max-width: 39.9375em) {
  .form .form-icon-medium {
    position: relative;
    top: auto;
    right: auto;
    bottom: auto;
    left: auto;
  }
}

@media screen and (max-width: 63.9375em) {
  .form .form-icon-large {
    position: relative;
    top: auto;
    right: auto;
    bottom: auto;
    left: auto;
  }
}

@media screen and (max-width: 99.9375em) {
  .form .form-icon-xlarge {
    position: relative;
    top: auto;
    right: auto;
    bottom: auto;
    left: auto;
  }
}

.form .form-icon-bottom {
  top: auto;
  bottom: 1.9375rem;
}

.form .form-icon-bottom-small {
  top: auto;
  bottom: 0.4375rem;
}

.form .form-icon-left {
  left: -4rem;
}

.form .form-icon-right,
.form .form-icon-right-2 {
  left: auto;
}

.form .form-icon-right {
  right: -2.75rem;
}

.form .form-icon-right-2 {
  right: -5.25rem;
}

@media screen and (min-width: 40em) {
  .form .form-icon-right {
    right: -3.5rem;
  }

  .form .form-icon-right-2 {
    right: -7rem;
  }
}

/* Form Validation
   ============================================================================ */

.control-group.error label {
  color: #ff4649;
}

.control-group.error input,
.control-group.error select,
.control-group.error textarea,
.control-group.error input:focus,
.control-group.error select:focus,
.control-group.error textarea:focus {
  border-color: #ff4649;
  box-shadow: 0 0 0 1px #ff4649 inset;
}

label.error {
  margin: -1rem 0 1rem;
  color: #ff4649;
  font-weight: 700;
  font-size: 12px;
}

/* Form Style 2
   ============================================================================ */

.form-style-2 input,
.form-style-2 select,
.form-style-2 textarea {
  border: 1px solid #fff;
  box-shadow: none;
}

.form-style-2 input:focus,
.form-style-2 select:focus,
.form-style-2 textarea:focus {
  border: 1px solid #fff;
}

/* ============================================================================
   Lists
   ============================================================================ */

ol,
ul {
  padding: 0;
  margin: 0 0 1.5rem 1.5rem;
}

/* No Bullet List
   ============================================================================ */

.no-bullet li {
  margin-bottom: 0.5rem;
}

.no-bullet .no-bullet {
  margin-top: 0.5rem;
  margin-left: 1rem;
}

/* Icon List
   ============================================================================ */

.list-icon {
  list-style: none;
  margin-left: 0;
  text-align: left;
}

.list-icon li {
  padding-left: 2em;
  position: relative;
}

.list-icon li::before {
  position: absolute;
  left: 0;
  opacity: 0.62;
  font: normal normal 1.4em/1 "Ionicons";
  speak: none;
  font-variant: normal;
  text-transform: none;
  text-indent: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.list-icon-ion-checkmark li::before {
  content: "\F122";
}

.list-icon-ion-alert-circled li::before {
  content: "\F100";
  opacity: 1;
}

.list-icon-ion-android-arrow-forward li::before {
  content: "\F30F";
}

/* Description List
   ============================================================================ */

dl {
  margin-bottom: 1.5em;
}

dt {
  margin-bottom: 0;
  font-weight: bold;
}

dd {
  margin: 0 0 0.75em;
}

/* Inline */

.list-description-inline dt {
  float: left;
  margin-right: 0.75em;
}

/* Flush */

@media screen and (min-width: 64em) {
  .list-description-flush dt {
    float: left;
    clear: left;
    width: 20%;
    margin-bottom: 0.75em;
    text-align: right;
  }

  .list-description-flush dd {
    float: left;
    width: 78%;
    margin-left: 2%;
  }
}

/* Centered List
   ============================================================================ */

.list-center {
  float: right;
  position: relative;
  right: 50%;
}

.list-center li {
  position: relative;
  left: 50%;
}

/* Inline List
   ============================================================================ */

.list-inline {
  margin-left: 0;
}

.list-inline li {
  background: none;
  border: 0;
  border-top-width: 1px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.25);
  margin: 0;
  padding: 8px 0;
}

.list-inline li:before {
  content: "";
}

.list-inline li:first-child {
  border-top: 0;
  padding-top: 0;
}

@media screen and (min-width: 768px) {
  .list-inline {
    overflow: hidden;
  }

  .list-inline li {
    border-top-width: 0;
    border-left-width: 1px;
    float: left;
    margin-bottom: 10px;
    padding: 0 8px;
  }

  .list-inline li:first-child {
    border-left: 0;
    padding-left: 0;
  }

  /* Right align */

  .list-inline-right {
    float: right;
  }

  .list-inline-right li:first-child {
    padding-left: 8px;
  }

  .list-inline-right li:last-child {
    padding-right: 0;
  }
}

/* Breadcrumb List
   ============================================================================ */

.breadcrumbs li {
  font-size: 14px;
  text-transform: none;
}

.breadcrumbs a {
  color: #b1b0b5;
  text-decoration: none;
}

.breadcrumbs a:hover,
.breadcrumbs a:focus {
  color: #fff;
  text-decoration: none;
}

.breadcrumbs .current a {
  color: #fff;
  font-weight: 700;
}

.breadcrumbs li:not(:last-child)::after {
  content: "\F125";
  color: #b1b0b5;
  font-family: "Ionicons";
  font-size: 12px;
}

/* Button List
   ============================================================================ */

.button-list {
  margin-left: 0;
  list-style: none;
}

.button-list li {
  border: 0;
  background: none;
  margin: 0;
}

.button-list li:before {
  content: "";
}

.button-list a,
.button-list-empty li {
  border: 0 !important;
  display: block;
  padding: 0.75em 1.3rem;
  transition: all 0.3s ease 0s;
}

.button-list a:hover,
.button-list a:focus {
  background: #f7f7f7;
}

.button-list a:active {
  background: #ebebeb;
}

/* Selected */

.button-list a.selected,
.button-list a.selected:hover,
.button-list a.selected:focus {
  background: #71acd1;
  color: #fff;
}

/* Disabled */

.button-list-empty li,
.button-list a.disabled,
.button-list a.disabled:hover,
.button-list a.disabled:focus {
  background: #b1b0b5;
  color: #fff !important;
  cursor: text;
}

/* Icon */

.button-list .button-list-icon {
  border: 0;
  background: none;
  display: inline-block;
  margin: 0;
  padding: 0;
}

.button-list .button-list-icon:hover,
.button-list .button-list-icon:focus {
  background: none;
}

.button-list .button-list-icon.icon:hover:before,
.button-list .button-list-icon.icon:focus:before {
  opacity: 1;
}

/* Inline */

.button-list-inline {
  overflow: hidden;
}

.button-list-inline a,
.button-list-inline.button-list-empty li {
  float: left;
  margin-right: 0.3125rem;
}

/* Ragged */

.button-list-ragged a {
  display: inline-block;
}

/* Style 2 */

.button-list-style-2 a {
  background: #f9f9f9;
  margin-bottom: 0.25rem;
  padding: 0.75em 1.125em;
}

.button-list-style-2 a:hover,
.button-list-style-2 a:focus {
  background: #f2f2f2;
}

.button-list-style-2 a:active {
  background: #eaeaea;
}

/* Style 3 */

.button-list-style-3 a {
  background: #eae9ee;
  margin-bottom: 0.25rem;
  padding: 0.75em 1.125em;
}

.button-list-style-3 a:hover,
.button-list-style-3 a:focus {
  background: #e3e2e6;
}

.button-list-style-3 a:active {
  background: #dbdadf;
}

/* ============================================================================
   Vue Pagination
   ============================================================================ */

.v-pagination {
  display: inline-flex;
  list-style-type: none;
  margin: 0;
  max-width: 100%;
  padding: 0;
  width: auto;
}

.v-pagination,
.v-pagination > li {
  align-items: center;
  margin: 0;
}

.v-pagination > li {
  display: flex;
}

.v-pagination__navigation--disabled {
  opacity: 0.6;
  pointer-events: none;
}

.v-pagination__item,
.v-pagination__navigation {
  box-shadow: none !important;
  font-family: inherit;
}

.v-pagination__item {
  font-size: 0.875rem;
}

.v-pagination .v-pagination__item,
.v-pagination .v-pagination__navigation {
  background: none;
  min-width: 0;
  height: auto;
  margin: 0.125rem;
  padding: 0.4375em 0.714em;
  color: #51606d;
  line-height: 1;
  transition: none;
  border-radius: 5px;
}

/* Hover */

.v-pagination .v-pagination__item:hover,
.v-pagination .v-pagination__navigation:hover {
  background: #b1b0b5;
  color: #fff;
}

/* Active */

.v-pagination .v-pagination__item--active,
.v-pagination .v-pagination__item--active:hover {
  background: #51606d;
  color: #fff;
  cursor: default;
}

/* Icon */

.v-pagination .v-pagination__navigation .v-icon {
  color: #51606d;
  transition: none;
}

.v-pagination .v-pagination__navigation:hover .v-icon {
  color: #fff;
}

/* Disabled */

.v-pagination__navigation--disabled {
  opacity: 0.25;
}

/* ============================================================================
   Gallery

   <div class="gallery gallery-small-col-3 gallery-style-2 clearfix">
       <div class="gallery-item">
           <a href="#">
               <img class="gallery-thumb" src="/xxx.jpg" alt="">
               <div class="gallery-caption">
                   <div class="gallery-inner">
                       <h5 class="gallery-title">Lorem</h5>
                       <i class="gallery-icon icon icon-xxx"></i>
                   </div>
               </div>
           </a>
       </div>
   </div>
   ============================================================================ */

.gallery {
  margin: -1% -1% 1.5rem;
}

table.gallery {
  margin-left: 0;
  margin-right: 0;
}

.gallery-item {
  float: left;
  width: 98%;
  margin: 1%;
  position: relative;
}

.gallery-item > li {
  background: none;
  margin: 0;
  padding: 0;
}

.gallery-item > li:before {
  content: "";
}

.gallery-item > a {
  border: 0;
  display: block;
  margin-right: 0;
  position: relative;
}

/* Gallery Columns
   ============================================================================ */

.gallery-small-col-1 .gallery-item {
  width: 98%;
  margin-bottom: 1.5rem;
}

.gallery-small-col-2 .gallery-item {
  width: 48%;
}

.gallery-small-col-3 .gallery-item {
  width: 31.33%;
}

.gallery-small-col-4 .gallery-item {
  width: 23%;
}

.gallery-small-col-5 .gallery-item {
  width: 18%;
}

.gallery-small-col-6 .gallery-item {
  width: 14.66%;
}

.gallery-small-col-7 .gallery-item {
  width: 12.28%;
}

.gallery-small-col-8 .gallery-item {
  width: 10.5%;
}

.gallery-small-col-9 .gallery-item {
  width: 9.11%;
}

.gallery-small-col-2 .gallery-item:nth-child(n),
.gallery-small-col-3 .gallery-item:nth-child(n),
.gallery-small-col-4 .gallery-item:nth-child(n),
.gallery-small-col-5 .gallery-item:nth-child(n),
.gallery-small-col-6 .gallery-item:nth-child(n),
.gallery-small-col-7 .gallery-item:nth-child(n),
.gallery-small-col-8 .gallery-item:nth-child(n),
.gallery-small-col-9 .gallery-item:nth-child(n) {
  clear: none;
  margin-bottom: 1%;
}

.gallery-small-col-2 .gallery-item:nth-child(2n + 1),
.gallery-small-col-3 .gallery-item:nth-child(3n + 1),
.gallery-small-col-4 .gallery-item:nth-child(4n + 1),
.gallery-small-col-5 .gallery-item:nth-child(5n + 1),
.gallery-small-col-6 .gallery-item:nth-child(6n + 1),
.gallery-small-col-7 .gallery-item:nth-child(7n + 1),
.gallery-small-col-8 .gallery-item:nth-child(8n + 1),
.gallery-small-col-9 .gallery-item:nth-child(9n + 1) {
  clear: left;
}

@media screen and (min-width: 40em) {
  .gallery-medium-col-1 .gallery-item {
    width: 98%;
    margin-bottom: 1.5rem;
  }

  .gallery-medium-col-2 .gallery-item {
    width: 48%;
  }

  .gallery-medium-col-3 .gallery-item {
    width: 31.33%;
  }

  .gallery-medium-col-4 .gallery-item {
    width: 23%;
  }

  .gallery-medium-col-5 .gallery-item {
    width: 18%;
  }

  .gallery-medium-col-6 .gallery-item {
    width: 14.66%;
  }

  .gallery-medium-col-7 .gallery-item {
    width: 12.28%;
  }

  .gallery-medium-col-8 .gallery-item {
    width: 10.5%;
  }

  .gallery-medium-col-9 .gallery-item {
    width: 9.11%;
  }

  .gallery-medium-col-2 .gallery-item:nth-child(n),
  .gallery-medium-col-3 .gallery-item:nth-child(n),
  .gallery-medium-col-4 .gallery-item:nth-child(n),
  .gallery-medium-col-5 .gallery-item:nth-child(n),
  .gallery-medium-col-6 .gallery-item:nth-child(n),
  .gallery-medium-col-7 .gallery-item:nth-child(n),
  .gallery-medium-col-8 .gallery-item:nth-child(n),
  .gallery-medium-col-9 .gallery-item:nth-child(n) {
    clear: none;
    margin-bottom: 1%;
  }

  .gallery-medium-col-2 .gallery-item:nth-child(2n + 1),
  .gallery-medium-col-3 .gallery-item:nth-child(3n + 1),
  .gallery-medium-col-4 .gallery-item:nth-child(4n + 1),
  .gallery-medium-col-5 .gallery-item:nth-child(5n + 1),
  .gallery-medium-col-6 .gallery-item:nth-child(6n + 1),
  .gallery-medium-col-7 .gallery-item:nth-child(7n + 1),
  .gallery-medium-col-8 .gallery-item:nth-child(8n + 1),
  .gallery-medium-col-9 .gallery-item:nth-child(9n + 1) {
    clear: left;
  }
}

@media screen and (min-width: 64em) {
  .gallery-large-col-1 .gallery-item {
    width: 98%;
    margin-bottom: 1.5rem;
  }

  .gallery-large-col-2 .gallery-item {
    width: 48%;
  }

  .gallery-large-col-3 .gallery-item {
    width: 31.33%;
  }

  .gallery-large-col-4 .gallery-item {
    width: 23%;
  }

  .gallery-large-col-5 .gallery-item {
    width: 18%;
  }

  .gallery-large-col-6 .gallery-item {
    width: 14.66%;
  }

  .gallery-large-col-7 .gallery-item {
    width: 12.28%;
  }

  .gallery-large-col-8 .gallery-item {
    width: 10.5%;
  }

  .gallery-large-col-9 .gallery-item {
    width: 9.11%;
  }

  .gallery-large-col-2 .gallery-item:nth-child(n),
  .gallery-large-col-3 .gallery-item:nth-child(n),
  .gallery-large-col-4 .gallery-item:nth-child(n),
  .gallery-large-col-5 .gallery-item:nth-child(n),
  .gallery-large-col-6 .gallery-item:nth-child(n),
  .gallery-large-col-7 .gallery-item:nth-child(n),
  .gallery-large-col-8 .gallery-item:nth-child(n),
  .gallery-large-col-9 .gallery-item:nth-child(n) {
    clear: none;
    margin-bottom: 1%;
  }

  .gallery-large-col-2 .gallery-item:nth-child(2n + 1),
  .gallery-large-col-3 .gallery-item:nth-child(3n + 1),
  .gallery-large-col-4 .gallery-item:nth-child(4n + 1),
  .gallery-large-col-5 .gallery-item:nth-child(5n + 1),
  .gallery-large-col-6 .gallery-item:nth-child(6n + 1),
  .gallery-large-col-7 .gallery-item:nth-child(7n + 1),
  .gallery-large-col-8 .gallery-item:nth-child(8n + 1),
  .gallery-large-col-9 .gallery-item:nth-child(9n + 1) {
    clear: left;
  }
}

@media screen and (min-width: 100em) {
  .gallery-xlarge-col-1 .gallery-item {
    width: 98%;
    margin-bottom: 1.5rem;
  }

  .gallery-xlarge-col-2 .gallery-item {
    width: 48%;
  }

  .gallery-xlarge-col-3 .gallery-item {
    width: 31.33%;
  }

  .gallery-xlarge-col-4 .gallery-item {
    width: 23%;
  }

  .gallery-xlarge-col-5 .gallery-item {
    width: 18%;
  }

  .gallery-xlarge-col-6 .gallery-item {
    width: 14.66%;
  }

  .gallery-xlarge-col-7 .gallery-item {
    width: 12.28%;
  }

  .gallery-xlarge-col-8 .gallery-item {
    width: 10.5%;
  }

  .gallery-xlarge-col-9 .gallery-item {
    width: 9.11%;
  }

  .gallery-xlarge-col-2 .gallery-item:nth-child(n),
  .gallery-xlarge-col-3 .gallery-item:nth-child(n),
  .gallery-xlarge-col-4 .gallery-item:nth-child(n),
  .gallery-xlarge-col-5 .gallery-item:nth-child(n),
  .gallery-xlarge-col-6 .gallery-item:nth-child(n),
  .gallery-xlarge-col-7 .gallery-item:nth-child(n),
  .gallery-xlarge-col-8 .gallery-item:nth-child(n),
  .gallery-xlarge-col-9 .gallery-item:nth-child(n) {
    clear: none;
    margin-bottom: 1%;
  }

  .gallery-xlarge-col-2 .gallery-item:nth-child(2n + 1),
  .gallery-xlarge-col-3 .gallery-item:nth-child(3n + 1),
  .gallery-xlarge-col-4 .gallery-item:nth-child(4n + 1),
  .gallery-xlarge-col-5 .gallery-item:nth-child(5n + 1),
  .gallery-xlarge-col-6 .gallery-item:nth-child(6n + 1),
  .gallery-xlarge-col-7 .gallery-item:nth-child(7n + 1),
  .gallery-xlarge-col-8 .gallery-item:nth-child(8n + 1),
  .gallery-xlarge-col-9 .gallery-item:nth-child(9n + 1) {
    clear: left;
  }
}

/* Margin None */

.gallery-margin-none {
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
}

.gallery-margin-none .gallery-item {
  width: 100%;
  margin: 0 !important;
}

.gallery-margin-none.gallery-small-col-2 .gallery-item {
  width: 50%;
}

.gallery-margin-none.gallery-small-col-3 .gallery-item {
  width: 33.33%;
}

.gallery-margin-none.gallery-small-col-4 .gallery-item {
  width: 25%;
}

.gallery-margin-none.gallery-small-col-5 .gallery-item {
  width: 20%;
}

.gallery-margin-none.gallery-small-col-6 .gallery-item {
  width: 16.66%;
}

.gallery-margin-none.gallery-small-col-7 .gallery-item {
  width: 14.28%;
}

.gallery-margin-none.gallery-small-col-8 .gallery-item {
  width: 12.5%;
}

.gallery-margin-none.gallery-small-col-9 .gallery-item {
  width: 11.11%;
}

/* Gallery Titles

   <div class="gallery gallery-medium-col-3 gallery-title-lines gallery-title-medium-lines-2">
   ============================================================================ */

.gallery-title-lines .gallery-title {
  overflow: hidden;
  padding: 0;
  margin: 0.8125em 0;
}

.gallery-title-small-lines-2 .gallery-title {
  height: 2.5em;
}

.gallery-title-small-lines-3 .gallery-title {
  height: 3.75em;
}

@media screen and (min-width: 40em) {
  .gallery-title-medium-lines-auto .gallery-title {
    height: auto;
  }

  .gallery-title-medium-lines-2 .gallery-title {
    height: 2.5em;
  }

  .gallery-title-medium-lines-3 .gallery-title {
    height: 3.75em;
  }
}

@media screen and (min-width: 64em) {
  .gallery-title-large-lines-auto .gallery-title {
    height: auto;
  }

  .gallery-title-large-lines-2 .gallery-title {
    height: 2.5em;
  }

  .gallery-title-large-lines-3 .gallery-title {
    height: 3.75em;
  }
}

@media screen and (min-width: 100em) {
  .gallery-title-xlarge-lines-auto .gallery-title {
    height: auto;
  }

  .gallery-title-xlarge-lines-2 .gallery-title {
    height: 2.5em;
  }

  .gallery-title-xlarge-lines-3 .gallery-title {
    height: 3.75em;
  }
}

/* Gallery Captions
   ============================================================================ */

.gallery-caption {
  margin: 0;
  text-align: center;
}

.gallery-inner {
  padding: 0.5rem;
}

.gallery p {
  margin-bottom: 0;
}

.gallery-thumb,
.gallery-caption,
.gallery-icon,
.gallery-title,
.gallery-item > a {
  transition: all 0.3s ease 0s;
}

/* Positioned */

.gallery-style-2 .gallery-caption,
.gallery-style-3 .gallery-caption {
  width: 100%;
  position: absolute;
  color: #fff;
  text-align: left;
}

.gallery-style-2 .gallery-title,
.gallery-style-3 .gallery-title {
  color: #fff;
  padding: 0 !important;
}

/* Gallery Style 2
   ============================================================================ */

.gallery-style-2 .gallery-caption {
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0;
  margin: 0;
  bottom: 0;
  text-align: center;
}

.gallery-style-2 .gallery-inner {
  width: 100%;
  padding: 6%;
}

/* Icon */

.gallery-style-2 .gallery-icon:before {
  display: none;
  opacity: 1;
  position: absolute;
  left: 50%;
  top: 50%;
  color: #fff;
  font-size: 36px;
  transform: translate(-50%);
}

.gallery-style-2 .gallery-title + .gallery-icon:before {
  top: 43%;
}

/* Hover */

.gallery-style-2 a:hover .gallery-thumb,
.gallery-style-2 a:focus .gallery-thumb,
.gallery-style-2 a.selected .gallery-thumb,
.gallery-style-2 .gallery-item.selected a .gallery-thumb {
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
  transform: scale(1.1);
}

.gallery-style-2 a:hover .gallery-caption,
.gallery-style-2 a:focus .gallery-caption,
.gallery-style-2 a.selected .gallery-caption,
.gallery-style-2 .gallery-item.selected a .gallery-thumb {
  background: rgba(57, 86, 105, 0.62);
}

/* Selected */

.gallery-style-2 a.selected .gallery-caption,
.gallery-style-2 .gallery-item.selected a .gallery-caption {
  box-shadow: inset 0 0 0 0.5rem #71acd1;
}

.gallery-style-2 .gallery-item.selected a .gallery-icon:before {
  display: block;
  -webkit-animation: fade-in 0.3s;
  animation: fade-in 0.3s;
}

.gallery-style-2 .gallery-item.selected a .gallery-title {
  padding-top: 2rem !important;
}

/* Gallery Style 3
   ============================================================================ */

.gallery-style-3 .gallery-image {
  border: 1px solid #b1b0b5;
}

.gallery-style-3 .gallery-caption {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0;
  margin: 0;
  bottom: 0;
  text-align: center;
  transition: none;
}

.gallery-style-3 .gallery-inner {
  width: 100%;
  padding: 6%;
}

/* Icon */

.gallery-style-3 .gallery-icon:before {
  display: none;
}

/* Hover */

.gallery-style-3 a:hover .gallery-caption,
.gallery-style-3 a:focus .gallery-caption,
.gallery-style-3 a.selected .gallery-caption {
  background: rgba(255, 255, 255, 0.18);
  box-shadow: inset 0 0 0 3px #71acd1;
}

/* Gallery Style 4
   ============================================================================ */

.gallery-style-4 .gallery-caption {
  box-shadow: inset 0 0 0 1px #b1b0b5;
  transition: none;
}

.gallery-style-4 .gallery-inner {
  padding: 18% 10%;
}

.gallery-style-4 a:hover .gallery-caption,
.gallery-style-4 a:focus .gallery-caption {
  box-shadow: inset 0 0 0 3px #71acd1;
}

/* Gallery Selected Label
   ============================================================================ */

.gallery .selected-label {
  position: absolute;
  bottom: 0;
  right: 0;
}

.gallery .selected-label::before {
  content: "";
  width: 0;
  height: 0;
  border-bottom: 3.5em solid #76bb76;
  border-left: 3.5em solid transparent;
  position: absolute;
  bottom: 0;
  right: 0;
}

.gallery .selected-label .icon {
  position: absolute;
  bottom: 6px;
  right: 8px;
}

.gallery .selected-label .icon::before {
  opacity: 1;
  margin: 0;
  font-size: 1.5em;
}

/* Large */

.gallery-selected-label-large .selected-label::before {
  border-bottom-width: 5em;
  border-left-width: 5em;
}

.gallery-selected-label-large .selected-label .icon {
  bottom: 14px;
  right: 11px;
}

.gallery-selected-label-large .selected-label .icon::before {
  font-size: 1.8em;
}

/* Small */

.gallery-selected-label-small .selected-label::before {
  border-bottom-width: 2em;
  border-left-width: 2em;
}

.gallery-selected-label-small .selected-label .icon {
  bottom: 0;
  right: 3px;
}

.gallery-selected-label-small .selected-label .icon::before {
  font-size: 0.9em;
}

/* Gallery Caption Style 2
   ============================================================================ */

.gallery-caption-style-2 .gallery-caption,
.gallery-caption-style-2 a:hover .gallery-caption,
.gallery-caption-style-2 a:focus .gallery-caption {
  background: none;
}

.gallery-caption-style-2 .gallery-item.selected .gallery-caption {
  box-shadow: none !important;
}

.gallery-caption-style-2 .gallery-item.selected .gallery-image {
  border: 0.5rem solid #76bb76;
}

/* Selected Error */

.gallery-caption-style-2 .gallery-item.selected-error .gallery-image {
  border-color: #ff4649;
}

.gallery .selected-error .selected-label::before {
  border-bottom-color: #ff4649;
}

/* Selected Warning */

.gallery-caption-style-2 .gallery-item.selected-warning .gallery-image {
  border-color: #edb15f;
}

.gallery .selected-warning .selected-label::before {
  border-bottom-color: #edb15f;
}

/* Gallery Formats
   ============================================================================ */

/* List */

.gallery-list {
  margin: 0 0 1.5rem;
}

.gallery-list-hidden {
  display: none;
}

.gallery-list .gallery-list-hidden {
  display: block;
}

.gallery-list .gallery-item {
  border-bottom: 1px solid #f1f0f5;
  width: 100%;
  margin: 0 0 1.5rem;
  padding-bottom: 1.5rem;
}

.gallery-list .gallery-thumb {
  border: 1px solid #b1b0b5;
  float: left;
  width: 12%;
}

.gallery-list .gallery-list-hidden {
  float: left;
  width: 88%;
}

.gallery-list .gallery-caption {
  display: block;
  position: static;
  color: #51606d;
  text-align: left;
  box-shadow: none;
}

.gallery-list a:hover .gallery-caption,
.gallery-list a:focus .gallery-caption {
  background: none;
  box-shadow: none;
}

.gallery-list .gallery-inner {
  padding: 0;
}

.gallery-list p {
  margin-bottom: 1.5rem;
}

/* ============================================================================
   Flexbox Gallery
   ============================================================================ */

.flex-gallery,
.flex-gallery-text {
  display: flex;
  flex-wrap: wrap;
  margin: -1% -1% 1.5rem;
}

.flex-gallery.gallery-margin-none {
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
}

.flex-gallery .gallery-item {
  width: auto;
}

/* Sizes */

.flex-gallery .gallery-image {
  height: 150px;
}

.flex-gallery-xxsmall .gallery-image {
  height: 60px;
}

.flex-gallery-xsmall .gallery-image {
  height: 72px;
}

.flex-gallery-small .gallery-image {
  height: 90px;
}

/* ============================================================================
   Calendar
   TODO
   ============================================================================ */

.calendar .gallery-item {
  height: 24rem;
}

.calendar .selected {
  box-shadow: inset 0 0 0 4px #71acd1;
}

/* ============================================================================
   Vertical Align Containers
   TODO: Replace with flexbox
   ============================================================================ */

.vertical-align-container-small {
  display: table;
  width: 100%;
}

.vertical-align-container-small .vertical-align-item {
  display: table-cell;
  vertical-align: middle;
  float: none !important;
}

.vertical-align-container-small .vertical-align-item h1:first-child,
.vertical-align-container-small .vertical-align-item h2:first-child,
.vertical-align-container-small .vertical-align-item h3:first-child,
.vertical-align-container-small .vertical-align-item h4:first-child,
.vertical-align-container-small .vertical-align-item h5:first-child,
.vertical-align-container-small .vertical-align-item h6:first-child {
  padding-top: 0;
}

.vertical-align-container-small .vertical-align-item ul,
.vertical-align-container-small .vertical-align-item .button,
.vertical-align-container-small .vertical-align-item p:last-child {
  margin-bottom: 0;
}

.vertical-align-container-small
  .vertical-align-item.text-right
  .button:last-child {
  margin-right: 0;
}

@media screen and (min-width: 40em) {
  .vertical-align-container-medium {
    display: table;
    width: 100%;
  }

  .vertical-align-container-medium .vertical-align-item {
    display: table-cell;
    vertical-align: middle;
    float: none !important;
  }

  .vertical-align-container-medium .vertical-align-item h1:first-child,
  .vertical-align-container-medium .vertical-align-item h2:first-child,
  .vertical-align-container-medium .vertical-align-item h3:first-child,
  .vertical-align-container-medium .vertical-align-item h4:first-child,
  .vertical-align-container-medium .vertical-align-item h5:first-child,
  .vertical-align-container-medium .vertical-align-item h6:first-child {
    padding-top: 0;
  }

  .vertical-align-container-medium .vertical-align-item ul,
  .vertical-align-container-medium .vertical-align-item .button,
  .vertical-align-container-medium .vertical-align-item p:last-child {
    margin-bottom: 0;
  }

  .vertical-align-container-medium
    .vertical-align-item.text-right
    .button:last-child {
    margin-right: 0;
  }
}

@media screen and (min-width: 64em) {
  .vertical-align-container-large {
    display: table;
    width: 100%;
  }

  .vertical-align-container-large .vertical-align-item {
    display: table-cell;
    vertical-align: middle;
    float: none !important;
  }

  .vertical-align-container-large .vertical-align-item h1:first-child,
  .vertical-align-container-large .vertical-align-item h2:first-child,
  .vertical-align-container-large .vertical-align-item h3:first-child,
  .vertical-align-container-large .vertical-align-item h4:first-child,
  .vertical-align-container-large .vertical-align-item h5:first-child,
  .vertical-align-container-large .vertical-align-item h6:first-child {
    padding-top: 0;
  }

  .vertical-align-container-large .vertical-align-item ul,
  .vertical-align-container-large .vertical-align-item .button,
  .vertical-align-container-large .vertical-align-item p:last-child {
    margin-bottom: 0;
  }

  .vertical-align-container-large
    .vertical-align-item.text-right
    .button:last-child {
    margin-right: 0;
  }
}

@media screen and (min-width: 100em) {
  .vertical-align-container-xlarge {
    display: table;
    width: 100%;
  }

  .vertical-align-container-xlarge .vertical-align-item {
    display: table-cell;
    vertical-align: middle;
    float: none !important;
  }

  .vertical-align-container-xlarge .vertical-align-item h1:first-child,
  .vertical-align-container-xlarge .vertical-align-item h2:first-child,
  .vertical-align-container-xlarge .vertical-align-item h3:first-child,
  .vertical-align-container-xlarge .vertical-align-item h4:first-child,
  .vertical-align-container-xlarge .vertical-align-item h5:first-child,
  .vertical-align-container-xlarge .vertical-align-item h6:first-child {
    padding-top: 0;
  }

  .vertical-align-container-xlarge .vertical-align-item ul,
  .vertical-align-container-xlarge .vertical-align-item .button,
  .vertical-align-container-xlarge .vertical-align-item p:last-child {
    margin-bottom: 0;
  }

  .vertical-align-container-xlarge
    .vertical-align-item.text-right
    .button:last-child {
    margin-right: 0;
  }
}

/* ============================================================================
   Vertical Align Middle
   TODO: Replace with flexbox
   ============================================================================ */

.valign-middle {
  display: table;
}

.valign-middle .column,
.valign-middle .columns {
  display: table-cell;
  vertical-align: middle;
}

.valign-middle .column,
.valign-middle .columns,
.valign-middle [class*="column"] + [class*="column"]:last-child {
  float: none;
}

/* ============================================================================
   Positioned Elements
   ============================================================================ */

.position-relative {
  position: relative;
}

/* Fixed
   ============================================================================ */

.position-fixed {
  position: fixed;
  z-index: 2;
}

.position-fixed-top-right {
  top: 1.5rem;
  right: 0;
}

.position-fixed-bottom-right {
  bottom: 0;
  right: 0;
}

.has-top-bar .position-fixed-top-right {
  top: 6rem;
  right: 0;
}

.has-top-bar-2 .position-fixed-top-right {
  top: 10.5rem;
  right: 0;
}

.has-top-bar-2 .position-fixed-top-right {
  top: 10.5rem;
  right: 0;
}

/* Absolute
   ============================================================================ */

.position-absolute {
  position: absolute !important;
}

.position-top-left,
.position-absolute-top-left {
  top: 0;
  left: 0;
}

.position-top-right,
.position-absolute-top-right {
  top: 0;
  right: 0;
}

.position-bottom-right,
.position-absolute-bottom-right {
  bottom: 0;
  right: 0;
}

.position-bottom-left,
.position-absolute-bottom-left {
  bottom: 0;
  left: 0;
}

.position-top-center,
.position-absolute-top-center {
  top: 0;
  left: 50%;
  transform: translate(-50%);
}

.position-right-center,
.position-absolute-right-center {
  right: 0;
  top: 50%;
  transform: translate(0, -50%);
}

.position-bottom-center,
.position-absolute-bottom-center {
  bottom: 0;
  left: 50%;
  transform: translate(-50%);
}

.position-left-center,
.position-absolute-left-center {
  left: 0;
  top: 50%;
  transform: translate(0, -50%);
}

@media screen and (min-width: 40em) {
  .position-absolute-medium {
    position: absolute;
  }
}

@media screen and (min-width: 64em) {
  .position-absolute-large {
    position: absolute;
  }
}

@media screen and (min-width: 100em) {
  .position-absolute-xlarge {
    position: absolute;
  }
}

/* Static
   ============================================================================ */

.position-static {
  position: static;
}

@media screen and (min-width: 40em) {
  .position-static-medium {
    position: static !important;
  }
}

@media screen and (min-width: 64em) {
  .position-static-large {
    position: static !important;
  }
}

@media screen and (min-width: 100em) {
  .position-static-xlarge {
    position: static !important;
  }
}

/* ============================================================================
   Data grid
   TODO: Review
   ============================================================================ */

.data-grid [type="checkbox"] {
  margin: 0;
}

.data-grid-header {
  background: #fff;
  border-bottom: 2px solid #f1f0f5;
  border-radius: 5px 5px 0 0;
}

.data-grid-body {
  background: #fff;
  border-radius: 0 0 5px 5px;
}

.data-grid-body .row-parent {
  border-bottom: 1px solid #f1f0f5;
  padding: 1.5rem 0;
}

.data-grid-body > div:last-child .row-parent:last-child {
  border-bottom: 0;
}

@media screen and (min-width: 40em) {
  .data-grid .row {
    align-items: center;
  }
}

/* ============================================================================
   Vue Data Table
   ============================================================================ */

.v-datatable {
  position: relative;
  margin: 5px 0 29px;
}

.theme--light.v-table {
  color: inherit;
}

.v-datatable .truncate {
  max-width: 30rem;
  display: table-cell;
}

/* Rounded corners */

.v-datatable::before,
.v-datatable::after {
  background: #fff;
  display: block;
  content: "";
  width: 100%;
  height: 5px;
  position: absolute;
}

.v-datatable::before {
  border-radius: 5px 5px 0 0;
  top: -5px;
}

.v-datatable::after {
  border-radius: 0 0 5px 5px;
  bottom: -5px;
}

/* Header */

.v-datatable th {
  border-bottom: 2px solid #f1f0f5 !important;
  padding: 1rem 0.5rem !important;
  font-size: 0.75rem;
  font-weight: 700 !important;
}

.v-datatable__progress th {
  border-bottom: 0 !important;
  padding: 0 !important;
}

.v-datatable thead th.column {
  float: none !important;
  width: auto !important;
}

.theme--light.v-datatable thead th,
.theme--light.v-datatable thead th.column.sortable.active,
.theme--light.v-datatable thead th.column.sortable.active i,
.theme--light.v-datatable thead th.column.sortable:hover {
  color: inherit;
}

/* Footer */

table.v-table tfoot td {
  font-size: 0.875rem;
}

/* Rows */

table.v-table thead tr {
  height: auto;
}

table.v-table tbody tr {
  transition: none;
}

.v-datatable tbody tr:nth-child(2n) {
  background: inherit;
}

.v-datatable tbody tr:hover {
  background: inherit !important;
}

.theme--light.v-table tbody tr[active] {
  background: inherit;
}

/* Cells */

.v-datatable td {
  border-bottom: 1px solid #f1f0f5;
  height: auto;
  padding: 0.5rem !important;
  font-size: 0.875rem;
}

.v-datatable tr:nth-last-child(3) td {
  border-bottom: 0;
}

/* Sort Icons */

.v-datatable th {
  position: relative;
}

.v-datatable th.sortable,
.sortable-div {
  background: none !important;
}

.v-datatable th.sortable:before,
.sortable-div:before {
  display: block;
  opacity: 0.62;
  color: #71acd1;
  font-family: "Icomoon";
  font-size: 0.75rem;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  left: 0.5625rem;
}

.sortable-div:before {
  left: -0.175rem;
}

@media screen and (min-width: 100em) {
  .v-datatable th.sortable:before,
  .sortable-div:before {
    font-size: 0.875rem;
    left: 0.75rem;
  }

  .sortable-div:before {
    left: 0rem;
  }
}

.v-datatable th.sortable:not(.active):before,
.sortable-div:not(.active):before {
  content: "\F0DC";
  color: #b1b0b5;
}

.v-datatable th.sortable.active.asc:before,
.sortable-div.active.asc:before {
  content: "\F0DE";
}

.v-datatable th.sortable.active.desc:before,
.sortable-div.active.desc:before {
  content: "\F0DD";
}

/* ============================================================================
   Tables
   ============================================================================ */

table {
  border-collapse: unset !important;
  border-spacing: 0 !important;
}

table thead,
table tbody,
table tfoot {
  background: #fff;
  color: #51606d;
}

table tbody tr:nth-child(n) {
  background: none;
}

/* Table Data
   ============================================================================ */

.recipient-data,
.table-data {
  position: relative;
  margin: 5px 0 29px;
}

/* Alignment */

.table-data-align-top td {
  vertical-align: top;
}

/* Rounded corners */

.table-data::before,
.table-data::after {
  background: #fff;
  display: block;
  content: "";
  width: 100%;
  height: 5px;
  position: absolute;
}

.table-data::before {
  border-radius: 5px 5px 0 0;
  top: -5px;
}

.table-data::after {
  border-radius: 0 0 5px 5px;
  bottom: -5px;
}

/* Various */

.table-data p,
.table-data-checkbox [type="checkbox"] {
  float: none;
  margin: 0;
}

.table-data p + p {
  margin-top: 1rem;
}

.table-data thead,
.table-data tfoot {
  background: #fff;
}

.table-data tbody {
  background: #f1f0f5;
}

.table-data tbody tr:nth-child(n) {
  background: #fff !important;
}

.table-data th {
  border-bottom: 2px solid #f1f0f5;
  padding: 1rem 0.5rem;
  font-size: 0.625rem;
  font-weight: 700;
}

.table-data td {
  border-bottom: 1px solid #f1f0f5;
  padding: 0.5rem;
  font-size: 0.75rem;
}

@media screen and (min-width: 100em) {
  .table-data th {
    font-size: 0.75rem;
  }

  .table-data td {
    font-size: 0.875rem;
  }
}

.table-data tbody tr:last-child td {
  border-bottom: 0;
}

/* Large */

.table-data-large th {
  padding: 0.5rem 0.5rem 0.5rem 1.5rem !important;
}

.table-data-large td {
  padding: 1rem 0.5rem 1rem 1.5rem !important;
}

@media screen and (min-width: 100em) {
  .table-data-large th {
    padding: 1rem 2rem !important;
  }

  .table-data-large td {
    padding: 2rem !important;
  }

  .deliverability-container {
    min-width: 135px;
  }
}

/* Columns */

.table-data .col1 {
  width: 10%;
}

.table-data .col2 {
  width: 10%;
}

.table-data .col3 {
  width: 80%;
}

.table-data .col4,
.table-data .col5,
.table-data .col6,
.table-data .col7,
.table-data .col8 {
  display: none;
}

@media screen and (min-width: 40em) {
  .table-data .col1 {
    width: 10%;
  }

  .table-data .col2 {
    width: 10%;
  }

  .table-data .col3 {
    width: 30%;
  }

  .table-data .col7 {
    width: 50%;
    display: table-cell;
  }
}

@media screen and (min-width: 69em) {
  .table-data .col1 {
    width: 5%;
  }

  .table-data .col2 {
    width: 5%;
  }

  .table-data .col3 {
    width: 20%;
  }

  .table-data .col4 {
    width: 20%;
    display: table-cell;
  }

  .table-data .col7 {
    width: 30%;
  }

  .table-data .col8 {
    width: 20%;
    display: table-cell;
  }
}

@media screen and (min-width: 90em) {
  .table-data .col1 {
    width: 5%;
  }

  .table-data .col2 {
    width: 5%;
  }

  .table-data .col3 {
    width: 15%;
  }

  .table-data .col4 {
    width: 15%;
  }

  .table-data .col5 {
    width: 15%;
    display: table-cell;
  }

  .table-data .col7 {
    width: 25%;
  }

  .table-data .col8 {
    width: 20%;
  }
}

@media screen and (min-width: 100em) {
  .table-data .col1 {
    width: 4%;
  }

  .table-data .col2 {
    width: 4%;
  }

  .table-data .col3 {
    width: 15%;
  }

  .table-data .col4 {
    width: 14%;
  }

  .table-data .col5 {
    width: 14%;
  }

  .table-data .col6 {
    width: 14%;
    display: table-cell;
  }

  .table-data .col7 {
    width: 20%;
  }

  .table-data .col8 {
    width: 15%;
  }

  /* Column scheme 2 */

  .table-data-col-scheme-2 .col5 {
    width: 10%;
  }

  .table-data-col-scheme-2 .col6 {
    width: 10%;
  }

  .table-data-col-scheme-2 .col7 {
    width: 10%;
    display: table-cell;
  }

  .table-data-col-scheme-2 .col8 {
    width: 18%;
  }

  .table-data-col-scheme-2 .col9 {
    width: 25%;
  }
}

/* Table Cards
   ============================================================================ */

.table-cards {
  margin: 0;
  box-sizing: border-box;
}

.table-cards thead,
.table-cards tbody,
.table-cards tfoot {
  background: none;
}

.table-cards tr {
  background: none;
}

.table-cards td {
  border: 0;
  display: block;
  padding: 0;
}

.table-cards td > a {
  border-radius: 5px;
  border: 2px solid #51606d;
  background: #f8f8fa;
  display: block;
  padding: 1rem 1.5rem;
  color: #51606d;
  transition: all 0.3s ease 0s;
}

.table-cards td > a:hover:not(.disabled),
.table-cards td > a:focus:not(.disabled),
.table-cards td > a.selected:not(.disabled) {
  border-color: #71acd1;
  box-shadow: inset 0 0 0 4px #71acd1;
}

.table-cards td > a.selected:not(.disabled) {
  background: #71acd1;
  color: #fff;
}

.table-cards td > a.selected:not(.disabled) p {
  color: #fff !important;
}

/* Exclusive */

.table-cards td > a.exclusive {
  border: 2px solid #ff4649;
}

.table-cards td > a.exclusive:hover:not(.disabled),
.table-cards td > a.exclusive:focus:not(.disabled),
.table-cards td > a.exclusive.selected:not(.disabled) {
  border-color: #ff4649;
  box-shadow: inset 0 0 0 4px #ff4649;
}

.table-cards td > a.exclusive.selected:not(.disabled) {
  background: #ff4649;
  color: #fff;
}

.table-cards td > a.exclusive.selected:not(.disabled) p {
  color: #fff !important;
}

/* Disabled */

.table-cards .disabled {
  opacity: 0.5;
}

/* Table scrollable
   ============================================================================ */

.table-scrollable .dataTables_wrapper .dataTables_info {
  margin-bottom: 1.5rem;
  padding: 0;
  text-align: left;
}

/* Extra Large */

.table-scrollable-xlarge {
  margin-top: -10%;
}

.table-scrollable-xlarge table {
  padding: 1rem 10%;
}

.table-scrollable-xlarge .table-scrollable-row {
  padding: 0 10%;
}

@media screen and (min-width: 40em) {
  .table-scrollable-xlarge {
    margin-top: -7rem;
    margin-bottom: -3rem;
  }

  .table-scrollable-xlarge table {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .table-scrollable-xlarge .table-scrollable-row {
    padding-left: 4.5rem;
    padding-right: 4.5rem;
  }

  /* Tabs */

  .table-scrollable-tabs {
    margin-top: -5rem;
  }

  .table-scrollable-tabs .tabs {
    position: absolute !important;
    right: 4rem !important;
    top: 82px !important;
  }
}

/* ============================================================================
   Icon
   ============================================================================ */

.icon:before {
  display: inline-block;
  width: 1em;
  margin-right: 0.5em;
  position: relative;
  top: -0.1rem;
  opacity: 0.62;
  font: normal normal 1.4em/0 "Ionicons";
  speak: none;
  font-variant: normal;
  text-transform: none;
  text-align: center;
  text-indent: 0;
  vertical-align: middle;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icomoon:before {
  font-family: "Icomoon";
}

/* Icon Formats
   ============================================================================ */

/* After */

.icon-after:before {
  margin: 0 0 0 0.5em;
}

/* Inline */

.icon-inline:before {
  margin-right: 0.2em;
  font-size: 1em;
}

.icon-inline.icon-after:before {
  margin: 0 0 0 0.2em;
}

/* Absolute */

.icon-absolute-left:before {
  position: absolute;
  top: 50%;
  left: -3rem;
}

.icon-absolute-right:before {
  position: absolute;
  top: 50%;
  right: -3rem;
}

/* Opacity */

.icon.opacity:before {
  opacity: 1;
}

/* Margin */

.icon-margin-none:before {
  margin: 0;
}

/* ============================================================================
   Badge
   ============================================================================ */

.badge {
  border-width: 2px;
  border-style: solid;
  width: 38px;
  height: 38px;
  line-height: 38px;
  margin: 0 0.75rem;
  padding: 0;
  position: relative;
  font-size: 0.8rem;
  font-weight: 700;
  box-sizing: content-box;
  transition: all 0.3s ease 0s;
}

a.badge:hover,
a.badge:focus,
a.badge:hover .icon:before,
a.badge:focus .icon:before,
button.badge:hover,
button.badge:focus,
button.badge:hover .icon:before,
button.badge:focus .icon:before {
  color: #fff !important;
}

button.badge:focus {
  outline: none;
}

.badge .icon:before {
  margin: 0;
  opacity: 1;
  position: absolute;
  top: 50%;
  left: 50%;
  color: #fff;
  transform: translate(-50%);
}

.badge .icon-product-social:before {
  transform: translateX(-56%);
}

/* Badge Sizes
   ============================================================================ */

.badge-xxxsmall {
  width: 10px;
  height: 10px;
  line-height: 10px;
  font-size: 0;
}

.badge-xxsmall {
  width: 16px;
  height: 16px;
  line-height: 16px;
  font-size: 0.35em;
}

.badge-xsmall {
  width: 26px;
  height: 26px;
  line-height: 26px;
  font-size: 0.5em;
}

.badge-small {
  width: 30px;
  height: 30px;
  line-height: 30px;
  font-size: 0.6em;
}

.badge-large {
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 1em;
}

.badge-xlarge {
  width: 68px;
  height: 68px;
  line-height: 68px;
  font-size: 1.6em;
}

.badge-xxlarge {
  width: 108px;
  height: 108px;
  line-height: 108px;
  font-size: 2.6em;
}

.badge-xxxlarge {
  width: 148px;
  height: 148px;
  line-height: 148px;
  font-size: 3.6em;
}

@media screen and (min-width: 100em) {
  .badge-xlarge-size-small {
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 0.6em;
  }

  .badge-xlarge-size-xlarge {
    width: 68px;
    height: 68px;
    line-height: 68px;
    font-size: 1.6em;
  }
}

/* Badge Formats
   ============================================================================ */

/* Zoom */

.badge-zoom {
  margin: 0 0.25rem;
}

.badge-zoom .icon:before {
  font-size: 2em;
}

.badge-zoom .icon-product-digital:before {
  font-size: 1.82em;
}

.badge-zoom .icon-product-local:before {
  font-size: 1.8em;
}

.badge-zoom .icon-product-social:before {
  font-size: 1.6em;
}

.badge-zoom .icon-product-postcards:before {
  font-size: 1.7em;
}

/* No Border */

.badge-no-border {
  border: 0;
}

/* Left */

.badge-left {
  float: left;
  margin-left: 0;
}

/* Right */

.badge-right {
  float: right;
  margin-right: 0;
}

/* Center */

.badge-center {
  display: block;
  margin: 0 auto;
}

/* ============================================================================
   Notifications Dot
   ============================================================================ */

.red-dot {
  top: 0.8125rem;
  right: 0.9375rem;
  width: 0.5rem;
  height: 0.5rem;
  box-shadow: 0 0 0 0.0625rem #fff;
}

/* ============================================================================
   Preview Indicator
   ============================================================================ */

.img-preview {
  position: relative;
  display: inline-block;
  transition: all 0.3s ease 0s;
}

.img-preview:hover .preview-icon {
  opacity: 1;
  transition: all 0.3s ease 0s;
}

.img-overlay-buttons {
  display: block;
  position: absolute;
  text-align: center;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  z-index: 9;
}

.preview-icon {
  left: 0;
  width: 100%;
  opacity: 0;
}

.preview-icon i {
  background: rgba(0, 0, 0, 0.5) none repeat scroll 0 0;
  border: medium none;
  color: #fff;
}

/* ============================================================================
   Button
   ============================================================================ */

.button {
  border-width: 2px;
  border-style: solid;
  border-radius: 999px;
  margin: 0 1.5rem 1.5rem 0;
  padding: 1.2em 2em;
  position: relative;
  color: #fff;
  font:
    700 1rem/1 "Nunito Sans",
    Arial,
    Helvetica,
    sans-serif;
  transition: all 0.3s ease 0s;
}

.button:hover,
.button:focus,
.button:hover .icon:before,
.button:focus .icon:before {
  color: #fff !important;
}

.button:active {
  position: relative;
  top: 1px;
}

.button.is-active {
  box-shadow: inset 0 0 0 0.4em rgba(255, 255, 255, 0.5);
}

.button-group .button {
  font-size: inherit;
}

.button-group .button:first-child {
  border-radius: 5px 0 0 5px;
}

.button-group .button:nth-child(2) {
  border-radius: 0 5px 5px 0;
}

/* Alignment */

.alignright .button:last-child,
.float-right .button:last-child,
.text-right .button:last-child,
.text-center .button:last-child {
  margin-right: auto;
}

/* Button Colors
   ============================================================================ */

.button-color1 {
  border-color: #71acd1;
  background-color: #71acd1 !important;
}

.button-color1:not(div):not(span):hover,
.button-color1:not(div):not(span):focus {
  border-color: #6092b2;
  background: #6092b2 !important;
}

.button-color1:not(div):not(span):active,
.button-color1:not(div):not(span).selected {
  border-color: #4f7892;
  background: #4f7892 !important;
}

.button-color1.button-hollow,
.button-color1.button-hollow .icon:before {
  color: #71acd1;
}

.button-color1.button-icon:hover .icon::before,
.button-color1.button-icon:focus .icon::before {
  color: #71acd1 !important;
}

.button-color2 {
  border-color: #ff4649;
  background-color: #ff4649 !important;
}

.button-color2:not(div):not(span):hover,
.button-color2:not(div):not(span):focus {
  border-color: #d93c3e;
  background: #d93c3e !important;
}

.button-color2:not(div):not(span):active,
.button-color2:not(div):not(span).selected {
  border-color: #b33133;
  background: #b33133 !important;
}

.button-color2.button-hollow,
.button-color2.button-hollow .icon:before {
  color: #ff4649;
}

.button-color2.button-icon:hover .icon::before,
.button-color2.button-icon:focus .icon::before {
  color: #ff4649 !important;
}

.button-color3 {
  border-color: #76bb76;
  background-color: #76bb76 !important;
}

.button-color3:not(div):not(span):hover,
.button-color3:not(div):not(span):focus {
  border-color: #649f64;
  background: #649f64 !important;
}

.button-color3:not(div):not(span):active,
.button-color3:not(div):not(span).selected {
  border-color: #538353;
  background: #538353 !important;
}

.button-color3.button-hollow,
.button-color3.button-hollow .icon:before {
  color: #76bb76;
}

.button-color3.button-icon:hover .icon::before,
.button-color3.button-icon:focus .icon::before {
  color: #76bb76 !important;
}

.button-color4 {
  border-color: #51606d;
  background-color: #51606d !important;
}

.button-color4:not(div):not(span):hover,
.button-color4:not(div):not(span):focus {
  border-color: #45525d;
  background: #45525d !important;
}

.button-color4:not(div):not(span):active,
.button-color4:not(div):not(span).selected {
  border-color: #39434c;
  background: #39434c !important;
}

.button-color4.button-hollow,
.button-color4.button-hollow .icon:before {
  color: #51606d;
}

.button-color4.button-icon:hover .icon::before,
.button-color4.button-icon:focus .icon::before {
  color: #51606d !important;
}

.button-color5 {
  border-color: #b1b0b5;
  background-color: #b1b0b5 !important;
}

.button-color5:not(div):not(span):hover,
.button-color5:not(div):not(span):focus {
  border-color: #96969a;
  background: #96969a !important;
}

.button-color5:not(div):not(span):active,
.button-color5:not(div):not(span).selected {
  border-color: #7c7b7f;
  background: #7c7b7f !important;
}

.button-color5.button-hollow,
.button-color5.button-hollow .icon:before {
  color: #b1b0b5;
}

.button-color5.button-icon:hover .icon::before,
.button-color5.button-icon:focus .icon::before {
  color: #b1b0b5 !important;
}

.button-color6 {
  border-color: #edb15f;
  background-color: #edb15f !important;
}

.button-color6:not(div):not(span):hover,
.button-color6:not(div):not(span):focus {
  border-color: #c99651;
  background: #c99651 !important;
}

.button-color6:not(div):not(span):active,
.button-color6:not(div):not(span).selected {
  border-color: #a67c43;
  background: #a67c43 !important;
}

.button-color6.button-hollow,
.button-color6.button-hollow .icon:before {
  color: #edb15f;
}

.button-color6.button-icon:hover .icon::before,
.button-color6.button-icon:focus .icon::before {
  color: #edb15f !important;
}

.button-color7 {
  border-color: #fff;
  background-color: #fff !important;
}

.button-color7:not(div):not(span):hover,
.button-color7:not(div):not(span):focus {
  border-color: #d9d9d9;
  background: #d9d9d9 !important;
}

.button-color7:not(div):not(span):active,
.button-color7:not(div):not(span).selected {
  border-color: #b3b3b3;
  background: #b3b3b3 !important;
}

.button-color7.button-hollow,
.button-color7.button-hollow .icon:before {
  color: #fff;
}

.button-color7.button-icon:hover .icon::before,
.button-color7.button-icon:focus .icon::before {
  color: #fff !important;
}

.button-color8 {
  border-color: #f1f0f5;
  background-color: #f1f0f5 !important;
}

.button-color8:not(div):not(span):hover,
.button-color8:not(div):not(span):focus {
  border-color: #cdccd0;
  background: #cdccd0 !important;
}

.button-color8:not(div):not(span):active,
.button-color8:not(div):not(span).selected {
  border-color: #a9a8ac;
  background: #a9a8ac !important;
}

.button-color8.button-hollow,
.button-color8.button-hollow .icon:before {
  color: #f1f0f5;
}

.button-color8.button-icon:hover .icon::before,
.button-color8.button-icon:focus .icon::before {
  color: #f1f0f5 !important;
}

.button-color9 {
  border-color: #234;
  background-color: #234 !important;
}

.button-color9:not(div):not(span):hover,
.button-color9:not(div):not(span):focus {
  border-color: #1d2b3a;
  background: #1d2b3a !important;
}

.button-color9:not(div):not(span):active,
.button-color9:not(div):not(span).selected {
  border-color: #182430;
  background: #182430 !important;
}

.button-color9.button-hollow,
.button-color9.button-hollow .icon:before {
  color: #234;
}

.button-color9.button-icon:hover .icon::before,
.button-color9.button-icon:focus .icon::before {
  color: #234 !important;
}

.button-color10 {
  border-color: #f9f9f9;
  background-color: #f9f9f9 !important;
}

.button-color10:not(div):not(span):hover,
.button-color10:not(div):not(span):focus {
  border-color: #d4d4d4;
  background: #d4d4d4 !important;
}

.button-color10:not(div):not(span):active,
.button-color10:not(div):not(span).selected {
  border-color: #aeaeae;
  background: #aeaeae !important;
}

.button-color10.button-hollow,
.button-color10.button-hollow .icon:before {
  color: #f9f9f9;
}

.button-color10.button-icon:hover .icon::before,
.button-color10.button-icon:focus .icon::before {
  color: #f9f9f9 !important;
}

.button-color11 {
  border-color: #ca82e6;
  background-color: #ca82e6 !important;
}

.button-color11:not(div):not(span):hover,
.button-color11:not(div):not(span):focus {
  border-color: #ac6fc4;
  background: #ac6fc4 !important;
}

.button-color11:not(div):not(span):active,
.button-color11:not(div):not(span).selected {
  border-color: #8d5ba1;
  background: #8d5ba1 !important;
}

.button-color11.button-hollow,
.button-color11.button-hollow .icon:before {
  color: #ca82e6;
}

.button-color11.button-icon:hover .icon::before,
.button-color11.button-icon:focus .icon::before {
  color: #ca82e6 !important;
}

.button-color12 {
  border-color: #e3e2e6;
  background-color: #e3e2e6 !important;
}

.button-color12:not(div):not(span):hover,
.button-color12:not(div):not(span):focus {
  border-color: #c1c0c4;
  background: #c1c0c4 !important;
}

.button-color12:not(div):not(span):active,
.button-color12:not(div):not(span).selected {
  border-color: #9f9ea1;
  background: #9f9ea1 !important;
}

.button-color12.button-hollow,
.button-color12.button-hollow .icon:before {
  color: #e3e2e6;
}

.button-color12.button-icon:hover .icon::before,
.button-color12.button-icon:focus .icon::before {
  color: #e3e2e6 !important;
}

.button-color13 {
  border-color: #f9ff50;
  background-color: #f9ff50 !important;
}

.button-color13:not(div):not(span):hover,
.button-color13:not(div):not(span):focus {
  border-color: #d4d944;
  background: #d4d944 !important;
}

.button-color13:not(div):not(span):active,
.button-color13:not(div):not(span).selected {
  border-color: #aeb338;
  background: #aeb338 !important;
}

.button-color13.button-hollow,
.button-color13.button-hollow .icon:before {
  color: #f9ff50;
}

.button-color13.button-icon:hover .icon::before,
.button-color13.button-icon:focus .icon::before {
  color: #f9ff50 !important;
}

.button-color14 {
  border-color: #202020;
  background-color: #202020 !important;
}

.button-color14:not(div):not(span):hover,
.button-color14:not(div):not(span):focus {
  border-color: #1b1b1b;
  background: #1b1b1b !important;
}

.button-color14:not(div):not(span):active,
.button-color14:not(div):not(span).selected {
  border-color: #161616;
  background: #161616 !important;
}

.button-color14.button-hollow,
.button-color14.button-hollow .icon:before {
  color: #202020;
}

.button-color14.button-icon:hover .icon::before,
.button-color14.button-icon:focus .icon::before {
  color: #202020 !important;
}

.button-color15 {
  border-color: #2d2d2d;
  background-color: #2d2d2d !important;
}

.button-color15:not(div):not(span):hover,
.button-color15:not(div):not(span):focus {
  border-color: #262626;
  background: #262626 !important;
}

.button-color15:not(div):not(span):active,
.button-color15:not(div):not(span).selected {
  border-color: #202020;
  background: #202020 !important;
}

.button-color15.button-hollow,
.button-color15.button-hollow .icon:before {
  color: #2d2d2d;
}

.button-color15.button-icon:hover .icon::before,
.button-color15.button-icon:focus .icon::before {
  color: #2d2d2d !important;
}

.button-color16 {
  border-color: #484848;
  background-color: #484848 !important;
}

.button-color16:not(div):not(span):hover,
.button-color16:not(div):not(span):focus {
  border-color: #3d3d3d;
  background: #3d3d3d !important;
}

.button-color16:not(div):not(span):active,
.button-color16:not(div):not(span).selected {
  border-color: #323232;
  background: #323232 !important;
}

.button-color16.button-hollow,
.button-color16.button-hollow .icon:before {
  color: #484848;
}

.button-color16.button-icon:hover .icon::before,
.button-color16.button-icon:focus .icon::before {
  color: #484848 !important;
}

.button-color17 {
  border-color: gray;
  background-color: gray !important;
}

.button-color17:not(div):not(span):hover,
.button-color17:not(div):not(span):focus {
  border-color: #6d6d6d;
  background: #6d6d6d !important;
}

.button-color17:not(div):not(span):active,
.button-color17:not(div):not(span).selected {
  border-color: #5a5a5a;
  background: #5a5a5a !important;
}

.button-color17.button-hollow,
.button-color17.button-hollow .icon:before {
  color: gray;
}

.button-color17.button-icon:hover .icon::before,
.button-color17.button-icon:focus .icon::before {
  color: gray !important;
}

.button-color18 {
  border-color: #ff74af;
  background-color: #ff74af !important;
}

.button-color18:not(div):not(span):hover,
.button-color18:not(div):not(span):focus {
  border-color: #d96395;
  background: #d96395 !important;
}

.button-color18:not(div):not(span):active,
.button-color18:not(div):not(span).selected {
  border-color: #b3517b;
  background: #b3517b !important;
}

.button-color18.button-hollow,
.button-color18.button-hollow .icon:before {
  color: #ff74af;
}

.button-color18.button-icon:hover .icon::before,
.button-color18.button-icon:focus .icon::before {
  color: #ff74af !important;
}

/* Social */

.button-google {
  border-color: #dd4b39;
  background-color: #dd4b39 !important;
}

.button-google:not(div):not(span):hover,
.button-google:not(div):not(span):focus {
  border-color: #bc4030;
  background: #bc4030 !important;
}

.button-google:not(div):not(span):active,
.button-google:not(div):not(span).selected {
  border-color: #9b3528;
  background: #9b3528 !important;
}

.button-google.button-hollow,
.button-google.button-hollow .icon:before {
  color: #dd4b39;
}

.button-google.button-icon:hover .icon::before,
.button-google.button-icon:focus .icon::before {
  color: #dd4b39 !important;
}

.button-facebook {
  border-color: #1877f2;
  background-color: #1877f2 !important;
}

.button-facebook:not(div):not(span):hover,
.button-facebook:not(div):not(span):focus {
  border-color: #1465ce;
  background: #1465ce !important;
}

.button-facebook:not(div):not(span):active,
.button-facebook:not(div):not(span).selected {
  border-color: #1153a9;
  background: #1153a9 !important;
}

.button-facebook.button-hollow,
.button-facebook.button-hollow .icon:before {
  color: #1877f2;
}

.button-facebook.button-icon:hover .icon::before,
.button-facebook.button-icon:focus .icon::before {
  color: #1877f2 !important;
}

.button-twitter {
  border-color: #1da1f2;
  background-color: #1da1f2 !important;
}

.button-twitter:not(div):not(span):hover,
.button-twitter:not(div):not(span):focus {
  border-color: #1989ce;
  background: #1989ce !important;
}

.button-twitter:not(div):not(span):active,
.button-twitter:not(div):not(span).selected {
  border-color: #1471a9;
  background: #1471a9 !important;
}

.button-twitter.button-hollow,
.button-twitter.button-hollow .icon:before {
  color: #1da1f2;
}

.button-twitter.button-icon:hover .icon::before,
.button-twitter.button-icon:focus .icon::before {
  color: #1da1f2 !important;
}

.button-pinterest {
  border-color: #bd081c;
  background-color: #bd081c !important;
}

.button-pinterest:not(div):not(span):hover,
.button-pinterest:not(div):not(span):focus {
  border-color: #a10718;
  background: #a10718 !important;
}

.button-pinterest:not(div):not(span):active,
.button-pinterest:not(div):not(span).selected {
  border-color: #840614;
  background: #840614 !important;
}

.button-pinterest.button-hollow,
.button-pinterest.button-hollow .icon:before {
  color: #bd081c;
}

.button-pinterest.button-icon:hover .icon::before,
.button-pinterest.button-icon:focus .icon::before {
  color: #bd081c !important;
}

.button-linkedin {
  border-color: #0077b5;
  background-color: #0077b5 !important;
}

.button-linkedin:not(div):not(span):hover,
.button-linkedin:not(div):not(span):focus {
  border-color: #00659a;
  background: #00659a !important;
}

.button-linkedin:not(div):not(span):active,
.button-linkedin:not(div):not(span).selected {
  border-color: #00537f;
  background: #00537f !important;
}

.button-linkedin.button-hollow,
.button-linkedin.button-hollow .icon:before {
  color: #0077b5;
}

.button-linkedin.button-icon:hover .icon::before,
.button-linkedin.button-icon:focus .icon::before {
  color: #0077b5 !important;
}

.button-instagram {
  border-color: #d6249f;
  background-color: #d6249f !important;
}

.button-instagram:not(div):not(span):hover,
.button-instagram:not(div):not(span):focus {
  border-color: #b61f87;
  background: #b61f87 !important;
}

.button-instagram:not(div):not(span):active,
.button-instagram:not(div):not(span).selected {
  border-color: #96196f;
  background: #96196f !important;
}

.button-instagram.button-hollow,
.button-instagram.button-hollow .icon:before {
  color: #d6249f;
}

.button-instagram.button-icon:hover .icon::before,
.button-instagram.button-icon:focus .icon::before {
  color: #d6249f !important;
}

.button-youtube {
  border-color: #ff0000;
  background-color: #ff0000 !important;
}

.button-youtube:not(div):not(span):hover,
.button-youtube:not(div):not(span):focus {
  border-color: #d90000;
  background: #d90000 !important;
}

.button-youtube:not(div):not(span):active,
.button-youtube:not(div):not(span).selected {
  border-color: #b30000;
  background: #b30000 !important;
}

.button-youtube.button-hollow,
.button-youtube.button-hollow .icon:before {
  color: #ff0000;
}

.button-youtube.button-icon:hover .icon::before,
.button-youtube.button-icon:focus .icon::before {
  color: #ff0000 !important;
}

.button-podcast {
  border-color: #ac4ddf;
  background-color: #ac4ddf !important;
}

.button-podcast:not(div):not(span):hover,
.button-podcast:not(div):not(span):focus {
  border-color: #9241be;
  background: #9241be !important;
}

.button-podcast:not(div):not(span):active,
.button-podcast:not(div):not(span).selected {
  border-color: #78369c;
  background: #78369c !important;
}

.button-podcast.button-hollow,
.button-podcast.button-hollow .icon:before {
  color: #ac4ddf;
}

.button-podcast.button-icon:hover .icon::before,
.button-podcast.button-icon:focus .icon::before {
  color: #ac4ddf !important;
}

/* Instagram */

.button-instagram-style-2 {
  border-color: transparent;
  background-color: #d6249f !important;
  background-image: radial-gradient(
    circle at 30% 107%,
    #fdf497 0%,
    #fdf497 5%,
    #fd5949 45%,
    #d6249f 60%,
    #285aeb 90%
  );
}

/* Button Sizes
   ============================================================================ */

@media screen and (min-width: 40em) {
  /* Large */

  .button-large {
    font-size: 1.125rem;
    padding: 1.2em 4em;
  }
}

/* Small */

.button-small {
  padding: 1em 2em;
  font-size: 0.75rem;
  text-transform: none;
}

/* Extra Small */

.button-xsmall {
  padding: 0.5em 1em;
  font-size: 0.6875rem;
  text-transform: none;
}

/* Button Formats
   ============================================================================ */

/* Alignment */

.alignright .button:last-child,
.float-right .button:last-child,
.text-right .button:last-child,
.text-center .button:last-child {
  margin-right: auto;
}

/* Margin */

.button-margin-small {
  margin: 0 0.3125rem 0.3125rem 0;
}

/* Stacked */

.buttons-stacked > .button {
  float: left;
  margin: 0 0.3125rem 0.3125rem 0;
  padding: 1em;
}

/* Group */

.button-group .button {
  font-size: inherit;
}

.button-group .button:first-child {
  border-radius: 5px 0 0 5px;
}

.button-group .button:nth-child(2) {
  border-radius: 0 5px 5px 0;
}

/* Actions */

.button-actions {
  color: #f1f0f5;
  font-weight: 400;
  text-transform: none;
}

/* Hollow */

.button-hollow {
  background: none !important;
}

/* Icon Right */

.button-icon-right {
  padding-right: 4em !important;
  text-align: left;
}

.button-icon-right .icon {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: 2em;
}

/* Icon Left */

.button-icon-left {
  padding-left: 4em !important;
  text-align: right;
}

.button-icon-left .icon {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  left: 2em;
}

/* Icon Right Large */

.button-large.button-icon-right {
  padding-left: 3em;
  padding-right: 5em;
}

/* Icon Left Large */

.button-large.button-icon-left {
  padding-left: 5em;
  padding-right: 3em;
}

/* Button Empty */

.button-empty {
  background: none;
  color: #71acd1;
  font-weight: 400;
  text-transform: none;
  transition: all 0.3s ease 0s;
}

.button.button-empty:hover,
.button.button-empty:focus,
.button.button-empty:hover .icon:before,
.button.button-empty:focus .icon:before {
  color: #51606d !important;
}

.button-empty:hover,
.button-empty:focus {
  background: #f7f7f7;
}

.button-empty:active,
.button-empty:active .icon:before {
  background: #f0f0f0;
}

/* Button Empty Style 2 */

.button-empty-style-2:hover,
.button-empty-style-2:focus {
  background: #eae9ee;
}

.button-empty-style-2:active,
.button-empty-style-2:active .icon:before {
  background: #dbdadf;
}

/* Button Empty Style 3 */

.button.button-empty-style-3:hover,
.button.button-empty-style-3:focus,
.button.button-empty-style-3:hover .icon:before,
.button.button-empty-style-3:focus .icon:before {
  color: #b1b0b5 !important;
}

.button-empty-style-3:hover,
.button-empty-style-3:focus {
  background: #29394a;
}

.button-empty-style-3:active,
.button-empty-style-3:active .icon:before {
  background: #364555;
}

/* Button Light */

.button-light {
  background: #f7f7f7;
  display: inline-block;
  color: #51606d;
  font-weight: 400;
  text-transform: none;
  transition: all 0.3s ease 0s;
}

.button.button-light:hover,
.button.button-light:focus,
.button.button-light:hover .icon:before,
.button.button-light:focus .icon:before {
  color: #51606d !important;
}

.button-light:hover,
.button-light:focus {
  background: #f0f0f0;
}

.button-light:active,
.button-light:active .icon:before {
  background: #e8e8e8;
}

/* Button Light Style 2 */

.button-light-style-2 {
  background: #f1f0f5;
}

.button-light-style-2:hover,
.button-light-style-2:focus {
  background: #eae9ee;
}

.button-light-style-2:active,
.button-light-style-2:active .icon:before {
  background: #e3e2e6;
}

/* Button Input */

.button-input {
  margin: 1.75rem 0 0 0;
  padding: 1.125rem 0;
  width: 100%;
}

/* Button Disabled */

.button-disabled {
  pointer-events: none;
  border-color: #ccc;
}

.button-disabled .icon:before {
  color: #bbb !important;
}

/* Button Icon
   ============================================================================ */

.button-icon {
  transition: all 0.3s ease 0s;
}

.button-icon:not(div):not(span):hover,
.button-icon:not(div):not(span):focus,
.button-icon:not(div):not(span):active,
.button-icon:not(div):not(span).selected {
  background: #f1f0f5 !important;
}

.button-icon:not(div):not(span):active {
  transform: translateY(1px);
}

/* Style 2 */

.button-icon-style-2:not(div):not(span):hover,
.button-icon-style-2:not(div):not(span):focus,
.button-icon-style-2:not(div):not(span):active,
.button-icon-style-2:not(div):not(span).selected {
  background: #e3e2e6 !important;
}

/* Style 3 */

.button-icon-style-3:not(div):not(span):hover,
.button-icon-style-3:not(div):not(span):focus,
.button-icon-style-3:not(div):not(span):active,
.button-icon-style-3:not(div):not(span).selected {
  background: #29394a !important;
}

/* Style 4 */

.button-icon-style-4:not(div):not(span):hover,
.button-icon-style-4:not(div):not(span):focus,
.button-icon-style-4:not(div):not(span):active,
.button-icon-style-4:not(div):not(span).selected {
  background: #3b3b3b !important;
}

/* ============================================================================
   Avatar
   ============================================================================ */

.avatar {
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: cover;
}

/* Avatar Sizes
   ============================================================================ */

.avatar-xxsmall {
  width: 2.125rem;
  height: 2.125rem;
}

.avatar-xsmall {
  width: 2.625rem;
  height: 2.625rem;
}

.avatar-small {
  width: 4.5rem;
  height: 4.5rem;
}

.avatar-medium {
  width: 6rem;
  height: 6rem;
}

.avatar-large {
  width: 7.8125rem;
  height: 7.8125rem;
}

.avatar-xlarge {
  width: 12.5rem;
  height: 12.5rem;
}

/* Avatar styles
   ============================================================================ */

/* Style 2 */

.avatar-style-2 {
  box-shadow:
    0 0 0 6px #234,
    0 0 0 7px #51606d;
  margin: 7px auto;
}

/* ============================================================================
   Product Image
   ============================================================================ */

.product-image {
  border: 1px solid rgba(0, 0, 0, 0.12) !important;
  box-sizing: border-box;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease 0s;
}

a:hover .product-image,
a:focus .product-image,
a.product-image:hover,
a.product-image:focus {
  box-shadow: 0 5px 12px 0 rgba(0, 0, 0, 0.18);
  transform: scale(1.05);
}

.product-image.float-left {
  margin-right: 0.8rem;
}

.product-image.float-right {
  margin-left: 0.8rem;
}

.item-select {
  display: inline-block;
  position: relative;
}

.item-select img {
  border: 3px solid #fff;
  border-radius: 5px;
}

.item-select.selected img {
  border: 3px solid #76bb76;
  border-radius: 5px;
}

.item-select:hover img {
  border: 3px solid #71acd1;
  border-radius: 5px;
}

.item-select.selected:hover img,
.item-select:active img {
  border: 3px solid #76bb76;
  border-radius: 5px;
}

.item-select.selected::after {
  display: inline-block;
  background: "/images/_demo/product/selected-item-check.png";
  background-position: bottom right;
  height: 54px;
  width: 54px;
  background-size: 64px 54px;
  content: "";
  bottom: 0;
  right: 0;
  position: absolute;
  opacity: 1;
  transition: ease-in 0.5s;
}

.item-select:hover.selected::after {
  opacity: 0;
}

/* Product Image Sizes
   ============================================================================ */

/* Extra Small */

.product-image-xsmall {
  width: 56px !important;
}

/* Small */

.product-image-small {
  width: 102px !important;
}

/* Medium */

.product-image-medium {
  width: 150px !important;
}

/* Large */

.product-image-large {
  width: 230px !important;
}

/* Extra Large */

.product-image-xlarge {
  width: 356px !important;
}

/* Product image styles
   ============================================================================ */

/* Style 2 */

.product-image-style-2 {
  box-shadow: -2px 2px 10px 0 rgba(0, 0, 0, 0.18);
  transform: rotate(-3deg);
}

.product-image-style-3 {
  border: none;
  box-shadow: 1px 1px 6px 0 rgba(0, 0, 0, 0.012);
  transition: all 0.3s ease 0s;
}

.product-image-style-radius {
  border-radius: 17px;
  overflow: hidden;
}

.product-image-reflection {
  -webkit-filter: blur(10px);
  filter: blur(10px);
  position: absolute;
  left: 0;
  z-index: 0;
  top: 10px;
  opacity: 0.55;
  border-radius: 37px;
}

/* Product image formats
   ============================================================================ */

.product-image-button {
  border: 5px solid #fff;
  width: 62px;
  height: 62px;
  line-height: 62px;
  position: absolute;
  bottom: 6rem;
  right: -3rem;
}

/* ============================================================================
   Overlay

   <div class="position-relative">
       <div class="overlay overlay-light">
           <div class="overlay-inner">
               <div class="badge button-color4">
                   <i class="icon ion-locked"></i>
               </div>
           </div>
       </div>
   </div>
   ============================================================================ */

.overlay {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  text-align: center;
}

.overlay-dark {
  background: rgba(0, 0, 0, 0.5);
}

.overlay-light {
  background: rgba(255, 255, 255, 0.75);
}

.overlay-inner {
  width: 100%;
  padding: 6%;
}

/* ============================================================================
   Image Overlay
   TODO: Eval and remove
   ============================================================================ */

.image-overlay-container {
  display: block;
  position: relative;
}

.image-overlay {
  background: rgba(255, 255, 255, 0.75);
  position: absolute;
  width: 100%;
  height: 100%;
}

.image-overlay-item {
  position: absolute;
  margin: 0;
  padding: 0;
}

.image-overlay-item-center-center {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.image-overlay-container:hover .product-image,
.image-overlay-container:focus .product-image {
  transform: none;
}

/* ============================================================================
   Box
   ============================================================================ */

.box {
  background: #fff;
  border-radius: 5px;
  display: block;
  margin-bottom: 1.5rem;
  position: relative;
  box-shadow: 0 5px 5px rgba(0, 22, 44, 0.03);
}

.box > h1:first-child,
.box > h2:first-child,
.box > h3:first-child,
.box > h4:first-child,
.box > h5:first-child,
.box > h6:first-child {
  padding-top: 0;
}

/* Box Style 2
   ============================================================================ */

.box-style-2 {
  border: 3px dashed #e3e2e6;
  border-radius: 22px;
  display: block;
}

.box-style-2,
.box-style-2 .badge-hover,
.box-style-2 .badge-hover .icon:before {
  transition: all 0.3s ease 0s;
}

a.box-style-2:hover,
a.box-style-2:focus,
.box-style-2-dragging {
  background: #f7f7f7;
  border-color: #71acd1;
}

a.box-style-2:hover .badge-hover,
a.box-style-2:focus .badge-hover,
.box-style-2-dragging .badge-hover {
  transform: translate(0, -5px);
}

a.box-style-2:hover .badge-hover .icon:before,
a.box-style-2:focus .badge-hover .icon:before,
.box-style-2-dragging .badge-hover .icon:before {
  color: #71acd1;
}

/* Dark */

.box-style-2-dark {
  border-color: #484848;
}

a.box-style-2-dark:hover,
a.box-style-2-dark:focus {
  background: #343434;
}

/* ============================================================================
   Menu
   ============================================================================ */

.menu > li > a {
  padding: 0.875rem 1rem;
}

.menu.vertical li {
  margin: 0;
}

/* ============================================================================
   Vue Dropdown
   ============================================================================ */

.dropdown-panel {
  border-radius: 5px;
  background: #fff;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.12);
  min-width: 12.5rem;
  max-height: 28.25rem;
  overflow-y: scroll;
}

/* ============================================================================
   Switch
   ============================================================================ */

.switch {
  margin-bottom: 0;
}

.switch.disabled {
  pointer-events: none;
}

.switch-paddle {
  background: #dcdee6 none repeat scroll 0 0;
  border-radius: 5000px;
  width: 4rem;
}

.switch-paddle::after {
  border-radius: 50%;
  left: 0.3rem;
}

.switch-paddle.active,
input:checked ~ .switch-paddle {
  background: #76bb76 !important;
}

.switch-paddle.active::after {
  left: 2.25rem;
}

.switch-active,
.switch-inactive {
  width: 1.9rem;
  font-size: 0.75rem;
  line-height: 1;
  text-align: center;
}

.switch-active {
  left: 0.3rem;
}

.switch-inactive {
  right: 0.3rem;
}

/* Large */

.switch-large .switch-paddle {
  width: 5rem;
}

.switch-large .switch-active,
.switch-large .switch-inactive {
  width: 2.9rem;
}

.switch-large .switch-paddle.active::after {
  left: 3.2rem;
}

/* Extra Large */

.switch-xlarge .switch-paddle {
  width: 6rem;
}

.switch-xlarge .switch-active,
.switch-xlarge .switch-inactive {
  width: 3.9rem;
}

.switch-xlarge .switch-paddle.active::after {
  left: 4.2rem;
}

/* Colors */

.switch-color1 .switch-paddle {
  background: #71acd1 !important;
}

.switch-color5 .switch-paddle {
  background: #b1b0b5 !important;
}

/* Position Center */

.switch-center .switch-paddle {
  margin: 0 auto;
}

/* Position Top Right */

.switch-top-right {
  border-bottom: 1px solid #eaeaea;
  display: block;
  float: left;
  margin-bottom: 2rem;
  position: relative;
  z-index: 8;
  width: 100%;
}

.switch-top-right .switch {
  float: right;
}

@media screen and (min-width: 40em) {
  .switch-top-right {
    position: absolute;
    right: 25px;
    z-index: 5;
    width: auto;
    margin-bottom: 0;
    border-bottom: none;
  }
}

/* ============================================================================
   Slider
   ============================================================================ */

.slider {
  border-radius: 999px;
  background-color: #f1f0f5;
  width: 100%;
  min-width: 9.375rem;
  height: 3px;
  margin: 0.5rem 0;
}

.slider-handle {
  border-radius: 999px;
  background-color: #71acd1;
  width: 1rem;
  height: 1rem;
}

/* ============================================================================
   Tooltip
   ============================================================================ */

.has-tip {
  font-weight: inherit;
  display: inherit;
}

.tooltip {
  border-radius: 5px;
  background-color: #202020;
  overflow-wrap: break-word;
  word-wrap: break-word;
  font-size: 0.875rem;
  /* Use the directive styles and apply sizes from Foundation.css */
}

.tooltip .tooltip-arrow {
  width: 0;
  height: 0;
  border-style: solid;
  position: absolute;
  margin: 0.75rem;
  border-color: #202020;
  z-index: 1;
}

.tooltip[x-placement^="top"] {
  margin-bottom: 0.75rem;
}

.tooltip[x-placement^="top"] .tooltip-arrow {
  border-width: 0.75rem 0.75rem 0 0.75rem;
  border-left-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  bottom: -0.75rem;
  left: calc(50% - 0.75rem);
  margin-top: 0;
  margin-bottom: 0;
}

.tooltip[x-placement^="bottom"] {
  margin-top: 0.75rem;
}

.tooltip[x-placement^="bottom"] .tooltip-arrow {
  border-width: 0 0.75rem 0.75rem 0.75rem;
  border-left-color: transparent !important;
  border-right-color: transparent !important;
  border-top-color: transparent !important;
  top: -0.75rem;
  left: calc(50% - 0.75rem);
  margin-top: 0;
  margin-bottom: 0;
}

.tooltip[x-placement^="right"] {
  margin-left: 0.75rem;
}

.tooltip[x-placement^="right"] .tooltip-arrow {
  border-width: 0.75rem 0.75rem 0.75rem 0;
  border-left-color: transparent !important;
  border-top-color: transparent !important;
  border-bottom-color: transparent !important;
  left: -0.75rem;
  top: calc(50% - 0.75rem);
  margin-left: 0;
  margin-right: 0;
}

.tooltip[x-placement^="left"] {
  margin-right: 0.75rem;
}

.tooltip[x-placement^="left"] .tooltip-arrow {
  border-width: 0.75rem 0 0.75rem 0.75rem;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  right: -0.75rem;
  top: calc(50% - 0.75rem);
  margin-left: 0;
  margin-right: 0;
}

/* remove the foundation added tooltip carat since the directive automatically places and adjusts carat position of the tooltip*/

.tooltip::before {
  display: none !important;
}

/* ============================================================================
   Label
   ============================================================================ */

.label {
  margin-left: 0.6em;
  padding: 0.4em;
  position: relative;
  color: #f1f0f5;
  font-size: 14px;
}

.label-list {
  margin: 0 0.6em 0.4em 0;
  position: static;
}

/* Label Position */

.label-position-top-right {
  top: -8px;
  left: 8px;
}

/* ============================================================================
   Progress Bar
   ============================================================================ */

.progress {
  border-radius: 999px;
  overflow: hidden;
}

.progress-meter {
  transition-property: width;
  transition-duration: 400ms;
  transition-timing-function: ease-out;
}

.progress.alert .progress-meter {
  transition: none;
}

/* ============================================================================
   Close Button
   ============================================================================ */

.close-button .icon:before {
  font-size: 28px;
  margin: 0;
  opacity: 1;
}

.close-button {
  top: 1rem;
  color: #b1b0b5;
}

.close-button:hover,
.close-button:focus {
  color: #7c7b7f;
}

.close-button-left {
  left: 1rem;
}

/* Menu */

.close-button-menu {
  top: 1rem;
  right: 1.5rem;
}

.close-button-menu.close-button-left {
  left: 1.5rem;
}

/* ============================================================================
   Modals
   ============================================================================ */

/* Overlay
   ============================================================================ */

.reveal-overlay {
  background-color: rgba(0, 0, 0, 0.62);
  z-index: 1006;
}

/* Modal
   ============================================================================ */

.reveal {
  border: 0;
  padding: 0;
}

.reveal.full {
  top: 0 !important;
  /* Fixes issue with zoom */
}

.reveal h1 {
  padding: 0 0 1.32em;
}

.reveal h1 + p {
  margin-top: -1.5rem;
}

/* Fix for modals as children of rows */

.reveal .form {
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
}

/* Modal header */

.modal-header {
  background: #1c2a38;
}

.modal-header .modal-heading {
  color: #f1f0f5;
}

.modal-header .modal-meta {
  display: block;
  margin: -1.5rem 0 0.5rem;
  color: #b1b0b5;
  font-size: 16px;
}

.modal-header .inner-large {
  padding: 9% 6%;
}

@media screen and (min-width: 40em) {
  .reveal {
    border-radius: 5px;
  }
}

/* Uploader initially hidden */

#import_file {
  display: none;
}

/* Modal formats
   ============================================================================ */

/* Preview */

.modal-preview .flipbook {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) !important;
}

/* ============================================================================
   Joyride
   ============================================================================ */

.is-joyride-open {
  overflow: hidden;
}

.joyride {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 0 40px 0 rgba(0, 0, 0, 0.5);
  width: 20rem;
  padding: 1.5rem;
  position: fixed;
  z-index: 9;
}

/* Highlight current target */

.joyride-target-current {
  z-index: 1007 !important;
}

/* Overlay */

.joyride-overlay.bg-none {
  z-index: 1008;
}

/* Carat */

.joyride:before {
  border-style: solid;
  border-width: 0.75rem;
  display: block;
  content: "";
  width: 0;
  height: 0;
  position: absolute;
}

.joyride-top-left:before,
.joyride-top-right:before {
  border-color: transparent transparent #fff;
  top: -1.5rem;
}

.joyride-right-top:before,
.joyride-right-bottom:before {
  border-color: transparent transparent transparent #fff;
  right: -1.5rem;
}

.joyride-bottom-right:before,
.joyride-bottom-left:before {
  border-color: #fff transparent transparent;
  bottom: -1.5rem;
}

.joyride-left-bottom:before,
.joyride-left-top:before {
  border-color: transparent #fff transparent transparent;
  left: -1.5rem;
}

.joyride-top-left:before {
  left: 1.5rem;
}

.joyride-top-right:before {
  right: 1.5rem;
}

.joyride-right-top:before {
  top: 1.5rem;
}

.joyride-right-bottom:before {
  bottom: 1.5rem;
}

.joyride-bottom-right:before {
  right: 1.5rem;
}

.joyride-bottom-left:before {
  left: 1.5rem;
}

.joyride-left-bottom:before {
  bottom: 1.5rem;
}

.joyride-left-top:before {
  top: 1.5rem;
}

/* Right align */

.joyride.align-right::before {
  left: auto;
  right: 12px;
}

/* Close button */

.joyride .close {
  display: none;
}

/* Table placeholder */

.joyride-placeholder-table {
  visibility: hidden;
  position: absolute;
  top: 76px;
}

/* ============================================================================
   Scrollable Module
   TODO: Add utility classes for interaction with padding and merge with below
   ============================================================================ */

.scrollable-body-xlarge {
  margin-top: -10%;
}

.scrollable-body .inner {
  padding: 6%;
}

@media screen and (min-width: 64em) {
  .scrollable-body {
    height: 400px;
    overflow: scroll;
  }

  .scrollable-body-xlarge {
    margin-top: -4.5rem;
    margin-bottom: -3rem;
  }

  .scrollable-body .inner {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .scrollable-body .inner-xlarge {
    padding-left: 4.5rem;
    padding-right: 4.5rem;
  }
}

/* ============================================================================
   Scrollable Panel
   ============================================================================ */

.scrollable-panel {
  height: 300px;
  overflow: scroll !important;
}

.scrollable-panel-small {
  height: 200px;
}

.scrollable-panel-large {
  height: 400px;
}

/* ============================================================================
   Toolbox
   ============================================================================ */

.toolbox {
  float: left;
  margin-right: 5.5rem;
}

.toolbox .badger-accordion-title {
  max-width: 12.5rem;
  /* 200px */
  line-height: 1.1;
}

@media screen and (max-width: 47.9375em) {
  .toolbox {
    display: none;
  }
}

@media screen and (min-width: 64em) and (max-width: 79.9375em) {
  .toolbox {
    display: none;
  }
}

@media screen and (min-width: 64em) {
  .toolbox {
    margin-right: 1.5rem;
  }
}

@media screen and (min-width: 88em) {
  .toolbox .badger-accordion-title {
    max-width: 21.25rem;
    /* 340px */
  }
}

/* ============================================================================
   Tabs
   ============================================================================ */

.tabs {
  border: 0;
  background: none;
  padding: 1rem 0.5rem 0;
}

.tabs-title {
  margin: 0 0.2rem;
}

.tabs-content {
  border: 0;
  background: none;
  clear: both;
  margin-bottom: 1.5rem;
  position: relative;
  color: inherit;
}

.tabs-panel {
  padding: 1.5rem;
}

.tabs-title a {
  border-radius: 5px;
  color: #51606d;
  font-size: 14px;
  margin-bottom: 15px;
  padding: 0.5rem;
}

@media screen and (min-width: 40em) {
  .tabs-title a {
    border-radius: 5px 5px 0 0;
    margin-bottom: 0;
    padding: 1.25rem 1.5rem;
  }
}

.tabs-title a:hover,
.tabs-title a:focus {
  background: #fff;
  color: #51606d;
}

.tabs-title a:active {
  background: #fff;
}

.tabs-title > a:focus,
.tabs-title > a[aria-selected="true"] {
  background: inherit;
}

.tabs-title.is-active a {
  background: #234;
  color: #fff;
  font-weight: bold;
}

@media screen and (min-width: 40em) {
  .tabs-title.is-active a {
    background: #fff;
    color: #51606d;
  }
}

/* Tabs Style 2
   ============================================================================ */

.tabs-style-2 > div > .tabs {
  background: none;
  padding: 0;
  position: relative;
  top: 1px;
  z-index: 1;
}

.tabs-style-2 > div > .tabs > .tabs-title > a {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background: #f1f0f5;
  margin-bottom: 0;
  padding: 0.7rem;
  color: inherit;
}

@media screen and (min-width: 40em) {
  .tabs-style-2 > div > .tabs > .tabs-title > a {
    padding: 1.25rem 1.5rem;
  }
}

.tabs-style-2 > div > .tabs > .is-active > a {
  border: 1px solid #b1b0b5;
  border-bottom: 0;
  background: #fff;
  color: #51606d;
}

.tabs-style-2 > .tabs-content {
  border-top: 1px solid #b1b0b5;
}

/* Tabs Style 3
   ============================================================================ */

.tabs-style-3 > div > .tabs {
  background: none;
  padding: 0;
  position: relative;
  top: 1px;
  z-index: 1;
}

.tabs-style-3 > div > .tabs > .tabs-title > a {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background: #fff;
  margin-bottom: 0;
  padding: 0.7rem;
}

@media screen and (min-width: 40em) {
  .tabs-style-3 > div > .tabs > .tabs-title > a {
    padding: 1.25rem 1.5rem;
  }
}

.tabs-style-3 > div > .tabs > .is-active > a {
  border: 1px solid #b1b0b5;
  border-bottom: 0;
  background: #f1f0f5;
  color: #51606d;
}

.tabs-style-3 > div > .tabs-content {
  border-top: 1px solid #b1b0b5;
}

/* Tabs Style 4
   ============================================================================ */

.tabs-style-4 > div > .tabs {
  border-radius: 5px;
  background: #f1f0f5;
}

.tabs-style-4 > div > .tabs > .tabs-title {
  margin: 0.2rem;
}

.tabs-style-4 > div > .tabs > .tabs-title > a {
  border-radius: 5px;
  background: #f1f0f5;
  margin-bottom: 0;
  padding: 0.7rem;
}

@media screen and (min-width: 40em) {
  .tabs-style-4 > div > .tabs > .tabs-title > a {
    padding: 1.25rem 1.5rem;
  }
}

.tabs-style-4 > div > .tabs > .is-active > a {
  background: #fff;
  color: #51606d;
}

.tabs-style-4 > div > .tabs-content > .tabs-panel {
  padding: 1.5rem 0;
}

/* Tabs Style 5
   ============================================================================ */

.tabs-style-5 > div > .tabs {
  border-radius: 5px;
  padding: 0;
}

.tabs-style-5 > div > .tabs > .tabs-title {
  margin: 1%;
}

.tabs-style-5 > div > .tabs > .tabs-title > a {
  border-radius: 5px;
  margin-bottom: 0;
  padding: 0.7rem;
  color: #fff;
}

@media screen and (min-width: 40em) {
  .tabs-style-5 > div > .tabs > .tabs-title > a {
    padding: 1.25rem 1.5rem;
  }
}

.tabs-style-5 > div > .tabs > .is-active > a {
  box-shadow: inset 0 0 0px 0.4rem rgba(255, 255, 255, 0.5);
  color: #fff;
  background: inherit;
}

.tabs-style-5 > div > .tabs-content > .tabs-panel {
  padding: 1.5rem 0;
}

/* Tabs Style 6
   ============================================================================ */

.tabs-style-6 > div > .tabs {
  padding: 0;
}

.tabs-style-6 > div > .tabs > .tabs-title {
  margin: 0 0.3125rem;
  padding: 0.875rem 0;
}

.tabs-style-6 > div > .tabs > .tabs-title > a {
  border-radius: 5px;
  margin: 0;
  padding: 0.875rem 1rem;
  color: #51606d;
}

.tabs-style-6 > div > .tabs > .tabs-title > a:hover,
.tabs-style-6 > div > .tabs > .tabs-title > a:focus {
  background: #f7f7f7;
}

.tabs-style-6 > div > .tabs > .is-active {
  font-weight: 700;
  box-shadow: #71acd1 0 -5px 0 0 inset;
}

.tabs-style-6 > div > .tabs > .is-active > a,
.tabs-style-6 > div > .tabs > .is-active > a:hover,
.tabs-style-6 > div > .tabs > .is-active > a:focus {
  background: none;
}

.tabs-style-6 > div > .tabs > .has-count > a {
  padding-right: 2.5rem;
}

.tabs-style-6 > .tabs-content > div > .tabs-panel {
  padding: 0;
}

/* Top Bar Tabs */

.tabs-style-6.top-bar-tabs .tabs-content {
  padding-top: 8.875rem;
}

/* Tabs Columns
   ============================================================================ */

.tabs-col-3 > div > .tabs > .tabs-title {
  margin: 0.5%;
  width: 32.33%;
  text-align: center;
}

/* Tabs Formats
   ============================================================================ */

/* Small */

.tabs-small > div > .tabs > .tabs-title a {
  padding: 0.7rem;
}

/* Tabs Vertical
   ============================================================================ */

.tabs-vertical > div > .tabs > .tabs-title {
  margin: 0.25rem 0 0.25rem 0.625rem;
}

/* Tabs Vertical Style 2
   ============================================================================ */

.tabs-vertical.tabs-style-2 .tabs {
  top: auto;
  left: 1px;
}

.tabs-vertical.tabs-style-2 > div > .tabs > .tabs-title a {
  border-right: 1px solid #b1b0b5;
  border-radius: 5px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  background: #f1f0f5;
}

.tabs-vertical.tabs-style-2 > div > .tabs > .is-active > a {
  border: 1px solid #b1b0b5;
  border-right-color: #fff;
  background: #fff;
}

.tabs-vertical.tabs-style-2 > div > .tabs-content {
  border-top: 0;
  border-left: 1px solid #b1b0b5;
}

/* Tabs Vertical Style 3
   ============================================================================ */

.tabs-vertical.tabs-style-3 > div > .tabs {
  border-right: 5px solid #f1f0f5;
  top: 0;
  left: 5px;
}

.tabs-vertical.tabs-style-3 > div > .tabs > li:first-child {
  margin-top: 0;
}

.tabs-vertical.tabs-style-3 > div > .tabs > li:last-child {
  margin-bottom: 0;
}

.tabs-vertical.tabs-style-3 > div > .tabs > .tabs-title a {
  border: 0;
  border-radius: 0;
  color: #7d8892;
}

.tabs-vertical.tabs-style-3 > div > .tabs > .is-active > a {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  background-color: #f1f0f5;
  box-shadow: 5px 0 0 0 #71acd1;
  color: #51606d;
}

.tabs-vertical.tabs-style-3 > div > .tabs > .is-active > a .icon:before {
  color: #71acd1;
  opacity: 1;
}

.tabs-vertical.tabs-style-3 > div > .tabs-content {
  border-top: 0;
  border-left: 5px solid #f1f0f5;
}

/* ============================================================================
   Accordion
   ============================================================================ */

.accordion-item {
  margin-bottom: 0;
}

.accordion-title {
  border: 1px solid rgba(0, 0, 0, 0.12) !important;
  /* !important needed for Foundation override */
  border-radius: 5px !important;
  /* !important needed for Foundation override */
  background-color: #f7f7f7;
  color: #51606d;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  padding: 1.5rem 3rem 1.5rem 1.5rem;
  text-align: left;
}

.accordion-title:hover,
.accordion-title:focus {
  background-color: #f0f0f0;
}

.accordion-title:before {
  margin: 0;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: 1rem;
  font-family: "Ionicons";
  font-size: 1rem;
  content: "\F123";
}

/* Active */

.accordion-title[aria-selected="true"] {
  border-bottom: 0 !important;
  /* !important needed for Foundation override */
  border-bottom-left-radius: 0 !important;
  /* !important needed for Foundation override */
  border-bottom-right-radius: 0 !important;
  /* !important needed for Foundation override */
  margin-bottom: 0;
}

.accordion-title[aria-selected="true"]::before {
  content: "\F126";
}

/* Accordion Formats
   ============================================================================ */

/* Small */

.accordion-small .accordion-title {
  padding: 0.7rem;
  padding-left: 2.75rem;
  font-size: 14px;
}

.accordion-small .accordion-title:before {
  font-size: 16px;
}

/* ============================================================================
   Vue Accordion
   ============================================================================ */

.component-badger-accordion {
  margin: 0 0 1.5rem 0;
}

.badger-accordion__header {
  margin: 0;
}

.js-badger-accordion-header {
  margin: 0 0 0.5rem 0;
  box-shadow: none;
  border: 1px solid rgba(0, 0, 0, 0.12);
  font-size: 1rem;
  line-height: 1;
  border-radius: 5px;
  overflow: hidden;
}

.badger-accordion-toggle {
  background-color: #f7f7f7 !important;
  padding: 1.5em;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease 0s;
  justify-content: space-between;
}

.badger-accordion-toggle:hover,
.badger-accordion-toggle:focus {
  background-color: #f0f0f0 !important;
}

.badger-accordion-title {
  color: #51606d;
  flex: 0 1 auto !important;
}

.badger-toggle-indicator {
  color: #51606d !important;
  vertical-align: middle;
  flex: 0 1 auto !important;
}

.badger-accordion__panel {
  background: #fff;
  border-radius: 5px;
  margin: 0;
  max-height: 9999px !important;
}

.badger-accordion--initialized .badger-accordion__panel {
  transition: max-height 0.3s cubic-bezier(0.25, 0.8, 0.5, 1);
}

/* Active */

.js-badger-accordion-header.-ba-is-active {
  border-bottom: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  margin-bottom: 0;
}

.badger-accordion__panel.-ba-is-active {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  margin-bottom: 0.5rem;
}

/* Vue Accordion Style 2
   ============================================================================ */

.accordion-style-2 .js-badger-accordion-header,
.accordion-style-2 .badger-accordion__panel {
  border: 0;
}

.accordion-style-2 .badger-accordion-toggle {
  background-color: #fff !important;
  font-size: 0.875rem;
  min-height: 0;
  padding: 0.835rem 1rem;
}

.accordion-style-2 .badger-accordion-toggle:hover {
  background-color: #f7f7f7 !important;
}

/* Active */

.accordion-style-2 .-ba-is-active {
  box-shadow: 0 6px 6px 0 rgba(0, 0, 0, 0.12);
}

.accordion-style-2 .-ba-is-active .badger-accordion-toggle {
  background-color: #f7f7f7 !important;
}

/* ============================================================================
   Tabcordion
   ============================================================================ */

@media screen and (max-width: 63.9375em) {
  .tabcordion .tabs-panel {
    border: 1px solid rgba(0, 0, 0, 0.12) !important;
    /* !important needed for Foundation override */
    border-top: 0 !important;
    /* !important needed for Foundation override */
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    margin-bottom: 0.5rem;
  }

  /* Tab Style 2 and 3 */

  .tabcordion.tabs-style-2 .tabs-content,
  .tabcordion.tabs-style-3 .tabs-content {
    border-top: 0;
  }

  /* Tab Vertical Style 3 */

  .tabcordion.tabs-vertical.tabs-style-3 .tabs-content {
    border-left: 0;
  }

  .tabcordion.tabs-vertical.tabs-style-3
    .accordion-title[aria-selected="true"]
    .icon::before {
    color: #71acd1;
    opacity: 1;
  }
}

/* ============================================================================
   Off-Canvas
   ============================================================================ */

.off-canvas,
.off-canvas-absolute {
  background: none;
  padding-top: 4.5rem;
}

.has-top-bar-2 .off-canvas,
.has-top-bar-2 .off-canvas-absolute {
  background: none;
  padding-top: 9rem;
}

.position-left.is-transition-push:after,
.position-right.is-transition-push:after,
.off-canvas.is-transition-overlap.is-open {
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.25);
}

.off-canvas-content {
  background: none;
  box-shadow: none;
}

/* Off-Canvas Sizes
   ============================================================================ */

.off-canvas.position-left {
  transform: translateX(-100%);
}

.off-canvas.position-right {
  transform: translateX(100%);
}

.off-canvas.is-open {
  transform: translateX(0px);
}

.off-canvas.is-transition-overlap {
  z-index: 5;
}

/* Full */

.off-canvas-full {
  width: 100%;
}

.off-canvas-full.position-left.is-open + .off-canvas-content {
  transform: translateX(100%);
}

.off-canvas-full.position-right.is-open + .off-canvas-content {
  transform: translateX(-100%);
}

.off-canvas-full.is-transition-overlap.is-open ~ .off-canvas-content {
  transform: none;
}

/* Extra Large */

.off-canvas-xlarge {
  width: 320px;
}

.off-canvas-xlarge.position-left.is-open + .off-canvas-content {
  transform: translateX(320px);
}

.off-canvas-xlarge.position-right.is-open + .off-canvas-content {
  transform: translateX(-320px);
}

/* Extra Extra Large */

.off-canvas-xxlarge {
  width: 320px;
}

.off-canvas-xxlarge.position-left.is-open + .off-canvas-content {
  transform: translateX(320px);
}

.off-canvas-xxlarge.position-right.is-open + .off-canvas-content {
  transform: translateX(-320px);
}

@media screen and (min-width: 64em) {
  .off-canvas.reveal-for-large {
    transform: none;
    z-index: 1;
  }

  .off-canvas-modal.reveal-for-large {
    padding-top: 0;
  }

  .off-canvas.position-left.reveal-for-large + .off-canvas-content,
  .off-canvas.position-right.reveal-for-large + .off-canvas-content {
    transform: none;
  }

  /* Off-canvas close button */

  .off-canvas.position-left.reveal-for-large + .off-canvas-content,
  .off-canvas.position-right.reveal-for-large + .off-canvas-content {
    transform: none;
  }

  /* Off-canvas close button */

  .off-canvas-full.position-right.reveal-for-large .close-button-menu {
    display: none;
  }

  /* Modal close button */

  .off-canvas-modal.reveal-for-large ~ .close-button-modal {
    right: 380px;
    z-index: 1;
  }

  /* Large */

  .off-canvas-large {
    width: 360px;
  }

  .off-canvas-large.position-left.reveal-for-large + .off-canvas-content {
    margin-left: 360px;
  }

  .off-canvas-large.position-right.reveal-for-large + .off-canvas-content {
    margin-right: 360px;
  }

  /* Extra Large */

  .off-canvas-xlarge.position-left.reveal-for-large + .off-canvas-content {
    margin-left: 320px;
  }

  .off-canvas-xlarge.position-right.reveal-for-large + .off-canvas-content {
    margin-right: 320px;
  }

  /* Extra Extra Large */

  .off-canvas-xxlarge.position-left.reveal-for-large + .off-canvas-content {
    margin-left: 320px;
  }

  .off-canvas-xxlarge.position-right.reveal-for-large + .off-canvas-content {
    margin-right: 320px;
  }
}

@media screen and (min-width: 75em) {
  /* Extra Large */

  .off-canvas-xlarge {
    width: 546px;
  }

  .off-canvas-xlarge.position-left.reveal-for-large + .off-canvas-content {
    margin-left: 546px;
  }

  .off-canvas-xlarge.position-right.reveal-for-large + .off-canvas-content {
    margin-right: 546px;
  }

  /* Extra Extra Large */

  .off-canvas-xxlarge {
    width: 600px;
  }

  .off-canvas-xxlarge.position-left.reveal-for-large + .off-canvas-content {
    margin-left: 600px;
  }

  .off-canvas-xxlarge.position-right.reveal-for-large + .off-canvas-content {
    margin-right: 600px;
  }
}

@media screen and (min-width: 100em) {
  /* Extra Large */

  .off-canvas-xlarge {
    width: 730px;
  }

  .off-canvas-xlarge.position-left.reveal-for-large + .off-canvas-content {
    margin-left: 730px;
  }

  .off-canvas-xlarge.position-right.reveal-for-large + .off-canvas-content {
    margin-right: 730px;
  }

  /* Extra Extra Large */

  .off-canvas-xxlarge {
    width: 1036px;
  }

  .off-canvas-xxlarge.position-left.reveal-for-large + .off-canvas-content {
    margin-left: 1036px;
  }

  .off-canvas-xxlarge.position-right.reveal-for-large + .off-canvas-content {
    margin-right: 1036px;
  }
}

/* Off-Canvas Visibility
   ============================================================================ */

@media print, screen and (min-width: 100em) {
  .position-left.reveal-for-xlarge {
    transform: none;
    z-index: 1;
  }

  .position-left.reveal-for-xlarge ~ .off-canvas-content {
    margin-left: 250px;
  }

  .position-right.reveal-for-xlarge {
    transform: none;
    z-index: 1;
  }

  .position-right.reveal-for-xlarge ~ .off-canvas-content {
    margin-right: 250px;
  }

  .position-top.reveal-for-xlarge {
    transform: none;
    z-index: 1;
  }

  .position-top.reveal-for-xlarge ~ .off-canvas-content {
    margin-top: 250px;
  }

  .position-bottom.reveal-for-xlarge {
    transform: none;
    z-index: 1;
  }

  .position-bottom.reveal-for-xlarge ~ .off-canvas-content {
    margin-bottom: 250px;
  }
}

/* Off-Canvas Shadow
   TODO: Required until Wizard.vue supports different layouts
   ============================================================================ */

.off-canvas-shadow:after {
  content: "";
  position: fixed;
  top: 0;
  width: 6px;
  height: 100%;
  box-shadow: 6px 0 6px -6px rgba(0, 0, 0, 0.12) inset;
}

/* ============================================================================
   Owl Carousel
   TODO: Refactor into Vue Carousel
   ============================================================================ */

.owl-theme .owl-nav {
  margin: 0;
}

.owl-dots {
  margin-bottom: 1.5rem;
}

.owl-nav .icon:before {
  margin: 0;
  color: #51606d;
  font-size: 30px;
}

.owl-prev,
.owl-next {
  background: none !important;
  position: absolute;
  top: 40%;
}

.owl-prev {
  left: -3rem;
}

.owl-next {
  right: -3rem;
}

@media screen and (min-width: 40em) {
  .owl-nav .icon:before {
    font-size: 40px;
  }

  .owl-prev {
    left: -4rem;
  }

  .owl-next {
    right: -4rem;
  }
}

/* Owl Carousel Formats
   ============================================================================ */

/* Overflow */

.owl-overflow .owl-stage-outer {
  overflow: visible;
}

.owl-overflow .owl-prev,
.owl-overflow .owl-next {
  top: auto;
}

/* Hide Nav */

.owl-hide-nav .owl-nav {
  display: none;
}

/* Flush */

.owl-flush .owl-stage {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* ============================================================================
   Vue Carousel
   ============================================================================ */

.VueCarousel-wrapper {
  margin-bottom: 1.5rem;
  border-radius: 5px;
}

.VueCarousel-slide img {
  width: 100%;
}

.VueCarousel-dot-container {
  margin-bottom: 1.5rem !important;
}

.VueCarousel-dot {
  padding: 7px !important;
  margin: 0 !important;
  line-height: 0;
}

.VueCarousel-dot-button {
  background-color: #c5c4c8 !important;
}

.VueCarousel-dot--active .VueCarousel-dot-button {
  background-color: #7a7a7d !important;
}

/* ============================================================================
   Vue Cropper
   ============================================================================ */

.cropper-bg {
  background-repeat: repeat;
}

/* ============================================================================
   Vue 2 Datepicker
   ============================================================================ */

.datepicker .c-pane-container {
  display: block !important;
  border-radius: 5px;
}

/* ============================================================================
   Vue 3 Datepicker
   ============================================================================ */

.datepicker .dp__outer_menu_wrap {
  width: 100%;
}

.datepicker .dp__menu_inner {
  background: #f7f7f7;
}

.datepicker .dp__active_date {
  border-radius: 999px;
  background: #71acd1;
}

.datepicker .dp__today {
  border-radius: 999px;
  border-color: #71acd1;
}

/* ============================================================================
   jQuery UI
   ============================================================================ */

.ui-widget {
  color: #51606d;
  font-family: "Nunito Sans", Arial, Helvetica, sans-serif;
}

.ui-widget.ui-widget-content {
  border: 1px solid #b1b0b5;
}

/* Sortable */

.sortable,
.sortable-div {
  position: relative;
}

.sortable-handle {
  cursor: ns-resize;
}

/* Autocomplete */

.ui-autocomplete {
  z-index: 1100;
  border-radius: 5px;
  max-height: 25rem;
  overflow-y: auto;
  margin-top: 0;
  padding: 1.5rem 0;
}

.ui-autocomplete-category {
  font-weight: bold;
  padding: 0 1.5rem;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.ui-menu-item + .ui-autocomplete-category {
  padding-top: 1.5rem;
}

.ui-autocomplete .ui-menu-item-wrapper {
  padding: 0.75rem 1.5rem;
}

.ui-autocomplete .ui-state-active {
  border: 0;
  background: #f1f0f5;
  margin: 0;
  color: #51606d;
}

.ui-autocomplete-loading {
  i: fa fa-spinner fa-spin fa-3x fa-fw;
}

/* ============================================================================
   Selection
   ============================================================================ */

::-moz-selection {
  background: #234;
  color: #fff;
  text-shadow: none;
}

::selection {
  background: #234;
  color: #fff;
  text-shadow: none;
}

/* ============================================================================
   Animations
   ============================================================================ */

@-webkit-keyframes fade-in {
  0% {
    display: none;
    opacity: 0;
  }

  1% {
    display: block;
    opacity: 0;
  }

  100% {
    display: block;
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    display: none;
    opacity: 0;
  }

  1% {
    display: block;
    opacity: 0;
  }

  100% {
    display: block;
    opacity: 1;
  }
}

/* ============================================================================
   Vue Transitions
   ============================================================================ */

/* Fade */

.transition-fade-enter,
.transition-fade-leave-to {
  opacity: 0;
}

.transition-fade-enter-active,
.transition-fade-leave-active {
  transition: opacity 0.3s;
}

/* Fade Down */

.translate-fade-down-enter-active,
.translate-fade-down-leave-active {
  transition: all 250ms;
  transition-timing-function: cubic-bezier(0.53, 2, 0.36, 0.85);
}

.translate-fade-down-enter,
.translate-fade-down-leave-active {
  opacity: 0;
}

.translate-fade-down-enter,
.translate-fade-down-leave-to {
  position: absolute;
}

.translate-fade-down-enter {
  transform: translateY(-10px);
}

.translate-fade-down-leave-active {
  transform: translateY(10px);
}

/* ============================================================================
   Overflow
   ============================================================================ */

.overflow-hidden {
  overflow: hidden;
}

.overflow-scroll {
  overflow: scroll;
}

@media screen and (min-width: 40em) {
  .overflow-hidden-medium {
    overflow: hidden;
  }

  .overflow-scroll-medium {
    overflow: scroll;
  }
}

@media screen and (min-width: 64em) {
  .overflow-hidden-large {
    overflow: hidden;
  }

  .overflow-scroll-large {
    overflow: scroll;
  }
}

@media screen and (min-width: 100em) {
  .overflow-hidden-xlarge {
    overflow: hidden;
  }

  .overflow-scroll-xlarge {
    overflow: scroll;
  }
}

/* ============================================================================
   Inner
   ============================================================================ */

.inner-xsmall {
  padding: 1rem;
}

.inner-small {
  padding: 1.5rem;
}

.inner {
  padding: 2rem;
}

/* Large */

.inner-large {
  padding: 6%;
}

.inner-top-negative-large {
  margin-top: -6%;
}

.inner-right-negative-large {
  margin-right: -6%;
}

.inner-bottom-negative-large {
  margin-bottom: -6%;
}

.inner-left-negative-large {
  margin-left: -6%;
}

/* Extra Large */

.inner-xlarge {
  padding: 10%;
}

.inner-top-negative-xlarge {
  margin-top: -12%;
}

.inner-right-negative-xlarge {
  margin-right: -12%;
}

.inner-bottom-negative-xlarge {
  margin-bottom: -12%;
}

.inner-left-negative-xlarge {
  margin-left: -12%;
}

@media screen and (min-width: 40em) {
  /* Large */

  .inner-large {
    padding: 3rem;
  }

  .inner-top-negative-large {
    margin-top: -3rem;
  }

  .inner-right-negative-large {
    margin-right: -3rem;
  }

  .inner-bottom-negative-large {
    margin-bottom: -3rem;
  }

  .inner-left-negative-large {
    margin-left: -3rem;
  }

  /* Extra Large */

  .inner-xlarge {
    padding: 4.5rem;
  }

  .inner-top-negative-xlarge {
    margin-top: -4.5rem;
  }

  .inner-right-negative-xlarge {
    margin-right: -4.5rem;
  }

  .inner-bottom-negative-xlarge {
    margin-bottom: -4.5rem;
  }

  .inner-left-negative-xlarge {
    margin-left: -4.5rem;
  }
}

/* Inner Breakpoints
   ============================================================================ */

@media screen and (min-width: 40em) {
  .inner-medium-none {
    padding: 0;
  }

  .inner-medium-size-xsmall {
    padding: 1rem;
  }

  .inner-medium-size-small {
    padding: 1.5rem;
  }

  .inner-medium-size-medium {
    padding: 2rem;
  }

  .inner-medium-size-large {
    padding: 3rem;
  }

  .inner-medium-size-xlarge {
    padding: 4.5rem;
  }
}

@media screen and (min-width: 64em) {
  .inner-large-none {
    padding: 0;
  }

  .inner-large-size-xsmall {
    padding: 1rem;
  }

  .inner-large-size-small {
    padding: 1.5rem;
  }

  .inner-large-size-medium {
    padding: 2rem;
  }

  .inner-large-size-large {
    padding: 3rem;
  }

  .inner-large-size-xlarge {
    padding: 4.5rem;
  }
}

@media screen and (min-width: 100em) {
  .inner-xlarge-none {
    padding: 0;
  }

  .inner-xlarge-size-xsmall {
    padding: 1rem;
  }

  .inner-xlarge-size-small {
    padding: 1.5rem;
  }

  .inner-xlarge-size-medium {
    padding: 2rem;
  }

  .inner-xlarge-size-large {
    padding: 3rem;
  }

  .inner-xlarge-size-xlarge {
    padding: 4.5rem;
  }
}

/* ============================================================================
   Ribbon
   ============================================================================ */

.ribbon-medium {
  margin: 0 -1.875rem;
  padding: 0 1.875rem;
}

.ribbon-large {
  margin: 0 -4.875rem;
  padding: 0 4.875rem;
}

.ribbon-large-top {
  margin-top: -4.875rem;
  padding-top: 4.875rem;
}

.ribbon-xlarge {
  margin: 0 -33% 1.5rem;
  padding: 1.5rem 33%;
}

@media screen and (min-width: 40em) {
  .ribbon-xlarge {
    margin-left: -9.75rem;
    margin-right: -9.75rem;
    padding-left: 9.75rem;
    padding-right: 9.75rem;
  }
}

/* ============================================================================
   Loaders
   ============================================================================ */

/* Ajax loader
   ============================================================================ */

.ajax-loader {
  display: none;
  background-color: rgba(255, 255, 255, 0.7);
  position: absolute;
  z-index: 100 !important;
  width: 100%;
  height: 100%;
}

.ajax-loader img {
  position: relative;
  top: 20%;
  left: 37%;
}

/* CSS loader
   ============================================================================ */

.loader-overlay {
  background-color: rgba(255, 255, 255, 0.7);
  position: fixed;
  z-index: 1010;
  width: 100%;
  height: 100%;
  cursor: wait;
}

.loader-overlay .loader {
  position: relative;
  top: 50%;
}

.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
}

.loader {
  background: #fff;
  color: #b1b0b5;
  font-size: 4px;
  text-indent: -99999em;
  margin: 0 auto;
  position: relative;
  width: 10em;
  height: 10em;
  box-shadow:
    inset 0 0 0 1em,
    0 0 0 4em #fff;
  transform: translateZ(0);
}

.loader:before,
.loader:after {
  position: absolute;
  content: "";
}

.loader:before {
  width: 5.2em;
  height: 10.2em;
  background: #fff;
  border-radius: 10.2em 0 0 10.2em;
  top: -0.1em;
  left: -0.1em;
  box-shadow: 0 0 0 2px #fff;
  /* Fix fuzziness */
  transform-origin: 5.2em 5.1em;
  -webkit-animation: load 1.5s infinite ease 1.2s;
  animation: load 1.5s infinite ease 1.2s;
}

.loader:after {
  width: 5.2em;
  height: 10.2em;
  background: #fff;
  border-radius: 0 10.2em 10.2em 0;
  top: -0.1em;
  left: 5.1em;
  transform-origin: 0 5.1em;
  -webkit-animation: load 1.5s infinite ease;
  animation: load 1.5s infinite ease;
}

@-webkit-keyframes load {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes load {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* ============================================================================
   Stepper (Deprecated)
   ============================================================================ */

.progress-container {
  padding: 1rem 0;
  position: relative;
}

.progress {
  background-color: #e6e6f0;
  height: 10px;
  border: none;
  padding: 0;
  margin-bottom: 0.625rem;
}

.bs-wizard {
  border-bottom: 0;
  display: flex;
  justify-content: center;
}

.bs-wizard-step {
  padding: 0;
  position: relative;
  width: 100%;
}

.bs-wizard-step .progress {
  background: #f1f0f5;
  position: relative;
  border-radius: 0;
  height: 0.5rem;
  margin: 0;
  box-shadow: none;
}

.bs-wizard-step .progress-bar {
  width: 0;
  box-shadow: none;
  background: #71acd1;
  height: 100%;
}

.bs-wizard-step:first-child .progress {
  left: 50%;
  width: 50%;
}

.bs-wizard-step:last-child .progress {
  width: 50%;
}

.bs-wizard-step.step_active .progress-bar {
  width: 50%;
}

.bs-wizard-step:first-child.step_active .progress-bar {
  width: 0%;
}

.bs-wizard-step:last-child.step_active .progress-bar {
  width: 100%;
}

.bs-wizard-step.step_complete .progress-bar {
  width: 100%;
}

.bs-wizard-dot {
  background: #f1f0f5;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.bs-wizard-step.step_complete .bs-wizard-dot,
.bs-wizard-step.step_active .bs-wizard-dot {
  background: #71acd1;
}

.bs-wizard-step.step_disabled .bs-wizard-dot {
  background-color: #f1f0f5;
}

/* ============================================================================
   Fluid Video 16:9
   ============================================================================ */

.video-wrap {
  overflow: hidden;
  height: 0;
  margin-bottom: 1.5rem;
  /*padding-top: 30px; /* Toolbar height (currently unnecessary) */
  padding-bottom: 56.25%;
  position: relative;
}

.video-wrap iframe,
.video-wrap object,
.video-wrap embed {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* ============================================================================
   Utilities
   ============================================================================ */

/* Border
   ============================================================================ */

.border-none {
  border: none !important;
}

.border {
  border: 1px solid #f1f0f5 !important;
}

.border-dashed {
  border-style: dashed;
}

/* Border Top */

.border-top-none {
  border-top: none !important;
}

.border-top {
  border-top: 1px solid #f1f0f5 !important;
}

/* Border Right */

.border-right-none {
  border-right: none !important;
}

.border-right {
  border-right: 1px solid #f1f0f5 !important;
}

/* Border Bottom */

.border-bottom-none {
  border-bottom: none !important;
}

.border-bottom {
  border-bottom: 1px solid #f1f0f5 !important;
}

.border-bottom-dark {
  border-bottom: 1px solid #d8d8da !important;
}

/* Border Left */

.border-left-none {
  border-left: none !important;
}

.border-left {
  border-left: 1px solid #f1f0f5 !important;
}

/* Border Sizes
   ============================================================================ */

.border-large {
  border-width: 2px !important;
}

.border-xlarge {
  border-width: 3px !important;
}

.border-xxlarge {
  border-width: 5px !important;
}

/* Border Colors
   ============================================================================ */

.border-transparent {
  border-color: transparent;
}

.border-color1 {
  border-color: #71acd1 !important;
}

.border-color2 {
  border-color: #ff4649 !important;
}

.border-color3 {
  border-color: #76bb76 !important;
}

.border-color4 {
  border-color: #51606d !important;
}

.border-light,
.border-color5 {
  border-color: #b1b0b5 !important;
}

.border-color6 {
  border-color: #edb15f !important;
}

.border-white,
.border-color7 {
  border-color: #fff !important;
}

.border-color8 {
  border-color: #f1f0f5 !important;
}

.border-color9 {
  border-color: #234 !important;
}

.border-color10 {
  border-color: #f9f9f9 !important;
}

.border-color11 {
  border-color: #ca82e6 !important;
}

.border-color12 {
  border-color: #e3e2e6 !important;
}

.border-color13 {
  border-color: #f9ff50 !important;
}

.border-black,
.border-color14 {
  border-color: #202020 !important;
}

.border-color15 {
  border-color: #2d2d2d !important;
}

.border-color16 {
  border-color: #484848 !important;
}

.border-dark,
.border-color17 {
  border-color: gray !important;
}

.border-color18 {
  border-color: #ff74af !important;
}

/* Background Colors
   ============================================================================ */

.bg-none,
.bg-color-none {
  background: none !important;
}

.bg-info,
.bg-color1 {
  background-color: #71acd1 !important;
}

.bg-error,
.bg-color2 {
  background-color: #ff4649 !important;
}

.bg-success,
.bg-color3 {
  background-color: #76bb76 !important;
}

.bg-warning,
.bg-color4 {
  background-color: #51606d !important;
}

.bg-light,
.bg-color5 {
  background-color: #b1b0b5 !important;
}

.bg-color6 {
  background-color: #edb15f !important;
}

.bg-white,
.bg-color7 {
  background-color: #fff !important;
}

.bg-color8 {
  background-color: #f1f0f5 !important;
}

.bg-color9 {
  background-color: #234 !important;
}

.bg-color10 {
  background-color: #f9f9f9 !important;
}

.bg-color11 {
  background-color: #ca82e6 !important;
}

.bg-color12 {
  background-color: #e3e2e6 !important;
}

.bg-color13 {
  background-color: #f9ff50 !important;
}

.bg-black,
.bg-color14 {
  background-color: #202020 !important;
}

.bg-color15 {
  background-color: #2d2d2d !important;
}

.bg-color16 {
  background-color: #484848 !important;
}

.bg-dark,
.bg-color17 {
  background-color: gray !important;
}

.bg-color18 {
  background-color: #ff74af !important;
}

/* Gradients
   ============================================================================ */

.bg-gradient8 {
  background-color: #f6f6fc !important;
  /* FF3.6-15 */
  /* Chrome10-25,Safari5.1-6 */
  background-image: linear-gradient(
    -45deg,
    #f6f7fe 0%,
    #eaeaf1 100%
  ) !important;
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.bg-gradient9 {
  background-color: #234 !important;
  /* FF3.6-15 */
  /* Chrome10-25,Safari5.1-6 */
  background-image: linear-gradient(
    -45deg,
    #415263 0%,
    #1c2d3e 100%
  ) !important;
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

/* Alignment
   ============================================================================ */

.alignleft {
  float: left;
  margin: 0 1.5rem 1.5rem 0;
}

.alignright {
  float: right;
  margin: 0 0 1.5rem 1.5rem;
}

.aligncenter {
  display: block;
  margin: 1.5rem auto;
}

.aligncenter:first-child {
  margin-top: 0;
}

.margin-left-auto {
  margin-left: auto;
}

.margin-right-auto {
  margin-right: auto;
}

.float-none {
  float: none !important;
}

@media screen and (min-width: 40em) {
  .float-none-medium {
    float: none !important;
  }

  .float-left-medium {
    float: left !important;
  }

  .float-right-medium {
    float: right !important;
  }
}

@media screen and (min-width: 64em) {
  .float-none-large {
    float: none !important;
  }

  .float-left-large {
    float: left !important;
  }

  .float-right-large {
    float: right !important;
  }
}

@media screen and (min-width: 100em) {
  .float-none-xlarge {
    float: none !important;
  }

  .float-left-xlarge {
    float: left !important;
  }

  .float-right-xlarge {
    float: right !important;
  }
}

/* Margin
   ============================================================================ */

.margin-xxsmall {
  margin: 0.125rem !important;
}

.margin-xsmall {
  margin: 0.25rem !important;
}

.margin-small {
  margin: 0.75rem !important;
}

.margin-small-medium {
  margin: 0.9375rem !important;
}

.margin {
  margin: 1.5rem !important;
}

.margin-medium-large {
  margin: 1.875rem !important;
}

.margin-large {
  margin: 2.25rem !important;
}

.margin-xlarge {
  margin: 3rem !important;
}

.margin-xxlarge {
  margin: 4.5rem !important;
}

.margin-none {
  margin: 0 !important;
}

@media screen and (min-width: 40em) {
  .margin-medium-size-xxsmall {
    margin: 0.125rem !important;
  }

  .margin-medium-size-xsmall {
    margin: 0.25rem !important;
  }

  .margin-medium-size-small {
    margin: 0.75rem !important;
  }

  .margin-medium-size-small-medium {
    margin: 0.9375rem !important;
  }

  .margin-medium-size-medium {
    margin: 1.5rem !important;
  }

  .margin-medium-size-medium-large {
    margin: 1.875rem !important;
  }

  .margin-medium-size-large {
    margin: 2.25rem !important;
  }

  .margin-medium-size-xlarge {
    margin: 3rem !important;
  }

  .margin-medium-size-xxlarge {
    margin: 4.5rem !important;
  }

  .margin-medium-none {
    margin: 0 !important;
  }
}

@media screen and (min-width: 64em) {
  .margin-large-size-xxsmall {
    margin: 0.125rem !important;
  }

  .margin-large-size-xsmall {
    margin: 0.25rem !important;
  }

  .margin-large-size-small {
    margin: 0.75rem !important;
  }

  .margin-large-size-small-medium {
    margin: 0.9375rem !important;
  }

  .margin-large-size-medium {
    margin: 1.5rem !important;
  }

  .margin-large-size-medium-large {
    margin: 1.875rem !important;
  }

  .margin-large-size-large {
    margin: 2.25rem !important;
  }

  .margin-large-size-xlarge {
    margin: 3rem !important;
  }

  .margin-large-size-xxlarge {
    margin: 4.5rem !important;
  }

  .margin-large-none {
    margin: 0 !important;
  }
}

@media screen and (min-width: 100em) {
  .margin-xlarge-size-xxsmall {
    margin: 0.125rem !important;
  }

  .margin-xlarge-size-xsmall {
    margin: 0.25rem !important;
  }

  .margin-xlarge-size-small {
    margin: 0.75rem !important;
  }

  .margin-xlarge-size-small-medium {
    margin: 0.9375rem !important;
  }

  .margin-xlarge-size-medium {
    margin: 1.5rem !important;
  }

  .margin-xlarge-size-medium-large {
    margin: 1.875rem !important;
  }

  .margin-xlarge-size-large {
    margin: 2.25rem !important;
  }

  .margin-xlarge-size-xlarge {
    margin: 3rem !important;
  }

  .margin-xlarge-size-xxlarge {
    margin: 4.5rem !important;
  }

  .margin-xlarge-none {
    margin: 0 !important;
  }
}

/* Margin Top */

.margin-top-xxsmall {
  margin-top: 0.125rem !important;
}

.margin-top-xsmall {
  margin-top: 0.25rem !important;
}

.margin-top-small {
  margin-top: 0.75rem !important;
}

.margin-top-small-medium {
  margin-top: 0.9375rem !important;
}

.margin-top {
  margin-top: 1.5rem !important;
}

.margin-top-medium-large {
  margin-top: 1.875rem !important;
}

.margin-top-large {
  margin-top: 2.25rem !important;
}

.margin-top-xlarge {
  margin-top: 3rem !important;
}

.margin-top-xxlarge {
  margin-top: 4.5rem !important;
}

.margin-top-none {
  margin-top: 0 !important;
}

/* Margin Right */

.margin-right-xxsmall {
  margin-right: 0.125rem !important;
}

.margin-right-xsmall {
  margin-right: 0.25rem !important;
}

.margin-right-small {
  margin-right: 0.75rem !important;
}

.margin-right-small-medium {
  margin-right: 0.9375rem !important;
}

.margin-right {
  margin-right: 1.5rem !important;
}

.margin-right-medium-large {
  margin-right: 1.875rem !important;
}

.margin-right-large {
  margin-right: 2.25rem !important;
}

.margin-right-xlarge {
  margin-right: 3rem !important;
}

.margin-right-xxlarge {
  margin-right: 4.5rem !important;
}

.margin-right-none {
  margin-right: 0 !important;
}

/* Margin Bottom */

.margin-bottom-xxsmall {
  margin-bottom: 0.125rem !important;
}

.margin-bottom-xsmall {
  margin-bottom: 0.25rem !important;
}

.margin-bottom-small {
  margin-bottom: 0.75rem !important;
}

.margin-bottom-small-medium {
  margin-bottom: 0.9375rem !important;
}

.margin-bottom {
  margin-bottom: 1.5rem !important;
}

.margin-bottom-medium-large {
  margin-bottom: 1.875rem !important;
}

.margin-bottom-large {
  margin-bottom: 2.25rem !important;
}

.margin-bottom-xlarge {
  margin-bottom: 3rem !important;
}

.margin-bottom-xxlarge {
  margin-bottom: 4.5rem !important;
}

.margin-bottom-none {
  margin-bottom: 0 !important;
}

/* Margin Left */

.margin-left-xxsmall {
  margin-left: 0.125rem !important;
}

.margin-left-xsmall {
  margin-left: 0.25rem !important;
}

.margin-left-small {
  margin-left: 0.75rem !important;
}

.margin-left-small-medium {
  margin-left: 0.9375rem !important;
}

.margin-left {
  margin-left: 1.5rem !important;
}

.margin-left-medium-large {
  margin-left: 1.875rem !important;
}

.margin-left-large {
  margin-left: 2.25rem !important;
}

.margin-left-xlarge {
  margin-left: 3rem !important;
}

.margin-left-xxlarge {
  margin-left: 4.5rem !important;
}

.margin-left-none {
  margin-left: 0 !important;
}

/* Margin Top Negative */

.margin-top-negative-xxsmall {
  margin-top: -0.125rem !important;
}

.margin-top-negative-xsmall {
  margin-top: -0.25rem !important;
}

.margin-top-negative-small {
  margin-top: -0.75rem !important;
}

.margin-top-negative-small-medium {
  margin-top: -0.9375rem !important;
}

.margin-top-negative {
  margin-top: -1.5rem !important;
}

.margin-top-negative-medium-large {
  margin-top: -1.875rem !important;
}

.margin-top-negative-large {
  margin-top: -2.25rem !important;
}

.margin-top-negative-xlarge {
  margin-top: -3rem !important;
}

.margin-top-negative-xxlarge {
  margin-top: -4.5rem !important;
}

/* Margin Right Negative */

.margin-right-negative-xxsmall {
  margin-right: -0.125rem !important;
}

.margin-right-negative-xsmall {
  margin-right: -0.25rem !important;
}

.margin-right-negative-small {
  margin-right: -0.75rem !important;
}

.margin-right-negative-small-medium {
  margin-right: -0.9375rem !important;
}

.margin-right-negative {
  margin-right: -1.5rem !important;
}

.margin-right-negative-medium-large {
  margin-right: -1.875rem !important;
}

.margin-right-negative-large {
  margin-right: -2.25rem !important;
}

.margin-right-negative-xlarge {
  margin-right: -3rem !important;
}

.margin-right-negative-xxlarge {
  margin-right: -4.5rem !important;
}

/* Margin Bottom Negative */

.margin-bottom-negative-xxsmall {
  margin-bottom: -0.125rem !important;
}

.margin-bottom-negative-xsmall {
  margin-bottom: -0.25rem !important;
}

.margin-bottom-negative-small {
  margin-bottom: -0.75rem !important;
}

.margin-bottom-negative-small-medium {
  margin-bottom: -0.9375rem !important;
}

.margin-bottom-negative {
  margin-bottom: -1.5rem !important;
}

.margin-bottom-negative-medium-large {
  margin-bottom: -1.875rem !important;
}

.margin-bottom-negative-large {
  margin-bottom: -2.25rem !important;
}

.margin-bottom-negative-xlarge {
  margin-bottom: -3rem !important;
}

.margin-bottom-negative-xxlarge {
  margin-bottom: -4.5rem !important;
}

/* Margin Left Negative */

.margin-left-negative-xxsmall {
  margin-left: -0.125rem !important;
}

.margin-left-negative-xsmall {
  margin-left: -0.25rem !important;
}

.margin-left-negative-small {
  margin-left: -0.75rem !important;
}

.margin-left-negative-small-medium {
  margin-left: -0.9375rem !important;
}

.margin-left-negative {
  margin-left: -1.5rem !important;
}

.margin-left-negative-medium-large {
  margin-left: -1.875rem !important;
}

.margin-left-negative-large {
  margin-left: -2.25rem !important;
}

.margin-left-negative-xlarge {
  margin-left: -3rem !important;
}

.margin-left-negative-xxlarge {
  margin-left: -4.5rem !important;
}

/* Padding
   ============================================================================ */

.padding-xxsmall {
  padding: 0.125rem !important;
}

.padding-xsmall {
  padding: 0.25rem !important;
}

.padding-small {
  padding: 0.75rem !important;
}

.padding-small-medium {
  padding: 0.9375rem !important;
}

.padding {
  padding: 1.5rem !important;
}

.padding-medium-large {
  padding: 1.875rem !important;
}

.padding-large {
  padding: 2.25rem !important;
}

.padding-xlarge {
  padding: 3rem !important;
}

.padding-xxlarge {
  padding: 4.5rem !important;
}

.padding-xxxlarge {
  padding: 5.5rem !important;
}

.padding-none {
  padding: 0 !important;
}

@media screen and (min-width: 40em) {
  .padding-medium-size-xxsmall {
    padding: 0.125rem !important;
  }

  .padding-medium-size-xsmall {
    padding: 0.25rem !important;
  }

  .padding-medium-size-small {
    padding: 0.75rem !important;
  }

  .padding-medium-size-small-medium {
    padding: 0.9375rem !important;
  }

  .padding-medium-size-medium {
    padding: 1.5rem !important;
  }

  .padding-medium-size-medium-large {
    padding: 1.875rem !important;
  }

  .padding-medium-size-large {
    padding: 2.25rem !important;
  }

  .padding-medium-size-xlarge {
    padding: 3rem !important;
  }

  .padding-medium-size-xxlarge {
    padding: 4.5rem !important;
  }

  .padding-medium-size-xxxlarge {
    padding: 5.5rem !important;
  }

  .padding-medium-none {
    padding: 0 !important;
  }
}

@media screen and (min-width: 64em) {
  .padding-large-size-xxsmall {
    padding: 0.125rem !important;
  }

  .padding-large-size-xsmall {
    padding: 0.25rem !important;
  }

  .padding-large-size-small {
    padding: 0.75rem !important;
  }

  .padding-large-size-small-medium {
    padding: 0.9375rem !important;
  }

  .padding-large-size-medium {
    padding: 1.5rem !important;
  }

  .padding-large-size-medium-large {
    padding: 1.875rem !important;
  }

  .padding-large-size-large {
    padding: 2.25rem !important;
  }

  .padding-large-size-xlarge {
    padding: 3rem !important;
  }

  .padding-large-size-xxlarge {
    padding: 4.5rem !important;
  }

  .padding-large-size-xxxlarge {
    padding: 5.5rem !important;
  }

  .padding-large-none {
    padding: 0 !important;
  }
}

@media screen and (min-width: 100em) {
  .padding-xlarge-size-xxsmall {
    padding: 0.125rem !important;
  }

  .padding-xlarge-size-xsmall {
    padding: 0.25rem !important;
  }

  .padding-xlarge-size-small {
    padding: 0.75rem !important;
  }

  .padding-xlarge-size-small-medium {
    padding: 0.9375rem !important;
  }

  .padding-xlarge-size-medium {
    padding: 1.5rem !important;
  }

  .padding-xlarge-size-medium-large {
    padding: 1.875rem !important;
  }

  .padding-xlarge-size-large {
    padding: 2.25rem !important;
  }

  .padding-xlarge-size-xlarge {
    padding: 3rem !important;
  }

  .padding-xlarge-size-xxlarge {
    padding: 4.5rem !important;
  }

  .padding-xlarge-size-xxxlarge {
    padding: 5.5rem !important;
  }

  .padding-xlarge-none {
    padding: 0 !important;
  }
}

/* Padding Top */

.padding-top-xxsmall {
  padding-top: 0.125rem !important;
}

.padding-top-xsmall {
  padding-top: 0.25rem !important;
}

.padding-top-small {
  padding-top: 0.75rem !important;
}

.padding-top-small-medium {
  padding-top: 0.9375rem !important;
}

.padding-top {
  padding-top: 1.5rem !important;
}

.padding-top-medium-large {
  padding-top: 1.875rem !important;
}

.padding-top-large {
  padding-top: 2.25rem !important;
}

.padding-top-xlarge {
  padding-top: 3rem !important;
}

.padding-top-xxlarge {
  padding-top: 4.5rem !important;
}

.padding-top-xxxlarge {
  padding-top: 5.5rem !important;
}

.padding-top-none {
  padding-top: 0 !important;
}

/* Padding Right */

.padding-right-xxsmall {
  padding-right: 0.125rem !important;
}

.padding-right-xsmall {
  padding-right: 0.25rem !important;
}

.padding-right-small {
  padding-right: 0.75rem !important;
}

.padding-right-small-medium {
  padding-right: 0.9375rem !important;
}

.padding-right {
  padding-right: 1.5rem !important;
}

.padding-right-medium-large {
  padding-right: 1.875rem !important;
}

.padding-right-large {
  padding-right: 2.25rem !important;
}

.padding-right-xlarge {
  padding-right: 3rem !important;
}

.padding-right-xxlarge {
  padding-right: 4.5rem !important;
}

.padding-right-xxxlarge {
  padding-right: 5.5rem !important;
}

.padding-right-none {
  padding-right: 0 !important;
}

/* Padding Bottom */

.padding-bottom-xxsmall {
  padding-bottom: 0.125rem !important;
}

.padding-bottom-xsmall {
  padding-bottom: 0.25rem !important;
}

.padding-bottom-small {
  padding-bottom: 0.75rem !important;
}

.padding-bottom-small-medium {
  padding-bottom: 0.9375rem !important;
}

.padding-bottom {
  padding-bottom: 1.5rem !important;
}

.padding-bottom-medium-large {
  padding-bottom: 1.875rem !important;
}

.padding-bottom-large {
  padding-bottom: 2.25rem !important;
}

.padding-bottom-xlarge {
  padding-bottom: 3rem !important;
}

.padding-bottom-xxlarge {
  padding-bottom: 4.5rem !important;
}

.padding-bottom-xxxlarge {
  padding-bottom: 5.5rem !important;
}

.padding-bottom-none {
  padding-bottom: 0 !important;
}

/* Padding Left */

.padding-left-xxsmall {
  padding-left: 0.125rem !important;
}

.padding-left-xsmall {
  padding-left: 0.25rem !important;
}

.padding-left-small {
  padding-left: 0.75rem !important;
}

.padding-left-small-medium {
  padding-left: 0.9375rem !important;
}

.padding-left {
  padding-left: 1.5rem !important;
}

.padding-left-medium-large {
  padding-left: 1.875rem !important;
}

.padding-left-large {
  padding-left: 2.25rem !important;
}

.padding-left-xlarge {
  padding-left: 3rem !important;
}

.padding-left-xxlarge {
  padding-left: 4.5rem !important;
}

.padding-left-xxxlarge {
  padding-left: 5.5rem !important;
}

.padding-left-none {
  padding-left: 0 !important;
}

/* Balance padding
   ============================================================================ */

.balance-padding > *:last-child,
.balance-padding > ul:last-child > li:last-child {
  margin-bottom: 0;
}

/* Flexbox
   ============================================================================ */

.display-flex {
  display: flex !important;
}

@media screen and (min-width: 40em) {
  .display-flex-medium {
    display: flex !important;
  }
}

@media screen and (min-width: 64em) {
  .display-flex-large {
    display: flex !important;
  }
}

@media screen and (min-width: 100em) {
  .display-flex-xlarge {
    display: flex !important;
  }
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-grow {
  flex-grow: 1;
}

.align-items-center {
  align-items: center;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-end {
  align-items: flex-end;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-end {
  justify-content: flex-end;
}

.justify-content-space-between {
  justify-content: space-between;
}

.justify-content-space-around {
  justify-content: space-around;
}

/* Opacity
   ============================================================================ */

.opacity-xxxlight {
  opacity: 0.12;
}

.opacity-xxlight {
  opacity: 0.18;
}

.opacity-xlight {
  opacity: 0.25;
}

.opacity-light {
  opacity: 0.37;
}

.opacity-medium {
  opacity: 0.5;
}

.opacity-dark {
  opacity: 0.62;
}

.opacity-xdark {
  opacity: 0.75;
}

/* Background
   ============================================================================ */

.bg-no-repeat {
  background-repeat: no-repeat;
}

.bg-cover {
  background-size: cover;
}

.bg-top-left {
  background-position: top left;
}

.bg-top-right {
  background-position: top right;
}

/* Border Radius
   ============================================================================ */

.radius {
  border-radius: 5px;
}

.radius-large {
  border-radius: 22px !important;
}

.radius-full {
  border-radius: 999px !important;
}

.radius-none {
  border-radius: 0 !important;
}

.radius-top-left {
  border-top-left-radius: 5px !important;
}

.radius-top-right {
  border-top-right-radius: 5px !important;
}

.radius-bottom-right {
  border-bottom-right-radius: 5px !important;
}

.radius-bottom-left {
  border-bottom-left-radius: 5px !important;
}

.radius-top {
  border-top-left-radius: 5px !important;
  border-top-right-radius: 5px !important;
}

.radius-bottom {
  border-bottom-right-radius: 5px !important;
  border-bottom-left-radius: 5px !important;
}

.radius-left {
  border-top-left-radius: 5px !important;
  border-bottom-left-radius: 5px !important;
}

.radius-right {
  border-top-right-radius: 5px !important;
  border-bottom-right-radius: 5px !important;
}

/* Shadow
   ============================================================================ */

.shadow-small {
  box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.25);
}

.shadow {
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.12);
}

.shadow-large {
  box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.12);
}

.shadow-none {
  box-shadow: none;
}

/* Max Width
   ============================================================================ */

.max-width-xsmall {
  max-width: 3.125rem;
  /* 50px */
}

.max-width-small {
  max-width: 6.25rem;
  /* 100px */
}

.max-width,
.max-width-medium {
  max-width: 12.5rem;
  /* 200px */
}

.max-width-large {
  max-width: 21.25rem;
  /* 340px */
}

.max-width-xlarge {
  max-width: 26.25rem;
  /* 420px */
}

.max-width-xxlarge {
  max-width: 33.75rem;
  /* 540px */
}

/* Other
   ============================================================================ */

.white-space-nowrap {
  white-space: nowrap;
}

.white-space-pre-line {
  white-space: pre-line;
}

.white-space-pre-wrap {
  white-space: pre-wrap;
}

.wrap {
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.truncate {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}

.full-height,
.height-full {
  min-height: 100vh;
}

.height-100 {
  height: 100%;
}

.max-height {
  max-height: 100%;
}

.height-auto {
  height: auto !important;
}

.width-auto {
  width: auto !important;
}

.display-block {
  display: block;
}

.display-inline-block {
  display: inline-block !important;
}

.clear-both {
  clear: both;
}

.z-index,
.z-index-1 {
  z-index: 1;
}

.z-index-0 {
  z-index: 0;
}

.z-index-negative-1 {
  z-index: -1;
}

.cursor-grab {
  cursor: -webkit-grab !important;
  cursor: grab !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

/* ============================================================================
   Top Bar
   ============================================================================ */

.top-bar {
  height: 4.5rem;
  padding: 0.9375rem;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  transition: all 0.3s ease 0s;
  z-index: 9;
  text-align: center;
  box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.25);
}

.top-bar input {
  max-width: none;
  margin-right: 0;
}

/* Menu */

.top-bar.bg-color9 .menu a {
  color: #b1b0b5;
}

.top-bar.bg-color9 .menu a:hover,
.top-bar.bg-color9 .menu a:focus {
  color: #fff;
}

.top-bar.bg-color9 .current a {
  color: #fff;
  font-weight: 700;
}

/* Top Bar Styles
   ============================================================================ */

/* Style 2 */

.top-bar-style-2 {
  padding: 0 1.5rem;
}

/* Top Bar Formats
   ============================================================================ */

/* Top 2 */

.top-bar-top-2 {
  top: 4.5rem;
  z-index: 8;
}

/* Menu button
   ============================================================================ */

.top-bar-menu-button {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  left: 1.5rem;
  padding: 0.625rem;
  color: #51606d;
}

.top-bar-menu-button .icon:before {
  margin: 0;
  font-size: 32px;
  opacity: 1;
}

/* Logo
   ============================================================================ */

.top-bar-logo img {
  height: 42px;
  padding: 0.5rem 0;
  margin: 0 auto;
}

@media screen and (min-width: 22.5em) {
  .top-bar-logo img {
    padding: 0.4rem 0;
  }
}

@media screen and (min-width: 40em) {
  .top-bar-logo {
    margin-left: 4.375rem;
    margin-right: 3rem;
  }
}

@media screen and (min-width: 64em) {
  .top-bar {
    text-align: left;
  }

  .top-bar-logo {
    margin-left: 0;
  }

  .top-bar-logo img {
    padding: 0.25rem;
  }
}

/* Add button
   ============================================================================ */

.nav-add {
  position: fixed;
  bottom: 3.5rem;
  right: 0;
}

@media screen and (min-width: 64em) {
  .nav-add {
    float: left;
    position: static;
  }
}

/* Top Bar Nav 1
   ============================================================================ */

.top-bar-nav-1 {
  float: left;
}

.top-bar-nav-1 .menu > li {
  font-size: 0.875rem;
}

.top-bar-nav-1-link {
  border-radius: 5px;
  display: block;
  line-height: 1;
  padding: 0.875rem 1rem;
  margin-right: 0.3125rem;
  color: #51606d;
  transition: all 0.3s ease 0s;
}

.top-bar-nav-1-link:hover,
.top-bar-nav-1-link:focus {
  background: #f7f7f7;
}

.top-bar-nav-1 .menu > li.current .top-bar-nav-1-link {
  font-weight: bold;
  position: relative;
}

.top-bar-nav-1 .menu > li.current .top-bar-nav-1-link::after {
  background: #ff4649;
  content: "";
  height: 0.3125rem;
  width: 100%;
  position: absolute;
  bottom: -0.9375rem;
  left: 0;
}

/* Top Bar Nav 2
   ============================================================================ */

.top-bar-nav-2 > ul {
  background: #234;
  display: table;
  width: 100%;
  height: 3rem;
  position: fixed;
  bottom: 0;
  left: 0;
}

a.top-bar-nav-2-link,
a.top-bar-nav-2-link:hover,
a.top-bar-nav-2-link:focus {
  color: #f1f0f5;
  padding: 0.875rem 1rem;
  display: block;
  line-height: 1;
}

a.top-bar-nav-2-link:hover,
a.top-bar-nav-2-link:focus {
  background: rgba(255, 255, 255, 0.12);
}

a.top-bar-nav-2-link .icon:before,
a.top-bar-nav-2-link .icon:before {
  opacity: 0.62;
  margin-right: 0;
}

a.top-bar-nav-2-link:hover .icon:before,
a.top-bar-nav-2-link:focus .icon:before {
  opacity: 1;
}

@media screen and (min-width: 64em) {
  .top-bar-nav-2 {
    float: left;
  }

  .top-bar-nav-2 > ul {
    background: none;
    display: block;
    width: auto;
    height: auto;
    position: static;
  }

  a.top-bar-nav-2-link {
    border-radius: 5px;
    margin-left: 0.3125rem;
    color: #51606d;
    transition: all 0.3s ease 0s;
  }

  a.top-bar-nav-2-link:hover,
  a.top-bar-nav-2-link:focus {
    background: #f7f7f7;
    color: #51606d;
  }

  a.top-bar-nav-2-link .icon:before,
  a.top-bar-nav-2-link .icon:before {
    opacity: 1;
  }
}

/* Account Nav
   ============================================================================ */

.nav-account {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: 1.5rem;
  display: none;
}

.nav-account > .menu > li > div > a {
  color: #51606d;
  padding: 0.914rem 1rem;
  display: block;
  line-height: 1;
}

.nav-account > .menu > li > div > a:hover,
.nav-account > .menu > li > div > a:focus {
  color: #39434c;
}

.nav-account > .menu > li > div > a .icon:before,
.nav-account > .menu > li > div > a .icon:before {
  opacity: 1;
  margin-right: 0;
}

@media screen and (min-width: 64em) {
  .nav-account {
    position: static;
    transform: none;
    float: left;
  }

  .nav-account > .menu > li > div > a {
    border-radius: 5px;
    margin-left: 0.3215rem;
    transition: all 0.3s ease 0s;
  }
}

/* ============================================================================
   Menu Bar
   ============================================================================ */

.menu-bar {
  position: fixed;
  top: 4.5rem;
  left: 0;
  height: 100%;
  width: 4.5rem;
  z-index: 1;
}

.has-top-bar-2 .menu-bar {
  top: 9rem;
}

.menu-bar-link {
  text-align: center;
}

.menu-bar-link:hover,
.menu-bar-link:focus {
  background-color: #272727;
}

/* Icon */

.menu-bar-link .icon::before {
  color: #a3a2a6;
}

.menu-bar-link:hover .icon::before,
.menu-bar-link:focus .icon::before {
  color: #b1b0b5;
}

/* Current */

.current .menu-bar-link {
  background-color: #2d2d2d;
}

.current .menu-bar-link .icon::before {
  color: #fff;
}

/* ============================================================================
   Sidebar
   ============================================================================ */

.sidebar.bg-color9 a {
  color: #71acd1;
}

.sidebar.bg-color9 a:hover,
.sidebar.bg-color9 a:focus {
  color: #a6cbe2;
}

/* Sidebar Profile
   ============================================================================ */

.sidebar-profile {
  padding: 1.875rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}

.sidebar-profile-heading {
  padding: 0.75rem 0 0.375rem;
  color: #f1f0f5;
  font-size: 1rem;
  font-weight: 700;
}

.sidebar-profile-subhead {
  padding: 0.75rem 0 0.375rem;
  color: #7a858f;
  font-size: 0.875rem;
  font-weight: 700;
}

.sidebar-profile-heading + .sidebar-profile-subhead {
  padding-top: 0;
}

/* Sidebar Nav
   ============================================================================ */

.sidebar-nav {
  padding-bottom: 9rem;
}

.sidebar-nav .menu > li {
  font-size: 0.875rem;
  position: relative;
}

.sidebar-nav .menu > li > a {
  display: block;
  padding: 1.7em 1.9em 1.7em 4.4em;
  color: #b1b0b5;
  transition: all 0.3s ease 0s;
}

.bg-color8 .sidebar-nav .menu > li > a {
  color: #7d8892;
}

.bg-color9 .sidebar-nav-style-2 .menu {
  background: #1f2e3d;
  border-bottom: #111a22 1px solid;
}

.bg-color9 .sidebar-nav .menu > li > a {
  color: #b1b0b5;
}

.sidebar-nav .menu > li > a:hover,
.sidebar-nav .menu > li > a:focus {
  padding-left: 5em;
}

.bg-color8 .sidebar-nav .menu > li > a:hover,
.bg-color8 .sidebar-nav .menu > li > a:focus {
  background: #d9d8dd;
  color: #51606d;
}

.bg-color9 .sidebar-nav .menu > li > a:hover,
.bg-color9 .sidebar-nav .menu > li > a:focus {
  background: #1a2633;
  color: #fff;
}

/* Parent */

.bg-color9 .sidebar-nav .sidebar-parent {
  border-bottom: #111a22 1px solid;
  color: #b1b0b5;
  transition: all 0.3s ease 0s;
}

.bg-color9 .sidebar-nav .sidebar-parent:hover,
.bg-color9 .sidebar-nav .sidebar-parent:focus {
  background: #1f2e3d;
  color: #fff;
}

/* Icon */

.sidebar-nav > ul > li > .icon::before {
  font-size: 1.5625rem;
  /* rem for IE11 */
  color: #fff;
  position: absolute;
  top: 50%;
  left: 1em;
  transition: all 0.3s ease 0s;
}

.sidebar-nav > ul > li > .icon-product-postcards::before {
  top: 46%;
}

.sidebar-nav > ul > li > .icon-product-social::before {
  font-size: 1.4rem;
  /* rem for IE11 */
  left: 1.1em;
}

.sidebar-nav > ul > li > .icon:hover::before,
.sidebar-nav > ul > li > .icon:focus::before {
  left: 1.4em;
}

.sidebar-nav > ul > li > .icon-product-social:hover::before,
.sidebar-nav > ul > li > .icon-product-social:focus::before {
  left: 1.5em;
}

/* Current State */

.sidebar-nav .menu > li.current > a {
  padding-left: 4em;
  color: #fff;
  font-weight: 700;
  position: relative;
}

.sidebar-nav .menu > li.current > .icon::before {
  left: 0.8em;
}

.bg-color8 .sidebar-nav .menu > li.current > a {
  background: #c1c0c4;
  color: #fff;
}

.bg-color9 .sidebar-nav .menu > li.current > a {
  background: #111a22;
  border-left: 5px solid #ff4649;
}

/* Carat */

.sidebar-nav .menu > li.current > a::after {
  border-color: rgba(0, 0, 0, 0) #fff;
  border-style: solid;
  border-width: 0.75rem 0.75rem 0.75rem 0;
  display: block;
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(0, -50%);
}

.bg-color8 .sidebar-nav .menu > li.current > a::after {
  border-color: transparent #fff transparent;
}

.bg-color9 .sidebar-nav .menu > li.current > a::after {
  border-color: transparent #f1f0f5 transparent;
}

/* Sidebar subnav */

.bg-color8 .sidebar-subnav {
  background: #79787b;
}

.bg-color9 .sidebar-subnav {
  background: #1a2633;
}

.sidebar-subnav hr {
  margin: 0;
}

.bg-color8 .sidebar-subnav hr {
  border-bottom-color: #d9d8dd;
}

.bg-color9 .sidebar-subnav hr {
  border-bottom-color: #596673;
}

.subnav-heading {
  padding: 1.5rem 0 0.4rem;
  font-size: 14px;
  font-weight: 700;
}

.bg-color8 .subnav-heading {
  color: #51606d;
}

.bg-color9 .subnav-heading {
  color: #f1f0f5;
}

.subnav-heading:first-child {
  padding-top: 0.4rem;
}

.sidebar-subnav li {
  font-size: 14px;
}

.sidebar-subnav a {
  display: inline-block;
  padding: 0.4rem 0;
}

.sidebar-subnav .submenu > li ul {
  margin-left: 1rem;
}

.bg-color8 .sidebar-subnav .submenu > li > a {
  color: #51606d;
}

.bg-color8 .sidebar-subnav .submenu > li > a:hover,
.bg-color8 .sidebar-subnav .submenu > li > a:focus {
  color: #7d8892;
}

.bg-color9 .sidebar-subnav .submenu > li > a {
  color: #b1b0b5;
}

.bg-color9 .sidebar-subnav .submenu > li > a:hover,
.bg-color9 .sidebar-subnav .submenu > li > a:focus {
  color: #fff;
}

/* Current state */

.sidebar-subnav .submenu > li.current > a {
  color: #fff;
  font-weight: 700;
}

/* Subnav item */

.subnav-item-border {
  border-bottom: 1px solid #596673;
  padding-bottom: 2rem;
  margin-bottom: 2rem !important;
}

/* ============================================================================
   Layouts
   ============================================================================ */

/* Layout Style 1
   ============================================================================ */

@media screen and (min-width: 40em) {
  .layout-style-1 {
    font-size: 128%;
  }
}

.layout-style-1 h1,
.layout-style-1 h2 {
  font-weight: 900;
}

/* Layout Style 2
   ============================================================================ */

@media screen and (min-width: 40em) {
  .layout-style-2 {
    font-size: 116%;
  }
}

@media screen and (min-width: 100em) {
  .layout-style-2 {
    font-size: 140%;
  }
}

.layout-style-2 h1,
.layout-style-2 h2,
.layout-style-2 p {
  font-weight: 300;
}

.layout-style-2 h1 {
  font-size: 2.6em;
  padding: 0.8em 0;
}

.layout-style-2 h2 {
  font-size: 1.8em;
  padding-bottom: 0.5em;
}

.layout-style-2 .text-small,
.layout-style-2 .text-small > li {
  font-size: 0.875rem !important;
}

.layout-style-2 .text-medium,
.layout-style-2 .text-medium > li {
  font-size: 1rem !important;
}

.layout-style-2 .text-large,
.layout-style-2 .text-large > li {
  font-size: 2rem !important;
}

@media screen and (min-width: 100em) {
  .layout-style-2 .text-small,
  .layout-style-2 .text-small > li {
    font-size: 1rem !important;
  }

  .layout-style-2 .text-medium,
  .layout-style-2 .text-medium > li {
    font-size: 1.2rem !important;
  }

  .layout-style-2 .text-large,
  .layout-style-2 .text-large > li {
    font-size: 2.2rem !important;
  }
}

/* Empty
   ============================================================================ */

.empty-padding-top {
  padding-top: 30%;
  padding-bottom: 3rem;
}

.empty-bg-container {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}

.empty-bg {
  width: 100%;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url("https://remindermedia.com/wp-content/uploads/cam/login.jpg");
}

.top-left-logo {
  position: absolute;
  top: 1.9375rem;
  left: 1.9375rem;
}

.top-left-logo img {
  height: 28px;
}

@media screen and (min-width: 28em) {
  .top-left-logo img {
    height: 39px;
  }
}

.top-right-button {
  position: absolute;
  top: 1.9375rem;
  right: 1.9375rem;
}

/* Chat
   ============================================================================ */

.chat-container {
  padding-top: 5.9375rem;
  padding-bottom: 4.625rem;
}

.chat-header,
.chat-footer {
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
}

.chat-header {
  padding-top: 6.375rem;
}

.chat-footer {
  padding-bottom: 4.875rem;
}

@media screen and (min-width: 64em) {
  .chat-header,
  .chat-footer {
    padding-left: 17.5rem;
    padding-right: 24.375rem;
  }

  .chat-footer {
    padding-bottom: 1.875rem;
  }
}

/* Magazine Contact Block
   ============================================================================ */

.cb-preview--container {
  border-radius: 5px;
  padding: 1em;
  background: #f9f9f9;
  /* Old browsers */
  /* FF3.6-15 */
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #f9f9f9 0%, #eeeeee 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#f9f9f9", endColorstr="#eeeeee", GradientType=0);
  /* IE6-9 */
}

.cb-preview--container.refreshing {
  opacity: 0.1;
}

.standard-fic-cb-preview {
  font-family: "TeX-Gyre-Adventor", "Nunito Sans", Arial, Helvetica, sans-serif;
}

/* Digital Edition Contact Block
   ============================================================================ */

.cb-de-email {
  font-family: "Nunito Sans", Helvetica, Arial, sans-serif;
  text-align: center;
  font-size: 80%;
  max-width: 26.25rem;
  margin: 0 auto 1.5rem;
}

.cb-de-email h4 {
  font-size: 1.5em;
  font-weight: 900;
  margin: 0 0 0.125em 0;
}

.cb-de-email-subtitle {
  font-size: 1.25em;
  font-weight: 700;
  color: #afb3bb;
  margin: 0;
}

.cb-de-email p {
  font-size: 1.25em;
  margin: 0.5em 0;
}

.cb-de-email a {
  color: #327ea8;
}

/* Button */

.cb-de-email-button {
  background: #1b1b1b;
  border-radius: 999px;
  display: block;
  margin-bottom: 1.5em;
  padding: 1.45em;
  color: #fff !important;
  font-family: "Nunito Sans", Helvetica, Arial, sans-serif;
  font-size: 1.375em;
  line-height: 1.375em;
  font-weight: 800;
  text-transform: uppercase;
  text-align: center;
  text-decoration: none;
}

/* Button Color 2 */

.cb-de-email-button-color-2 {
  background: #ff4649;
}

/* Images */

.cb-de-email-headshot,
.cb-de-email-logo,
.cb-de-email-industry-logo {
  display: block;
  margin: 0 auto 1.5em;
}

.cb-de-email-headshot {
  max-width: 12.5em;
}

.cb-de-email-logo {
  max-width: 8em;
}

.cb-de-email-industry-logo {
  height: 2.375em;
}

/* Local Events Contact Block
   ============================================================================ */

.cb-local {
  font-family: "Lato", Arial, Helvetica, sans-serif;
  color: #424d56;
}

/* Profile Editing
   ============================================================================ */

#profile-tabs-overlay {
  background: #fff;
  position: absolute;
  opacity: 0.9;
  width: 100%;
  height: 100%;
  z-index: 999;
  top: 0;
}

#profile-tabs-overlay img {
  position: absolute;
  top: 50%;
  left: 50%;
}

/* Digital Edition Previews
   ============================================================================ */

/* Front Inside Cover */

.preview-de-fic p {
  font-family: "Open Sans", Arial, Helvetica, sans-serif;
  font-size: 0.875rem;
  letter-spacing: 0.03rem;
  line-height: 2;
  color: #51606d !important;
}

/* ============================================================================
   Previews
   ============================================================================ */

/* Mobile */

.preview-mobile {
  border-radius: 70px;
  background: #e3e2e6;
  padding: 70px 35px;
  width: 480px;
  height: 820px;
  margin: 0 auto;
}

@media screen and (min-width: 100em) {
  .preview-mobile {
    border-radius: 70px;
    width: 480px;
    height: 820px;
  }
}

.preview-mobile-content {
  height: 100%;
  overflow-y: scroll;
}

/* ============================================================================
   Body
   ============================================================================ */

.content-wrap {
  padding: 1.875rem 0.9375rem 4.875rem;
}

.has-top-bar .content-wrap {
  padding-top: 6.375rem;
}

.has-top-bar-2 .content-wrap {
  padding-top: 10.875rem;
}

@media screen and (min-width: 64em) {
  .content-wrap {
    padding-bottom: 1.875rem;
    min-height: 100vh;
  }
}

/* ============================================================================
   Magazine
   ============================================================================ */

.recipient-number-container {
  position: relative;
  bottom: 0;
  top: 0;
}

@media screen and (min-width: 40em) {
  .recipient-number-container {
    position: absolute;
    right: 0;
  }
}

.recipient-number {
  color: #fff;
}

.recipient-number .count {
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1.5rem;
  display: block;
}

@media screen and (min-width: 40em) {
  .recipient-number .count {
    font-size: 3rem;
    line-height: 3.25rem;
  }
}

.icon-xlarge {
  font-size: 17px;
}

@media screen and (min-width: 40em) {
  .icon-xlarge {
    font-size: 37px;
  }
}

.issue-number {
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1.5rem;
}

@media screen and (min-width: 40em) {
  .issue-number {
    font-size: 2rem;
    line-height: inherit;
  }
}

.left-angle {
  background: #ffffff none repeat scroll 0 0;
  bottom: -15px;
  display: inline-block;
  height: 10em;
  left: -25px;
  position: absolute;
  top: -15px;
  transform: rotate(195deg);
  width: 40px;
}

.box.issue-number {
  font-size: 2.75rem;
  font-weight: bold;
}

.v-divider {
  padding: 0 10px;
}

.center-vertically {
  top: 50%;
  transform: translateY(-70%);
  position: -webkit-sticky;
  position: sticky;
}

.box-title {
  font-size: 0.9rem;
  font-weight: bold;
}

.link-overlay {
  bottom: 0;
  display: block;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 7;
}

.magazine-delivery-date p {
  font-size: 0.7rem;
  margin-bottom: 4px;
}

@media screen and (min-width: 40em) {
  .box-title {
    font-size: 28px;
    font-weight: normal;
  }

  .magazine-delivery-date p {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
}

/* Input Group Number */

.input-group {
  width: 215px;
}

.input-group > *:last-child > * {
  border-radius: 5px;
}

.input-group-addon,
.input-group-btn,
.input-group .form-control {
  display: table-cell;
}

.input-group-addon,
.input-group-btn {
  vertical-align: middle;
  white-space: nowrap;
  width: 1%;
}

.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group > .btn,
.input-group-btn:first-child > .dropdown-toggle,
.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.input-group-addon:first-child {
  border-right: 0 none;
}

.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group > .btn,
.input-group-btn:last-child > .dropdown-toggle,
.input-group-btn:first-child > .btn:not(:first-child),
.input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.input-group-addon:last-child {
  border-left: 0 none;
}

.input-group-btn {
  font-size: 0;
  position: relative;
  white-space: nowrap;
}

.input-group-btn > .btn {
  position: relative;
}

.input-group-btn > .btn + .btn {
  margin-left: -1px;
}

.input-group-btn > .btn:hover,
.input-group-btn > .btn:focus,
.input-group-btn > .btn:active {
  z-index: 2;
}

.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group {
  margin-right: -1px;
}

.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group {
  margin-left: -1px;
}

.input-number {
  border-bottom: 2px solid #71acd1;
  border-top: 2px solid #71acd1;
  height: 3.25rem;
  min-width: 5rem;
  text-align: center;
  color: #51606d;
}

table .input-group {
  width: 120px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 15px;
}

table .input-group .input-number {
  margin-bottom: 0;
  height: 2.15rem;
  min-width: 2rem;
}

table .input-group .button {
  padding: 0.14rem;
  margin-bottom: 0;
}

table .input-number {
  border-bottom: 2px solid #51606d;
  border-top: 2px solid #51606d;
}

/* ============================================================================
   Digital
   ============================================================================ */

.month-header {
  font-size: 1.15rem;
  font-weight: bold;
  line-height: 1.5rem;
  position: relative;
  z-index: 10;
}

@media screen and (min-width: 40em) {
  .month-header {
    font-size: 1.75rem;
    line-height: 1.75rem;
  }
}

.badge-overlay-left {
  left: -0.25rem;
  position: absolute;
  top: 30%;
}

.badge-overlay-right {
  right: -2rem;
  position: absolute;
  top: 69%;
}

.badge-overlay-social {
  right: 0.1rem;
  position: absolute;
  bottom: -20px;
}

.form .letter-content {
  max-height: 30vh;
  min-height: 30vh;
  resize: none;
}

/* Add Mailing Preview
   ============================================================================ */

.mailing-preview--sample--container * {
  color: #586671;
  margin: 0;
  padding: 0;
}

.mailing-preview--sample--container table {
  margin-bottom: 0;
  font-size: 85%;
  line-height: 1.5;
}

.mailing-preview--sample--container table tbody tr:nth-child(2n) {
  background-color: #fff;
}

.mailing-preview--sample--container th {
  text-align: left;
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

.mailing-preview--sample--container p {
  font-size: 1rem;
  font-weight: normal;
  line-height: 1.6;
  margin-bottom: 1.25rem;
  text-rendering: optimizelegibility;
}

.mailing-preview--mail-chrome--table th,
.mailing-preview--mail-chrome--table td {
  vertical-align: top;
  width: 1px;
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

.mailing-preview--sample h1,
.mailing-preview--sample h2,
.mailing-preview--sample h3,
.mailing-preview--sample h4,
.mailing-preview--sample h5,
.mailing-preview--sample h6,
.mailing-preview--sample p,
.mailing-preview--sample ul,
.mailing-preview--sample li {
  color: inherit;
}

.mailing-preview--sample .share-logo--container table {
  width: inherit;
}

.mailing-preview--sample p {
  line-height: normal;
  margin: auto;
}

.mailing-preview--sample td {
  padding: 0;
  background-color: transparent !important;
}

.mailing-preview--sample tr:last-child {
  border-bottom: 0 !important;
  border-bottom-color: transparent;
}

.mailing-preview--sample .share-icon--container {
  display: flex !important;
  margin-left: 95%;
  width: 26px !important;
}

.mailing-preview--sample .article-preview--image {
  width: initial !important;
}

.mailing-preview--sample .mj-container > table tbody tr:nth-child(2n) {
  background-color: #1d222d;
}

.mailing-preview--sample .mj-container table tbody {
  border: 0;
  background-color: transparent;
}

/* Card
   ============================================================================ */

.de-front-cover-image--container {
  position: relative;
}

.de-front-cover-image--preview-icon {
  background: rgba(0, 0, 0, 0.35) none repeat scroll 0 0;
  border-radius: 50%;
  color: white;
  padding: 1em;
  position: absolute;
  bottom: 2.5rem;
  right: 2.5rem;
  transition: all 0.2s ease-out;
}

.de-front-cover-image--preview-icon .icon::before {
  margin: 0;
}

.de-front-cover-image--container:hover .de-front-cover-image--preview-icon {
  background: rgba(255, 255, 255, 0.6) none repeat scroll 0 0;
  transform: scale(1.5);
}

.de-front-cover-image--container:hover .de-front-cover-image--preview-icon i {
  color: #333;
}

.de-front-cover-image--preview-icon i {
  transition: all 0.2s ease-out;
  color: white;
}

/* Analytics
   TODO: Review and clean up
   ============================================================================ */

.mailing-detail--activity--container * {
  font:
    normal 100.01% "Nunito Sans",
    Helvetica,
    Arial,
    sans-serif;
  line-height: 1;
}

.mailing-detail--activity--container .pillow-small--container .pillow {
  height: 46px;
  width: 58px;
}

.mailing-detail--activity--container
  .mailing-detail--activity--report--item--value {
  font-weight: bold;
}

.mailing-detail--activity--container
  .mailing-detail--activity--report--item--label {
  font-size: 75%;
  font-weight: normal;
}

.mailing-detail--header {
  font-weight: bold;
  padding-bottom: 0.5em;
}

.mailing-detail-item--container {
  border-bottom: 1px solid #edf0f4;
  display: flex;
  padding: 1em;
  justify-content: space-between;
  font-size: 80%;
  transition: background-color 0.2s ease-out;
}

.mailing-detail-item--container:hover {
  background-color: #eee;
}

.mailing-detail-item--container:last-child {
  border-bottom: 0;
}

.mailing-detail--action-items button {
  margin-left: 1em;
  margin-bottom: 1em;
}

.mailing-detail--activity--container {
  display: flex;
  justify-content: flex-end;
}

.mailing-detail--activity--report--container {
  width: 185px;
  height: 108px;
  display: flex;
  flex-flow: wrap;
  cursor: pointer;
}

.tab-menu--container {
  padding-bottom: 2em;
  display: flex;
  justify-content: center;
}

.tab-menu--content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0.25em;
  background-color: #eef1f4;
  border-radius: 0.35em;
}

.tab-menu--item {
  width: 18em;
  text-align: center;
  font-weight: bold;
  font-size: 80%;
  color: #748390;
  padding: 0.25em;
  margin: 0.25em;
  cursor: pointer;
  border-radius: 0.25em;
}

.tab-menu--item:hover {
  background-color: #f4f7fa;
}

.tab-menu--item.tab-active,
.tab-menu--item.tab-active:hover {
  background-color: white;
}

.pillow {
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.pillow-small--container .pillow {
  height: 42px;
  width: 54px;
  padding: 2px;
  border-radius: 4px;
  margin: 4px;
}

.pillow-large--container .pillow {
  height: 125px;
  width: 210px;
  padding: 2px;
  border-radius: 0.4em;
}

.pillow-large--container .mailing-detail--activity--report--item--value {
  font-size: 250%;
}

.pillow-large--container .mailing-detail--activity--report--item--label {
  font-weight: bold;
}

.mailing-detail--activity--action {
  margin: auto 0;
  cursor: pointer;
}

.mailing-detail--activity--report--item--value {
  text-align: center;
  font-weight: bold;
  margin-bottom: 4px;
  font-size: 100%;
  line-height: 1;
}

.mailing-detail--activity--report--item--label {
  font-size: 75%;
  text-align: center;
  font-weight: 600;
}

.attempt-rate {
  background-color: #c677cd;
}

.success-rate {
  background-color: #f1c346;
}

.open-rate {
  background-color: #78c282;
}

.click-rate {
  background-color: #76a9d9;
}

/* Apex Charts */

.apexcharts-radialbar .apexcharts-datalabel-label {
  transform: translateY(5px);
  font-size: 1.2rem;
  font-weight: 700;
}

.recipient-group--action-resend-new {
  padding-top: 0.5em;
  font-size: 80%;
  cursor: pointer;
}

.recipient-group--action-resend-new i {
  width: 1.1em;
}

.recipient-group-detail--header {
  font-weight: bold;
  padding-bottom: 0.5em;
}

.mailing-detail--activity--report--no-data {
  width: 100%;
  cursor: not-allowed;
}

.new-volume--container {
  position: relative;
  border-radius: 5px;
  overflow: hidden;
}

.new-volume--preview--cta {
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.new-volume--preview img {
  width: 100%;
}

.recipient-rollup--charts--container {
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
}

.recipient-rollup--metrics--container {
  padding-top: 2em;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid transparent;
}

.recipient-rollup--congrats--container {
  border: 2px solid #eef1f4;
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 3em 0 1em;
  padding: 1em;
}

.recipient-rollup--congrats--icon {
  font-size: 200%;
  line-height: 1.5;
  padding: 0 0 0 0.2em;
}

.recipient-rollup--congrats--icon .ion-trophy {
  color: #f1c346;
  background-color: inherit;
}

.recipient-rollup--congrats--message {
  color: #87949f;
  width: 100%;
  font-size: 125%;
  line-height: 1.25em;
}

.recipient-engagement--container {
  padding: 0 1em;
  position: relative;
}

.recipient-engagement--header {
  padding: 0 0.5em 0.5em;
  font-weight: bold;
  color: #7c8791;
}

.recipient-engagement--list--container {
  display: flex;
  flex-direction: column;
}

.recipient-engagement--list--item {
  padding: 0.5em;
  font-size: 80%;
  background-color: #fcfdff;
}

.recipient-engagement--list--item span {
  color: #76a9d9;
}

.recipient-engagement--list--item:nth-child(odd) {
  background-color: #f7fbff;
}

.recipient-engagement--list--button-viewall {
  padding-top: 1em;
}

.recipient-rollup--activity-overview--container,
.recipient-rollup--recipient-details--container {
  position: relative;
  min-height: 460px;
}

.recipient-rollup--recipient-details--container {
  color: #51606d;
}

.recipient-rollup--recipient-details--container table thead,
.recipient-rollup--recipient-details--container table tbody,
.recipient-rollup--recipient-details--container table tfoot {
  border: 1px solid #f1f1f1;
  background-color: #fff;
}

.recipient-rollup--recipient-details--container table tr {
  line-height: 1.5;
}

.recipient-rollup--recipient-details--container
  table.dataTable
  thead
  .sorting_asc::before,
.recipient-rollup--recipient-details--container
  table.dataTable
  thead
  .sorting::before {
  content: normal;
}

.recipient-rollup--recipient-details--container
  .dataTables_wrapper
  .dataTables_filter
  input {
  display: inline-block;
  height: 30px;
  padding: 4px 6px;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 20px;
  color: #555555;
  border-radius: 4px;
  vertical-align: middle;
  width: 206px;
}

/* ============================================================================
   Tear Out Cards (TOC)
   ============================================================================ */

.toc-front-holder {
  position: relative;
  max-width: 700px;
  margin: 0 auto 1.5rem auto;
}

.toc-front-contact-block {
  position: absolute;
  bottom: 6%;
  left: 35.7%;
  width: 61.5%;
  text-align: left;
}

.toc-front-office-logo {
  width: 14.5%;
  position: absolute;
  top: 8.2%;
  right: 4%;
  text-align: right;
}

.toc-front-office-logo img {
  max-height: 3.75rem;
}

.standard-toc-cb-preview p {
  margin: 0;
  font-family: "TeX-Gyre-Adventor", Arial, Helvetica, sans-serif;
}

.standard-toc-cb-preview .margin-bottom {
  margin-bottom: 0.7em !important;
}

.standard-toc-cb-preview .DisplayName,
.standard-toc-cb-preview .ContactBlockName2 {
  font-weight: bold;
}

.standard-toc-cb-preview .ContactBlockName2 {
  margin-top: -6px;
}

.standard-toc-cb-preview .Title1,
.standard-toc-cb-preview .Title2,
.standard-toc-cb-preview .Title3,
.standard-toc-cb-preview .Title4,
.standard-toc-cb-preview .Title5,
.standard-toc-cb-preview .Title6,
.standard-toc-cb-preview .Title7 {
  font-style: italic;
}

.standard-toc-cb-preview .ContactType2,
.standard-toc-cb-preview .ContactType3,
.standard-toc-cb-preview .ContactType4 {
  font-weight: bold;
}

.standard-toc-cb-preview .DisplayName,
.standard-toc-cb-preview .ContactBlockName2,
.standard-toc-cb-preview .Website,
.standard-toc-cb-preview .ContactType {
  font-weight: bold;
}

.standard-toc-cb-preview .agentPhoto {
  width: 21.7%;
  position: absolute;
  bottom: 0;
}

.standard-toc-cb-preview .toc-cb-top-half {
  position: relative;
  height: 10.2rem;
}

.standard-toc-cb-preview .toc-cb-name-and-titles {
  position: absolute;
  left: 24%;
  bottom: 0;
}

.standard-toc-cb-preview hr {
  background-color: gray;
  height: 1px;
  border: 0;
  margin: 0.7em 0 0.3em 0;
}

@media screen and (max-width: 53.5em) {
  .toc-front-contact-block {
    font-size: 1.85vw;
  }

  .standard-toc-cb-preview .toc-cb-top-half {
    height: 15.4vw;
  }

  .toc-front-office-logo img {
    max-height: 7vw;
  }
}

@media screen and (min-width: 64em) and (max-width: 69.125em) {
  .toc-front-contact-block {
    font-size: 1.45vw;
  }

  .standard-toc-cb-preview .toc-cb-top-half {
    height: 12.6vw;
  }

  .toc-front-office-logo img {
    max-height: 5.4vw;
  }
}

@media screen and (min-width: 100em) and (max-width: 114.75em) {
  .toc-front-contact-block {
    font-size: 0.86vw;
  }

  .standard-toc-cb-preview .toc-cb-top-half {
    height: 8vw;
  }

  .toc-front-office-logo img {
    max-height: 3.2vw;
  }
}

/* ============================================================================
   Back Cover (BC), Back Inside Cover (BIC)
   ============================================================================ */

.cover-thumb {
  background-size: 100% !important;
  position: relative;
}

.back-inside-cover-thumb {
  width: 539px;
  height: 658px;
  border: 1px solid #cdd7e3;
  margin: 0 auto 1.5rem auto;
  display: block;
  font-family: "TeX-Gyre-Adventor", Arial, Helvetica, sans-serif;
}

.back-cover-lg-thumb {
  width: 539px;
  height: 658px;
  border: 1px solid #cdd7e3;
  margin: 0 auto 1.5rem auto;
  display: block;
}

.back-cover-mailing-label {
  width: 539px;
  height: 190px;
  border-left: 1px solid #cdd7e3;
  border-top: 1px solid #cdd7e3;
  border-right: 1px solid #cdd7e3;
  margin: 0 auto;
  display: block;
}

.back-cover-thumb {
  width: 539px;
  height: 468px;
  border-right: 1px solid #cdd7e3;
  border-bottom: 1px solid #cdd7e3;
  border-left: 1px solid #cdd7e3;
  margin: 0 auto 1.5rem auto;
  display: block;
  font-family: "TeX-Gyre-Adventor";
}

/* ============================================================================
   Right Sidebar
   ============================================================================ */

.right-sidebar img {
  border-radius: 2px;
}

/* ============================================================================
   Flatpickr
   ============================================================================ */

.flatpickr-input[readonly] {
  background-color: inherit;
}

/* ============================================================================
   Promo Page
   ============================================================================ */

.promo-bg {
  background-position: 55% 5%;
}

h1.promo-header {
  font-size: 50px;
  font-weight: 800;
  text-transform: uppercase;
  line-height: 3rem;
}

@media screen and (min-width: 40em) {
  h1.promo-header {
    font-size: 90px;
    font-weight: 800;
    text-transform: uppercase;
    line-height: 5rem;
  }
}

@media screen and (min-width: 64em) {
  .center-vertically-large {
    top: 50%;
    transform: translateY(-40%);
    position: -webkit-sticky;
    position: sticky;
  }
}

@media screen and (min-width: 90em) {
  .promo-bg {
    background-position: 65% 25%;
  }

  h1.promo-header {
    font-size: 110px;
    font-weight: 800;
    text-transform: uppercase;
    line-height: 6rem;
  }
}

@media screen and (min-width: 100em) {
  .promo-bg {
    background-position: 95% 25%;
  }

  h1.promo-header {
    font-size: 110px;
    font-weight: 800;
    text-transform: uppercase;
    line-height: 6rem;
  }
}

/* ============================================================================
   My Mailing List
   TODO: Review
   ============================================================================ */

.client-list-legend-icon {
  width: 16px;
}

.client-list-table-form select {
  width: 75px;
}

.disable-print-modal-client-list-container {
  max-height: 200px;
  overflow-y: auto;
  border-top: 1px solid #f1f0f5;
  border-bottom: 1px solid #f1f0f5;
}

.disable-print-modal-client-list-container .disable-print-modal-client-table {
  width: 100%;
}

/* ============================================================================
   My Photos
   TODO: Review
   ============================================================================ */

.new-main-photo--container .main-photo--thumbnail {
  width: 150px;
}

.new-main-photo--container .update-covers--switch-container {
  position: relative;
  left: 50%;
  float: left;
}

.new-main-photo--container
  .update-covers--switch-container
  .update-covers--switch-options {
  position: relative;
  right: 50%;
}

.new-main-photo--container .cover-image--thumbnail {
  width: 75px;
}

.button-account {
  box-shadow: none !important;
  will-change: unset !important;
  text-transform: none;
  background-color: transparent !important;
  color: #51606d !important;
  margin: 4px 4px;
}

.employee-photo {
  border-radius: 50%;
  display: inline-block;
  overflow: hidden;
  height: 60px;
  width: 60px;
}

.employee-photo-referred {
  border: 3px solid #76bb76;
}

.button-submit-referrals {
  position: fixed;
  bottom: 0;
  right: 1.5rem;
}

/* ============================================================================
   Online Checkout
   ============================================================================ */

.left-bg-container {
  position: fixed;
  left: 0;
}

.checkout-bg {
  width: 100%;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

/* ============================================================================
   Address File Upload
   ============================================================================ */

.address-file-upload-bg {
  width: 100%;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-image: url("/images/alm/background/mag-bg.jpg");
}

/* ============================================================================
   Service Agreement
   ============================================================================ */

/* Table */

.service-agreement-container table {
  border: 0;
  padding: 0;
}

.service-agreement-container br,
.service-agreement-container .header,
.service-agreement-container .contractMargin,
.service-agreement > h3:first-child {
  display: none;
}

.service-agreement-container .contractContainer,
.service-agreement-container .contentArea,
.service-agreement-container .contractInfo {
  width: 100% !important;
  padding: 0;
}

.service-agreement-container .contractInfo {
  font-size: 0.6875rem;
}

@media screen and (min-width: 40em) {
  .service-agreement-container .contractInfo {
    font-size: 1rem;
  }
}

@media screen and (min-width: 64em) {
  .service-agreement-container .contractInfo {
    font-size: 0.6875rem;
  }
}

@media screen and (min-width: 100em) {
  .service-agreement-container .contractInfo {
    font-size: 1rem;
  }
}

.service-agreement-container .contractInfo th {
  padding-top: 1.5rem;
  padding-bottom: 0;
  color: inherit;
}

.service-agreement-container .contractInfo td {
  padding-bottom: 1.5rem;
}

.service-agreement-container .contractHalf {
  width: 50%;
}

/* Copy */

.service-agreement {
  width: auto !important;
  padding: 0 !important;
  font-family: inherit !important;
}

.service-agreement ul,
.service-agreement ol {
  margin: 1.5rem 0 1.5rem 1.5rem !important;
  font-family: inherit;
}

.service-agreement li {
  padding: 0 !important;
}

/* ============================================================================
   Vue Tags Input
   http://www.vue-tags-input.com/#/examples/styling
   ============================================================================ */

.vue-tags-input {
  max-width: none !important;
}

.vue-tags-input.ti-disabled {
  opacity: 0.43;
}

.vue-tags-input.ti-disabled .ti-input {
  cursor: not-allowed;
}

.vue-tags-input.ti-focus {
  box-shadow: 0 0 5px #71acd1;
  border-radius: 5px !important;
}

.vue-tags-input .ti-input {
  background-color: #fff;
  border: 1px solid #d4d3d8 !important;
  border-radius: 5px !important;
  box-shadow: none;
  width: 100%;
  height: auto;
  color: #51606d;
  transition: none;
}

.vue-tags-input .ti-input ul.ti-tags li input.ti-new-tag-input {
  padding: 0.3rem;
}

.vue-tags-input .ti-input ul.ti-tags li input.ti-new-tag-input:focus {
  box-shadow: none !important;
}

.vue-tags-input .ti-tag {
  background: #51606d !important;
}

.vue-tags-input .ti-autocomplete ul li.ti-item {
  margin: 0 !important;
}

.spin-1s {
  -webkit-animation: load 1s infinite linear;
  animation: load 1s infinite linear;
}

/* ============================================================================
   Donut Chart Demo
   ============================================================================ */

.donut-chart {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
}

.donut-chart .slice {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.donut-chart .chart-center {
  position: absolute;
  border-radius: 50%;
}

.donut-chart .chart-center span {
  display: block;
  text-align: center;
}

.donut-chart.chart-1 {
  width: 140px;
  height: 140px;
  background: #ff4649;
}

.donut-chart.chart-1 .slice.one {
  clip: rect(0 140px 70px 0);
  transform: rotate(90deg);
  background: #76bb76;
}

.donut-chart.chart-1 .slice.two {
  clip: rect(0 70px 140px 0);
  transform: rotate(270deg);
  background: #76bb76;
}

.donut-chart.chart-1 .slice.three {
  clip: rect(0 70px 140px 0);
  transform: rotate(324deg);
  background: #edb15f;
}

.donut-chart.chart-1 .chart-center {
  top: 35px;
  left: 35px;
  width: 70px;
  height: 70px;
  background: #fff;
}

.donut-chart.chart-1 .chart-center span {
  font-family: "Montserrat", Arial, Helvetica, sans-serif;
  font-weight: 500;
  font-size: 2rem;
  line-height: 70px;
  color: #51606d;
}

.donut-chart.chart-1 .chart-center span:after {
  content: "75%";
}

.donut-chart.chart-2 {
  width: 140px;
  height: 140px;
  background: #76bb76;
}

.donut-chart.chart-2 .slice.one {
  clip: rect(0 140px 70px 0);
  transform: rotate(90deg);
  background: #76bb76;
}

.donut-chart.chart-2 .slice.two {
  clip: rect(0 70px 140px 0);
  transform: rotate(360deg);
  background: #76bb76;
}

.donut-chart.chart-2 .slice.three {
  clip: rect(0 70px 140px 0);
  transform: rotate(360deg);
  background: #76bb76;
}

.donut-chart.chart-2 .chart-center {
  top: 35px;
  left: 35px;
  width: 70px;
  height: 70px;
  background: #fff;
}

.donut-chart.chart-2 .chart-center span {
  font-family: "Montserrat", Arial, Helvetica, sans-serif;
  font-weight: 500;
  font-size: 2rem;
  line-height: 70px;
  color: #51606d;
}

.donut-chart.chart-2 .chart-center span:after {
  content: "100%";
}

.donut-chart.chart-3 {
  width: 140px;
  height: 140px;
  background: #f7f7f7;
}

.donut-chart.chart-3 .slice.one {
  clip: rect(0 140px 70px 0);
  transform: rotate(90deg);
  background: #76bb76;
}

.donut-chart.chart-3 .slice.two {
  clip: rect(0 70px 140px 0);
  transform: rotate(216deg);
  background: #76bb76;
}

.donut-chart.chart-3 .slice.three {
  clip: rect(0 70px 140px 0);
  transform: rotate(216deg);
  background: #76bb76;
}

.donut-chart.chart-3 .chart-center {
  top: 35px;
  left: 35px;
  width: 70px;
  height: 70px;
  background: #fff;
}

.donut-chart.chart-3 .chart-center span {
  font-family: "Montserrat", Arial, Helvetica, sans-serif;
  font-weight: 500;
  font-size: 2rem;
  line-height: 70px;
  color: #51606d;
}

.donut-chart.chart-3 .chart-center span:after {
  content: "60%";
}

.donut-chart.chart-4 {
  width: 140px;
  height: 140px;
  background: #76bb76;
}

.donut-chart.chart-4 .slice.one {
  clip: rect(0 140px 70px 0);
  transform: rotate(180deg);
  background: #f7f7f7;
}

.donut-chart.chart-4 .slice.two {
  clip: rect(0 70px 140px 0);
  transform: rotate(0deg);
  background: #f7f7f7;
}

.donut-chart.chart-4 .slice.three {
  clip: rect(0 70px 140px 0);
  transform: rotate(90deg);
  background: #76bb76;
}

.donut-chart.chart-4 .chart-center {
  top: 10px;
  left: 10px;
  width: 120px;
  height: 120px;
  background: #fff;
}

.donut-chart.chart-4 .chart-center span {
  font-family: "Montserrat", Arial, Helvetica, sans-serif;
  font-weight: 500;
  font-size: 2rem;
  line-height: 120px;
  color: #51606d;
}

.donut-chart.chart-4 .chart-center span:after {
  content: "25%";
}

/* ============================================================================
   Loading Bar
   ============================================================================ */

/* Make clicks pass-through */

#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: #71acd1;
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
}

/* Fancy blur effect */

/* Remove these to get rid of the spinner */

#nprogress .spinner {
  display: block;
  position: fixed;
  z-index: 1031;
  top: 15px;
  right: 15px;
}

#nprogress .spinner-icon {
  width: 18px;
  height: 18px;
  box-sizing: border-box;
  border: solid 2px transparent;
  border-top-color: #71acd1;
  border-left-color: #71acd1;
  border-radius: 50%;
  -webkit-animation: nprogress-spinner 400ms linear infinite;
  animation: nprogress-spinner 400ms linear infinite;
}

.nprogress-custom-parent {
  overflow: hidden;
  position: relative;
}

.nprogress-custom-parent #nprogress .spinner,
.nprogress-custom-parent #nprogress .bar {
  position: absolute;
}

@-webkit-keyframes nprogress-spinner {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes nprogress-spinner {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
