(function () {
  function renderHTML(values) {
    const photoUrl = values.photo?.url || values.photo;

    return `

<!-- Flex container -->
<div class="contact-block-container">
  <!-- Left: content -->
  <div class="contact-content">
    <!-- Contact header -->
    <div class="contact-header">
      <div class="contact-info">
        <a href="#">
          <img class="contact-photo" style="background-image: url('${photoUrl}');" />
        </a>
        <div class="contact-text">
          <p class="contact-name">${values.name}</p>
          <p class="contact-title">${values.titleLicense}</p>
        </div>
      </div>
      <p class="contact-phone">${values.phone}</p>
    </div>

    <!-- Form -->
    <div class="form-section">
      <p class="form-header">${values.formHeader}</p>
      <div class="form-headline-wrap">
        <h1 class="form-headline">
          ${values.formHeadline}
        </h1>
      </div>
      <form class="custom-form">
        <input
          type="text"
          placeholder="${values.formPlaceholderName}"
          class="form-input"
        />
        <input
          type="email"
          placeholder="${values.formPlaceholderEmail}"
          class="form-input"
        />
        <input
          type="tel"
          placeholder="${values.formPlaceholderPhone}"
          class="form-input"
        />
        <button type="submit" class="form-button">
          ${values.formButtonText}
        </button>
      </form>
    </div>
  </div>

  <!-- Right: image -->
  <div class="contact-image-wrap">
   <img 
    src="${values.rightImage?.url || values.rightImage}" 
    class="contact-image"
    alt="Landing Page Graphic"
   />
  </div>
</div>

<style>
  .contact-block-container {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    background: white;
  }

  .contact-content {
    flex: 1 1 300px;
    padding: 0.5rem 0.5rem 2rem 0.5rem;
  }

  .contact-header {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    position: relative;
    background: white;
  }

  .contact-info {
    display: flex;
    padding-right: 1rem;
  }

  .contact-photo {
    width: 40px;
    height: 40px;
    border-radius: 9999px;
    background-size: cover;
    margin-right: 0.5rem;
  }

  .contact-text {
    text-align: left;
  }

  .contact-name {
    font-weight: bold;
    color: #314253;
    margin: 0;
  }

  .contact-title {
    font-weight: lighter;
    font-size: 14px;
    color: rgb(220, 220, 220);
    margin: 0;
  }

  .contact-phone {
    font-weight: bold;
    color: #314253;
    margin: 0;
  }

  .form-section {
    padding: 30px 50px 50px 50px;
    font-family: Arial, sans-serif;
    text-align: center;
    background: white;
    margin-top: 100px;
  }

  .form-header {
    font-weight: bold;
  }

  .form-headline-wrap {
    text-align: center;
    margin-top: 20px;
  }

  .form-headline {
    width: 75%;
    margin: 0 auto 20px auto;
    font-size: 24px;
    font-weight: bold;
  }

  .custom-form {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .form-input {
    width: 80%;
    padding: 15px;
    margin: 10px 0;
    border: none;
    background: #f1f1f1;
    border-radius: 5px;
    font-size: 14px;
  }

  .form-button {
    margin-top: 30px;
    width: 80%;
    padding: 15px;
    background: #6bbf4e;
    color: white;
    font-weight: bold;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
  }

  .contact-image-wrap {
    flex: 1 1 300px;
  }

  .contact-image {
    width: 100%;
    height: auto;
    display: block;
  }


  @media (max-width: 1000px) {
  .contact-block-container {
    flex-direction: column;
  }

  .form-section {
    padding: 0 0 75px 0;
    margin-top: 20px
  }

  .contact-image-wrap {
    order: -1;
  }

  .contact-header{
  margin-top: 20px;
  }
}

</style>



    `;
  }

  unlayer.registerTool({
    name: "lead_magnet",
    label: "Lead Magnet",
    icon: "fa-address-card",
    supportedDisplayModes: ["web", "email"],
    options: {
      contact: {
        title: "Contact Info",
        position: 1,
        options: {
          name: {
            label: "Name",
            widget: "text",
            defaultValue: "Tina Silvernail",
          },
          titleLicense: {
            label: "Title & License",
            widget: "text",
            defaultValue: "REALTOR® License Number: 297610",
          },
          phone: {
            label: "Phone",
            widget: "text",
            defaultValue: "(*************",
          },
          photo: {
            label: "Photo",
            widget: "image",
            defaultValue: {
              url: "https://my.remindermedia.net/images/placeholders/avatar.png",
            },
          },
          formHeader: {
            label: "Form Header",
            widget: "text",
            defaultValue: "Free Download:",
          },
          formHeadline: {
            label: "Form Headline",
            widget: "text",
            defaultValue:
              "The 5 Biggest Mistakes to Avoid When Selling Your Home",
          },
          formPlaceholderName: {
            label: "Placeholder: Full Name",
            widget: "text",
            defaultValue: "Full Name",
          },
          formPlaceholderEmail: {
            label: "Placeholder: Email Address",
            widget: "text",
            defaultValue: "Email Address",
          },
          formPlaceholderPhone: {
            label: "Placeholder: Phone Number",
            widget: "text",
            defaultValue: "Phone Number",
          },
          formButtonText: {
            label: "Button Text",
            widget: "text",
            defaultValue: "DOWNLOAD",
          },
          rightImage: {
            label: "Right Side Image",
            widget: "image",
            defaultValue: {
              url: "https://account.remindermedia.com/images/product/landing-pages/lead-magnet/01/large.jpg",
            },
          },
        },
      },
    },
    values: {},
    renderer: {
      Viewer: unlayer.createViewer({
        render(values) {
          return renderHTML(values);
        },
      }),
      exporters: {
        web(values) {
          return renderHTML(values);
        },
      },
    },
  });
})();
