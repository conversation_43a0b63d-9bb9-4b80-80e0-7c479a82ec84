// eslint-disable-next-line
(function() {
  'use strict';

  // Global variables for merge tag functionality
  let tagRegistry = null;
  let sampleData = {};
  let autocompleteDropdown = null;
  let previewTooltip = null;

  /**
   * Initialize merge tag enhancements
   */
  function initializeMergeTagEnhancements() {
    if (typeof unlayer === 'undefined') {
      console.warn('Unlayer not available, retrying...');
      setTimeout(initializeMergeTagEnhancements, 100);
      return;
    }

    // Wait for unlayer to be fully loaded
    unlayer.init({
      displayMode: 'email'
    }).then(() => {
      setupMergeTagEnhancements();
    }).catch((error) => {
      console.error('Error initializing unlayer:', error);
      // Fallback - try to setup anyway
      setupMergeTagEnhancements();
    });
  }

  /**
   * Setup merge tag enhancement features
   */
  function setupMergeTagEnhancements() {
    // Get merge tags and sample data from global unlayer options
    try {
      const mergeTagsOption = unlayer.getOptions && unlayer.getOptions().mergeTags;
      if (mergeTagsOption) {
        Object.keys(mergeTagsOption).forEach(tagId => {
          const tag = mergeTagsOption[tagId];
          sampleData[tagId] = tag.sample || 'Sample Data';
        });
      }
    } catch (error) {
      console.warn('Could not access merge tags from unlayer options:', error);
    }

    // Setup autocomplete on text editor focus
    setupAutocomplete();
    
    // Setup hover preview
    setupHoverPreview();
    
    // Setup fallback syntax support
    setupFallbackSyntax();
    
    console.log('Merge tag enhancements initialized');
  }

  /**
   * Setup autocomplete functionality for merge tags
   */
  function setupAutocomplete() {
    // Monitor for text input focus in unlayer
    document.addEventListener('focusin', function(event) {
      const target = event.target;
      
      // Check if this is a text editor within unlayer
      if (isUnlayerTextEditor(target)) {
        setupTextEditorAutocomplete(target);
      }
    });
  }

  /**
   * Check if element is an Unlayer text editor
   */
  function isUnlayerTextEditor(element) {
    // Look for common patterns in Unlayer text editors
    return element && (
      element.contentEditable === 'true' ||
      element.tagName === 'TEXTAREA' ||
      element.classList.contains('fr-element') || // Froala editor
      element.closest('.unlayer-editor') ||
      element.closest('.fr-box') // Froala container
    );
  }

  /**
   * Setup autocomplete for a specific text editor
   */
  function setupTextEditorAutocomplete(editor) {
    let lastCaretPosition = null;
    
    // Handle keyup events for autocomplete trigger
    function handleKeyup(event) {
      const text = getEditorText(editor);
      const caretPos = getCaretPosition(editor);
      
      // Look for {{ pattern before cursor
      const beforeCaret = text.substring(0, caretPos);
      const match = beforeCaret.match(/\{\{([^}]*)$/);
      
      if (match) {
        const query = match[1];
        const startPos = caretPos - query.length;
        showAutocompleteDropdown(editor, query, startPos, caretPos);
      } else {
        hideAutocompleteDropdown();
      }
    }

    // Handle keydown for navigation
    function handleKeydown(event) {
      if (autocompleteDropdown && autocompleteDropdown.style.display !== 'none') {
        if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
          event.preventDefault();
          navigateAutocomplete(event.key === 'ArrowDown' ? 1 : -1);
        } else if (event.key === 'Enter') {
          event.preventDefault();
          selectAutocompleteItem();
        } else if (event.key === 'Escape') {
          hideAutocompleteDropdown();
        }
      }
    }

    editor.addEventListener('keyup', handleKeyup);
    editor.addEventListener('keydown', handleKeydown);
    editor.addEventListener('blur', function() {
      // Delay hiding to allow clicking on dropdown
      setTimeout(hideAutocompleteDropdown, 150);
    });
  }

  /**
   * Get text content from editor (handles both input and contentEditable)
   */
  function getEditorText(editor) {
    if (editor.tagName === 'TEXTAREA' || editor.tagName === 'INPUT') {
      return editor.value;
    } else {
      return editor.textContent || editor.innerText || '';
    }
  }

  /**
   * Get caret position in editor
   */
  function getCaretPosition(editor) {
    if (editor.tagName === 'TEXTAREA' || editor.tagName === 'INPUT') {
      return editor.selectionStart;
    } else {
      // For contentEditable elements
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const preCaretRange = range.cloneRange();
        preCaretRange.selectNodeContents(editor);
        preCaretRange.setEnd(range.endContainer, range.endOffset);
        return preCaretRange.toString().length;
      }
    }
    return 0;
  }

  /**
   * Show autocomplete dropdown
   */
  function showAutocompleteDropdown(editor, query, startPos, endPos) {
    const suggestions = getMergeTagSuggestions(query);
    
    if (suggestions.length === 0) {
      hideAutocompleteDropdown();
      return;
    }

    // Create dropdown if it doesn't exist
    if (!autocompleteDropdown) {
      createAutocompleteDropdown();
    }

    // Position dropdown
    const rect = editor.getBoundingClientRect();
    const caretCoords = getCaretCoordinates(editor);
    
    autocompleteDropdown.style.left = (rect.left + caretCoords.left) + 'px';
    autocompleteDropdown.style.top = (rect.top + caretCoords.top + 20) + 'px';
    autocompleteDropdown.style.display = 'block';

    // Populate suggestions
    populateAutocompleteDropdown(suggestions, editor, startPos, endPos);
  }

  /**
   * Create autocomplete dropdown element
   */
  function createAutocompleteDropdown() {
    autocompleteDropdown = document.createElement('div');
    autocompleteDropdown.id = 'merge-tag-autocomplete';
    autocompleteDropdown.style.cssText = `
      position: fixed;
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      max-height: 200px;
      overflow-y: auto;
      z-index: 10000;
      min-width: 250px;
      display: none;
    `;
    document.body.appendChild(autocompleteDropdown);
  }

  /**
   * Get suggestions for merge tags
   */
  function getMergeTagSuggestions(query) {
    const suggestions = [];
    const lowerQuery = query.toLowerCase();

    // Get merge tags from unlayer options
    try {
      const mergeTagsOption = unlayer.getOptions && unlayer.getOptions().mergeTags;
      if (mergeTagsOption) {
        Object.keys(mergeTagsOption).forEach(tagId => {
          const tag = mergeTagsOption[tagId];
          if (!query || 
              tagId.toLowerCase().includes(lowerQuery) || 
              (tag.name && tag.name.toLowerCase().includes(lowerQuery))) {
            suggestions.push({
              id: tagId,
              name: tag.name || tagId,
              sample: tag.sample || 'Sample Data',
              category: tag.category || 'General'
            });
          }
        });
      }
    } catch (error) {
      console.warn('Error getting merge tag suggestions:', error);
    }

    return suggestions.slice(0, 10); // Limit to 10 suggestions
  }

  /**
   * Populate autocomplete dropdown with suggestions
   */
  function populateAutocompleteDropdown(suggestions, editor, startPos, endPos) {
    autocompleteDropdown.innerHTML = '';
    
    suggestions.forEach((suggestion, index) => {
      const item = document.createElement('div');
      item.className = 'autocomplete-item';
      item.style.cssText = `
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
        font-size: 14px;
      `;
      
      item.innerHTML = `
        <div style="font-weight: bold; color: #333;">${suggestion.name}</div>
        <div style="font-size: 12px; color: #666;">${suggestion.category} - ${suggestion.sample}</div>
      `;
      
      // Hover effects
      item.addEventListener('mouseenter', function() {
        clearAutocompleteSelection();
        item.style.backgroundColor = '#f0f0f0';
        item.classList.add('selected');
      });
      
      item.addEventListener('mouseleave', function() {
        item.style.backgroundColor = '';
      });
      
      // Click to select
      item.addEventListener('click', function() {
        insertMergeTag(editor, suggestion.id, startPos, endPos);
        hideAutocompleteDropdown();
      });
      
      autocompleteDropdown.appendChild(item);
    });
  }

  /**
   * Navigate autocomplete with arrow keys
   */
  function navigateAutocomplete(direction) {
    const items = autocompleteDropdown.querySelectorAll('.autocomplete-item');
    if (items.length === 0) return;
    
    let selectedIndex = -1;
    items.forEach((item, index) => {
      if (item.classList.contains('selected')) {
        selectedIndex = index;
      }
    });
    
    clearAutocompleteSelection();
    
    selectedIndex += direction;
    if (selectedIndex < 0) selectedIndex = items.length - 1;
    if (selectedIndex >= items.length) selectedIndex = 0;
    
    items[selectedIndex].classList.add('selected');
    items[selectedIndex].style.backgroundColor = '#f0f0f0';
    items[selectedIndex].scrollIntoView({ block: 'nearest' });
  }

  /**
   * Clear autocomplete selection
   */
  function clearAutocompleteSelection() {
    const items = autocompleteDropdown.querySelectorAll('.autocomplete-item');
    items.forEach(item => {
      item.classList.remove('selected');
      item.style.backgroundColor = '';
    });
  }

  /**
   * Select current autocomplete item
   */
  function selectAutocompleteItem() {
    const selectedItem = autocompleteDropdown.querySelector('.autocomplete-item.selected');
    if (selectedItem) {
      selectedItem.click();
    }
  }

  /**
   * Insert merge tag into editor and log analytics
   */
  function insertMergeTag(editor, tagId, startPos, endPos) {
    const mergeTag = `{{${tagId}}}`;
    
    if (editor.tagName === 'TEXTAREA' || editor.tagName === 'INPUT') {
      const text = editor.value;
      const newText = text.substring(0, startPos - 2) + mergeTag + text.substring(endPos);
      editor.value = newText;
      editor.setSelectionRange(startPos - 2 + mergeTag.length, startPos - 2 + mergeTag.length);
    } else {
      // For contentEditable elements
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        range.insertNode(document.createTextNode(mergeTag));
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
    
    // Trigger change event
    editor.dispatchEvent(new Event('input', { bubbles: true }));
    
    // Log analytics event
    logMergeTagUsageEvent(tagId, 'unlayer-plugin');
  }

  /**
   * Hide autocomplete dropdown
   */
  function hideAutocompleteDropdown() {
    if (autocompleteDropdown) {
      autocompleteDropdown.style.display = 'none';
    }
  }

  /**
   * Get approximate caret coordinates
   */
  function getCaretCoordinates(editor) {
    // Simple approximation - in a real implementation, you might want to use
    // a library like caret-pos for more accurate positioning
    return { left: 0, top: 0 };
  }

  /**
   * Setup hover preview functionality
   */
  function setupHoverPreview() {
    document.addEventListener('mouseover', function(event) {
      const target = event.target;
      const text = target.textContent || target.innerText || '';
      
      // Look for merge tag patterns
      const mergeTagRegex = /\{\{([^|}]+)(\|([^}]*))?\}\}/g;
      let match;
      
      while ((match = mergeTagRegex.exec(text)) !== null) {
        const fullMatch = match[0];
        const tagId = match[1].trim();
        const fallback = match[3] ? match[3].trim() : null;
        
        // Check if hover is over this specific tag
        if (isHoveringOverText(target, fullMatch, event)) {
          showPreviewTooltip(event, tagId, fallback, fullMatch);
          break;
        }
      }
    });

    document.addEventListener('mouseout', function(event) {
      hidePreviewTooltip();
    });
  }

  /**
   * Check if mouse is hovering over specific text
   */
  function isHoveringOverText(element, searchText, event) {
    // Simple approximation - in a real implementation, you'd want more precise text positioning
    const text = element.textContent || element.innerText || '';
    return text.includes(searchText);
  }

  /**
   * Show preview tooltip
   */
  function showPreviewTooltip(event, tagId, fallback, originalText) {
    const resolvedValue = resolveMergeTagValue(tagId, fallback);
    
    if (!previewTooltip) {
      createPreviewTooltip();
    }
    
    previewTooltip.innerHTML = `
      <div style="font-weight: bold; margin-bottom: 4px;">${originalText}</div>
      <div style="color: #666; font-size: 12px;">Preview: ${resolvedValue}</div>
    `;
    
    previewTooltip.style.left = (event.pageX + 10) + 'px';
    previewTooltip.style.top = (event.pageY - 10) + 'px';
    previewTooltip.style.display = 'block';
  }

  /**
   * Create preview tooltip element
   */
  function createPreviewTooltip() {
    previewTooltip = document.createElement('div');
    previewTooltip.id = 'merge-tag-preview';
    previewTooltip.style.cssText = `
      position: absolute;
      background: #333;
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 10001;
      max-width: 300px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      display: none;
      pointer-events: none;
    `;
    document.body.appendChild(previewTooltip);
  }

  /**
   * Hide preview tooltip
   */
  function hidePreviewTooltip() {
    if (previewTooltip) {
      previewTooltip.style.display = 'none';
    }
  }

  /**
   * Resolve merge tag value with fallback support
   */
  function resolveMergeTagValue(tagId, fallback) {
    const value = sampleData[tagId];
    
    if (value !== undefined && value !== null && value !== '') {
      return value;
    }
    
    return fallback !== null ? fallback : `{{${tagId}}}`;
  }

  /**
   * Setup fallback syntax support
   */
  function setupFallbackSyntax() {
    // This is handled in the preview functionality above
    // The parsing logic supports {{tagId|fallback}} syntax
    console.log('Fallback syntax support enabled');
  }

  /**
   * Log merge tag usage event to analytics
   */
  function logMergeTagUsageEvent(tagId, insertionSource = 'unlayer-plugin') {
    try {
      // Get template and account context from window or unlayer
      const templateId = window.currentTemplateId || 'unknown';
      const accountId = window.currentAccountId || 'unknown';
      const userId = window.currentUserId || 'unknown';
      
      // Create analytics event
      const eventData = {
        accountId: accountId,
        templateId: templateId,
        tagId: tagId,
        templateType: 'email', // Default for Unlayer
        userId: userId,
        insertionSource: insertionSource,
        context: {
          component: 'unlayer-editor',
          userAgent: navigator.userAgent
        },
        timestamp: new Date().toISOString()
      };
      
      // Send to analytics endpoint
      if (window.titanAnalytics && typeof window.titanAnalytics.logMergeTagUsage === 'function') {
        window.titanAnalytics.logMergeTagUsage(eventData);
      } else {
        // Fallback: store in localStorage for batch upload
        const pendingEvents = JSON.parse(localStorage.getItem('pendingMergeTagEvents') || '[]');
        pendingEvents.push(eventData);
        localStorage.setItem('pendingMergeTagEvents', JSON.stringify(pendingEvents));
        
        console.log('Merge tag usage logged (pending upload):', eventData);
      }
      
    } catch (error) {
      console.error('Error logging merge tag usage:', error);
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeMergeTagEnhancements);
  } else {
    initializeMergeTagEnhancements();
  }

  // Also expose some functions globally for debugging
  window.mergeTagEnhancements = {
    initializeMergeTagEnhancements,
    showAutocompleteDropdown,
    hideAutocompleteDropdown,
    showPreviewTooltip,
    hidePreviewTooltip,
    resolveMergeTagValue
  };

})();
