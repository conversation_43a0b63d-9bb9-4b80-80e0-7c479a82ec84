(function () {
  function renderHTML({ visual = true } = {}) {
    // Display [Recipient Name] in Unlayer editor, but use {{recipientName}} in exported HTML
    const namePlaceholder = visual ? "[Recipient Name]" : "{{recipientName}}";

    const contentHTML = `
      <div style="background: #f5f5f5; padding: 2rem; border-radius: 8px; text-align: center;">
        <h2 id="greeting" style="font-size: 1.5rem; margin-bottom: 1rem;">
          Hi, ${namePlaceholder}!
        </h2>
        <p style="font-size: 1rem; color: #555;">
          These are your local events happening soon.
        </p>
      </div>
    `;

    // Add a script only in exported HTML (not in the Unlayer editor)
    const scriptTag = !visual
      ? `
        <script>
          fetch("https://jsonplaceholder.typicode.com/users/1")
            .then(res => res.json())
            .then(data => {
              const name = data.name || "there";
              const el = document.getElementById("greeting");
              if (el) {
                el.innerHTML = el.innerHTML.replace("{{recipientName}}", name);
              }
            })
            .catch(() => {});
        </script>
      `
      : "";

    return contentHTML + scriptTag;
  }

  // Register the custom block with Unlayer
  unlayer.registerTool({
    name: "custom_local_events_block",
    label: "Local Events Block",
    icon: "fa-calendar",
    supportedDisplayModes: ["web"],
    options: {},
    values: {},
    renderer: {
      Viewer: unlayer.createViewer({
        render() {
          return renderHTML({ visual: true }); // Show editor-friendly placeholder
        },
      }),
      exporters: {
        web() {
          return renderHTML({ visual: false }); // Output HTML with {{recipientName}} + script
        },
      },
    },
  });
})();
