(function () {
  // Inject Ionicons CSS dynamically
  const ioniconsCSS = document.createElement("link");
  ioniconsCSS.rel = "stylesheet";
  ioniconsCSS.href =
    "https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css";
  document.head.appendChild(ioniconsCSS);

  // Shared renderer function for both Viewer and Exporter
  function renderHTML(values) {
    const avatarImageUrl = values.avatarImage?.url || values.avatarImage;
    const officeLogoUrl = values.officeLogo?.url || values.officeLogo;
    const designationLogoUrl =
      values.designationLogo?.url || values.designationLogo;

    return `
      <div style="background: #faf7fb; padding: 1.5rem; text-align: center; max-width: 21.875rem; margin: 0 auto; border-radius: 4px; border: solid 1px black">
        <div style="background-image: url('${avatarImageUrl}'); width: 200px; height: 200px; border-radius: 50%; margin: 0 auto 20px; background-size: cover;"></div>
        <div style="font-size: 24px; font-weight: bold; margin-bottom: 5px;">${values.name}</div>
        <div style="font-size: 14px; color: #51606d; margin-bottom: 15px;">${values.subtitle}</div>
        <div style="font-size: 16px; color: #51606d; margin-bottom: 20px;">${values.message}</div>
        <button type="button" style="background: #ff4649; border: 2px solid #ff4649; border-radius: 4px; width: 100%; padding: 1.2em 4em; color: white; font-weight: bold; text-transform: uppercase; margin-bottom: 20px; cursor: pointer;">
          ${values.buttonText}
        </button>
        <div style="display: flex; padding: 10px 0; border-bottom: 1px solid rgba(0, 0, 0, .09); text-align: left;">
          <div style="margin-right: 1rem; opacity: 0.62; font-size: 24px; color: #51606d;">
            <i class="icon ion-email"></i>
          </div>
          <div>
            <a href="mailto:${values.email}" style="color: #314253; text-decoration: none;">${values.email}</a>
            <div style="color: #a6a6a6; font-size: 12px;">Email</div>
          </div>
        </div>
        <div style="display: flex; padding: 10px 0; text-align: left;">
          <div style="margin-right: 1rem; opacity: 0.62; font-size: 24px; color: #51606d;">
            <i class="icon ion-ios-location"></i>
          </div>
          <div style="color: #51606d;">
            ${values.address.replace(/\n/g, "<br>")}
            <div style="color: #a6a6a6; font-size: 12px;">Office</div>
          </div>
        </div>
        <div style="margin: 1.5rem auto; width: 66.66667%;">
          <img src="${officeLogoUrl}" alt="Office Logo">
        </div>
        <div style="margin: 0 auto 1.5rem;">
          <img src="${designationLogoUrl}" alt="Designation Logo" style="max-height: 2rem;">
        </div>
      </div>
    `;
  }

  // Unlayer Custom Tool Registration
  unlayer.registerTool({
    name: "content_block",
    label: "Content Block",
    icon: "fa-user",
    supportedDisplayModes: ["web", "email"],
    options: {
      profile: {
        title: "Profile Settings",
        position: 1,
        options: {
          avatarImage: {
            label: "Profile Image",
            widget: "image",
            defaultValue: {
              url: "https://account.remindermedia.com/storage/images/agent_photos/e/5/0/39910f254b222c1ed17f41e9bc635/*****************.tif_large.png",
            },
          },
          name: {
            label: "Name",
            widget: "text",
            defaultValue: "Stacey Shanner",
          },
          subtitle: {
            label: "Title",
            widget: "text",
            defaultValue: "REALTOR®",
          },
          message: {
            label: "Message",
            widget: "text",
            defaultValue: "Contact me with any questions!",
          },
          buttonText: {
            label: "Button Text",
            widget: "text",
            defaultValue: "GET IN TOUCH",
          },
          email: {
            label: "Email",
            widget: "text",
            defaultValue: "<EMAIL>",
          },
          address: {
            label: "Office Address",
            widget: "text",
            defaultValue:
              "ReminderMedia\n1100 First Avenue\nSuite 200\nKing of Prussia, PA 19406",
          },
          officeLogo: {
            label: "Office Logo",
            widget: "image",
            defaultValue: {
              url: "https://account.remindermedia.com/storage/images/officelogos/OL_FUR_4A1758410.gif",
            },
          },
          designationLogo: {
            label: "Designation Logo",
            widget: "image",
            defaultValue: {
              url: "https://digital.remindermedia.com/storage/app/media/industry-logos/realtor-equal-housing-opportunity.jpg",
            },
          },
        },
      },
    },
    values: {},
    renderer: {
      Viewer: unlayer.createViewer({
        render(values) {
          return renderHTML(values);
        },
      }),
      exporters: {
        web(values) {
          return renderHTML(values);
        },
      },
    },
  });
})();
