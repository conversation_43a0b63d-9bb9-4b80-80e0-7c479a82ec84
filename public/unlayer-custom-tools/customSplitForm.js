(function () {
  // Register the Custom Tool with Unlayer
  unlayer.registerTool({
    name: "Split Form",
    label: "Split Form",
    icon: "fa-home", // Choose an icon class you like
    supportedDisplayModes: ["web", "email"],

    // ---- Editable fields in the Unlayer editor ----
    options: {
      pageSettings: {
        title: "Page Settings",
        position: 1,
        options: {
          agentName: {
            label: "Agent Name",
            widget: "text",
            defaultValue: "STEVE SMITH",
          },
          agentTagline: {
            label: "Agent Tagline",
            widget: "text",
            defaultValue: "Service for Life!",
          },
          agentPhone: {
            label: "Agent Phone",
            widget: "text",
            defaultValue: "(*************",
          },
          largeImage: {
            label: "Desktop Background Image",
            widget: "image",
            defaultValue: {
              url: "https://rm-unlayer-data.s3.us-east-2.amazonaws.com/1741642195954-837789",
            },
          },
          freeDownloadLabel: {
            label: "Free Download Label",
            widget: "text",
            defaultValue: "Free Download:",
          },
          mainHeading: {
            label: "Main Heading",
            widget: "text",
            defaultValue: "Is A Multigenerational Home Right For You?",
          },
          formButtonText: {
            label: "Button Text",
            widget: "text",
            defaultValue: "Download",
          },
          fullNamePlaceholder: {
            label: "Full Name Placeholder",
            widget: "text",
            defaultValue: "Full Name",
          },
          emailPlaceholder: {
            label: "Email Placeholder",
            widget: "text",
            defaultValue: "Email Address",
          },
          phonePlaceholder: {
            label: "Phone Placeholder",
            widget: "text",
            defaultValue: "Phone Number",
          },
        },
      },
    },

    values: {},

    renderer: {
      // 1) VIEWER: What appears in the Unlayer editor
      Viewer: unlayer.createViewer({
        render(values) {
          // Grab field values (or fall back to defaults)
          const agentName = values.agentName || "STEVE SMITH";
          const agentTagline = values.agentTagline || "Service for Life!";
          const agentPhone = values.agentPhone || "(*************";
          const largeImage =
            values.largeImage?.url ||
            "https://rm-unlayer-data.s3.us-east-2.amazonaws.com/1741642195954-837789";
          const freeDownloadLabel =
            values.freeDownloadLabel || "Free Download:";
          const mainHeading =
            values.mainHeading || "Is A Multigenerational Home Right For You?";
          const formButtonText = values.formButtonText || "Download";
          const fullNamePlaceholder = values.fullNamePlaceholder || "Full Name";
          const emailPlaceholder = values.emailPlaceholder || "Email Address";
          const phonePlaceholder = values.phonePlaceholder || "Phone Number";

          // Return the HTML for the Unlayer editor preview
          return `
          <style>
            * { box-sizing: border-box; margin: 0; padding: 0; }
            body { font-family: 'Arial', sans-serif; background: #f8f8f8; color: #333; line-height: 1.5; }
            .lp-container { display: flex; flex-wrap: wrap; min-height: 100vh; background: #fff; }
            .column.large-6 { width: 100%; padding: 2rem; display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center; }
            @media (min-width: 1024px) { .column.large-6 { width: 50%; } }
            .empty-bg-container { display: none; }
            @media (min-width: 1024px) { .empty-bg-container { display: block; width: 50%; min-height: 100vh; background-size: cover; background-position: center; } }
            
            /* TOP SECTION (Agent Info & Phone) */
            .top-section { width: 100%; display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
            .agent-info { display: flex; align-items: center; }
            .avatar { width: 40px; height: 40px; border-radius: 50%; background-size: cover; background-position: center; margin-right: 10px; }
            .agent-text { text-align: left; }
            .text-bold { font-weight: bold; color: #000; }
            .agent-phone { font-weight: bold; color: #000; }

            /* MIDDLE SECTION (Title & Heading) */
            .middle-section { text-align: center; margin-bottom: 20px; }
            h5 { font-size: 16px; font-weight: bold; color: #333; margin-bottom: 10px; }
            h1 { font-size: 28px; font-weight: bold; color: #000; margin-bottom: 20px; line-height: 1.2; }

            /* BOTTOM SECTION (Form) */
            .form-section { width: 100%; max-width: 400px; }
            form input { width: 100%; padding: 14px; margin-bottom: 15px; border: 1px solid #ccc; border-radius: 5px; font-size: 16px; }
            form input::placeholder { color: #aaa; }
            button.button { width: 100%; padding: 14px; font-size: 16px; font-weight: bold; text-transform: uppercase; background: #3CA247; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button.button:hover { background: #2b7c34; }
          </style>

          <div class="preview-padding">
            <div class="row expanded collapse lp-container">
              <!-- LEFT COLUMN -->
              <div class="column large-6">
                
                <!-- Top Section: Agent Info & Phone -->
                <div class="top-section">
                  <div class="agent-info">
                    <div class="avatar" style="background-image: url('https://rm-unlayer-data.s3.us-east-2.amazonaws.com/1741642195954-837789');"></div>
                    <div class="agent-text">
                      <div class="text-bold">${agentName}</div>
                      <div>${agentTagline}</div>
                    </div>
                  </div>
                  <div class="agent-phone">${agentPhone}</div>
                </div>

                <!-- Middle Section: Free Download & Heading -->
                <div class="middle-section">
                  <h5>${freeDownloadLabel}</h5>
                  <h1>${mainHeading}</h1>
                </div>

                <!-- Bottom Section: Form -->
                <div class="form-section">
                  <form>
                    <input type="text" placeholder="${fullNamePlaceholder}">
                    <input type="email" placeholder="${emailPlaceholder}">
                    <input type="text" placeholder="${phonePlaceholder}">
                    <button class="button large expanded" type="button">${formButtonText}</button>
                  </form>
                </div>

              </div>

              <!-- RIGHT COLUMN (Image) -->
              <div class="column large-6 show-for-large empty-bg-container" style="background-image: url('${largeImage}');">
              </div>

            </div>
          </div>
        `;
        },
      }),

      // 2) EXPORTERS: What HTML actually gets exported
      exporters: {
        web: function (values) {
          // Use placeholders {{ }} if you want them replaced by your own system later
          return `
          <style>
            * { box-sizing: border-box; margin: 0; padding: 0; }
            body { font-family: 'Arial', sans-serif; background: #f8f8f8; color: #333; line-height: 1.5; }
            .lp-container { display: flex; flex-wrap: wrap; min-height: 100vh; background: #fff; }
            .column.large-6 { width: 100%; padding: 2rem; display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center; }
            @media (min-width: 1024px) { .column.large-6 { width: 50%; } }
            .empty-bg-container { display: none; }
            @media (min-width: 1024px) { .empty-bg-container { display: block; width: 50%; min-height: 100vh; background-size: cover; background-position: center; } }
            
            /* TOP SECTION (Agent Info & Phone) */
            .top-section { width: 100%; display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
            .agent-info { display: flex; align-items: center; }
            .avatar { width: 40px; height: 40px; border-radius: 50%; background-size: cover; background-position: center; margin-right: 10px; }
            .agent-text { text-align: left; }
            .text-bold { font-weight: bold; color: #000; }
            .agent-phone { font-weight: bold; color: #000; }

            /* MIDDLE SECTION (Title & Heading) */
            .middle-section { text-align: center; margin-bottom: 20px; }
            h5 { font-size: 16px; font-weight: bold; color: #333; margin-bottom: 10px; }
            h1 { font-size: 28px; font-weight: bold; color: #000; margin-bottom: 20px; line-height: 1.2; }

            /* BOTTOM SECTION (Form) */
            .form-section { width: 100%; max-width: 400px; }
            form input { width: 100%; padding: 14px; margin-bottom: 15px; border: 1px solid #ccc; border-radius: 5px; font-size: 16px; }
            form input::placeholder { color: #aaa; }
            button.button { width: 100%; padding: 14px; font-size: 16px; font-weight: bold; text-transform: uppercase; background: #3CA247; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button.button:hover { background: #2b7c34; }
          </style>

          <div class="preview-padding">
            <div class="row expanded collapse lp-container">
              <!-- LEFT COLUMN -->
              <div class="column large-6">
                
                <!-- Top Section: Agent Info & Phone -->
                <div class="top-section">
                  <div class="agent-info">
                    <div class="avatar" style="background-image: url('https://rm-unlayer-data.s3.us-east-2.amazonaws.com/1741642195954-837789');"></div>
                    <div class="agent-text">
                      <div class="text-bold">${agentName}</div>
                      <div>${agentTagline}</div>
                    </div>
                  </div>
                  <div class="agent-phone">${agentPhone}</div>
                </div>

                <!-- Middle Section: Free Download & Heading -->
                <div class="middle-section">
                  <h5>${freeDownloadLabel}</h5>
                  <h1>${mainHeading}</h1>
                </div>

                <!-- Bottom Section: Form -->
                <div class="form-section">
                  <form>
                    <input type="text" placeholder="${fullNamePlaceholder}">
                    <input type="email" placeholder="${emailPlaceholder}">
                    <input type="text" placeholder="${phonePlaceholder}">
                    <button class="button large expanded" type="button">${formButtonText}</button>
                  </form>
                </div>

              </div>

              <!-- RIGHT COLUMN (Image) -->
              <div class="column large-6 show-for-large empty-bg-container" style="background-image: url('${largeImage}');">
              </div>

            </div>
          </div>
        `;
        },
      },
    },
  });
})();
