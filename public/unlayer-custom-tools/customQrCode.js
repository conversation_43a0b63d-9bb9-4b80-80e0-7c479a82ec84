// eslint-disable-next-line
unlayer?.registerTool({
  name: "qr_tool",
  label: "QR Code",
  icon: "fa-qrcode",
  supportedDisplayModes: ["web", "email"],
  options: {
    qr: {
      title: "QR Content",
      position: 1,
      options: {
        qr: {
          label: "QR URL",
          defaultValue: "",
          widget: "qr_generator",
        },
        alignment: {
          label: "Alignment",
          defaultValue: "center",
          widget: "alignment",
        },
      },
    },
  },
  values: {},
  renderer: {
    // eslint-disable-next-line
    Viewer: unlayer?.createViewer({
      render(values) {
        return `<div class="qr-tool" style="margin: auto; text-align: ${
          values.alignment
        }">
                  ${
                    values?.qr?.qrCode
                      ? `<img src="${values.qr.qrCode}" style="width: ${values.qr.width}%;"/>`
                      : `<h3>Enter url and QR Code will be generated here</h3>`
                  }
                </div>`;
      },
    }),
    exporters: {
      web: function (values) {
        return `<div class="qr-tool" style="margin: auto; text-align: ${
          values.alignment
        }">
                ${
                  values?.qr?.qrCode
                    ? `<img src="${values.qr.qrCode}" style="width: ${values.qr.width}%;"/>`
                    : `<h3>Enter url and QR Code will be generated here</h3>`
                }
              </div>`;
      },
      email: function (values) {
        return `<div class="qr-tool" style="margin: auto; text-align: ${
          values.alignment
        }">
                ${
                  values?.qr?.qrCode
                    ? `<img src="${values.qr.qrCode}" style="width: ${values.qr.width}%;"/>`
                    : `<h3>Enter url and QR Code will be generated here</h3>`
                }
              </div>`;
      },
    },
    head: {
      css: function (values) {},
      js: function (values) {},
    },
  },
});
// eslint-disable-next-line
unlayer.registerPropertyEditor({
  name: "qr_generator",
  layout: "bottom",
  // eslint-disable-next-line
  Widget: unlayer.createWidget({
    render(value, updateValue, data) {
      console.log(value);
      return `
        <label>Enter URL</label><br />
        <input id="qr-url" class="form-control" type="text" placeholder="Enter URL here..." value="${
          value.srcUrl || ""
        }" />
        <br />
        ${
          value.qrCode
            ? `<label>Width</label> <br /> <input id="qr-width" class="form-control" type="number" min="1" max="100" value="${50}"/>`
            : ``
        }
      `;
    },
    mount(node, value, updateValue, data) {
      var url = node.querySelector("#qr-url");
      var width = node.querySelector("#qr-width");
      url.onchange = function (e) {
        // Get index of item being updated
        var val = e.target.value;
        // eslint-disable-next-line
        var qr = new QRious({
          value: val,
        });
        // eslint-disable-next-line
        dataUrl = qr.toDataURL("image/jpeg");
        // eslint-disable-next-line
        updateValue({ ...value, qrCode: dataUrl, srcUrl: val });
      };
      if (!width) return;
      width.onchange = function (e) {
        // Get index of item being updated
        var val = e.target.value;
        updateValue({ ...value, width: val });
      };
    },
  }),
});
