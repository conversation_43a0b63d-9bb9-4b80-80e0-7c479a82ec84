{"swagger": "2.0", "info": {"title": "ReminderMedia Gaia API", "description": "API configuration for ReminderMedia Gaia", "version": "1.0.0"}, "host": "us-central1-rm-gaia-dev.cloudfunctions.net", "schemes": ["https"], "produces": ["application/json"], "securityDefinitions": {"api_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "key", "in": "query"}, "crm_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/rm-crm-service-account-dev%40rm-gaia-dev.iam.gserviceaccount.com"}}, "tags": [{"name": "Gaia"}], "paths": {"/listings": {"get": {"summary": "Get listings", "description": "Get listings by filter, orderby, limit and location", "operationId": "listings", "x-google-backend": {"address": "https://us-central1-rm-gaia-dev.cloudfunctions.net/listings"}, "security": [{"api_key": [], "crm_token": []}], "parameters": [{"name": "filter", "in": "query", "description": "Filter", "required": false, "type": "string"}, {"name": "orderby", "in": "query", "description": "Order by", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "Limit", "required": false, "type": "string"}, {"name": "next", "in": "query", "description": "Next", "required": false, "type": "string"}, {"name": "location", "in": "query", "description": "Location", "required": false, "type": "string"}], "responses": {"200": {"description": "A successful response, returns a list of listings in data and next link", "schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object"}}, "next": {"type": "string"}}}}, "500": {"description": "An error occurred", "schema": {"type": "string"}}}}}, "/listing": {"get": {"summary": "Get listing", "description": "Get listing by listingId", "operationId": "listing", "x-google-backend": {"address": "https://us-central1-rm-gaia-dev.cloudfunctions.net/listing"}, "security": [{"api_key": [], "crm_token": []}], "parameters": [{"name": "listingId", "in": "query", "description": "Listing ID", "required": true, "type": "string", "x-example": "**********"}], "responses": {"200": {"description": "A successful response, returns the listing in data", "schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object"}}}}}, "400": {"description": "An error occurred", "schema": {"type": "string"}}}}}, "/matchAccountToMLS": {"get": {"summary": "Match Account to MLS", "description": "Match Account to MLS", "operationId": "matchAccountToMLS", "x-google-backend": {"address": "https://us-central1-rm-gaia-dev.cloudfunctions.net/matchAccountToMLS"}, "security": [{"api_key": [], "crm_token": []}], "parameters": [{"name": "accountId", "in": "query", "description": "Account ID", "required": true, "type": "string", "x-example": "**********"}, {"name": "firstName", "in": "query", "description": "First Name", "required": true, "type": "string", "x-example": "<PERSON>"}, {"name": "lastName", "in": "query", "description": "Last Name", "required": true, "type": "string", "x-example": "<PERSON><PERSON>"}, {"name": "phoneNumber", "in": "query", "description": "Phone Number", "required": true, "type": "string", "x-example": "**********"}, {"name": "address1", "in": "query", "description": "Address 1", "required": false, "type": "string", "x-example": "123 Main St"}, {"name": "city", "in": "query", "description": "City", "required": false, "type": "string", "x-example": "New York"}, {"name": "state", "in": "query", "description": "State abbreviation", "required": false, "type": "string", "x-example": "NY"}, {"name": "postalCode", "in": "query", "description": "Postal Code", "required": false, "type": "string", "x-example": "10001"}, {"name": "updateMLS", "in": "query", "description": "Update MLS, used to update the existing MLS data, defaults to false", "required": false, "type": "boolean", "x-example": "true"}], "responses": {"200": {"description": "A successful response, returns the member if matched, or if already saved, returns the existing member, or returns a list of guesses if no match found", "schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object"}}, "source": {"type": "string"}}}}, "400": {"description": "Missing required parameters: accountId, firstName, lastName, phoneNumber", "schema": {"type": "string"}}, "500": {"description": "An error occurred", "schema": {"type": "string"}}}}}, "/saveAccountMLS": {"get": {"summary": "Save Account MLS", "description": "Save Account MLS", "operationId": "saveAccountMLS", "x-google-backend": {"address": "https://us-central1-rm-gaia-dev.cloudfunctions.net/saveAccountMLS"}, "security": [{"api_key": [], "crm_token": []}], "parameters": [{"name": "accountId", "in": "query", "description": "Account ID", "required": true, "type": "string", "x-example": "**********"}, {"name": "firstName", "in": "query", "description": "First Name", "required": true, "type": "string", "x-example": "<PERSON>"}], "responses": {"200": {"description": "A successful response, saves the account and the mls data to the firestore", "schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object"}}, "source": {"type": "string"}}}}, "400": {"description": "Missing required parameters: accountId, firstName", "schema": {"type": "string"}}, "500": {"description": "An error occurred", "schema": {"type": "string"}}}}}, "/member": {"get": {"summary": "Get member", "description": "Returns the member by filter, orderby, limit and location", "operationId": "member", "x-google-backend": {"address": "https://us-central1-rm-gaia-dev.cloudfunctions.net/member"}, "security": [{"api_key": [], "crm_token": []}], "parameters": [{"name": "filter", "in": "query", "description": "Filter", "required": false, "type": "string"}, {"name": "orderby", "in": "query", "description": "Order by", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "Limit", "required": false, "type": "string"}, {"name": "next", "in": "query", "description": "Next", "required": false, "type": "string"}, {"name": "location", "in": "query", "description": "Location", "required": false, "type": "string"}], "responses": {"200": {"description": "A successful response, returns a list of members in data and next link", "schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object"}}, "next": {"type": "string"}}}}, "500": {"description": "An error occurred", "schema": {"type": "string"}}}}}, "/updateMlsData": {"patch": {"summary": "Invoke updateMlsData", "description": "Invoke the updateMlsData function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "updateMlsData", "x-google-backend": {"address": "https://us-central1-rm-gaia-dev.cloudfunctions.net/updateMlsData"}, "security": [{"api_key": [], "crm_token": []}]}}}}