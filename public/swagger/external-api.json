{"openapi": "3.0.0", "info": {"title": "ReminderMedia External API", "version": "1.0", "description": "API for managing ReminderMedia business objects"}, "servers": [{"url": "https://us-central1-rm-titan-dev.cloudfunctions.net", "description": "Development server"}], "paths": {"/api/v1/recipients": {"get": {"summary": "Retrieve a paginated list of Recipients", "description": "Returns a list of active Recipients with their associated contact information and details", "tags": ["Recipients"], "operationId": "getRecipients", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "Page number for pagination", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "pageSize", "in": "query", "description": "Number of items per page (max 100)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "sort_by", "in": "query", "description": "Field to sort by", "required": false, "schema": {"type": "string", "enum": ["createdAt", "updatedAt"], "default": "updatedAt"}}, {"name": "sort_order", "in": "query", "description": "Sort order direction", "required": false, "schema": {"type": "string", "enum": ["asc", "desc"], "default": "desc"}}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Recipient"}}, "pagination": {"type": "object", "properties": {"currentPage": {"type": "integer", "example": 1}, "totalPages": {"type": "integer", "example": 10}, "totalRecords": {"type": "integer", "example": 195}, "pageSize": {"type": "integer", "example": 20}}}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"summary": "Create new Recipient", "description": "Creates a new Recipient with associated contact information and details", "tags": ["Recipients"], "operationId": "createRecipient", "security": [{"ApiKeyAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/RecipientInput"}, {"type": "array", "items": {"$ref": "#/components/schemas/RecipientInput"}}]}}}}, "responses": {"201": {"description": "Recipient created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 201}, "message": {"type": "string", "example": "Recipients created successfully"}, "data": {"oneOf": [{"$ref": "#/components/schemas/Recipient"}, {"type": "array", "items": {"$ref": "#/components/schemas/Recipient"}}]}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 400}, "error": {"type": "string", "example": "Bad Request"}, "message": {"type": "string", "example": "First name and last name are required for all recipients."}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/recipients/{recipientId}": {"put": {"summary": "Update an existing recipient", "description": "Updates a Recipient's information, allowing partial updates of main fields and **complete replacement of sub-collections**.\n## WARNING: All sub-collection items will be removed and replaced with what items are provided in the payload.\n", "tags": ["Recipients"], "operationId": "updateRecipient", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "recipientId", "in": "path", "description": "ID of the Recipient to update", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "object", "properties": {"firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}, "middle": {"type": "string", "example": "V", "nullable": true}, "nickname": {"type": "string", "example": "<PERSON>", "nullable": true}, "prefix": {"type": "string", "example": "Mr", "nullable": true}, "suffix": {"type": "string", "example": "XIII", "nullable": true}}}, "significantOther": {"type": "object", "properties": {"firstName": {"type": "string", "example": "<PERSON>", "nullable": true}, "lastName": {"type": "string", "example": "<PERSON><PERSON>", "nullable": true}}}, "emailAddresses": {"type": "array", "description": "If provided, replaces all existing email addresses", "items": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "label": {"type": "string", "example": "Personal"}, "primary": {"type": "boolean", "example": true}}}}, "phoneNumbers": {"type": "array", "description": "If provided, replaces all existing phone numbers", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Home"}, "phoneNumber": {"type": "string", "example": "+**********"}, "phoneExtension": {"type": "string", "example": "123"}, "primary": {"type": "boolean", "example": true}}}}, "notes": {"type": "array", "description": "If provided, replaces all existing notes", "items": {"type": "object", "properties": {"value": {"type": "string", "example": "Birthday coming up"}}}}, "significantDates": {"type": "array", "description": "If provided, replaces all existing significant dates", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Birthday"}, "date": {"type": "string", "format": "date", "example": "1990-01-01"}}}}, "socialMediaAccounts": {"type": "array", "description": "If provided, replaces all existing social media accounts", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Twitter"}, "value": {"type": "string", "example": "@johndoe"}}}}, "userEnteredMailingAddresses": {"type": "array", "description": "If provided, replaces all existing mailing addresses", "items": {"type": "object", "properties": {"address1": {"type": "string", "example": "639 Bluegrass Trail"}, "address2": {"type": "string", "example": "Apt C"}, "city": {"type": "string", "example": "Kokomo"}, "state": {"type": "string", "example": "IN"}, "postalCode": {"type": "string", "example": "46901"}, "label": {"type": "string", "example": "Home"}}}}, "websites": {"type": "array", "description": "If provided, replaces all existing websites", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Portfolio"}, "url": {"type": "string", "format": "uri", "example": "https://johndoe.com"}}}}, "externalIds": {"type": "array", "description": "If provided, merges externalIds with existing externalIds. This is a list of IDs relating to other sites this contact is linked to.", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Follow Up Boss"}, "externalId": {"type": "string", "format": "id", "example": "14343123"}}}}}}}}}, "responses": {"200": {"description": "Recipient updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Recipient updated successfully"}, "data": {"$ref": "#/components/schemas/Recipient"}}}}}}, "404": {"description": "Recipient not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 404}, "error": {"type": "string", "example": "Not Found"}, "message": {"type": "string", "example": "Recipient not found."}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "delete": {"summary": "Soft delete a Recipient", "description": "Marks a Recipient as inactive (soft delete)", "tags": ["Recipients"], "operationId": "deleteRecipient", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "recipientId", "in": "path", "description": "ID of the Recipient to delete", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Recipient successfully deleted", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Recipient has been successfully deleted"}}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 400}, "error": {"type": "string", "example": "Recipient ID is required"}}}}}}, "404": {"description": "Recipient not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 404}, "error": {"type": "string", "example": "Recipient not found"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/recipient-groups": {"get": {"summary": "List Recipient Groups", "description": "Retrieves a paginated list of active Recipient Groups", "tags": ["Recipient Groups"], "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1, "minimum": 1}, "description": "Page number for pagination"}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "default": 20, "minimum": 1, "maximum": 100}, "description": "Number of items per page"}, {"name": "sort_by", "in": "query", "schema": {"type": "string", "enum": ["createdAt", "updatedAt"], "default": "updatedAt"}, "description": "Field to sort by"}, {"name": "sort_order", "in": "query", "schema": {"type": "string", "enum": ["asc", "desc"], "default": "desc"}, "description": "Sort order direction"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "enum": [200]}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RecipientGroup"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponse"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"summary": "Create Recipient Group", "description": "Creates a new Recipient Group", "tags": ["Recipient Groups"], "security": [{"ApiKeyAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Name of the Recipient Group"}, "description": {"type": "string", "description": "Optional description of the Recipient Group"}}}}}}, "responses": {"201": {"description": "Recipient Group created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "enum": [201]}, "data": {"$ref": "#/components/schemas/RecipientGroup"}}}}}}, "400": {"description": "Bad request - missing required fields", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/recipient-groups/{groupId}": {"parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID of the Recipient Group"}], "put": {"summary": "Update Recipient Group", "description": "Updates an existing Recipient Group, allowing partial updates of main fields and **complete replacement of sub-collections**.\n## WARNING: All sub-collection items will be removed and replaced with what items are provided in the payload.\n", "tags": ["Recipient Groups"], "security": [{"ApiKeyAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Updated name of the Recipient Group"}, "description": {"type": "string", "description": "Updated description of the Recipient Group"}}}}}}, "responses": {"200": {"description": "Recipient Group updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "enum": [200]}, "data": {"$ref": "#/components/schemas/RecipientGroup"}}}}}}, "400": {"description": "Bad request - missing required fields", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Recipient Group not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "delete": {"summary": "Delete Recipient Group", "description": "Soft deletes a Recipient Group by marking it as inactive", "tags": ["Recipient Groups"], "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Recipient Group deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "enum": [200]}, "message": {"type": "string"}}}}}}, "404": {"description": "Recipient Group not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/custom-fields": {"get": {"summary": "List Custom Fields", "description": "Retrieves a paginated list of active Custom Fields", "tags": ["Custom Fields"], "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1, "minimum": 1}, "description": "Page number for pagination"}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "default": 20, "minimum": 1, "maximum": 100}, "description": "Number of items per page"}, {"name": "sort_by", "in": "query", "schema": {"type": "string", "enum": ["createdAt", "updatedAt"], "default": "updatedAt"}, "description": "Field to sort by"}, {"name": "sort_order", "in": "query", "schema": {"type": "string", "enum": ["asc", "desc"], "default": "desc"}, "description": "Sort order direction"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "enum": [200]}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CustomFields"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponse"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"summary": "Create Custom Field", "description": "Creates a new Custom Field", "tags": ["Custom Fields"], "security": [{"ApiKeyAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"label": {"type": "string", "description": "Name of the Custom Field"}, "type": {"type": "string", "description": "Type of the Custom Field (valid values: text, number, date, email, phone, currency, url)"}, "isActive": {"type": "boolean", "description": "Is the Custom Field active"}, "sortable": {"type": "boolean", "description": "Is the Custom Field sortable"}}}}}}, "responses": {"201": {"description": "Recipient Group created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "enum": [201]}, "data": {"$ref": "#/components/schemas/CustomFields"}}}}}}, "400": {"description": "Bad request - missing required fields", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/custom-fields/{customFieldsId}": {"parameters": [{"name": "customFieldsId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID of the Custom Field"}], "get": {"summary": "Get Custom Field", "description": "Gets a single Custom Field.\n", "tags": ["Custom Fields"], "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "enum": [200]}, "data": {"$ref": "#/components/schemas/CustomFields"}}}}}}, "400": {"description": "Bad request - missing required fields", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Custom Field not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "put": {"summary": "Update Custom Field", "description": "Updates an existing Custom Field, allowing partial updates of label, isActive, and sortable fields.\n", "tags": ["Custom Fields"], "security": [{"ApiKeyAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"label": {"type": "string", "description": "Updated name of the Custom Field"}, "isActive": {"type": "boolean", "description": "Is the Custom Field active"}, "sortable": {"type": "boolean", "description": "Is the Custom Field sortable"}}}}}}, "responses": {"200": {"description": "Custom Field updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "enum": [200]}, "data": {"$ref": "#/components/schemas/CustomFields"}}}}}}, "400": {"description": "Bad request - missing required fields", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Custom Field not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "delete": {"summary": "Delete Custom Field", "description": "Hard deletes a Custom Field", "tags": ["Custom Fields"], "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Custom Field deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "enum": [200]}, "message": {"type": "string"}}}}}}, "404": {"description": "Custom Field not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key"}}, "schemas": {"Recipient": {"type": "object", "properties": {"id": {"type": "string", "example": "6dwSBCxEL6UuX7wPehX7"}, "accountId": {"type": "string"}, "name": {"type": "object", "properties": {"firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}, "middle": {"type": "string", "example": "V"}, "nickname": {"type": "string", "example": "<PERSON>"}, "prefix": {"type": "string", "example": "Mr"}, "suffix": {"type": "string", "example": "XIII"}}}, "recipientGroupIds": {"type": "array", "items": {"type": "string"}}, "significantOther": {"type": "object", "properties": {"firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}}}, "emailAddresses": {"type": "array", "items": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "label": {"type": "string", "example": "Personal"}, "primary": {"type": "boolean", "example": true}}}}, "phoneNumbers": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Home"}, "phoneNumber": {"type": "string", "example": "+**********"}, "phoneExtension": {"type": "string", "example": "123"}, "primary": {"type": "boolean", "example": true}}}}, "notes": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "string", "example": "Birthday coming up"}}}}, "significantDates": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Birthday"}, "date": {"type": "string", "format": "date-time"}}}}, "socialMediaAccounts": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Twitter"}, "value": {"type": "string", "example": "@johndoe"}}}}, "userEnteredMailingAddresses": {"type": "array", "items": {"type": "object", "properties": {"address1": {"type": "string", "example": "639 Bluegrass Trail"}, "address2": {"type": "string", "example": "Apt C"}, "city": {"type": "string", "example": "Kokomo"}, "state": {"type": "string", "example": "IN"}, "postalCode": {"type": "string", "example": "46901"}, "label": {"type": "string", "example": "Home"}}}}, "websites": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Portfolio"}, "url": {"type": "string", "format": "uri", "example": "https://johndoe.com"}}}}, "externalIds": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Follow Up Boss"}, "externalId": {"type": "string", "format": "id", "example": "********"}}}}, "source": {"type": "string", "example": "Follow Up Boss"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "accountId", "name"]}, "RecipientInput": {"type": "object", "required": ["name", "recipientGroupIds"], "properties": {"name": {"type": "object", "required": ["firstName", "lastName"], "properties": {"firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}, "middle": {"type": "string", "example": "V"}, "nickname": {"type": "string", "example": "<PERSON>"}, "prefix": {"type": "string", "example": "Mr"}, "suffix": {"type": "string", "example": "XIII"}}}, "recipientGroupIds": {"type": "array", "items": {"type": "string"}}, "significantOther": {"type": "object", "properties": {"firstName": {"type": "string", "example": "<PERSON>", "nullable": true}, "lastName": {"type": "string", "example": "<PERSON><PERSON>", "nullable": true}}}, "emailAddresses": {"type": "array", "items": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "label": {"type": "string", "example": "Personal"}, "primary": {"type": "boolean", "example": true}}}}, "phoneNumbers": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Home"}, "phoneNumber": {"type": "string", "example": "+**********"}, "phoneExtension": {"type": "string", "example": "123"}, "primary": {"type": "boolean", "example": true}}}}, "notes": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "string", "example": "Birthday coming up"}}}}, "significantDates": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Birthday"}, "date": {"type": "string", "format": "date", "example": "1990-01-01"}}}}, "socialMediaAccounts": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Twitter"}, "value": {"type": "string", "example": "@johndoe"}}}}, "userEnteredMailingAddresses": {"type": "array", "items": {"type": "object", "properties": {"address1": {"type": "string", "example": "639 Bluegrass Trail"}, "address2": {"type": "string", "example": "Apt C"}, "city": {"type": "string", "example": "Kokomo"}, "state": {"type": "string", "example": "IN"}, "postalCode": {"type": "string", "example": "46901"}, "label": {"type": "string", "example": "Home"}}}}, "websites": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Portfolio"}, "url": {"type": "string", "format": "uri", "example": "https://johndoe.com"}}}}, "externalIds": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "example": "Follow Up Boss"}, "externalId": {"type": "string", "format": "id", "example": "********"}}}}}}, "RecipientGroup": {"type": "object", "required": ["name"], "properties": {"id": {"type": "string", "description": "Unique identifier for the Recipient Group"}, "name": {"type": "string", "description": "Name of the Recipient Group"}, "description": {"type": "string", "description": "Optional description of the Recipient Group"}, "assignments": {"type": "array", "items": {"type": "string"}, "description": "List of product types assigned to the Recipient Group"}, "productPlans": {"type": "object", "description": "Product plans associated with the Recipient Group"}, "createdAt": {"type": "string", "format": "date-time", "description": "Timestamp when the Recipient Group was created"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Timestamp when the Recipient Group was last updated"}}}, "CustomFields": {"type": "object", "required": ["label"], "properties": {"id": {"type": "string", "description": "Unique identifier for the Custom Field"}, "name": {"type": "string", "description": "Name of the Custom Field. This is set automatically when the Custom Field is created."}, "label": {"type": "string", "description": "Label (display name) of the Custom Field"}, "type": {"type": "string", "description": "Type of the Custom Field (valid values: text, number, date, email, phone, currency, url)"}, "isActive": {"type": "boolean", "description": "Is the Custom Field active"}, "sortable": {"type": "boolean", "description": "Is the Custom Field sortable"}, "createdAt": {"type": "string", "format": "date-time", "description": "Timestamp when the Custom Field was created"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Timestamp when the Custom Field was last updated"}}}, "PaginationResponse": {"type": "object", "properties": {"currentPage": {"type": "integer", "minimum": 1, "description": "Current page number"}, "totalPages": {"type": "integer", "minimum": 1, "description": "Total number of pages"}, "totalRecords": {"type": "integer", "minimum": 0, "description": "Total number of records"}, "pageSize": {"type": "integer", "minimum": 1, "maximum": 100, "description": "Number of records per page"}}}, "Error": {"type": "object", "properties": {"status": {"type": "integer", "example": 500}, "error": {"type": "string", "example": "Internal server error"}, "details": {"type": "string", "example": "Error message details"}}}}}}