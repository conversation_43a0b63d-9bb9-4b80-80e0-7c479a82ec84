{"swagger": "2.0", "info": {"title": "ReminderMedia Mercury Server", "description": "Server Structure for Mercury Functions", "version": "1.0.0"}, "host": "us-central1-rm-mercury-dev.cloudfunctions.net", "schemes": ["https"], "produces": ["application/json"], "securityDefinitions": {"api_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "key", "in": "query"}, "rmc_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"}, "socialmedia_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"}, "titan_token": {"authorizationUrl": "", "flow": "implicit", "type": "oauth2", "x-google-issuer": "<EMAIL>", "x-google-jwks_uri": "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"}}, "tags": [{"name": "Schedule Post"}, {"name": "Legacy"}, {"name": "Facebook Tokens"}, {"name": "Facebook Pages"}, {"name": "Instagram Accounts"}, {"name": "Facebook Ads"}, {"name": "Facebook Leads"}, {"name": "Facebook Posts"}], "paths": {"/getPosts": {"get": {"tags": ["Schedule Post"], "summary": "Get posts", "description": "Retrieve posts for a certain account.", "parameters": [{"name": "accountId", "in": "query", "description": "get posts for this accountId", "required": true, "type": "string"}, {"name": "date", "in": "query", "description": "specify the date of the posts.", "required": false, "type": "string", "default": "current date"}, {"name": "datePreset", "in": "query", "description": "specify the date preset.", "required": false, "type": "string", "default": "today", "enum": ["today", "this_month"]}, {"name": "pageSize", "in": "query", "description": "specify the count of posts per page.", "required": false, "type": "string", "default": "20"}, {"name": "before", "in": "query", "description": "specify the post id that we need to fetch posts before it.", "required": false, "type": "string"}, {"name": "after", "in": "query", "description": "specify the post id that we need to fetch ads after it.", "required": false, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/SocialPosts"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getPosts", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getPosts"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/schedulePost": {"post": {"tags": ["Schedule Post"], "summary": "Schedule post", "description": "Schedule a post to be shared on specified social media platforms.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Payload to schedule the post", "required": true, "schema": {"$ref": "#/definitions/CreateOrUpdatePostRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "schedulePost", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/schedulePost"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/publishPostLegacy": {"post": {"tags": ["Schedule Post"], "summary": "Publish post legacy", "description": "Publish post legacy.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Payload to schedule the post", "required": true, "schema": {"$ref": "#/definitions/PublishPostLegacyRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/PublishPostLegacyResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "publishPostLegacy", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/publishPostLegacy"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/updateSchedulePost": {"post": {"tags": ["Schedule Post"], "summary": "Update a scheduled post", "description": "Update an already scheduled post.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "Upate post", "required": true, "schema": {"$ref": "#/definitions/CreateOrUpdatePostRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "updateSchedulePost", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/updateSchedulePost"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/checkPublishPostStatus": {"get": {"tags": ["Schedule Post"], "summary": "Check publish post status", "description": "Check publish post status.", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "publishPostID", "in": "query", "description": "process publish post id to check status", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/PublishPostLegacyResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "checkPublishPostStatus", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/checkPublishPostStatus"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/deleteSchedulePost": {"patch": {"summary": "Invoke deleteSchedulePost", "description": "Invoke the deleteSchedulePost function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "deleteSchedulePost", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/deleteSchedulePost"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/updateBasicAccountInfo": {"post": {"summary": "Updates basic account information from Titan", "description": "Updates basic account information from Titan", "parameters": [{"in": "body", "name": "body", "description": "Basic Account Information", "required": true, "schema": {"$ref": "#/definitions/AccountInfoBasic"}}], "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "updateBasicAccountInfo", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/updateBasicAccountInfo"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/saveToken": {"post": {"tags": ["Facebook Tokens"], "summary": "Save Facebook Token", "description": "Replace short lived token with long lived token and save it in DB. Sync all facebook assets.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/SaveTokenRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/FacebookTokenResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "saveToken", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/saveToken"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getToken": {"get": {"tags": ["Facebook Tokens"], "summary": "Get Facebook Token", "description": "Retrieve facebook token for a certain account.", "parameters": [{"name": "accountId", "in": "query", "description": "get token for this accountId", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/FacebookTokenResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getToken", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getToken"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getPages": {"get": {"tags": ["Facebook Pages"], "summary": "Get facebook pages", "description": "Retrieve facebook pages for a certain account.", "parameters": [{"name": "accountId", "in": "query", "description": "get pages for this accountId", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/FacebookPagesResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getPages", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getPages"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getDefaultFacebookPage": {"get": {"tags": ["Facebook Pages"], "summary": "Get default facebook page", "description": "Retrieve default facebook page for a certain account.", "parameters": [{"name": "accountId", "in": "query", "description": "get default page for this accountId", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/FacebookPageResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getDefaultFacebookPage", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getDefaultFacebookPage"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/setDefaultFacebookPage": {"post": {"tags": ["Facebook Pages"], "summary": "Set default facebook page", "description": "Set default facebook page for a certain account.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/setDefaultFacebookPageRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/FacebookPageResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "setDefaultFacebookPage", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/setDefaultFacebookPage"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getInstagramAccounts": {"get": {"tags": ["Instagram Accounts"], "summary": "Get instagram accounts", "description": "Retrieve instagram accounts for a certain account.", "parameters": [{"name": "accountId", "in": "query", "description": "get instagram accounts for this accountId", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/InstagramAccountsResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getInstagramAccounts", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getInstagramAccounts"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getDefaultInstagramAccount": {"get": {"tags": ["Instagram Accounts"], "summary": "Get default instagram account", "description": "Retrieve default instagram account for a certain account.", "parameters": [{"name": "accountId", "in": "query", "description": "get default instagram account for this accountId", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/InstagramAccountResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getDefaultInstagramAccount", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getDefaultInstagramAccount"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/setDefaultInstagramAccount": {"post": {"tags": ["Instagram Accounts"], "summary": "Set default instagram account", "description": "Set default instagram account for a certain account.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/setDefaultInstagramAccountRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/InstagramAccountResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "setDefaultInstagramAccount", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/setDefaultInstagramAccount"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getAd": {"get": {"tags": ["Facebook Ads"], "summary": "Get facebook ad by id", "description": "Retrieve facebook ad by id.", "parameters": [{"name": "accountId", "in": "query", "description": "get facebook ad for this accountId", "required": true, "type": "string"}, {"name": "adId", "in": "query", "description": "get facebook ad with this id.", "required": true, "type": "string"}, {"name": "datePreset", "in": "query", "description": "specify the insights date preset.", "required": false, "type": "string", "enum": ["last_7d", "last_30d", "this_month", "maximum"]}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/FacebookAdsResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getAd", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getAd"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getAds": {"get": {"tags": ["Facebook Ads"], "summary": "Get facebook ads", "description": "Retrieve facebook ads for a certain account which created through RM.", "parameters": [{"name": "accountId", "in": "query", "description": "get facebook ads for this accountId", "required": true, "type": "string"}, {"name": "adStatus", "in": "query", "description": "filter by ad status.", "required": false, "type": "string", "enum": ["PAUSED", "ACTIVE"]}, {"name": "datePreset", "in": "query", "description": "specify the insights date preset.", "required": false, "type": "string", "enum": ["last_7d", "last_30d", "this_month", "maximum"]}, {"name": "pageSize", "in": "query", "description": "specify the count of ads per page.", "required": false, "type": "string", "default": "20"}, {"name": "before", "in": "query", "description": "specify the ad id that we need to fetch ads before it.", "required": false, "type": "string"}, {"name": "after", "in": "query", "description": "specify the ad id that we need to fetch ads after it.", "required": false, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/FacebookAdsResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getAds", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getAds"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/syncAds": {"post": {"tags": ["Facebook Ads"], "summary": "Sync facebook ads", "description": "Sync facebook ads for a certain account which created through RM.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/BasicRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "syncAds", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/syncAds"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/updateAdStatus": {"post": {"tags": ["Facebook Ads"], "summary": "Update facebook ad status", "description": "Update facebook ad status for a certain ad.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateAdStatusRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "updateAdStatus", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/updateAdStatus"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/updateAdSetStatus": {"post": {"tags": ["Facebook AdSets"], "summary": "Update facebook adSet status", "description": "Update facebook adSet status for a certain adSet.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateAdSetStatusRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "updateAdSetStatus", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/updateAdSetStatus"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/deleteDefaultAdsFacebookPage": {"post": {"tags": ["Facebook Pages"], "summary": "Delete default ads facebook page", "description": "Delete default ads facebook page for a certain account.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/BasicRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "deleteDefaultAdsFacebookPage", "x-goolge-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/deleteDefaultAdsFacebookPage"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}], "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/deleteDefaultAdsFacebookPage"}}}, "/deleteDefaultFacebookPage": {"post": {"tags": ["Facebook Pages"], "summary": "Delete default facebook page", "description": "Delete default facebook page for a certain account.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/BasicRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "deleteDefaultFacebookPage", "x-goolge-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/deleteDefaultFacebookPage"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}], "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/deleteDefaultFacebookPage"}}}, "/deleteDefaultInstagramAccount": {"post": {"tags": ["Instagram Accounts"], "summary": "Delete default instagram account", "description": "Delete default instagram account for a certain account.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/BasicRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "deleteDefaultInstagramAccount", "x-goolge-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/deleteDefaultInstagramAccount"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}], "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/deleteDefaultInstagramAccount"}}}, "/disconnectFBConnection": {"post": {"tags": ["Facebook Connections"], "summary": "Disconnects FB connection", "description": "Disconnects FB connection for an account and all default pages and account.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/BasicRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "disconnectFBConnection", "x-goolge-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/disconnectFBConnection"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}], "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/disconnectFBConnection"}}}, "/getDefaultAdsFacebookPage": {"get": {"tags": ["Facebook Pages"], "summary": "Get default Ads facebook page", "description": "Retrieve default ads facebook page for a certain account.", "parameters": [{"name": "accountId", "in": "query", "description": "get default ads page for this accountId", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/FacebookPageResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getDefaultAdsFacebookPage", "x-goolge-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getDefaultAdsFacebookPage"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}], "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getDefaultAdsFacebookPage"}}}, "/getLegacyAccount": {"get": {"tags": ["Legacy"], "summary": "Get Legacy default connection", "description": "Get default connections with legacy format.", "parameters": [{"name": "accountId", "in": "query", "description": "get default connection for this accountId", "required": true, "type": "string"}, {"name": "type", "in": "query", "description": "type of connection", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/LegacyAccountResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getLegacyAccount", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getLegacyAccount"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/syncRMCLeads": {"post": {"tags": ["Legacy"], "summary": "Sync RMC Leads to Mercury DB", "description": "Sync RMC Leads to Mercury DB.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/syncRMCLeadsRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "syncRMCLeads", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/syncRMCLeads"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getAccountsConnectedToPage": {"get": {"tags": ["Legacy"], "summary": "Get Legacy default connection connected to certain page", "description": "Get default connections connected to certain page with legacy format.", "parameters": [{"name": "pageId", "in": "query", "description": "get default connection for this pageId", "required": true, "type": "string"}, {"name": "type", "in": "query", "description": "type of connection", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/LegacyAccountsResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getAccountsConnectedToPage", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getAccountsConnectedToPage"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getMercuryAccount": {"get": {"tags": ["Accounts"], "summary": "<PERSON> Account", "description": "Retrieve mercury account.", "parameters": [{"name": "accountId", "in": "query", "description": "get account for this id", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/AccountInfoBasic"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getMercuryAccount", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getMercuryAccount"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/setDefaultAdsFacebookPage": {"post": {"tags": ["Facebook Pages"], "summary": "Set default ads facebook page", "description": "Set default ads facebook page for a certain account.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/setDefaultFacebookPageRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/FacebookPageResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "setDefaultAdsFacebookPage", "x-goolge-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/setDefaultAdsFacebookPage"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}], "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/setDefaultAdsFacebookPage"}}}, "/createGroupConversation": {"patch": {"summary": "Invoke createGroupConversation", "description": "Invoke the createGroupConversation function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "createGroupConversation", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/createGroupConversation"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/createInstagramPost": {"patch": {"summary": "Invoke createInstagramPost", "description": "Invoke the createInstagramPost function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "createInstagramPost", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/createInstagramPost"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getLead": {"get": {"tags": ["Facebook Leads"], "summary": "Get facebook lead by id", "description": "Retrieve facebook lead by id.", "parameters": [{"name": "accountId", "in": "query", "description": "get facebook leads for this accountId", "required": true, "type": "string"}, {"name": "leadId", "in": "query", "description": "Facebook lead external id to fetch the lead.", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/FacebookLeadResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getLead", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getLead"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getLeads": {"get": {"tags": ["Facebook Leads"], "summary": "Get facebook leads", "description": "Retrieve facebook leads for a certain account which created through RM.", "parameters": [{"name": "accountId", "in": "query", "description": "get facebook leads for this accountId", "required": true, "type": "string"}, {"name": "adId", "in": "query", "description": "filter by ad id.", "required": false, "type": "string"}, {"name": "pageSize", "in": "query", "description": "specify the count of leads per page.", "required": false, "type": "string", "default": "25"}, {"name": "before", "in": "query", "description": "specify the lead docId that we need to fetch leads before it.", "required": false, "type": "string"}, {"name": "after", "in": "query", "description": "specify the lead docId that we need to fetch ads after it.", "required": false, "type": "string"}, {"name": "orderBy", "in": "query", "description": "specify the order by field.", "required": false, "type": "string", "default": "createdTime", "enum": ["firstName", "createdTime", "submissions"]}, {"name": "orderDir", "in": "query", "description": "specify the order direction of leads.", "required": false, "type": "string", "default": "desc", "enum": ["asc", "desc"]}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/FacebookLeadsResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getLeads", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getLeads"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getRecentLeadsCount": {"get": {"tags": ["Facebook Leads"], "summary": "Get facebook recent leads count ", "description": "Retrieve facebook recent leads for a certain account during the last day.", "parameters": [{"name": "accountId", "in": "query", "description": "get facebook leads count for this accountId", "required": true, "type": "string"}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/CountResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "getRecentLeadsCount", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getRecentLeadsCount"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/updateLead": {"post": {"tags": ["Facebook Leads"], "summary": "Update facebook lead", "description": "Update facebook lead.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateLeadRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "updateLead", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/updateLead"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/createLead": {"post": {"tags": ["Facebook Leads"], "summary": "Create facebook lead", "description": "Create facebook lead.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateLeadRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "createLead", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/createLead"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/testInstagram": {"patch": {"summary": "Invoke testInstagram", "description": "Invoke the testInstagram function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "testInstagram", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/testInstagram"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getAccount": {"patch": {"summary": "Invoke getAccount", "description": "Invoke the getAccount function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "getAccount", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getAccount"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/postAccount": {"patch": {"summary": "Invoke postAccount", "description": "Invoke the postAccount function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "postAccount", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/postAccount"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/postCreateCustomPosts": {"patch": {"summary": "Invoke postCreateCustomPosts", "description": "Invoke the postCreateCustomPosts function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "postCreateCustomPosts", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/postCreateCustomPosts"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/availablePhoneNumbers": {"patch": {"summary": "Invoke availablePhoneNumbers", "description": "Invoke the availablePhoneNumbers function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/facebookWebhooks": {"patch": {"summary": "Invoke facebookWebhooks", "description": "Invoke the facebookWebhooks function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/incomingMessageLogsByPhoneNumber": {"patch": {"summary": "Invoke incomingMessageLogsByPhoneNumber", "description": "Invoke the incomingMessageLogsByPhoneNumber function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/messagingEventStream": {"patch": {"summary": "Invoke messagingEventStream", "description": "Invoke the messagingEventStream function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/outgoingMessageLogsByPhoneNumber": {"patch": {"summary": "Invoke outgoingMessageLogsByPhoneNumber", "description": "Invoke the outgoingMessageLogsByPhoneNumber function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/postingFailed": {"post": {"tags": ["Schedule Post"], "summary": "Notify Mercury that posting to facebook failed", "description": "Notify Mercury that posting to facebook failed.", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/postingFailedRequest"}}], "responses": {"200": {"description": "Successful operation", "schema": {"$ref": "#/definitions/BasicResponse"}}, "400": {"description": "Failed operation", "schema": {"$ref": "#/definitions/ErrorResponse"}}}, "operationId": "postingFailed", "x-goolge-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/postingFailed"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}], "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/postingFailed"}}}, "/purchasePhoneNumber": {"patch": {"summary": "Invoke purchasePhoneNumber", "description": "Invoke the purchasePhoneNumber function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/raiaResponseHandler": {"patch": {"summary": "Invoke raiaR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Invoke the raiaResponse<PERSON><PERSON><PERSON> function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/releasePhoneNumber": {"patch": {"summary": "Invoke releasePhoneNumber", "description": "Invoke the releasePhoneNumber function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/syncIncomingNumbersToAccounts": {"patch": {"summary": "Invoke syncIncomingNumbersToAccounts", "description": "Invoke the syncIncomingNumbersToAccounts function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/twilioCallbackWebhook": {"patch": {"summary": "Invoke twilioCallbackWebhook", "description": "Invoke the twilioCallbackWebhook function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/twilioFallbackWebhook": {"patch": {"summary": "Invoke twilioFallbackWebhook", "description": "Invoke the twilioFallbackWebhook function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/twilioMessageHandler": {"patch": {"summary": "Invoke twi<PERSON>M<PERSON>ageH<PERSON><PERSON>", "description": "Invoke the twilioMessageHandler function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/twilioRequestWebhook": {"patch": {"summary": "Invoke twilioRequestWebhook", "description": "Invoke the twilioRequestWebhook function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/updateMessagingSid": {"patch": {"summary": "Invoke updateMessagingSid", "description": "Invoke the updateMessagingSid function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/getAllPosts": {"patch": {"summary": "Invoke getAllPosts", "description": "Invoke the getAllPosts function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "getAllPosts", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getAllPosts"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/createSMSChannel": {"patch": {"summary": "Invoke createSMSChannel", "description": "Invoke the createSMSChannel function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "createSMSChannel", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/createSMSChannel"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getPaginatedPosts": {"patch": {"summary": "Invoke getPaginatedPosts", "description": "Invoke the getPaginatedPosts function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "getPaginatedPosts", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getPaginatedPosts"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/testGetAllPosts": {"patch": {"summary": "Invoke testGetAllPosts", "description": "Invoke the testGetAllPosts function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/deleteScheduleCustomPost": {"patch": {"summary": "Invoke deleteScheduleCustomPost", "description": "Invoke the deleteScheduleCustomPost function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "deleteScheduleCustomPost", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/deleteScheduleCustomPost"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getCalendarPosts": {"patch": {"summary": "Invoke getCalendarPosts", "description": "Invoke the getCalendarPosts function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "getCalendarPosts", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getCalendarPosts"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/getTest": {"patch": {"summary": "Invoke getTest", "description": "Invoke the getTest function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "getTest", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/getTest"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/onMessageAdded": {"patch": {"summary": "Invoke onMessageAdded", "description": "Invoke the onMessageAdded function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "onMessageAdded", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/onMessageAdded"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/purchasePhoneNumberForSMSChannel": {"patch": {"summary": "Invoke purchasePhoneNumberForSMSChannel", "description": "Invoke the purchasePhoneNumberForSMSChannel function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "purchasePhoneNumberForSMSChannel", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/purchasePhoneNumberForSMSChannel"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/scheduleCustomPost": {"patch": {"summary": "Invoke scheduleCustomPost", "description": "Invoke the scheduleCustomPost function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "scheduleCustomPost", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/scheduleCustomPost"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/sendSMSToGroup": {"patch": {"summary": "Invoke sendSMSToGroup", "description": "Invoke the sendSMSToGroup function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "sendSMSToGroup", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/sendSMSToGroup"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/testSdk": {"patch": {"summary": "Invoke testSdk", "description": "Invoke the testSdk function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "testSdk", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/testSdk"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/twilioGroupCallbackWebhook": {"patch": {"summary": "Invoke twilioGroupCallbackWebhook", "description": "Invoke the twilioGroupCallbackWebhook function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "twilioGroupCallbackWebhook", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/twilioGroupCallbackWebhook"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/twilioGroupFallbackWebhook": {"patch": {"summary": "Invoke twilioGroupFallbackWebhook", "description": "Invoke the twilioGroupFallbackWebhook function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "twilioGroupFallbackWebhook", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/twilioGroupFallbackWebhook"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/twilioGroupRequestWebhook": {"patch": {"summary": "Invoke twilioGroupRequestWebhook", "description": "Invoke the twilioGroupRequestWebhook function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "twilioGroupRequestWebhook", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/twilioGroupRequestWebhook"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/updateAdSet": {"patch": {"summary": "Invoke updateAdSet", "description": "Invoke the updateAdSet function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "updateAdSet", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/updateAdSet"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/updateScheduleCustomPost": {"patch": {"summary": "Invoke updateScheduleCustomPost", "description": "Invoke the updateScheduleCustomPost function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "updateScheduleCustomPost", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/updateScheduleCustomPost"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/addSeedDataAdManger": {"patch": {"summary": "Invoke addSeedDataAdManger", "description": "Invoke the addSeedDataAdManger function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "addSeedDataAdManger", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/addSeedDataAdManger"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}, "/testCheckAdsAlerts": {"patch": {"summary": "Invoke testCheckAdsAlerts", "description": "Invoke the testCheckAdsAlerts function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/testCheckUserFacebookActivities": {"patch": {"summary": "Invoke testCheckUserFacebookActivities", "description": "Invoke the testCheckUserFacebookActivities function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}}}, "/testSyncFacebookAdsScheduler": {"patch": {"summary": "Invoke testSyncFacebookAdsScheduler", "description": "Invoke the testSyncFacebookAdsScheduler function", "responses": {"200": {"description": "A successful response", "schema": {"type": "string"}}}, "operationId": "testSyncFacebookAdsScheduler", "x-google-backend": {"address": "https://us-central1-rm-mercury-dev.cloudfunctions.net/testSyncFacebookAdsScheduler"}, "security": [{"api_key": [], "rmc_token": []}, {"api_key": [], "socialmedia_token": []}, {"api_key": [], "titan_token": []}]}}}, "definitions": {"CountResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"type": "object", "properties": {"count": {"type": "integer", "example": 3}}}}}]}, "SinglePost": {"type": "object", "properties": {"id": {"type": "string", "example": "*********"}, "scheduleDateTime": {"type": "string", "example": "2024-07-14T20:00:01.000Z"}, "status": {"type": "string", "example": "scheduled"}, "imagePath": {"type": "string", "example": "scheduledPosts/0/57363209-abec-4e6b-913d-0aeea9c07de3.jpg"}, "caption": {"type": "string", "example": "Text"}, "url": {"type": "string", "example": "https://google.com"}, "imageURLSigned": {"type": "string", "example": "https://scontent-dfw5-2..."}, "shareTo": {"type": "array", "items": {"type": "string", "enum": ["facebook", "instagram"]}}}}, "SocialPosts": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/SinglePost"}}, "metadata": {"type": "object", "properties": {"pageSize": {"type": "string", "example": "20"}, "before": {"type": "string", "example": "120207525115940056"}, "after": {"type": "string", "example": "120207525115940056"}}}}}]}, "CreateOrUpdatePostRequest": {"type": "object", "description": "Payload to schedule the custom post", "properties": {"accountId": {"type": "string", "example": "24"}, "shareTo": {"type": "array", "items": {"type": "string", "enum": ["facebook", "instagram"]}}, "image": {"type": "string", "example": "base44"}, "scheduleDateTime": {"type": "string", "example": "**********"}, "caption": {"type": "string", "example": "Text"}, "url": {"type": "string", "example": "https://google.com"}}}, "PublishPostLegacyRequest": {"type": "object", "description": "Payload to publish legacy post", "allOf": [{"$ref": "#/definitions/BasicRequest"}, {"type": "object", "properties": {"accountId": {"type": "string", "example": "24"}, "shareTo": {"type": "array", "items": {"type": "string", "enum": ["facebook", "instagram"]}}, "mediaTypa": {"type": "string", "example": "image | video | article", "enum": ["image", "video", "article"]}, "imagePath": {"type": "string"}, "scheduleDateTime": {"type": "string", "example": "**********"}, "caption": {"type": "string", "example": "Text"}, "link": {"type": "string", "example": "https://google.com"}, "video": {"type": "string", "example": "https://video.sample.mp4"}}}]}, "AccountInfoBasic": {"type": "object", "description": "General information about the API.", "required": ["accountId", "displayName", "name", "slug", "timezone"], "additionalProperties": false, "properties": {"accountId": {"type": "string", "description": "accountId"}, "displayName": {"type": "string", "description": "displayName"}, "name": {"type": "string", "description": "name"}, "slug": {"type": "string", "description": "url slug"}, "timezone": {"type": "string", "description": "displayName"}}}, "BasicRequest": {"type": "object", "properties": {"accountId": {"type": "string", "example": "********"}}, "required": ["accountId"]}, "BasicResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "success"}, "message": {"type": "string", "example": "Operation done successfully"}}}, "PublishPostLegacyResponse": {"type": "object", "properties": {"id": {"type": "string", "example": "NLqW4PtJONqaNndzaO4q"}, "facebookPageId": {"type": "string", "example": "*********"}, "facebookPostStatus": {"type": "string", "example": "IN_PROGRESS"}, "pageAccessToken": {"type": "string", "example": "EAAKbjtvsMgQBOZBMKgt..."}, "instagramAccountId": {"type": "string", "example": "*********"}, "instagramPostStatus": {"type": "string", "example": "IN_PROGRESS"}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "failure"}, "message": {"type": "string", "example": "Operation failed"}}}, "UpdateAdStatusRequest": {"allOf": [{"$ref": "#/definitions/BasicRequest"}, {"type": "object", "properties": {"adId": {"type": "string", "example": "*********"}, "adStatus": {"type": "string", "enum": ["ACTIVE", "PAUSED"]}}}]}, "UpdateAdSetStatusRequest": {"allOf": [{"$ref": "#/definitions/BasicRequest"}, {"type": "object", "properties": {"adSetId": {"type": "string", "example": "*********"}, "adSetStatus": {"type": "string", "enum": ["ACTIVE", "PAUSED"]}}}]}, "UpdateLeadRequest": {"allOf": [{"$ref": "#/definitions/BasicRequest"}, {"type": "object", "properties": {"leadId": {"type": "string", "example": "*********"}, "status": {"type": "string", "enum": ["NEW", "INTERESTED", "NOT_INTERESTED", "LEFT_VOICE_MAIL", "TRY_AGAIN_LATER", "SENT_TEXT", "CONVERTED_TO_CONTACT"]}}}]}, "CreateLeadRequest": {"allOf": [{"$ref": "#/definitions/BasicRequest"}, {"type": "object", "properties": {"phoneNumber": {"type": "string", "example": "*********"}}}]}, "SaveTokenRequest": {"allOf": [{"$ref": "#/definitions/BasicRequest"}, {"type": "object", "properties": {"shortLivedToken": {"type": "string", "example": "EAAKbjtvsMgQBOZBMKgt..."}}}]}, "LegacyAccountsResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/LegacyAccount"}}}}]}, "LegacyAccountResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/LegacyAccount"}}}]}, "LegacyAccount": {"type": "object", "properties": {"id": {"type": "integer", "example": 0}, "account_id": {"type": "string", "example": "*********"}, "data": {"$ref": "#/definitions/LegacyAccountData"}}}, "LegacyAccountData": {"type": "object", "properties": {"user_id": {"type": "string", "example": "*********"}, "user_name": {"type": "string", "example": "Facebook User"}, "user_access_token": {"type": "string", "example": "EAAKbjtvsMgQBO9fH..."}, "token_expiration": {"type": "string", "example": "*********"}, "user_picture_url": {"type": "string", "example": "https://scontent-dfw5-2..."}, "pages": {"type": "array", "items": {"$ref": "#/definitions/LegacyAccountPage"}}}}, "LegacyAccountPage": {"type": "object", "properties": {"is_default": {"type": "boolean"}, "page_id": {"type": "string", "example": "*********"}, "name": {"type": "string", "example": "Page Name"}, "page_access_token": {"type": "string", "example": "EAAKbjtvsMgQBO9uF..."}, "picture": {"type": "string", "example": "*********"}, "category": {"type": "string", "example": "Clothing"}, "subscribed_fields": {"type": "array", "items": {}}}}, "FacebookTokenResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/FacebookToken"}}}]}, "FacebookToken": {"type": "object", "properties": {"longLivedToken": {"type": "string", "example": "EAAKbjtvsMgQBOZBMKgt..."}}}, "FacebookPagesResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/FacebookPage"}}}}]}, "FacebookPageResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/FacebookPage"}}}]}, "FacebookPage": {"type": "object", "properties": {"id": {"type": "string", "example": "*********"}, "name": {"type": "string", "example": "Page Name"}, "access_token": {"type": "string", "example": "EAAKbjtvsMgQBOZBMKgt..."}, "fan_count": {"type": "integer", "example": 0}, "about": {"type": "string", "example": "About page..."}, "posts": {"type": "array", "items": {"$ref": "#/definitions/FacebookPost"}}, "insights": {"type": "array", "items": {"$ref": "#/definitions/FacebookPageInsight"}}}}, "FacebookPageMinimum": {"type": "object", "properties": {"id": {"type": "string", "example": "*********"}, "name": {"type": "string", "example": "Page Name"}, "access_token": {"type": "string", "example": "EAAKbjtvsMgQBOZBMKgt..."}}}, "FacebookPost": {"type": "object", "properties": {"id": {"type": "string", "example": "*********_987654321"}, "message": {"type": "string", "example": "Post Message..."}, "created_time": {"type": "string", "example": "2023-09-25T06:59:58+0000"}}}, "FacebookPageInsight": {"type": "object", "properties": {"period": {"type": "string", "enum": ["day", "week", "days_28"]}, "id": {"type": "string", "example": "*********/insights/insight_name/period"}, "name": {"type": "string", "example": "insight_name"}, "title": {"type": "string", "example": "Insight title"}, "description": {"type": "string", "example": "Insight description..."}, "values": {"type": "array", "items": {"type": "object", "properties": {"end_time": {"type": "string", "example": "2023-09-25T06:59:58+0000"}, "value": {"type": "integer", "example": 0}}}}}}, "syncRMCLeadsRequest": {"allOf": [{"$ref": "#/definitions/BasicRequest"}, {"type": "object", "properties": {"leads": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "*********"}, "status": {"type": "string", "example": "NEW"}, "contactId": {"type": "integer", "example": 12345}}}}}}]}, "setDefaultFacebookPageRequest": {"allOf": [{"$ref": "#/definitions/BasicRequest"}, {"type": "object", "properties": {"pageId": {"type": "string", "example": "*********"}}, "required": ["pageId"]}]}, "InstagramAccountsResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/InstagramAccount"}}}}]}, "InstagramAccountResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/InstagramAccount"}}}]}, "InstagramAccount": {"type": "object", "properties": {"id": {"type": "string", "example": "*********"}, "username": {"type": "string", "example": "accountusername"}, "connectedPage": {"$ref": "#/definitions/FacebookPageMinimum"}}}, "setDefaultInstagramAccountRequest": {"allOf": [{"$ref": "#/definitions/BasicRequest"}, {"type": "object", "properties": {"instagramAccountId": {"type": "string", "example": "*********"}}, "required": ["instagramAccountId"]}]}, "FacebookLeadResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/FacebookLead"}}}]}, "FacebookLeadsResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/FacebookLead"}}, "metadata": {"type": "object", "properties": {"count": {"type": "string", "example": "10"}, "pageSize": {"type": "string", "example": "25"}, "before": {"type": "string", "example": "120207525115940056"}, "after": {"type": "string", "example": "120207525115940056"}}}}}]}, "FacebookLead": {"type": "object", "properties": {"id": {"type": "string", "example": "*********"}, "firstName": {"type": "string", "example": "Lead firstName"}, "lastName": {"type": "string", "example": "Lead last<PERSON>ame"}, "fullName": {"type": "string", "example": "Lead Name"}, "email": {"type": "string", "example": "<EMAIL>"}, "phoneNumber": {"type": "string", "example": "+1234567"}, "adId": {"type": "string", "example": "**********"}, "adIds": {"type": "array", "items": {"type": "string", "example": "**********"}}, "adName": {"type": "string", "example": "Ad Name"}, "createdTime": {"type": "string", "example": "2023-09-25T06:59:58+0000"}, "status": {"type": "string", "example": "NEW"}, "submissions": {"type": "integer", "example": 1}}}, "FacebookAdResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/FacebookAd"}}}]}, "FacebookAdsResponse": {"allOf": [{"$ref": "#/definitions/BasicResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/FacebookAd"}}, "metadata": {"type": "object", "properties": {"pageSize": {"type": "string", "example": "20"}, "before": {"type": "string", "example": "120207525115940056"}, "after": {"type": "string", "example": "120207525115940056"}}}}}]}, "FacebookAd": {"type": "object", "properties": {"id": {"type": "string", "example": "*********"}, "name": {"type": "string", "example": "Ad Name"}, "status": {"type": "string", "example": "PAUSED"}, "account_id": {"type": "string", "example": "**********"}, "created_time": {"type": "string", "example": "2023-09-25T06:59:58+0000"}, "campaign": {"$ref": "#/definitions/FacebookCampaign"}, "adset": {"$ref": "#/definitions/FacebookAdSet"}, "creative": {"$ref": "#/definitions/FacebookAdCreative"}}}, "FacebookCampaign": {"type": "object", "properties": {"id": {"type": "string", "example": "*********"}, "name": {"type": "string", "example": "Campaign Name"}, "daily_budget": {"type": "string", "example": "100000"}, "start_time": {"type": "string", "example": "2023-09-25T06:59:58+0000"}}}, "FacebookAdSet": {"type": "object", "properties": {"id": {"type": "string", "example": "*********"}, "name": {"type": "string", "example": "Campaign Name"}, "daily_budget": {"type": "string", "example": "100000"}}}, "FacebookAdCreative": {"type": "object", "properties": {"id": {"type": "string", "example": "*********"}, "actor_id": {"type": "string", "example": "*********"}, "instagram_actor_id": {"type": "string", "example": "*********"}}}, "postingFailedRequest": {"allOf": [{"$ref": "#/definitions/BasicRequest"}, {"type": "object", "properties": {"event": {"type": "string", "example": "facebookService"}}, "required": ["event"]}]}, "AvailablePhoneNumberRequest": {"allOf": [{"type": "object", "properties": {"type": {"type": "string", "example": "areaCode"}, "value": {"type": "string", "example": "803"}}}]}, "AvailablePhoneNumbersResponse": {"type": "array", "items": {"type": "object", "properties": {"addressRequirements": {"type": "string", "example": "none"}, "beta": {"type": "boolean", "example": false}, "capabilities": {"type": "object", "properties": {"voice": {"type": "boolean", "example": true}, "SMS": {"type": "boolean", "example": false}, "MMS": {"type": "boolean", "example": true}}}, "friendlyName": {"type": "string", "example": "(*************"}, "isoCountry": {"type": "string", "example": "US"}, "lata": {"type": "string", "example": "432"}, "latitude": {"type": "string", "example": "34.1234567"}, "locality": {"type": "string", "example": "Pageland"}, "longitude": {"type": "string", "example": "-80.410400"}, "phoneNumber": {"type": "string", "example": "+***********"}, "postalCode": {"type": "string", "example": "29728"}, "rataCenter": {"type": "string", "example": "PAGELAND"}, "region": {"type": "string", "example": "SC"}}}}, "PurchasePhoneNumberRequest": {"allOf": [{"type": "object", "properties": {"phoneNumber": {"type": "string", "example": "+***********"}, "accountId": {"type": "string", "example": "*********"}}}]}, "PurchasePhoneNumberResponse": {"type": "object", "properties": {"account_sid": {"type": "string", "example": "ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "address_requirements": {"type": "string", "example": "none"}, "address_sid": {"type": "string", "example": "ADXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "api_version": {"type": "string", "example": "2010-04-01"}, "beta": {"type": "boolean", "example": false}, "capabilities": {"type": "object", "properties": {"voice": {"type": "boolean", "example": true}, "sms": {"type": "boolean", "example": false}, "mms": {"type": "boolean", "example": true}, "fax": {"type": "boolean", "example": false}}}, "date_created": {"type": "string", "example": "Thu, 30 Jul 2015 23:19:04 +0000"}, "date_updated": {"type": "string", "example": "Thu, 30 Jul 2015 23:19:04 +0000"}, "emergency_status": {"type": "string", "example": "Active"}, "emergency_address_sid": {"type": "string", "example": "ADXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "emergency_address_status": {"type": "string", "example": "registered"}, "friendly_name": {"type": "string", "example": "friendly_name"}, "identity_sid": {"type": "string", "example": "RIXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "origin": {"type": "string", "example": "origin"}, "phone_number": {"type": "string", "example": "+18089255327"}, "sid": {"type": "string", "example": "PNXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "sms_application_sid": {"type": "string", "example": "APXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "sms_fallback_method": {"type": "string", "example": "GET"}, "sms_fallback_url": {"type": "string", "example": "https://example.com"}, "sms_method": {"type": "string", "example": "GET"}, "sms_url": {"type": "string", "example": "https://example.com"}, "status_callback": {"type": "string", "example": "https://example.com"}, "status_callback_method": {"type": "string", "example": "GET"}, "trunk_sid": {"type": "string", "example": null}, "uri": {"type": "string", "example": "/2010-04-01/Accounts/ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/IncomingPhoneNumbers/PNXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX.json"}, "voice_application_sid": {"type": "string", "example": "APXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "voice_caller_id_lookup": {"type": "boolean", "example": false}, "voice_fallback_method": {"type": "string", "example": "GET"}, "voice_fallback_url": {"type": "string", "example": "https://example.com"}, "voice_method": {"type": "string", "example": "GET"}, "voice_url": {"type": "string", "example": "https://example.com"}, "bundle_sid": {"type": "string", "example": "BUXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "voice_receive_mode": {"type": "string", "example": "voice"}, "status": {"type": "string", "example": "in-use"}}}, "ReleasePhoneNumberRequest": {"allOf": [{"type": "object", "properties": {"sid": {"type": "string", "example": "ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "accountId": {"type": "string", "example": "*********"}}}]}, "MessageHandlerRequest": {"allOf": [{"type": "object", "properties": {"MessageSid": {"type": "string", "example": "SMXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "SmsSid": {"type": "string", "example": "SMXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "SmsMessageSid": {"type": "string", "example": "SMXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "AccountSid": {"type": "string", "example": "ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "MessagingServiceSid": {"type": "string", "example": "MGXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "From": {"type": "string", "example": "+***********"}, "To": {"type": "string", "example": "+***********"}, "Body": {"type": "string", "example": "Ahoy! We can't wait to see what you build."}, "NumMedia": {"type": "number", "example": 0}, "NumSegments": {"type": "number", "example": 1}}}]}, "UpdateMessagingSidRequest": {"allOf": [{"type": "object", "properties": {"phoneNumberSid": {"type": "string", "example": "PNXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"}, "accountId": {"type": "string", "example": "********"}}}]}}}