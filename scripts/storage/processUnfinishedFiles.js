const { spawn } = require("child_process");
const { execSync } = require("child_process");
const extEnv = require("../../extEnv.json");

// Set the GOOGLE_APPLICATION_CREDENTIALS environment variable
process.env.GOOGLE_APPLICATION_CREDENTIALS = extEnv["rm-titan-prod"];

// Ensure GOOGLE_APPLICATION_CREDENTIALS is set
if (!process.env.GOOGLE_APPLICATION_CREDENTIALS) {
  console.error(
    "The environment variable GOOGLE_APPLICATION_CREDENTIALS must be set."
  );
  process.exit(1);
}

/**
 * Find path to gsutil via 'which gsutil'.
 */
function getGsutilPath() {
  try {
    const gsutilPath = execSync("which gsutil", { encoding: "utf8" }).trim();
    if (!gsutilPath) {
      throw new Error("gsutil not found");
    }
    return gsutilPath;
  } catch (error) {
    console.error("Error finding gsutil path:", error.message);
    console.log("Please install the gcloud CLI to correct this error");
    process.exit(1);
  }
}

/**
 * Spawn a child process to list all files in the bucket.
 * Returns a Promise that resolves to an array of file URIs.
 */
function listFiles(gsutilPath, bucketName) {
  return new Promise((resolve, reject) => {
    const child = spawn(gsutilPath, ["ls", `gs://${bucketName}/**`]);

    let stdout = "";
    let stderr = "";

    child.stdout.on("data", (chunk) => {
      stdout += chunk.toString();
    });

    child.stderr.on("data", (chunk) => {
      stderr += chunk.toString();
    });

    child.on("error", (err) => {
      reject(err);
    });

    child.on("close", (code) => {
      if (code === 0) {
        // Split stdout into lines to get each file URI
        const allFiles = stdout.split("\n").filter(Boolean);
        resolve(allFiles);
      } else {
        // If there's no match, gsutil may output: "One or more URLs matched no objects."
        if (stderr.includes("One or more URLs matched no objects")) {
          // Resolve with an empty list if you want to treat that as "no files found."
          resolve([]);
        } else {
          reject(new Error(stderr || `gsutil ls exited with code ${code}`));
        }
      }
    });
  });
}

/**
 * Spawn a child process to rename (move) a file in GCS.
 * Returns a Promise that resolves once the file is renamed or rejects on error.
 */
function renameFile(gsutilPath, oldUri, newUri) {
  return new Promise((resolve, reject) => {
    const child = spawn(gsutilPath, ["mv", oldUri, newUri]);

    let stdout = "";
    let stderr = "";

    child.stdout.on("data", (chunk) => {
      stdout += chunk.toString();
    });

    child.stderr.on("data", (chunk) => {
      stderr += chunk.toString();
    });

    child.on("error", (err) => {
      reject(err);
    });

    child.on("close", (code) => {
      if (code === 0) {
        resolve(stdout);
      } else {
        reject(new Error(stderr || `gsutil mv exited with code ${code}`));
      }
    });
  });
}

const bucketName = "rm-titan-prod-migrations";
const prefixToAdd = "repo-";
const CONCURRENCY_LIMIT = 100;

async function reprocessAllFiles() {
  const gsutilPath = getGsutilPath();

  console.log(`Listing files in bucket: ${bucketName}`);
  let files;
  try {
    files = await listFiles(gsutilPath, bucketName);
  } catch (err) {
    console.error("Error listing files:", err.message);
    return;
  }

  if (!files.length) {
    console.log(`No files found in bucket: ${bucketName}`);
    return;
  }

  console.log(`Total files found: ${files.length}`);
  console.log(
    `Starting up to ${CONCURRENCY_LIMIT} parallel rename operations...`
  );

  // We’ll track progress
  let completedCount = 0;
  const totalCount = files.length;
  let currentIndex = 0;

  /**
   * Worker function: picks the next file from `files`, renames it if needed,
   * then repeats until no files remain.
   */
  async function processNext() {
    while (true) {
      // Grab the next file index
      const index = currentIndex++;
      if (index >= totalCount) {
        // No more files left to process
        return;
      }

      const fileUri = files[index];
      const parts = fileUri.trim().split("/");
      const fileName = parts[parts.length - 1];

      // If already has the prefix, skip
      if (fileName.startsWith(prefixToAdd)) {
        completedCount++;
        console.log(
          `[${completedCount}/${totalCount}] Skipped ${fileName} (prefix already added)`
        );
        continue;
      }

      // Otherwise, rename
      const newName = prefixToAdd + fileName;
      const newUri = fileUri.replace(fileName, newName);

      try {
        await renameFile(gsutilPath, fileUri, newUri);
        completedCount++;
        console.log(
          `[${completedCount}/${totalCount}] Renamed ${fileName} -> ${newName}`
        );
      } catch (err) {
        // Log the error but continue processing next files
        completedCount++;
        console.error(
          `[${completedCount}/${totalCount}] ERROR renaming file ${fileName}:`,
          err.message
        );
      }
    }
  }

  // Create an array of "worker" Promises
  const workers = [];
  for (let i = 0; i < CONCURRENCY_LIMIT; i++) {
    workers.push(processNext());
  }

  // Wait until all workers have processed all files
  await Promise.all(workers);

  console.log("Finished reprocessing all files in bucket.");
}

// If you want to run the function here directly, uncomment:
(async () => {
  await reprocessAllFiles();
})();
