// Query with JavaScript using the Firebase Admin SDK
// See examples at https://firefoo.app/go/firestore-js-query
async function run() {
  const querySnapshot = await db.collection("Account").get();

  for (const accountDoc of querySnapshot.docs) {
    const recipientsCollectionRef = accountDoc.ref.collection("Recipients");
    const recipientsSnapshot = await recipientsCollectionRef.get();

    for (const recipientDoc of recipientsSnapshot.docs) {
      // Delete all documents in UserEnteredMailingAddress subcollection
      const userEnteredMailingAddressRef = recipientDoc.ref.collection(
        "UserEnteredMailingAddress"
      );
      const userEnteredMailingAddressSnapshot =
        await userEnteredMailingAddressRef.get();

      for (const doc of userEnteredMailingAddressSnapshot.docs) {
        //await doc.ref.delete();
        console.log(
          "deleting: ",
          `Account/${accountDoc.id}/Recipient/${recipientDoc.id}/UserEnteredMailingAddress/${doc.id}`
        );
      }

      // Delete all documents in EmailAddresses subcollection
      const emailAddressesRef = recipientDoc.ref.collection("EmailAddresses");
      const emailAddressesSnapshot = await emailAddressesRef.get();

      for (const doc of emailAddressesSnapshot.docs) {
        //await doc.ref.delete();
        console.log(
          "deleting: ",
          `Account/${accountDoc.id}/Recipient/${recipientDoc.id}/EmailAddresses/${doc.id}`
        );
      }

      // Delete all documents in PhoneNumbers subcollection
      const phoneNumbersRef = recipientDoc.ref.collection("PhoneNumbers");
      const phoneNumbersSnapshot = await phoneNumbersRef.get();

      for (const doc of phoneNumbersSnapshot.docs) {
        //await doc.ref.delete();
        console.log(
          "deleting: ",
          `Account/${accountDoc.id}/Recipient/${recipientDoc.id}/PhoneNumbers/${doc.id}`
        );
      }

      // Delete all recipients notes Notes subcollection
      const notesRef = recipientDoc.ref.collection("Notes");
      const notesSnapshot = await notesRef.get();

      for (const doc of notesSnapshot.docs) {
        //await doc.ref.delete();
        console.log(
          "deleting: ",
          `Account/${accountDoc.id}/Recipient/${recipientDoc.id}/Notes/${doc.id}`
        );
      }

      // Delete all recipients SignificantDates subcollection
      const significantDatesRef =
        recipientDoc.ref.collection("SignificantDates");
      const significantDatesSnapshot = await significantDatesRef.get();

      for (const doc of significantDatesSnapshot.docs) {
        //await doc.ref.delete();
        console.log(
          "deleting: ",
          `Account/${accountDoc.id}/Recipient/${recipientDoc.id}/SignificantDates/${doc.id}`
        );
      }

      // Delete all recipients SocialMedia subcollection
      const socialMediaAccountsRef = recipientDoc.ref.collection(
        "SocialMediaAccounts"
      );
      const socialMediaAccountsSnapshot = await socialMediaAccountsRef.get();

      for (const doc of socialMediaAccountsSnapshot.docs) {
        //await doc.ref.delete();
        console.log(
          "deleting: ",
          `Account/${accountDoc.id}/Recipient/${recipientDoc.id}/SocialMediaAccounts/${doc.id}`
        );
      }

      // Delete all recipients Website subcollection
      const websitesRef = recipientDoc.ref.collection("Websites");
      const websitesSnapshot = await websitesRef.get();

      for (const doc of websitesSnapshot.docs) {
        //await doc.ref.delete();
        console.log(
          "deleting: ",
          `Account/${accountDoc.id}/Recipient/${recipientDoc.id}/Websites/${doc.id}`
        );
      }

      // Delete all recipients External Ids subcollection
      const externalIdsRef = recipientDoc.ref.collection("ExternalIds");
      const externalIdsSnapshot = await externalIdsRef.get();

      for (const doc of externalIdsSnapshot.docs) {
        //await doc.ref.delete();
        console.log(
          "deleting: ",
          `Account/${accountDoc.id}/Recipient/${recipientDoc.id}/ExternalIds/${doc.id}`
        );
      }

      // Delete RealEstate subcollection
      const realEstateRef = recipientDoc.ref.collection("RealEstate");
      const realEstateSnapshot = await realEstateRef.get();

      for (const doc of realEstateSnapshot.docs) {
        //await doc.ref.delete();
        console.log(
          "deleting: ",
          `Account/${accountDoc.id}/Recipient/${recipientDoc.id}/RealEstate/${doc.id}`
        );
      }

      // Delete the recipient document itself
      //await recipientDoc.ref.delete();
      console.log(
        "deleting: ",
        `Account/${accountDoc.id}/Recipient/${recipientDoc.id}`
      );
    }
  }

  console.log("Deletion complete.");
}
