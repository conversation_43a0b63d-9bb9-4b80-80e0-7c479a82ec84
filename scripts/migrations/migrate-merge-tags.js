#!/usr/bin/env node

/**
 * Migration script to back-fill existing MergeTags with new required fields
 * 
 * This script adds the following new fields to existing MergeTag documents:
 * - category: MergeTagCategory
 * - dataSource: MergeTagDataSource  
 * - path?: string
 * - createdBy: string
 * - updatedAt: Timestamp
 * - isSystem: boolean
 * 
 * Usage:
 *   node migrate-merge-tags.js [--dry-run] [--batch-size=100]
 */

const { initializeApp } = require('firebase/app');
const { 
  getFirestore, 
  collection, 
  getDocs, 
  doc, 
  updateDoc, 
  serverTimestamp,
  writeBatch,
  query,
  limit,
  startAfter,
  orderBy
} = require('firebase/firestore');

// Firebase configuration (you may need to adjust this based on your setup)
const firebaseConfig = {
  // Add your Firebase config here
  // This should match your production/staging environment
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// System user ID for migration (adjust as needed)
const SYSTEM_USER_ID = 'system-migration';
const BATCH_SIZE = 100;

/**
 * Determines migration defaults based on existing merge tag data
 */
function determineMigrationDefaults(mergeTag) {
  const defaults = {
    category: 'custom',
    dataSource: 'recipient',
    createdBy: SYSTEM_USER_ID,
    isSystem: false,
    updatedAt: serverTimestamp()
  };

  // Determine category based on tag ID patterns
  const tagId = mergeTag.id?.toLowerCase() || '';
  const tagName = mergeTag.name?.toLowerCase() || '';
  
  if (tagId.includes('first') || tagId.includes('last') || tagId.includes('name') || 
      tagId.includes('email') || tagId.includes('phone')) {
    defaults.category = 'recipient';
    defaults.dataSource = 'recipient';
    defaults.path = `recipients.${tagId}`;
  } else if (tagId.includes('address') || tagId.includes('city') || tagId.includes('state') || 
             tagId.includes('zip') || tagId.includes('postal')) {
    defaults.category = 'address';
    defaults.dataSource = 'mailingAddress';
    defaults.path = `mailingAddress.${tagId}`;
  } else if (tagId.includes('campaign') || tagId.includes('offer')) {
    defaults.category = 'campaign';
    defaults.dataSource = 'campaign';
    defaults.path = `campaign.${tagId}`;
  } else if (tagId.includes('account') || tagId.includes('company')) {
    defaults.category = 'account';
    defaults.dataSource = 'account';
    defaults.path = `account.${tagId}`;
  } else if (tagId.includes('date') || tagId.includes('time') || tagId.includes('timestamp')) {
    defaults.category = 'system';
    defaults.dataSource = 'computed';
    defaults.isSystem = true;
  }

  // Set system flags for common system tags
  const systemTags = [
    'currentDate', 'currentTime', 'unsubscribeLink', 'trackingPixel',
    'recipientId', 'campaignId', 'accountId'
  ];
  
  if (systemTags.includes(tagId)) {
    defaults.isSystem = true;
    defaults.category = 'system';
    defaults.dataSource = 'computed';
  }

  return defaults;
}

/**
 * Migrates a batch of merge tags
 */
async function migrateBatch(mergeTagDocs) {
  const batch = writeBatch(db);
  let updateCount = 0;

  for (const mergeTagDoc of mergeTagDocs) {
    const data = mergeTagDoc.data();
    
    // Skip if already migrated (has category field)
    if (data.category) {
      console.log(`Skipping already migrated tag: ${data.id || mergeTagDoc.id}`);
      continue;
    }

    const defaults = determineMigrationDefaults(data);
    const docRef = doc(db, 'MergeTags', mergeTagDoc.id);
    
    batch.update(docRef, defaults);
    updateCount++;
    
    console.log(`Queued update for tag: ${data.id || mergeTagDoc.id} -> category: ${defaults.category}, dataSource: ${defaults.dataSource}`);
  }

  if (updateCount > 0) {
    await batch.commit();
    console.log(`✅ Successfully updated ${updateCount} merge tags`);
  }

  return updateCount;
}

/**
 * Main migration function
 */
async function migrateMergeTags(isDryRun = false, batchSize = BATCH_SIZE) {
  console.log(`🚀 Starting MergeTags migration${isDryRun ? ' (DRY RUN)' : ''}...`);
  console.log(`Batch size: ${batchSize}`);
  
  let totalProcessed = 0;
  let totalUpdated = 0;
  let lastDoc = null;

  try {
    while (true) {
      // Build query for next batch
      let mergeTagsQuery = query(
        collection(db, 'MergeTags'),
        orderBy('__name__'), // Use document ID for consistent pagination
        limit(batchSize)
      );

      if (lastDoc) {
        mergeTagsQuery = query(mergeTagsQuery, startAfter(lastDoc));
      }

      const snapshot = await getDocs(mergeTagsQuery);
      
      if (snapshot.empty) {
        console.log('📋 No more documents to process');
        break;
      }

      console.log(`\n📦 Processing batch of ${snapshot.docs.length} documents...`);
      totalProcessed += snapshot.docs.length;

      if (!isDryRun) {
        const updatedCount = await migrateBatch(snapshot.docs);
        totalUpdated += updatedCount;
      } else {
        // Dry run - just log what would be updated
        for (const doc of snapshot.docs) {
          const data = doc.data();
          if (!data.category) {
            const defaults = determineMigrationDefaults(data);
            console.log(`[DRY RUN] Would update ${data.id || doc.id} -> category: ${defaults.category}, dataSource: ${defaults.dataSource}`);
            totalUpdated++;
          }
        }
      }

      // Set up for next iteration
      lastDoc = snapshot.docs[snapshot.docs.length - 1];
      
      // Add a small delay to avoid overwhelming Firestore
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log(`\n✅ Migration complete!`);
    console.log(`📊 Total documents processed: ${totalProcessed}`);
    console.log(`🔄 Total documents ${isDryRun ? 'that would be updated' : 'updated'}: ${totalUpdated}`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

/**
 * Validates the migration by checking a sample of updated documents
 */
async function validateMigration() {
  console.log('\n🔍 Validating migration...');
  
  const sampleQuery = query(collection(db, 'MergeTags'), limit(5));
  const snapshot = await getDocs(sampleQuery);
  
  let validCount = 0;
  for (const doc of snapshot.docs) {
    const data = doc.data();
    const hasRequiredFields = data.category && data.dataSource && 
                             data.createdBy !== undefined && 
                             data.isSystem !== undefined;
    
    if (hasRequiredFields) {
      validCount++;
      console.log(`✅ Valid: ${data.id} (category: ${data.category}, dataSource: ${data.dataSource})`);
    } else {
      console.log(`❌ Invalid: ${data.id} - missing required fields`);
    }
  }
  
  console.log(`\n📋 Validation result: ${validCount}/${snapshot.docs.length} documents valid`);
}

// Parse command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const batchSizeArg = args.find(arg => arg.startsWith('--batch-size='));
const batchSize = batchSizeArg ? parseInt(batchSizeArg.split('=')[1]) : BATCH_SIZE;
const validate = args.includes('--validate');

// Run migration
if (validate) {
  validateMigration()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Validation failed:', error);
      process.exit(1);
    });
} else {
  migrateMergeTags(isDryRun, batchSize)
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
