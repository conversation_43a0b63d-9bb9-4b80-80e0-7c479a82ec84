const admin = require("firebase-admin");
const { Timestamp, FieldValue } = require("firebase-admin/firestore");

const serviceAccount = require("../../../Keys/rm-titan-prod.json");

if (admin.apps.length === 0) {
  // Check if Firebase has already been initialized
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

const db = admin.firestore();

function extractAfterPEquals(text) {
  const index = text.indexOf('p=');
  if (index !== -1) {
    return text.slice(index + 2);
  }
  return '';
}

const fixLandingPages = async () => {
  const oneDayAgo = new Date();
  oneDayAgo.setDate(oneDayAgo.getDate() - 1);
  console.log("oneDayAgo: ", oneDayAgo);
  console.log("===============================================");
  console.log("===============================================");
  const landingPagesRef = db.collection("codes")
  .where("qrCodeGenerated", ">=", Timestamp.fromDate(oneDayAgo))
  .orderBy("qrCodeGenerated", "desc");
  
  const landingPages = await landingPagesRef.get();

  for(const lp of landingPages.docs) {
    const lpData = lp.data();
    if(!lpData.longUrl.includes("p=")) {
      console.log("Skipping: ", lp.id, " as it does not contain 'p='");
      console.log("===============================================");
      continue;
    }
    if(lpData.linkData && lpData.linkData.page_uuid) {
      continue;
    }
    const uuid = extractAfterPEquals(lpData.longUrl);
    const newRecord = {
      linkData: {
        page_uuid: uuid
      },
    };
    console.log("Updating: ", lp.id, " with page_uuid ", newRecord.linkData.page_uuid);
    await db.collection("codes").doc(lp.id).update(newRecord, { merge: true });
    console.log("Updated: ", lp.id, " with page_uuid ", newRecord.linkData.page_uuid);
    console.log("===============================================");
  }
};

fixLandingPages().catch(console.error);
