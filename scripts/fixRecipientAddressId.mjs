import  { FieldValue } from "firebase-admin/firestore";
import inquirer from "inquirer";
import admin from "firebase-admin";
import fs from "fs/promises";
import extEnv from "../extEnv.json" assert { type: "json" };
import { recursiveDocDelete } from "../functions/utils/recursiveDocDelete.js";

// Parse command line arguments
const args = process.argv.slice(2);
const dryRun = args.includes('--dry-run') || args.includes('-d');
const help = args.includes('--help') || args.includes('-h');
const noLog = args.includes('--no-log') || args.includes('-n');
const quiet = args.includes('--quiet') || args.includes('-q');

if (help) {
  console.log(`
Usage: node updateRecipientAddressId.mjs [--dry-run|-d]

Options:
  --dry-run, -d  Prints the actions that would be taken without actually making any changes
  --no-log, -n   Disables logging action detail
  --quiet, -q    Disables logging
  --help, -h     Shows this help message
`);
  process.exit(0);
}

// Prompt the user to select the GCP project
const promptProjectSelection = async () => {
  const { projectId } = await inquirer.prompt({
    type: "list",
    name: "projectId",
    message: "Select the GCP project:",
    choices: ["rm-titan-dev", "rm-titan-prod"],
  });

  return projectId;
};

// Prompt for Account ID
const promptForAccountId = async () => {
  const { accountId } = await inquirer.prompt({
    type: "input",
    name: "accountId",
    message: "Enter Account ID to process:",
    validate: (input) => (input && !isNaN(input) ? true : "Account ID must be numeric."),
  });

  return accountId;
}

const initDb = async (projectId) => {
  try {
    const serviceAccountPath = extEnv[projectId];
    const serviceAccountContent = await fs.readFile(serviceAccountPath,"utf8");
    const serviceAccount = JSON.parse(serviceAccountContent);

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
    });

    return admin.firestore();
  } catch (error) {
    console.error("❌ An error occurred initializing the database:", error);
    process.exit(1);
  }
}

const updateRecipientsAddressId = async (titanDb, accountId) => {
  let processed = 0;
  let updated = 0;
  let changed = false;

  try {
    const accountRef = titanDb
      .collection("Account")
      .doc(accountId);

    const accountSnapshot = await accountRef.get();
    if (!accountSnapshot.exists) {
      console.error("❌ Account not found");
      process.exit(1);
    }
    if (!quiet) console.log("Processing account: ", accountSnapshot.data().name);

    const recipientsSnapshot = await accountRef
      .collection("Recipients")
      .get();

    if (!recipientsSnapshot || !recipientsSnapshot.docs.length) {
      if (!quiet) console.log("No recipients found.");
      return;
    }

    for (const recipientDoc of recipientsSnapshot.docs) {
      changed = false;
      const batch = titanDb.batch();

      const addressDocs = await recipientDoc.ref
        .collection("UserEnteredMailingAddress")
        .get();

      if (addressDocs.docs.length) {
        processed++;

        const recipientData = recipientDoc.data();
        if (!noLog || !quiet) console.log("handling recipient w/address: ", `${recipientData.name.firstName} ${recipientData.name.lastName} (${recipientDoc.id})`);

        // do we have a "0" doc?
        let found = false;
        for (const addressDoc of addressDocs.docs) {
          if (addressDoc.id === "0") {
            if (!noLog || !quiet) console.log(" 🌟 found \"0\" address ");
            found = true;
          }
        }
        // if not, find a valid address and add one
        if (!found) {
          for (const addressDoc of addressDocs.docs) {
            const addressData = addressDoc.data();
            if (addressData.address1 !== "" || (addressData.city !== "" && addressData.state !== "" && addressData.postalCode !== "")) {
              if (!noLog || !quiet) console.log(" 👉 creating \"0\" address from: ", `${addressData.address1}, ${addressData.city}, ${addressData.state}, ${addressData.postalCode} (${addressDoc.id})`);
              if (!dryRun) {
                const subDocRef = recipientDoc.ref
                  .collection("UserEnteredMailingAddress")
                  .doc("0");
                batch.set(subDocRef, {
                  ...addressData,
                });
                changed = true;
              }
              found = true;
              break;
            }
          }
        }
        // if no valid address found, remove deliverability field
        if (!found) {
          if (!noLog || !quiet) console.log(" 🗑️  no valid address found, deleting: addressDeliverability");
          if (!dryRun) {
            await recipientDoc.ref.update({
              addressDeliverability: FieldValue.delete()
            });
            changed = true;
          }
        }
      }

      // clear out all old address docs and any empty docs
      const addressDocRefs = await recipientDoc.ref
        .collection("UserEnteredMailingAddress")
        .listDocuments();

      for (const addressDocRef of addressDocRefs) {
        if (addressDocRef.id !== "0") {
          if (!noLog || !quiet) console.log(" 🗑️  deleting: ", `.../UserEnteredMailingAddress/${addressDocRef.id}`);
          if (!dryRun) {
            await recursiveDocDelete(addressDocRef, batch);
            changed = true;
          }
        }
      }

      if (changed) updated++;

      await batch.commit();
    }
    if (!quiet) console.log(`📝 Stats - processed: ${processed}, updated: ${updated}`);
  } catch (error) {
    console.error("❌ An error occurred:", error);
  }
}

//
// main function
//
const main = async () => {
  // insert clock icon into console log
  if (!quiet) console.time("🕒 Time taken");
  if (dryRun) console.log("🧹 DRY RUN Mode Enabled");
  const projectId = await promptProjectSelection();
  const accountId = await promptForAccountId();
  const titanDb = await initDb(projectId);

  await updateRecipientsAddressId(titanDb, accountId);

  if (!quiet) console.timeEnd("🕒 Time taken");
  if (!quiet) console.log("🎉 Done!");
}

// Run script
main().catch(error => {
  console.error('❌ Error:', error.message);
  process.exit(1);
});