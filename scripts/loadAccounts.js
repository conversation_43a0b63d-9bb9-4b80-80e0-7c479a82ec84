const fs = require("fs");
const admin = require("firebase-admin");
const { Timestamp, FieldValue } = require("firebase-admin/firestore");

// Adjust the path as needed
const jsonFilePath =
  "/Users/<USER>/MyCode/rm-campaigns/src/data/files/accounts.json";
// Ensure the relative path to serviceAccount.json is correct from the script's location
const serviceAccount = require("../serviceAccount.json");

if (admin.apps.length === 0) {
  // Check if Firebase has already been initialized
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

const db = admin.firestore();

const loadData = async () => {
  const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, "utf8"));

  // Assuming jsonData[0].accounts_json is an array of accounts to be added
  for (const doc of jsonData[0].accounts_json) {
    const docId = `${doc.accountId}`;
    if (typeof docId !== "string" || docId.trim() === "") {
      console.error(
        "Invalid or missing document ID:",
        docId,
        "for document",
        JSON.stringify(doc)
      );
      continue; // Skip this document
    }
    if (doc.accountCreated) {
      const date = new Date(doc.accountCreated);
      doc.accountCreated = Timestamp.fromDate(date);
    }
    delete doc.accountId;
    doc.created = FieldValue.serverTimestamp();
    doc.isActive = true;

    try {
      const docRef = await db.collection("Account").doc(docId);
      await docRef.set(doc);
      console.log(`Document with ID ${docId} successfully written.`);
    } catch (error) {
      console.error(`Error writing document with ID ${docId}:`, error);
    }
  }
};

loadData().catch(console.error);
