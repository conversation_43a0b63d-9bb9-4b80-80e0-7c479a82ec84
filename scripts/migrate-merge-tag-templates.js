/**
 * Template Migration Script - Legacy to Enhanced Merge Tags
 * 
 * This script migrates existing templates from legacy merge tag syntax
 * to the new enhanced merge tag syntax.
 * 
 * Legacy: [tagName]
 * New: {{tagName}}
 */

import { collection, getDocs, updateDoc, doc } from 'firebase/firestore';
import { db } from '../src/providers/database.js';

// Configuration
const DRY_RUN = true; // Set to false to actually perform the migration
const BATCH_SIZE = 50; // Number of templates to process at once

// Collections to migrate
const COLLECTIONS_TO_MIGRATE = [
  'emailTemplates',
  'smsTemplates',
  'printTemplates',
  'campaignTemplates'
];

// Migration functions
function migrateMergeTagSyntax(content) {
  if (!content || typeof content !== 'string') {
    return content;
  }

  // Convert [tagName] to {{tagName}}
  // This regex matches square brackets with word characters inside
  const migrated = content.replace(/\[(\w+)\]/g, '{{$1}}');
  
  return migrated;
}

function analyzeTemplate(template) {
  const analysis = {
    hasLegacyTags: false,
    hasNewTags: false,
    legacyTagCount: 0,
    newTagCount: 0,
    fields: []
  };

  // Check all string fields in the template
  for (const [key, value] of Object.entries(template)) {
    if (typeof value === 'string') {
      const legacyMatches = value.match(/\[\w+\]/g) || [];
      const newMatches = value.match(/\{\{\w+\}\}/g) || [];
      
      if (legacyMatches.length > 0) {
        analysis.hasLegacyTags = true;
        analysis.legacyTagCount += legacyMatches.length;
        analysis.fields.push({
          field: key,
          legacyTags: legacyMatches,
          needsMigration: true
        });
      }
      
      if (newMatches.length > 0) {
        analysis.hasNewTags = true;
        analysis.newTagCount += newMatches.length;
        analysis.fields.push({
          field: key,
          newTags: newMatches,
          needsMigration: false
        });
      }
    }
  }

  return analysis;
}

async function migrateTemplate(templateDoc, collectionName) {
  const template = templateDoc.data();
  const analysis = analyzeTemplate(template);
  
  if (!analysis.hasLegacyTags) {
    console.log(`✓ Template ${templateDoc.id} already uses new syntax`);
    return { success: true, migrated: false };
  }

  console.log(`🔄 Migrating template ${templateDoc.id} in ${collectionName}`);
  console.log(`   Legacy tags found: ${analysis.legacyTagCount}`);
  
  // Create migrated version
  const migratedTemplate = { ...template };
  
  for (const fieldInfo of analysis.fields) {
    if (fieldInfo.needsMigration) {
      const originalValue = migratedTemplate[fieldInfo.field];
      const migratedValue = migrateMergeTagSyntax(originalValue);
      migratedTemplate[fieldInfo.field] = migratedValue;
      
      console.log(`   Field: ${fieldInfo.field}`);
      console.log(`   Before: ${originalValue.substring(0, 100)}...`);
      console.log(`   After:  ${migratedValue.substring(0, 100)}...`);
    }
  }

  // Add migration metadata
  migratedTemplate.migrationInfo = {
    migratedAt: new Date(),
    migratedBy: 'merge-tag-migration-script',
    originalLegacyTagCount: analysis.legacyTagCount,
    version: '2.0'
  };

  if (!DRY_RUN) {
    try {
      const templateRef = doc(db, collectionName, templateDoc.id);
      await updateDoc(templateRef, migratedTemplate);
      console.log(`✅ Successfully migrated template ${templateDoc.id}`);
      return { success: true, migrated: true };
    } catch (error) {
      console.error(`❌ Failed to migrate template ${templateDoc.id}:`, error);
      return { success: false, migrated: false, error };
    }
  } else {
    console.log(`🧪 DRY RUN: Would migrate template ${templateDoc.id}`);
    return { success: true, migrated: true, dryRun: true };
  }
}

async function migrateCollection(collectionName) {
  console.log(`\n📁 Processing collection: ${collectionName}`);
  
  try {
    const querySnapshot = await getDocs(collection(db, collectionName));
    const templates = querySnapshot.docs;
    
    console.log(`Found ${templates.length} templates to process`);
    
    const results = {
      total: templates.length,
      migrated: 0,
      alreadyMigrated: 0,
      failed: 0,
      errors: []
    };

    // Process in batches
    for (let i = 0; i < templates.length; i += BATCH_SIZE) {
      const batch = templates.slice(i, i + BATCH_SIZE);
      console.log(`\nProcessing batch ${Math.floor(i / BATCH_SIZE) + 1}...`);
      
      for (const templateDoc of batch) {
        const result = await migrateTemplate(templateDoc, collectionName);
        
        if (result.success && result.migrated) {
          results.migrated++;
        } else if (result.success && !result.migrated) {
          results.alreadyMigrated++;
        } else {
          results.failed++;
          results.errors.push({
            templateId: templateDoc.id,
            error: result.error
          });
        }
      }
      
      // Small delay between batches to avoid overwhelming Firestore
      if (i + BATCH_SIZE < templates.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`\n📊 Collection ${collectionName} Results:`);
    console.log(`   Total templates: ${results.total}`);
    console.log(`   Migrated: ${results.migrated}`);
    console.log(`   Already migrated: ${results.alreadyMigrated}`);
    console.log(`   Failed: ${results.failed}`);
    
    if (results.errors.length > 0) {
      console.log(`\n❌ Errors:`);
      results.errors.forEach(err => {
        console.log(`   ${err.templateId}: ${err.error.message}`);
      });
    }

    return results;
    
  } catch (error) {
    console.error(`❌ Failed to process collection ${collectionName}:`, error);
    return { error, total: 0, migrated: 0, alreadyMigrated: 0, failed: 0 };
  }
}

async function runMigration() {
  console.log('🚀 Starting Merge Tag Template Migration');
  console.log(`Mode: ${DRY_RUN ? 'DRY RUN' : 'LIVE MIGRATION'}`);
  console.log(`Collections to process: ${COLLECTIONS_TO_MIGRATE.join(', ')}`);
  
  const overallResults = {
    total: 0,
    migrated: 0,
    alreadyMigrated: 0,
    failed: 0,
    collections: {}
  };

  for (const collectionName of COLLECTIONS_TO_MIGRATE) {
    const result = await migrateCollection(collectionName);
    overallResults.collections[collectionName] = result;
    overallResults.total += result.total;
    overallResults.migrated += result.migrated;
    overallResults.alreadyMigrated += result.alreadyMigrated;
    overallResults.failed += result.failed;
  }

  console.log('\n🏁 Migration Complete!');
  console.log('═'.repeat(50));
  console.log(`Total templates processed: ${overallResults.total}`);
  console.log(`Successfully migrated: ${overallResults.migrated}`);
  console.log(`Already using new syntax: ${overallResults.alreadyMigrated}`);
  console.log(`Failed migrations: ${overallResults.failed}`);
  
  if (DRY_RUN) {
    console.log('\n⚠️  This was a DRY RUN - no changes were made');
    console.log('Set DRY_RUN = false to perform actual migration');
  }

  return overallResults;
}

// Validation function to check migration results
async function validateMigration() {
  console.log('\n🔍 Validating migration results...');
  
  for (const collectionName of COLLECTIONS_TO_MIGRATE) {
    const querySnapshot = await getDocs(collection(db, collectionName));
    const templates = querySnapshot.docs;
    
    let legacyTagsFound = 0;
    let templatesWithLegacyTags = 0;
    
    templates.forEach(templateDoc => {
      const analysis = analyzeTemplate(templateDoc.data());
      if (analysis.hasLegacyTags) {
        templatesWithLegacyTags++;
        legacyTagsFound += analysis.legacyTagCount;
      }
    });
    
    if (templatesWithLegacyTags > 0) {
      console.log(`⚠️  ${collectionName}: ${templatesWithLegacyTags} templates still have legacy tags (${legacyTagsFound} total)`);
    } else {
      console.log(`✅ ${collectionName}: All templates migrated successfully`);
    }
  }
}

// CLI interface
if (process.argv.includes('--validate')) {
  validateMigration().catch(console.error);
} else if (process.argv.includes('--live')) {
  // Override DRY_RUN for live migration
  DRY_RUN = false;
  runMigration().catch(console.error);
} else {
  runMigration().catch(console.error);
}

// Export functions for use in other scripts
export {
  migrateMergeTagSyntax,
  analyzeTemplate,
  migrateTemplate,
  migrateCollection,
  runMigration,
  validateMigration
};
