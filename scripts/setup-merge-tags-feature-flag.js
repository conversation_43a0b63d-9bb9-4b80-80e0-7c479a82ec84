/**
 * Feature Flag Setup Script for Enhanced Merge Tags
 * 
 * This script sets up the feature flag for the enhanced merge tags system
 * and manages the gradual rollout process.
 */

import { doc, setDoc, updateDoc, getDoc } from 'firebase/firestore';
import { db } from '../src/providers/database.js';

const FEATURE_FLAG_ID = 'enhanced-merge-tags';

// Rollout phases configuration
const ROLLOUT_PHASES = {
  phase1: {
    name: 'Internal Testing',
    accountIds: [
      'dev-account-1',
      'dev-account-2',
      'dev-account-3'
    ],
    percentage: 0,
    description: 'Development team testing only'
  },
  phase2: {
    name: 'Beta Testing',
    accountIds: [
      'beta-customer-1',
      'beta-customer-2',
      'beta-customer-3',
      'beta-customer-4',
      'beta-customer-5'
    ],
    percentage: 0,
    description: 'Select beta customers'
  },
  phase3: {
    name: 'Gradual Rollout',
    accountIds: [],
    percentage: 10, // Start with 10% of all accounts
    description: 'Gradual percentage-based rollout'
  },
  phase4: {
    name: 'Full Rollout',
    accountIds: [],
    percentage: 100,
    description: 'All accounts enabled'
  }
};

/**
 * Create the initial feature flag
 */
async function createFeatureFlag() {
  const featureFlagData = {
    name: 'Enhanced Merge Tags',
    description: 'Enhanced merge tag system with improved picker, Unlayer integration, and analytics',
    isLive: false,
    isArchived: false,
    accountIds: ROLLOUT_PHASES.phase1.accountIds,
    goingLive: null, // Will be set when scheduling rollout
    createdAt: new Date(),
    createdBy: 'system',
    currentPhase: 'phase1',
    rolloutPlan: ROLLOUT_PHASES,
    metadata: {
      version: '2.0.0',
      components: [
        'MergeTagPicker',
        'MergeTagToolbarButton',
        'Unlayer Integration',
        'Analytics Tracking'
      ],
      fallbackBehavior: 'legacy-merge-tags'
    }
  };

  try {
    const featureFlagRef = doc(db, 'FeatureFlag', FEATURE_FLAG_ID);
    await setDoc(featureFlagRef, featureFlagData);
    console.log('✅ Feature flag created successfully');
    return featureFlagData;
  } catch (error) {
    console.error('❌ Failed to create feature flag:', error);
    throw error;
  }
}

/**
 * Update feature flag for a specific rollout phase
 */
async function updateRolloutPhase(phase) {
  if (!ROLLOUT_PHASES[phase]) {
    throw new Error(`Invalid phase: ${phase}`);
  }

  const phaseConfig = ROLLOUT_PHASES[phase];
  
  try {
    const featureFlagRef = doc(db, 'FeatureFlag', FEATURE_FLAG_ID);
    const updateData = {
      accountIds: phaseConfig.accountIds,
      currentPhase: phase,
      updatedAt: new Date(),
      updatedBy: 'system'
    };

    // For percentage-based rollout, we might need additional logic
    if (phaseConfig.percentage > 0) {
      updateData.rolloutPercentage = phaseConfig.percentage;
      
      // If it's full rollout, set isLive to true
      if (phaseConfig.percentage === 100) {
        updateData.isLive = true;
        updateData.goingLive = new Date();
      }
    }

    await updateDoc(featureFlagRef, updateData);
    console.log(`✅ Updated feature flag to ${phaseConfig.name} (${phase})`);
    console.log(`   Accounts: ${phaseConfig.accountIds.length}`);
    console.log(`   Percentage: ${phaseConfig.percentage}%`);
    
    return updateData;
  } catch (error) {
    console.error(`❌ Failed to update rollout phase to ${phase}:`, error);
    throw error;
  }
}

/**
 * Add specific accounts to the feature flag
 */
async function addAccountsToFeatureFlag(accountIds) {
  try {
    const featureFlagRef = doc(db, 'FeatureFlag', FEATURE_FLAG_ID);
    const featureFlagSnap = await getDoc(featureFlagRef);
    
    if (!featureFlagSnap.exists()) {
      throw new Error('Feature flag does not exist. Create it first.');
    }

    const currentData = featureFlagSnap.data();
    const currentAccountIds = currentData.accountIds || [];
    const newAccountIds = [...new Set([...currentAccountIds, ...accountIds])];

    await updateDoc(featureFlagRef, {
      accountIds: newAccountIds,
      updatedAt: new Date(),
      updatedBy: 'system'
    });

    console.log(`✅ Added ${accountIds.length} accounts to feature flag`);
    console.log(`   Total accounts now: ${newAccountIds.length}`);
    
    return newAccountIds;
  } catch (error) {
    console.error('❌ Failed to add accounts to feature flag:', error);
    throw error;
  }
}

/**
 * Remove specific accounts from the feature flag
 */
async function removeAccountsFromFeatureFlag(accountIds) {
  try {
    const featureFlagRef = doc(db, 'FeatureFlag', FEATURE_FLAG_ID);
    const featureFlagSnap = await getDoc(featureFlagRef);
    
    if (!featureFlagSnap.exists()) {
      throw new Error('Feature flag does not exist');
    }

    const currentData = featureFlagSnap.data();
    const currentAccountIds = currentData.accountIds || [];
    const newAccountIds = currentAccountIds.filter(id => !accountIds.includes(id));

    await updateDoc(featureFlagRef, {
      accountIds: newAccountIds,
      updatedAt: new Date(),
      updatedBy: 'system'
    });

    console.log(`✅ Removed ${accountIds.length} accounts from feature flag`);
    console.log(`   Total accounts now: ${newAccountIds.length}`);
    
    return newAccountIds;
  } catch (error) {
    console.error('❌ Failed to remove accounts from feature flag:', error);
    throw error;
  }
}

/**
 * Get current feature flag status
 */
async function getFeatureFlagStatus() {
  try {
    const featureFlagRef = doc(db, 'FeatureFlag', FEATURE_FLAG_ID);
    const featureFlagSnap = await getDoc(featureFlagRef);
    
    if (!featureFlagSnap.exists()) {
      console.log('⚠️  Feature flag does not exist');
      return null;
    }

    const data = featureFlagSnap.data();
    console.log('📊 Feature Flag Status:');
    console.log(`   Name: ${data.name}`);
    console.log(`   Current Phase: ${data.currentPhase}`);
    console.log(`   Is Live: ${data.isLive}`);
    console.log(`   Enabled Accounts: ${data.accountIds?.length || 0}`);
    console.log(`   Rollout Percentage: ${data.rolloutPercentage || 0}%`);
    console.log(`   Created: ${data.createdAt?.toDate?.() || data.createdAt}`);
    console.log(`   Last Updated: ${data.updatedAt?.toDate?.() || data.updatedAt || 'Never'}`);
    
    return data;
  } catch (error) {
    console.error('❌ Failed to get feature flag status:', error);
    throw error;
  }
}

/**
 * Enable feature flag for all accounts (full rollout)
 */
async function enableForAllAccounts() {
  try {
    const featureFlagRef = doc(db, 'FeatureFlag', FEATURE_FLAG_ID);
    
    await updateDoc(featureFlagRef, {
      isLive: true,
      currentPhase: 'phase4',
      rolloutPercentage: 100,
      goingLive: new Date(),
      updatedAt: new Date(),
      updatedBy: 'system'
    });

    console.log('✅ Feature flag enabled for ALL accounts');
    console.log('   Phase: Full Rollout (100%)');
    
  } catch (error) {
    console.error('❌ Failed to enable feature flag for all accounts:', error);
    throw error;
  }
}

/**
 * Disable feature flag (emergency rollback)
 */
async function disableFeatureFlag() {
  try {
    const featureFlagRef = doc(db, 'FeatureFlag', FEATURE_FLAG_ID);
    
    await updateDoc(featureFlagRef, {
      isLive: false,
      accountIds: [],
      rolloutPercentage: 0,
      currentPhase: 'disabled',
      disabledAt: new Date(),
      updatedAt: new Date(),
      updatedBy: 'system'
    });

    console.log('🛑 Feature flag DISABLED (emergency rollback)');
    
  } catch (error) {
    console.error('❌ Failed to disable feature flag:', error);
    throw error;
  }
}

// CLI interface
async function main() {
  const command = process.argv[2];
  const args = process.argv.slice(3);

  try {
    switch (command) {
      case 'create':
        await createFeatureFlag();
        break;
        
      case 'status':
        await getFeatureFlagStatus();
        break;
        
      case 'phase':
        const phase = args[0];
        if (!phase) {
          console.error('Please specify a phase: phase1, phase2, phase3, or phase4');
          process.exit(1);
        }
        await updateRolloutPhase(phase);
        break;
        
      case 'add-accounts':
        const accountsToAdd = args;
        if (accountsToAdd.length === 0) {
          console.error('Please specify account IDs to add');
          process.exit(1);
        }
        await addAccountsToFeatureFlag(accountsToAdd);
        break;
        
      case 'remove-accounts':
        const accountsToRemove = args;
        if (accountsToRemove.length === 0) {
          console.error('Please specify account IDs to remove');
          process.exit(1);
        }
        await removeAccountsFromFeatureFlag(accountsToRemove);
        break;
        
      case 'enable-all':
        await enableForAllAccounts();
        break;
        
      case 'disable':
        await disableFeatureFlag();
        break;
        
      default:
        console.log('Enhanced Merge Tags Feature Flag Management');
        console.log('');
        console.log('Usage:');
        console.log('  node setup-merge-tags-feature-flag.js create              # Create initial feature flag');
        console.log('  node setup-merge-tags-feature-flag.js status              # Show current status');
        console.log('  node setup-merge-tags-feature-flag.js phase <phase>       # Update to specific phase');
        console.log('  node setup-merge-tags-feature-flag.js add-accounts <ids>  # Add specific accounts');
        console.log('  node setup-merge-tags-feature-flag.js remove-accounts <ids> # Remove specific accounts');
        console.log('  node setup-merge-tags-feature-flag.js enable-all          # Enable for all accounts');
        console.log('  node setup-merge-tags-feature-flag.js disable             # Emergency disable');
        console.log('');
        console.log('Phases: phase1 (internal), phase2 (beta), phase3 (gradual), phase4 (full)');
        break;
    }
  } catch (error) {
    console.error('💥 Command failed:', error.message);
    process.exit(1);
  }
}

// Export functions for use in other scripts
export {
  createFeatureFlag,
  updateRolloutPhase,
  addAccountsToFeatureFlag,
  removeAccountsFromFeatureFlag,
  getFeatureFlagStatus,
  enableForAllAccounts,
  disableFeatureFlag,
  ROLLOUT_PHASES
};

// Run CLI if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
