const fs = require("fs");
const admin = require("firebase-admin");
const { Timestamp, FieldValue } = require("firebase-admin/firestore");

// Adjust the path as needed
const jsonFilePath =
  "/Users/<USER>/MyCode/rm-campaigns/src/data/files/ads.json";
// Ensure the relative path to serviceAccount.json is correct from the script's location
const serviceAccount = require("../serviceAccount.json");

if (admin.apps.length === 0) {
  // Check if Firebase has already been initialized
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

const db = admin.firestore();

const loadAds = async () => {
  const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, "utf8"));

  // Assuming jsonData[0].accounts_json is an array of accounts to be added
  for (const doc of jsonData) {
    if (doc.adCreated) {
      const date = new Date(doc.adCreated);
      doc.adCreated = Timestamp.fromDate(date);
    }
    if (doc.createdAt) {
      const date = new Date(doc.createdAt);
      doc.createdAt = Timestamp.fromDate(date);
    }
    if (doc.updatedAt) {
      const date = new Date(doc.updatedAt);
      doc.updatedAt = Timestamp.fromDate(date);
    }
    if (doc.dateStart) {
      const date = new Date(doc.dateStart);
      doc.dateStart = Timestamp.fromDate(date);
    }
    doc.created = FieldValue.serverTimestamp();

    try {
      const docRef = db.collection("Ads").doc();
      await docRef.set(doc);
    } catch (error) {
      console.error(`Error writing document: `, error);
    }
  }
};

loadAds().catch(console.error);
