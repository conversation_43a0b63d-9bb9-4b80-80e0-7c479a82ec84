import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import OpenAI from 'openai';
import readline from 'readline';
import ora from 'ora';
import 'dotenv/config';

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});

const extendedPrompt = `Return only the executable code in proper format for execution. Remove all comments. `;

async function getAIResponse(prompt) {
    const spinner = ora('Waiting for response...').start();
    try {
        let responseText = '';
        const updatedPrompt = `${extendedPrompt} ${prompt}`;
        let response = await openai.chat.completions.create({
            model: 'gpt-4',
            messages: [{ role: 'user', content: updatedPrompt }],
            max_tokens: 2048,
        });

        responseText += response.choices[0].message.content;
        responseText = responseText.replace(/```javascript|```/g, '');

        while (response.choices[0].finish_reason !== 'stop') {
            response = await openai.chat.completions.create({
                model: 'gpt-4',
                messages: [{ role: 'user', content: 'Continue' }],
                max_tokens: 2048,
            });
            responseText += response.choices[0].message.content;
            responseText = responseText.replace(/```javascript|```/g, '');
        }

        spinner.succeed('Response received.');
        return responseText;
    } catch (error) {
        spinner.fail('Failed to get response.');
        throw error;
    }
}

function waitForPromptInput() {
    return new Promise((resolve, reject) => {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
        });

        rl.question('Please enter your prompt (or press ESC to cancel): ', (input) => {
            rl.close();
            resolve(input);
        });

        rl.input.on('keypress', (char, key) => {
            if (key && key.name === 'escape') {
                rl.close();
                console.log('Prompt canceled.');
                reject('Prompt canceled by the user.');
            }
        });
    });
}

async function main() {
    try {
        const prompt = await waitForPromptInput();
        if (!prompt) {
            console.log('No prompt provided.');
            return;
        }

        const response = await getAIResponse(prompt);
        console.log(response);
    } catch (error) {
        console.error('Error:', error);
    }
}

main();
