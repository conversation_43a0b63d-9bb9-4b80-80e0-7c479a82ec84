import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import OpenAI from 'openai';
import 'dotenv/config';

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const sourceDir = path.join(__dirname, '..', 'titan/src/domain/documentation');
const testDir = path.join(__dirname, '..', 'titan/tests');
const extendedPrompt = `Return only the executable code in proper format for execution. Remove all comments. `;

fs.ensureDirSync(testDir);

async function generateTestsForDirectory(dir) {
    const files = fs.readdirSync(dir);

    for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
            await generateTestsForDirectory(filePath);
        } else if (stat.isFile() && file.endsWith('.js')) {
            await generateTestForFile(filePath);
            await new Promise(resolve => setTimeout(resolve, 30000));
        }
    }
}

async function generateTestForFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(sourceDir, filePath);
    const testFileName = `${path.basename(filePath, '.js')}.test.js`;
    const testFilePath = path.join(testDir, path.dirname(relativePath), testFileName);

    fs.ensureDirSync(path.dirname(testFilePath));

    const prompt = `Generate a Jest unit test for the following JavaScript code:\n\n${content}`;
    const testContent = await getAIResponse(extendedPrompt + prompt);

    fs.writeFileSync(testFilePath, testContent, 'utf8');
    console.log(`Generated test for ${relativePath} at ${testFilePath}`);
}

async function getAIResponse(prompt) {
    let responseText = '';
    let response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 2048,
    });

    responseText += response.choices[0].message.content;
    responseText = responseText.replace(/```javascript|```/g, '');

    while (response.choices[0].finish_reason !== 'stop') {
        response = await openai.chat.completions.create({
            model: 'gpt-4o',
            messages: [{ role: 'user', content: 'Continue' }],
            max_tokens: 2048,
        });
        responseText += response.choices[0].message.content;
        responseText = responseText.replace(/```javascript|```/g, '');
    }

    return responseText;
}

async function combineTests(dir) {
    const files = fs.readdirSync(dir);
    let combinedContent = '';

    for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
            await combineTests(filePath);
        } else if (stat.isFile() && file.endsWith('.test.js')) {
            const content = fs.readFileSync(filePath, 'utf8');
            combinedContent += `\n\n// Test from ${file}\n\n${content}`;
        }
    }

    if (combinedContent) {
        const dirName = path.basename(dir);
        const combinedFilePath = path.join(testDir, `${dirName}.test.js`);
        fs.writeFileSync(combinedFilePath, combinedContent, 'utf8');
        console.log(`Combined tests into ${combinedFilePath}`);

        // Delete individual test files
        files.forEach(file => {
            const filePath = path.join(dir, file);
            if (filePath !== combinedFilePath && file.endsWith('.test.js')) {
                fs.unlinkSync(filePath);
            }
        });

        // Send the combined test file to OpenAI for fixing
        const fixedContent = await getAIResponse(`Review and fix any errors in the following Jest test code:\n\n${combinedContent} Merge into a singular test file.`);
        fs.writeFileSync(combinedFilePath, fixedContent, 'utf8');
        console.log(`Fixed test file at ${combinedFilePath}`);
    }
}

async function main() {
    try {
        console.log('Generating Jest unit tests for ', sourceDir);
        await generateTestsForDirectory(sourceDir);
        console.log('Test generation complete.');

        console.log('Combining test files...');
        await combineTests(testDir);
        console.log('Test files combined, individual files deleted, and errors fixed.');
    } catch (error) {
        console.error('Error generating tests:', error);
    }
}

main();
